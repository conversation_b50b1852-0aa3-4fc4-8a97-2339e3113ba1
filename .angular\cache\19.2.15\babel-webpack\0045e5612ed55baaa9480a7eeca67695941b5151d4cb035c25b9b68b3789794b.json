{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatDialogModule, MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nfunction ConfirmModalComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ConfirmModalComponent_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDismiss());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ConfirmModalComponent_div_5_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onConfirm());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"GENERAL.NO\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"GENERAL.YES\"), \" \");\n  }\n}\nfunction ConfirmModalComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ConfirmModalComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDismiss());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.OK\"), \" \");\n  }\n}\nexport let ConfirmModalComponent = /*#__PURE__*/(() => {\n  class ConfirmModalComponent {\n    translate;\n    dialogRef;\n    data;\n    title;\n    message;\n    okMode = false;\n    constructor(translate, dialogRef, data) {\n      this.translate = translate;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      // Update view with given values\n      this.title = data.title;\n      this.message = data.message;\n      this.okMode = data.okMode;\n    }\n    ngOnInit() {}\n    onConfirm() {\n      // Close the dialog, return true\n      this.dialogRef.close(true);\n    }\n    onDismiss() {\n      // Close the dialog, return false\n      this.dialogRef.close(false);\n    }\n    static ɵfac = function ConfirmModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfirmModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfirmModalComponent,\n      selectors: [[\"app-confirm-modal\"]],\n      decls: 7,\n      vars: 4,\n      consts: [[\"mat-dialog-title\", \"\"], [\"mat-dialog-content\", \"\"], [\"mat-dialog-actions\", \"\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function ConfirmModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h1\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"p\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, ConfirmModalComponent_div_5_Template, 7, 6, \"div\", 2)(6, ConfirmModalComponent_div_6_Template, 4, 3, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.title, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.message);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.okMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.okMode);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, MatDialogModule, i2.MatDialogTitle, i2.MatDialogActions, i2.MatDialogContent, MatButtonModule, i4.MatButton, TranslateModule, i1.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n  return ConfirmModalComponent;\n})();\n/**\n * Class to represent confirm dialog model.\n *\n * It has been kept here to keep it as part of shared component.\n */\nexport class ConfirmDialogModel {\n  title;\n  message;\n  okMode;\n  constructor(title, message, okMode) {\n    this.title = title;\n    this.message = message;\n    this.okMode = okMode;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}