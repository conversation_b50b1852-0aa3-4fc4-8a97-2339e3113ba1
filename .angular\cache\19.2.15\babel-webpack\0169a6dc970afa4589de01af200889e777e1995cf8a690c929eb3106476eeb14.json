{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../../confirm-modal/confirm-modal.component';\nimport { ProgramConditionViewMoodEnum } from 'src/app/core/enums/programs/program-condition-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/program-services/program-conditions.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nfunction SettingMaxmumSubscribeComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"img\", 3);\n    i0.ɵɵlistener(\"click\", function SettingMaxmumSubscribeComponent_ng_container_0_Template_img_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.confirmDialog());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"input\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingMaxmumSubscribeComponent_ng_container_0_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.maxmumSubscribeModel.value, $event) || (ctx_r1.maxmumSubscribeModel.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"label\", 7)(10, \"input\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingMaxmumSubscribeComponent_ng_container_0_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.maxmumSubscribeModel.isRequired, $event) || (ctx_r1.maxmumSubscribeModel.isRequired = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 9);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function SettingMaxmumSubscribeComponent_ng_container_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveProgramConditions());\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.maxmumSubscribeModel.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.close, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.maxmumSubscribeModel.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.maxmumSubscribeModel.isRequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 6, \"GENERAL.MANDATORY\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 8, \"GENERAL.SAVE\"), \" \");\n  }\n}\nfunction SettingMaxmumSubscribeComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 11)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(5, 2, \"CONDITIONDS.MAXIMUM_SUBSCRIBERS\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 4, \"CONDITIONDS.LESS_THAN\"), \"\");\n  }\n}\nfunction SettingMaxmumSubscribeComponent_ng_container_2_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL.MANDATORY\"), \" \");\n  }\n}\nfunction SettingMaxmumSubscribeComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"label\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" \\u00A0 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \" \\u00A0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"label\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \" \\u00A0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SettingMaxmumSubscribeComponent_ng_container_2_span_11_Template, 3, 3, \"span\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.maxmumSubscribeModel.title, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.maxmumSubscribeModel.value, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxmumSubscribeModel.isRequired);\n  }\n}\nexport let SettingMaxmumSubscribeComponent = /*#__PURE__*/(() => {\n  class SettingMaxmumSubscribeComponent {\n    languageService;\n    translate;\n    programConditionsService;\n    dialog;\n    alertify;\n    imagesPathesService;\n    progIdToLoadProgCond = new EventEmitter();\n    programConditionsModel = {};\n    ViewprogCondmood = ProgramConditionViewMoodEnum.conditionSettingViewMood;\n    maxmumSubscribeModel = {};\n    resultMessage = {};\n    updateProgramConditionDetailsModel = {};\n    result = '';\n    programConditionViewMoodEnum = ProgramConditionViewMoodEnum;\n    constructor(languageService, translate, programConditionsService, dialog, alertify, imagesPathesService) {\n      this.languageService = languageService;\n      this.translate = translate;\n      this.programConditionsService = programConditionsService;\n      this.dialog = dialog;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.populateData();\n    }\n    populateData() {\n      this.maxmumSubscribeModel = JSON.parse(this.programConditionsModel.progCondValue || '{}') || {};\n      this.maxmumSubscribeModel.id = this.programConditionsModel.id;\n      this.maxmumSubscribeModel.condId = this.programConditionsModel.condId;\n      this.maxmumSubscribeModel.isRequired = this.programConditionsModel.condRequired;\n      this.maxmumSubscribeModel.progId = this.programConditionsModel.progId;\n      this.maxmumSubscribeModel.title = this.programConditionsModel.title;\n    }\n    saveProgramConditions() {\n      this.resultMessage = {};\n      this.updateProgramConditionDetailsModel.id = this.maxmumSubscribeModel.id;\n      this.updateProgramConditionDetailsModel.progCondDetails = JSON.stringify(this.maxmumSubscribeModel);\n      this.updateProgramConditionDetailsModel.isRequired = this.maxmumSubscribeModel.isRequired;\n      this.programConditionsService.updateProgramConditionDetails(this.updateProgramConditionDetailsModel).subscribe(res => {\n        let response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    confirmDialog() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this condition\" : \"هل متأكد من حذف هذا الشرط\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete condition' : 'حذف الشرط', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n        if (dialogResult == true) {\n          this.programConditionsService.deleteProgramCondition(this.maxmumSubscribeModel.id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.progIdToLoadProgCond.emit(this.maxmumSubscribeModel.progId);\n              this.alertify.success(res.message || '');\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    static ɵfac = function SettingMaxmumSubscribeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingMaxmumSubscribeComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ProgramConditionsService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingMaxmumSubscribeComponent,\n      selectors: [[\"app-setting-maxmum-subscribe\"]],\n      inputs: {\n        programConditionsModel: \"programConditionsModel\",\n        ViewprogCondmood: \"ViewprogCondmood\"\n      },\n      outputs: {\n        progIdToLoadProgCond: \"progIdToLoadProgCond\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[4, \"ngIf\"], [1, \"card-setting\", \"viewMode\"], [1, \"head\"], [1, \"close\", 3, \"click\", \"src\"], [1, \"body\"], [\"type\", \"text\", 1, \"form-control\", \"w-100\", 3, \"ngModelChange\", \"ngModel\"], [1, \"row\", \"buttons-center\", \"mt-3\"], [1, \"switch\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"slider\", \"round\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [1, \"form-group\", \"w-90\"], [1, \"title\"], [1, \"title_data\"], [\"class\", \"requiredFlag\", 4, \"ngIf\"], [1, \"requiredFlag\"]],\n      template: function SettingMaxmumSubscribeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SettingMaxmumSubscribeComponent_ng_container_0_Template, 17, 10, \"ng-container\", 0)(1, SettingMaxmumSubscribeComponent_ng_container_1_Template, 11, 6, \"ng-container\", 0)(2, SettingMaxmumSubscribeComponent_ng_container_2_Template, 12, 3, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionProgramViewMood);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionSettingViewMood);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionBasicInfoViewMood);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card-setting[_ngcontent-%COMP%]{padding:1rem;width:100%;height:100%;background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem;opacity:1}.card-setting[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:1rem;margin-bottom:.5rem;display:flex;justify-content:space-between}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-size:.875rem;color:#333}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto}.card-setting[_ngcontent-%COMP%]   .w-90[_ngcontent-%COMP%]{width:90%!important}.card-setting[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0}.card-setting[_ngcontent-%COMP%]   .buttons-center[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin-left:.4rem;margin-right:.4rem}.title[_ngcontent-%COMP%]{color:var(--second_color);font-size:.875rem;font-weight:700}.requiredFlag[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.875rem;font-family:normal normal normal;opacity:1}\"]\n    });\n  }\n  return SettingMaxmumSubscribeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}