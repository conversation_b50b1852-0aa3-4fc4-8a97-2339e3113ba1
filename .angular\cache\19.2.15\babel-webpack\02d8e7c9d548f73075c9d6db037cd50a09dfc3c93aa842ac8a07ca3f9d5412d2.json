{"ast": null, "code": "import { StudentVacationRequestTabComponent } from './student-vacation-request-tab/student-vacation-request-tab.component';\nimport { StudentProgramVacationStatusEnum } from '../../../../../../core/enums/StudentProgramVacationStatus/student-program-vacation-status.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction StuVacationsRequestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"app-student-program-vacation-reject\", 5);\n    i0.ɵɵlistener(\"closePopup\", function StuVacationsRequestComponent_div_3_Template_app_student_program_vacation_reject_closePopup_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForms());\n    })(\"closeRejectedRequest\", function StuVacationsRequestComponent_div_3_Template_app_student_program_vacation_reject_closeRejectedRequest_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeRejectedRequest());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"itemStuReq\", ctx_r1.itemStuReq);\n  }\n}\nfunction StuVacationsRequestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"app-student-program-vacation-advanced-search\", 6);\n    i0.ɵɵlistener(\"closeAdvancedSearch\", function StuVacationsRequestComponent_div_4_Template_app_student_program_vacation_advanced_search_closeAdvancedSearch_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStuAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"filter\", ctx_r1.filter);\n  }\n}\nexport let StuVacationsRequestComponent = /*#__PURE__*/(() => {\n  class StuVacationsRequestComponent {\n    studentVacationRequestTab;\n    filter = {\n      statusNum: StudentProgramVacationStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortOrder: 1,\n      page: 1\n    };\n    showTap = 'Pending';\n    itemStuReq = {};\n    openStuRejectOverlay = false;\n    openStuAdvancedSearch = false;\n    constructor() {}\n    ngOnInit() {}\n    openRejectRequest(event) {\n      this.itemStuReq = event;\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n    }\n    closeRejectedRequest() {\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n      this.studentVacationRequestTab?.getStudentProgramVacationRequests();\n    }\n    closeStuAdvancedSearch(event) {\n      this.openStuAdvancedSearch = false;\n      this.filter = event;\n      this.studentVacationRequestTab?.getStudentProgramVacationRequests();\n    }\n    openStuAdvancedSearchPopup(event) {\n      this.openStuAdvancedSearch = true;\n      this.filter = event;\n    }\n    // as per issue number 3250\n    closeForms() {\n      this.openStuAdvancedSearch = false;\n      this.openStuRejectOverlay = false;\n    }\n    static ɵfac = function StuVacationsRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StuVacationsRequestComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StuVacationsRequestComponent,\n      selectors: [[\"app-stu-vacations-request\"]],\n      viewQuery: function StuVacationsRequestComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StudentVacationRequestTabComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.studentVacationRequestTab = _t.first);\n        }\n      },\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"row\"], [1, \"col-12\"], [3, \"advancedSearchEvent\", \"closePopup\", \"itemStuReq\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closePopup\", \"closeRejectedRequest\", \"itemStuReq\"], [3, \"closeAdvancedSearch\", \"filter\"]],\n      template: function StuVacationsRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-student-vacation-request-tab\", 2);\n          i0.ɵɵlistener(\"advancedSearchEvent\", function StuVacationsRequestComponent_Template_app_student_vacation_request_tab_advancedSearchEvent_2_listener($event) {\n            return ctx.openStuAdvancedSearchPopup($event);\n          })(\"closePopup\", function StuVacationsRequestComponent_Template_app_student_vacation_request_tab_closePopup_2_listener() {\n            return ctx.closeForms();\n          })(\"itemStuReq\", function StuVacationsRequestComponent_Template_app_student_vacation_request_tab_itemStuReq_2_listener($event) {\n            return ctx.openRejectRequest($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(3, StuVacationsRequestComponent_div_3_Template, 2, 1, \"div\", 3)(4, StuVacationsRequestComponent_div_4_Template, 2, 1, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.openStuRejectOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openStuAdvancedSearch);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return StuVacationsRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}