{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    languageService;\n    translate;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('DASHBOARD.TITLE'));\n    }\n    static ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 25,\n      vars: 0,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"m-0\"], [1, \"col-xl-9\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"part_one\"], [1, \"row\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"px-0\"], [1, \"col-xl-6\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-7\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-5\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-3\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"part_two\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-admin-dash-board-stud-statistic-widgets\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 5);\n          i0.ɵɵelement(8, \"app-admin-dash-board-task-count-widgets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 5);\n          i0.ɵɵelement(10, \"app-admin-dash-board-time-tasmea-count-widgets\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"div\", 6);\n          i0.ɵɵelement(13, \"app-admin-dash-board-stus-desc-count-widgets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 7);\n          i0.ɵɵelement(15, \"app-admin-dash-board-stud-total-task-degree-widgets\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 3)(17, \"div\", 8);\n          i0.ɵɵelement(18, \"app-admin-dash-board-stud-total-exam-task-degree-widgets\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 3)(21, \"div\", 8);\n          i0.ɵɵelement(22, \"app-admin-dash-board-honor-board-widgets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 8);\n          i0.ɵɵelement(24, \"app-admin-dash-board-count-matrial-prog-widgets\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [\".part_two[_ngcontent-%COMP%], .part_one[_ngcontent-%COMP%]{height:85vh;overflow-y:auto;overflow-x:hidden}@media (max-width: 64rem){.part_one[_ngcontent-%COMP%]{height:55vh;overflow-y:auto;margin-bottom:1rem;overflow-x:hidden}.part_two[_ngcontent-%COMP%]{height:30vh;overflow-y:auto}}@media (min-width: 100rem){.part_two[_ngcontent-%COMP%], .part_one[_ngcontent-%COMP%]{height:90vh}}\"]\n    });\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}