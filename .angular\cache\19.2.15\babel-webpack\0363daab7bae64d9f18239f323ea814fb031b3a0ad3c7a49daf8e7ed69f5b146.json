{"ast": null, "code": "import easingFuncs from './easing.js';\nimport { isFunction, noop } from '../core/util.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nvar Clip = function () {\n  function Clip(opts) {\n    this._inited = false;\n    this._startTime = 0;\n    this._pausedTime = 0;\n    this._paused = false;\n    this._life = opts.life || 1000;\n    this._delay = opts.delay || 0;\n    this.loop = opts.loop || false;\n    this.onframe = opts.onframe || noop;\n    this.ondestroy = opts.ondestroy || noop;\n    this.onrestart = opts.onrestart || noop;\n    opts.easing && this.setEasing(opts.easing);\n  }\n  Clip.prototype.step = function (globalTime, deltaTime) {\n    if (!this._inited) {\n      this._startTime = globalTime + this._delay;\n      this._inited = true;\n    }\n    if (this._paused) {\n      this._pausedTime += deltaTime;\n      return;\n    }\n    var life = this._life;\n    var elapsedTime = globalTime - this._startTime - this._pausedTime;\n    var percent = elapsedTime / life;\n    if (percent < 0) {\n      percent = 0;\n    }\n    percent = Math.min(percent, 1);\n    var easingFunc = this.easingFunc;\n    var schedule = easingFunc ? easingFunc(percent) : percent;\n    this.onframe(schedule);\n    if (percent === 1) {\n      if (this.loop) {\n        var remainder = elapsedTime % life;\n        this._startTime = globalTime - remainder;\n        this._pausedTime = 0;\n        this.onrestart();\n      } else {\n        return true;\n      }\n    }\n    return false;\n  };\n  Clip.prototype.pause = function () {\n    this._paused = true;\n  };\n  Clip.prototype.resume = function () {\n    this._paused = false;\n  };\n  Clip.prototype.setEasing = function (easing) {\n    this.easing = easing;\n    this.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n  };\n  return Clip;\n}();\nexport default Clip;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}