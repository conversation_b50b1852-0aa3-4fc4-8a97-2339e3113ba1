{"ast": null, "code": "import { StudTotalExamTaskDegreeEchartComponent } from \"../../../../../../../shared/components/admin-dash-board-widgets/stud-total-exam-task-degree-echart/stud-total-exam-task-degree-echart.component\";\nimport { ProgramDayTasksDetails } from \"../../../../../../../core/enums/programs/program-day-tasks-details.enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../../../core/services/program-batches-service/program-batches.service\";\nimport * as i2 from \"../../../../../../../core/services/admin-dash-bord-services/admin-dash-board.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"../../../../../../../core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@ng-select/ng-select\";\nfunction AdminStudentTotalExamTaskDegreeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"mat-radio-group\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminStudentTotalExamTaskDegreeComponent_div_8_Template_mat_radio_group_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp, $event) || (ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function AdminStudentTotalExamTaskDegreeComponent_div_8_Template_mat_radio_group_change_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeBatch());\n    });\n    i0.ɵɵelementStart(2, \"mat-radio-button\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-radio-button\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.typeExamEnum.TaskTestPhased);\n    i0.ɵɵpropertyInterpolate(\"checked\", ctx_r1.typeExamEnum.TaskTestPhased);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, \"ADMIN_DASH_BORD.PHASE_EXAM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.typeExamEnum.TaskDailyTest);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 8, \"ADMIN_DASH_BORD.DAILY_EXAM\"), \" \");\n  }\n}\nfunction AdminStudentTotalExamTaskDegreeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"input\", 12);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminStudentTotalExamTaskDegreeComponent_div_9_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay, $event) || (ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function AdminStudentTotalExamTaskDegreeComponent_div_9_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeBatch());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \" \", i0.ɵɵpipeBind1(2, 3, \"ADMIN_DASH_BORD.DAY\"), \" \");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay);\n  }\n}\nfunction AdminStudentTotalExamTaskDegreeComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"app-stud-total-exam-task-degree-echart\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"adminDashBoardStudTotalExamTaskDegree\", ctx_r1.adminDashBoardTotalExamTaskDegreeList);\n  }\n}\nexport let AdminStudentTotalExamTaskDegreeComponent = /*#__PURE__*/(() => {\n  class AdminStudentTotalExamTaskDegreeComponent {\n    programBatchesService;\n    adminDashBoardService;\n    translate;\n    alertfy;\n    examCountEchart;\n    allProgBatchs = [];\n    batId;\n    typeExamEnum = ProgramDayTasksDetails;\n    adminDashBoardTotalExamTaskDegreeReq = {\n      taskTyp: ProgramDayTasksDetails.TaskTestPhased,\n      fromDay: 1,\n      toDay: 10\n    };\n    adminDashBoardTotalExamTaskDegreeList;\n    // listOfOptions= [\n    //   {\"name\": this.translate.instant('ADMIN_DASH_BORD.PHASE_EXAM'), ID:ProgramDayTasksDetails.TaskTestPhased, \"checked\": true},\n    //   {\"name\": this.translate.instant('ADMIN_DASH_BORD.DAILY_EXAM'), ID: ProgramDayTasksDetails.TaskDailyTest, \"checked\": false}\n    // ]\n    constructor(programBatchesService, adminDashBoardService, translate, alertfy) {\n      this.programBatchesService = programBatchesService;\n      this.adminDashBoardService = adminDashBoardService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n    }\n    ngOnInit() {\n      this.getAllProgs();\n      this.getAdminDashBoardExamTaskDegree();\n    }\n    getAllProgs() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n          this.batId = '';\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.adminDashBoardTotalExamTaskDegreeList = [];\n      this.adminDashBoardTotalExamTaskDegreeReq.batId = this.batId;\n      if (this.adminDashBoardTotalExamTaskDegreeReq && !this.adminDashBoardTotalExamTaskDegreeReq.fromDay) this.adminDashBoardTotalExamTaskDegreeReq.fromDay = 1;\n      if (this.adminDashBoardTotalExamTaskDegreeReq && this.adminDashBoardTotalExamTaskDegreeReq.fromDay) this.adminDashBoardTotalExamTaskDegreeReq.toDay = this.adminDashBoardTotalExamTaskDegreeReq.fromDay + 9;\n      this.getAdminDashBoardExamTaskDegree();\n    }\n    getAdminDashBoardExamTaskDegree() {\n      this.adminDashBoardService.getAdminDashBoardStudTotalExamTaskDegree(this.adminDashBoardTotalExamTaskDegreeReq || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.adminDashBoardTotalExamTaskDegreeList = res.data;\n          this.examCountEchart?.initiate(this.adminDashBoardTotalExamTaskDegreeList || []);\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function AdminStudentTotalExamTaskDegreeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentTotalExamTaskDegreeComponent)(i0.ɵɵdirectiveInject(i1.ProgramBatchesService), i0.ɵɵdirectiveInject(i2.AdminDashBoardService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentTotalExamTaskDegreeComponent,\n      selectors: [[\"app-admin-student-total-exam-task-degree\"]],\n      viewQuery: function AdminStudentTotalExamTaskDegreeComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StudTotalExamTaskDegreeEchartComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.examCountEchart = _t.first);\n        }\n      },\n      decls: 11,\n      vars: 8,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\", \"pt-3\"], [1, \"col-3\"], [1, \"header\"], [1, \"w-75\", \"select\"], [\"bindLabel\", \"progBatNameAr\", \"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"items\", \"ngModel\"], [\"class\", \"col-3\", 4, \"ngIf\"], [\"class\", \"col-3 w-75\", 4, \"ngIf\"], [\"class\", \"col-12 \", 4, \"ngIf\"], [\"aria-label\", \"Select an option\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"px-2\", 3, \"value\", \"checked\"], [1, \"px-2\", 3, \"value\"], [1, \"col-3\", \"w-75\"], [\"type\", \"number\", \"min\", \"1\", 1, \"form-control\", \"select\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [1, \"col-12\"], [3, \"adminDashBoardStudTotalExamTaskDegree\"]],\n      template: function AdminStudentTotalExamTaskDegreeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 1)(6, \"div\", 3)(7, \"ng-select\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminStudentTotalExamTaskDegreeComponent_Template_ng_select_ngModelChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminStudentTotalExamTaskDegreeComponent_Template_ng_select_change_7_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, AdminStudentTotalExamTaskDegreeComponent_div_8_Template, 8, 10, \"div\", 5)(9, AdminStudentTotalExamTaskDegreeComponent_div_9_Template, 3, 5, \"div\", 6)(10, AdminStudentTotalExamTaskDegreeComponent_div_10_Template, 2, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, \"ADMIN_DASH_BORD.COUNT_TASK_EXAM_STUD\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"items\", ctx.allProgBatchs);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeReq);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeReq);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeList);\n        }\n      },\n      dependencies: [i5.NgIf, i6.DefaultValueAccessor, i6.NumberValueAccessor, i6.NgControlStatus, i6.MinValidator, i6.NgModel, i7.NgSelectComponent, i3.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .Register__Label[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}.program_result[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:.25rem}.part_two[_ngcontent-%COMP%], .part_one[_ngcontent-%COMP%]{height:73vh;overflow-y:auto;overflow-x:hidden}\"]\n    });\n  }\n  return AdminStudentTotalExamTaskDegreeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}