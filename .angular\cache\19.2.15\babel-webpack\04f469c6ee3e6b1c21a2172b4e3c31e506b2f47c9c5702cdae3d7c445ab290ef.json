{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { LanguageEnum } from '../../../core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../confirm-modal/confirm-modal.component';\nimport { saveAs } from \"file-saver\";\nlet TeacherDetailsViewComponent = class TeacherDetailsViewComponent {\n  teacherProfileService;\n  translate;\n  dialog;\n  alert;\n  userService;\n  excelExpService;\n  imagesPathesService;\n  hideUserDetails = new EventEmitter();\n  resiveUserId;\n  adminView = false;\n  refreshTeachersList = new EventEmitter();\n  toggel;\n  resMessage = {};\n  teacherProfileDetails = {};\n  langEnum = LanguageEnum;\n  starsSelected = 0;\n  lang = LanguageEnum;\n  message;\n  dialogData;\n  constructor(teacherProfileService, translate, dialog, alert, userService, excelExpService, imagesPathesService) {\n    this.teacherProfileService = teacherProfileService;\n    this.translate = translate;\n    this.dialog = dialog;\n    this.alert = alert;\n    this.userService = userService;\n    this.excelExpService = excelExpService;\n    this.imagesPathesService = imagesPathesService;\n  }\n  ngOnInit() {\n    this.getTeacherProfile();\n  }\n  hideUserDetailsView() {\n    this.hideUserDetails.emit(false);\n  }\n  getTeacherProfile() {\n    if (!this.resiveUserId?.usrId) {\n      this.teacherProfileDetails = {};\n      return;\n    }\n    this.teacherProfileService.viewTeacherProfileDetails(this.resiveUserId?.usrId || '').subscribe(res => {\n      if (res.isSuccess) {\n        this.teacherProfileDetails = res.data;\n      } else {\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }, error => {\n      //logging\n    });\n  }\n  onCheckboxChange() {\n    if (this.teacherProfileDetails?.isSus) {\n      this.message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to activate this Teacher\" : \"هل متأكد من  تفعيل هذا المعلم\";\n      this.dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'activate teacher' : 'تفعيل معلم', this.message);\n    } else {\n      this.message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to deactivate this Teacher\" : \"هل متأكد من عدم تفعيل هذا المعلم\";\n      this.dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Deactivate teacher' : 'عدم تفعيل معلم', this.message);\n    }\n    this.teacherProfileDetails.isSus = !this.teacherProfileDetails.isSus;\n    const dialogRef = this.dialog.open(ConfirmModalComponent, {\n      maxWidth: \"25rem\",\n      data: this.dialogData\n    });\n    dialogRef.afterClosed().subscribe(dialogResult => {\n      if (dialogResult == true) {\n        this.userService.activateAndDeactivateTheUser(this.resiveUserId?.usrId || '').subscribe(res => {\n          if (res.isSuccess) {\n            this.getTeacherProfile();\n            this.alert.success(res.message || '');\n            this.refreshTeachersList.emit();\n          } else {\n            this.alert.error(res.message || '');\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.teacherProfileDetails.isSus = !this.teacherProfileDetails.isSus;\n        // this.getTeacherProfile()\n      }\n    });\n  }\n  deleteTeacher() {\n    const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this Teacher\" : \"هل متأكد من حذف هذا المعلم\";\n    const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete teacher' : 'حذف معلم', message);\n    const dialogRef = this.dialog.open(ConfirmModalComponent, {\n      maxWidth: \"25rem\",\n      data: dialogData\n    });\n    dialogRef.afterClosed().subscribe(dialogResult => {\n      if (dialogResult == true) {\n        this.teacherProfileService.deleteTeacherById(this.resiveUserId?.usrId || '').subscribe(res => {\n          if (res.isSuccess) {\n            this.alert.success(res.message || '');\n            this.refreshTeachersList.emit();\n          } else {\n            this.alert.error(res.message || '');\n          }\n        }, error => {\n          //logging\n        });\n      }\n    });\n  }\n  exportTeacherDetailsById() {\n    this.excelExpService.exportTeacherDetailsById(this.resiveUserId?.usrId || '').subscribe(res => {\n      if (res.isSuccess) {\n        let data = res.data;\n        let buff = new Buffer(data.content, 'base64');\n        const file = new Blob([buff]);\n        saveAs(file, data.fileName);\n      }\n    }, error => {\n      //logging\n    });\n  }\n  checkNameSpace(str) {\n    let reg = new RegExp(/^ *$/);\n    return str.match(reg) === null;\n  }\n};\n__decorate([Output()], TeacherDetailsViewComponent.prototype, \"hideUserDetails\", void 0);\n__decorate([Input()], TeacherDetailsViewComponent.prototype, \"resiveUserId\", void 0);\n__decorate([Input()], TeacherDetailsViewComponent.prototype, \"adminView\", void 0);\n__decorate([Output()], TeacherDetailsViewComponent.prototype, \"refreshTeachersList\", void 0);\nTeacherDetailsViewComponent = __decorate([Component({\n  selector: 'app-teacher-details-view',\n  templateUrl: './teacher-details-view.component.html',\n  styleUrls: ['./teacher-details-view.component.scss']\n})], TeacherDetailsViewComponent);\nexport { TeacherDetailsViewComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}