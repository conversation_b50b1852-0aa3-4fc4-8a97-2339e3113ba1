{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isFunction, keys } from 'zrender/lib/core/util.js';\nvar SYMBOL_PROPS_WITH_CB = ['symbol', 'symbolSize', 'symbolRotate', 'symbolOffset'];\nvar SYMBOL_PROPS = SYMBOL_PROPS_WITH_CB.concat(['symbolKeepAspect']);\n// Encoding visual for all series include which is filtered for legend drawing\nvar seriesSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    if (seriesModel.legendIcon) {\n      data.setVisual('legendIcon', seriesModel.legendIcon);\n    }\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    var symbolOptions = {};\n    var symbolOptionsCb = {};\n    var hasCallback = false;\n    for (var i = 0; i < SYMBOL_PROPS_WITH_CB.length; i++) {\n      var symbolPropName = SYMBOL_PROPS_WITH_CB[i];\n      var val = seriesModel.get(symbolPropName);\n      if (isFunction(val)) {\n        hasCallback = true;\n        symbolOptionsCb[symbolPropName] = val;\n      } else {\n        symbolOptions[symbolPropName] = val;\n      }\n    }\n    symbolOptions.symbol = symbolOptions.symbol || seriesModel.defaultSymbol;\n    data.setVisual(extend({\n      legendIcon: seriesModel.legendIcon || symbolOptions.symbol,\n      symbolKeepAspect: seriesModel.get('symbolKeepAspect')\n    }, symbolOptions));\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var symbolPropsCb = keys(symbolOptionsCb);\n    function dataEach(data, idx) {\n      var rawValue = seriesModel.getRawValue(idx);\n      var params = seriesModel.getDataParams(idx);\n      for (var i = 0; i < symbolPropsCb.length; i++) {\n        var symbolPropName = symbolPropsCb[i];\n        data.setItemVisual(idx, symbolPropName, symbolOptionsCb[symbolPropName](rawValue, params));\n      }\n    }\n    return {\n      dataEach: hasCallback ? dataEach : null\n    };\n  }\n};\nvar dataSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      for (var i = 0; i < SYMBOL_PROPS.length; i++) {\n        var symbolPropName = SYMBOL_PROPS[i];\n        var val = itemModel.getShallow(symbolPropName, true);\n        if (val != null) {\n          data.setItemVisual(idx, symbolPropName, val);\n        }\n      }\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport { seriesSymbolTask, dataSymbolTask };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}