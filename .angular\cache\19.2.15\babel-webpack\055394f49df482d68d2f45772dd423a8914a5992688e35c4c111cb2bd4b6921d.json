{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map } from 'zrender/lib/core/util.js';\nimport { linearMap, getPixelPrecision, round } from '../util/number.js';\nimport { createAxisTicks, createAxisLabels, calculateCategoryInterval } from './axisTickLabelBuilder.js';\nvar NORMALIZED_EXTENT = [0, 1];\n/**\r\n * Base class of Axis.\r\n */\nvar Axis = /** @class */function () {\n  function Axis(dim, scale, extent) {\n    this.onBand = false;\n    this.inverse = false;\n    this.dim = dim;\n    this.scale = scale;\n    this._extent = extent || [0, 0];\n  }\n  /**\r\n   * If axis extent contain given coord\r\n   */\n  Axis.prototype.contain = function (coord) {\n    var extent = this._extent;\n    var min = Math.min(extent[0], extent[1]);\n    var max = Math.max(extent[0], extent[1]);\n    return coord >= min && coord <= max;\n  };\n  /**\r\n   * If axis extent contain given data\r\n   */\n  Axis.prototype.containData = function (data) {\n    return this.scale.contain(data);\n  };\n  /**\r\n   * Get coord extent.\r\n   */\n  Axis.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\r\n   * Get precision used for formatting\r\n   */\n  Axis.prototype.getPixelPrecision = function (dataExtent) {\n    return getPixelPrecision(dataExtent || this.scale.getExtent(), this._extent);\n  };\n  /**\r\n   * Set coord extent\r\n   */\n  Axis.prototype.setExtent = function (start, end) {\n    var extent = this._extent;\n    extent[0] = start;\n    extent[1] = end;\n  };\n  /**\r\n   * Convert data to coord. Data is the rank if it has an ordinal scale\r\n   */\n  Axis.prototype.dataToCoord = function (data, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    data = scale.normalize(data);\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    return linearMap(data, NORMALIZED_EXTENT, extent, clamp);\n  };\n  /**\r\n   * Convert coord to data. Data is the rank if it has an ordinal scale\r\n   */\n  Axis.prototype.coordToData = function (coord, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    var t = linearMap(coord, extent, NORMALIZED_EXTENT, clamp);\n    return this.scale.scale(t);\n  };\n  /**\r\n   * Convert pixel point to data in axis\r\n   */\n  Axis.prototype.pointToData = function (point, clamp) {\n    // Should be implemented in derived class if necessary.\n    return;\n  };\n  /**\r\n   * Different from `zrUtil.map(axis.getTicks(), axis.dataToCoord, axis)`,\r\n   * `axis.getTicksCoords` considers `onBand`, which is used by\r\n   * `boundaryGap:true` of category axis and splitLine and splitArea.\r\n   * @param opt.tickModel default: axis.model.getModel('axisTick')\r\n   * @param opt.clamp If `true`, the first and the last\r\n   *        tick must be at the axis end points. Otherwise, clip ticks\r\n   *        that outside the axis extent.\r\n   */\n  Axis.prototype.getTicksCoords = function (opt) {\n    opt = opt || {};\n    var tickModel = opt.tickModel || this.getTickModel();\n    var result = createAxisTicks(this, tickModel);\n    var ticks = result.ticks;\n    var ticksCoords = map(ticks, function (tickVal) {\n      return {\n        coord: this.dataToCoord(this.scale.type === 'ordinal' ? this.scale.getRawOrdinalNumber(tickVal) : tickVal),\n        tickValue: tickVal\n      };\n    }, this);\n    var alignWithLabel = tickModel.get('alignWithLabel');\n    fixOnBandTicksCoords(this, ticksCoords, alignWithLabel, opt.clamp);\n    return ticksCoords;\n  };\n  Axis.prototype.getMinorTicksCoords = function () {\n    if (this.scale.type === 'ordinal') {\n      // Category axis doesn't support minor ticks\n      return [];\n    }\n    var minorTickModel = this.model.getModel('minorTick');\n    var splitNumber = minorTickModel.get('splitNumber');\n    // Protection.\n    if (!(splitNumber > 0 && splitNumber < 100)) {\n      splitNumber = 5;\n    }\n    var minorTicks = this.scale.getMinorTicks(splitNumber);\n    var minorTicksCoords = map(minorTicks, function (minorTicksGroup) {\n      return map(minorTicksGroup, function (minorTick) {\n        return {\n          coord: this.dataToCoord(minorTick),\n          tickValue: minorTick\n        };\n      }, this);\n    }, this);\n    return minorTicksCoords;\n  };\n  Axis.prototype.getViewLabels = function () {\n    return createAxisLabels(this).labels;\n  };\n  Axis.prototype.getLabelModel = function () {\n    return this.model.getModel('axisLabel');\n  };\n  /**\r\n   * Notice here we only get the default tick model. For splitLine\r\n   * or splitArea, we should pass the splitLineModel or splitAreaModel\r\n   * manually when calling `getTicksCoords`.\r\n   * In GL, this method may be overridden to:\r\n   * `axisModel.getModel('axisTick', grid3DModel.getModel('axisTick'));`\r\n   */\n  Axis.prototype.getTickModel = function () {\n    return this.model.getModel('axisTick');\n  };\n  /**\r\n   * Get width of band\r\n   */\n  Axis.prototype.getBandWidth = function () {\n    var axisExtent = this._extent;\n    var dataExtent = this.scale.getExtent();\n    var len = dataExtent[1] - dataExtent[0] + (this.onBand ? 1 : 0);\n    // Fix #2728, avoid NaN when only one data.\n    len === 0 && (len = 1);\n    var size = Math.abs(axisExtent[1] - axisExtent[0]);\n    return Math.abs(size) / len;\n  };\n  /**\r\n   * Only be called in category axis.\r\n   * Can be overridden, consider other axes like in 3D.\r\n   * @return Auto interval for cateogry axis tick and label\r\n   */\n  Axis.prototype.calculateCategoryInterval = function () {\n    return calculateCategoryInterval(this);\n  };\n  return Axis;\n}();\nfunction fixExtentWithBands(extent, nTick) {\n  var size = extent[1] - extent[0];\n  var len = nTick;\n  var margin = size / len / 2;\n  extent[0] += margin;\n  extent[1] -= margin;\n}\n// If axis has labels [1, 2, 3, 4]. Bands on the axis are\n// |---1---|---2---|---3---|---4---|.\n// So the displayed ticks and splitLine/splitArea should between\n// each data item, otherwise cause misleading (e.g., split tow bars\n// of a single data item when there are two bar series).\n// Also consider if tickCategoryInterval > 0 and onBand, ticks and\n// splitLine/spliteArea should layout appropriately corresponding\n// to displayed labels. (So we should not use `getBandWidth` in this\n// case).\nfunction fixOnBandTicksCoords(axis, ticksCoords, alignWithLabel, clamp) {\n  var ticksLen = ticksCoords.length;\n  if (!axis.onBand || alignWithLabel || !ticksLen) {\n    return;\n  }\n  var axisExtent = axis.getExtent();\n  var last;\n  var diffSize;\n  if (ticksLen === 1) {\n    ticksCoords[0].coord = axisExtent[0];\n    last = ticksCoords[1] = {\n      coord: axisExtent[1],\n      tickValue: ticksCoords[0].tickValue\n    };\n  } else {\n    var crossLen = ticksCoords[ticksLen - 1].tickValue - ticksCoords[0].tickValue;\n    var shift_1 = (ticksCoords[ticksLen - 1].coord - ticksCoords[0].coord) / crossLen;\n    each(ticksCoords, function (ticksItem) {\n      ticksItem.coord -= shift_1 / 2;\n    });\n    var dataExtent = axis.scale.getExtent();\n    diffSize = 1 + dataExtent[1] - ticksCoords[ticksLen - 1].tickValue;\n    last = {\n      coord: ticksCoords[ticksLen - 1].coord + shift_1 * diffSize,\n      tickValue: dataExtent[1] + 1\n    };\n    ticksCoords.push(last);\n  }\n  var inverse = axisExtent[0] > axisExtent[1];\n  // Handling clamp.\n  if (littleThan(ticksCoords[0].coord, axisExtent[0])) {\n    clamp ? ticksCoords[0].coord = axisExtent[0] : ticksCoords.shift();\n  }\n  if (clamp && littleThan(axisExtent[0], ticksCoords[0].coord)) {\n    ticksCoords.unshift({\n      coord: axisExtent[0]\n    });\n  }\n  if (littleThan(axisExtent[1], last.coord)) {\n    clamp ? last.coord = axisExtent[1] : ticksCoords.pop();\n  }\n  if (clamp && littleThan(last.coord, axisExtent[1])) {\n    ticksCoords.push({\n      coord: axisExtent[1]\n    });\n  }\n  function littleThan(a, b) {\n    // Avoid rounding error cause calculated tick coord different with extent.\n    // It may cause an extra unnecessary tick added.\n    a = round(a);\n    b = round(b);\n    return inverse ? a > b : a < b;\n  }\n}\nexport default Axis;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}