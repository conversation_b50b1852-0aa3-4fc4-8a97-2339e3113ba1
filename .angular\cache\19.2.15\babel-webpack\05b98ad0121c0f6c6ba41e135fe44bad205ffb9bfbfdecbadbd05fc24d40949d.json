{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let TeacherProfileService = /*#__PURE__*/(() => {\n  class TeacherProfileService {\n    http;\n    updateTeacherUrl = environment.baseUrl + 'Teacher/update-teacher-profile/';\n    viewTeacherProfileDetailsURL = environment.baseUrl + 'Teacher/view-teacher-profile-details/';\n    getTeacherSystemSubscriptionAdvancedFilterUrl = environment.baseUrl + 'Teacher/get-teacher-system-subscription-advanced-filter/';\n    teacherSubscriptionsAcceptanceUrl = environment.baseUrl + 'Teacher/accept-teacher-system-subscription/';\n    TeacherSubscriptionsRejectionUrl = environment.baseUrl + 'Teacher/reject-teacher-system-subscription/';\n    deleteTeacherUrl = environment.baseUrl + 'Teacher/delete-teacher/';\n    changeProfileStatusUrl = environment.baseUrl + 'Teacher/change-profile-status/';\n    constructor(http) {\n      this.http = http;\n    }\n    updateTeacher(model) {\n      return this.http.put(this.updateTeacherUrl, model);\n    }\n    viewTeacherProfileDetails(id) {\n      return this.http.get(this.viewTeacherProfileDetailsURL + id);\n    }\n    getTeacherSystemSubscriptionAdvancedFilter(model) {\n      return this.http.post(this.getTeacherSystemSubscriptionAdvancedFilterUrl, model);\n    }\n    teacherSubscriptionsAcceptance(model) {\n      return this.http.put(this.teacherSubscriptionsAcceptanceUrl, model);\n    }\n    teacherSubscriptionsRejection(model) {\n      return this.http.put(this.TeacherSubscriptionsRejectionUrl, model);\n    }\n    deleteTeacherById(id) {\n      return this.http.delete(this.deleteTeacherUrl + id);\n    }\n    changeProfileStatus(UserId) {\n      return this.http.put(this.changeProfileStatusUrl + UserId, null);\n    }\n    static ɵfac = function TeacherProfileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherProfileService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TeacherProfileService,\n      factory: TeacherProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TeacherProfileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}