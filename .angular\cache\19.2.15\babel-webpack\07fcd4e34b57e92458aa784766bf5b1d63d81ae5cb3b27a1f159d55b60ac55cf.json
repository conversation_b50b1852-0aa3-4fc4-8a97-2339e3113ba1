{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from '../../../../core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"default_btn\": a0,\n  \"asend_desend_btn\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"asend_desend_btn\": a0,\n  \"default_btn\": a1\n});\nfunction UserGroupManagementComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.sort, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.desend, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.desend, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.sort, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.desend, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.desend, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserGroupManagementComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"app-user-group-management-card\", 19);\n    i0.ɵɵlistener(\"refreshUsersCards\", function UserGroupManagementComponent_div_28_Template_app_user_group_management_card_refreshUsersCards_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.refreshUsersCardsMethod());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"userData\", item_r3);\n  }\n}\nfunction UserGroupManagementComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let UserGroupManagementComponent = /*#__PURE__*/(() => {\n  class UserGroupManagementComponent {\n    translate;\n    imagesPathesService;\n    listUsers = [];\n    selectedRoleId = '';\n    AddUserOverLayMethodEvent = new EventEmitter();\n    filter = {\n      skip: 0,\n      take: 9,\n      page: 1,\n      sortOrder: 1\n    };\n    refreshUsersCards = new EventEmitter();\n    numberPerRow = 3;\n    totalCount = 0;\n    orderTypeToggel = 1;\n    usersRoleFilterEvent = new EventEmitter();\n    currentWindowWidth;\n    constructor(translate, imagesPathesService) {\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.filter.sortField = 'requestdate';\n      this.numberPerRow = window.innerWidth > BaseConstantModel.labtop ? 3 : 2;\n    }\n    onResize(event) {\n      if (event.target.innerWidth > BaseConstantModel.labtop) {\n        this.numberPerRow = 3;\n      }\n      if (event.target.innerWidth <= BaseConstantModel.labtop && event.target.innerWidth >= BaseConstantModel.tablet_screen) {\n        this.numberPerRow = 2;\n      }\n      if (event.target.innerWidth < BaseConstantModel.mobile_screen) {\n        this.numberPerRow = 1;\n      }\n    }\n    //End of dynamic number per one line according to screen size responsiveness.\n    filterByText(searchKey) {\n      this.filter.usrName = searchKey;\n      this.filter.roleId = this.selectedRoleId;\n      this.usersRoleFilterEvent.emit(this.filter);\n    }\n    openAddUserOverLay() {\n      this.AddUserOverLayMethodEvent.emit(this.filter);\n    }\n    refreshUsersCardsMethod() {\n      this.refreshUsersCards.emit();\n    }\n    sortUsersCardsByName() {\n      this.filter.sortField = this.translate.currentLang === LanguageEnum.ar ? 'fullNameAr' : 'fullNameEn';\n      this.filter.sortOrder = this.orderTypeToggel = this.orderTypeToggel === 1 ? -1 : 1;\n      this.usersRoleFilterEvent.emit(this.filter);\n    }\n    sortUsersCardsByNameOrderType() {\n      if ((this.filter.sortField === 'fullNameAr' || this.filter.sortField === 'fullNameEn') && this.filter.sortOrder == 1) {\n        return 'asend';\n      }\n      if ((this.filter.sortField === 'fullNameAr' || this.filter.sortField === 'fullNameEn') && this.filter.sortOrder == -1) {\n        return 'desend';\n      }\n      return '';\n    }\n    sortByUsersCardsRequestDate() {\n      this.filter.sortField = 'requestdate';\n      this.filter.sortOrder = this.orderTypeToggel = this.orderTypeToggel === 1 ? -1 : 1;\n      this.usersRoleFilterEvent.emit(this.filter);\n    }\n    sortByUsersCardsRequestDateOrderType() {\n      if (this.filter.sortField === 'requestdate' && this.filter.sortOrder == 1) {\n        return 'asend';\n      }\n      if (this.filter.sortField === 'requestdate' && this.filter.sortOrder == -1) {\n        return 'desend';\n      }\n      return '';\n    }\n    onUsersCardsPageChange() {\n      this.filter.skip = (this.filter.page - 1) * this.filter.take;\n      this.usersRoleFilterEvent.emit(this.filter);\n    }\n    static ɵfac = function UserGroupManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserGroupManagementComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserGroupManagementComponent,\n      selectors: [[\"app-user-group-management\"]],\n      inputs: {\n        listUsers: \"listUsers\",\n        selectedRoleId: \"selectedRoleId\",\n        filter: \"filter\",\n        numberPerRow: \"numberPerRow\",\n        totalCount: \"totalCount\"\n      },\n      outputs: {\n        AddUserOverLayMethodEvent: \"AddUserOverLayMethodEvent\",\n        refreshUsersCards: \"refreshUsersCards\",\n        usersRoleFilterEvent: \"usersRoleFilterEvent\"\n      },\n      decls: 30,\n      vars: 35,\n      consts: [[1, \"group_user\"], [1, \"col-12\", \"p-0\"], [1, \"row\"], [1, \"col-xl-7\", \"col-lg-5\", \"col-md-5\", \"col-sm-6\", \"col-xs-12\"], [\"for\", \"\", 1, \"label_sreach\"], [1, \"col-xl-5\", \"col-lg-7\", \"col-md-7\", \"col-sm-6\", \"col-xs-12\", \"d-flex\", \"align-items-center\", \"justify-content-end\"], [3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", \"mx-2\", \"px-0\", 3, \"click\"], [1, \"row\", \"scientific-problem-grid\"], [1, \"col-xl-9\", \"col-lg-9\", \"col-md-9\", \"col-sm-12\", \"col-xs-12\", \"col-9\", \"p-0\", \"d-flex\", \"justify-content-start\"], [3, \"pageChange\", \"maxSize\", \"collectionSize\", \"page\", \"pageSize\", \"rotate\", \"boundaryLinks\"], [3, \"click\", \"ngClass\"], [4, \"ngIf\"], [1, \"row\", \"mt-2\"], [\"class\", \"col-xl-4 col-lg-6 col-md-6 col-sm-12  my-3 w-100\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"asend_btn\", 3, \"src\"], [1, \"desend_btn\", 3, \"src\"], [1, \"col-xl-4\", \"col-lg-6\", \"col-md-6\", \"col-sm-12\", \"my-3\", \"w-100\"], [1, \"mb-2\", 3, \"refreshUsersCards\", \"userData\"], [1, \"col-12\"]],\n      template: function UserGroupManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"app-search-input\", 6);\n          i0.ɵɵlistener(\"searchTerm\", function UserGroupManagementComponent_Template_app_search_input_searchTerm_8_listener($event) {\n            return ctx.filterByText($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 7);\n          i0.ɵɵlistener(\"click\", function UserGroupManagementComponent_Template_a_click_9_listener() {\n            return ctx.openAddUserOverLay();\n          });\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"ngb-pagination\", 10);\n          i0.ɵɵtwoWayListener(\"pageChange\", function UserGroupManagementComponent_Template_ngb_pagination_pageChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filter.page, $event) || (ctx.filter.page = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function UserGroupManagementComponent_Template_ngb_pagination_pageChange_14_listener() {\n            return ctx.onUsersCardsPageChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function UserGroupManagementComponent_Template_button_click_15_listener() {\n            return ctx.sortUsersCardsByName();\n          });\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵtemplate(18, UserGroupManagementComponent_span_18_Template, 2, 1, \"span\", 12)(19, UserGroupManagementComponent_span_19_Template, 2, 1, \"span\", 12)(20, UserGroupManagementComponent_span_20_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function UserGroupManagementComponent_Template_button_click_21_listener() {\n            return ctx.sortByUsersCardsRequestDate();\n          });\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵtemplate(24, UserGroupManagementComponent_span_24_Template, 2, 1, \"span\", 12)(25, UserGroupManagementComponent_span_25_Template, 2, 1, \"span\", 12)(26, UserGroupManagementComponent_span_26_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 13);\n          i0.ɵɵtemplate(28, UserGroupManagementComponent_div_28_Template, 2, 1, \"div\", 14)(29, UserGroupManagementComponent_div_29_Template, 3, 3, \"div\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 21, \"Role_Management.INDIVIDUALS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"searchKey\", ctx.filter.usrName || \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 23, \"USER_MANAGEMENT.ADD_USER\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"maxSize\", 3)(\"collectionSize\", ctx.totalCount);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.filter.page);\n          i0.ɵɵproperty(\"pageSize\", ctx.filter.take)(\"rotate\", true)(\"boundaryLinks\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(29, _c0, ctx.filter.sortField != \"fullNameAr\" && ctx.filter.sortField != \"fullNameEn\", ctx.filter.sortField != \"requestdate\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 25, \"User_Requests.SORT_NAME\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filter.sortField != \"fullNameAr\" && ctx.filter.sortField != \"fullNameEn\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sortUsersCardsByNameOrderType() == \"asend\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sortUsersCardsByNameOrderType() == \"desend\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(32, _c1, ctx.filter.sortField != \"fullNameAr\" && ctx.filter.sortField != \"fullNameEn\", ctx.filter.sortField != \"requestdate\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 27, \"User_Requests.SORT_DATE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filter.sortField != \"requestdate\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sortByUsersCardsRequestDateOrderType() == \"asend\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sortByUsersCardsRequestDateOrderType() == \"desend\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUsers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalCount === 0);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i1.TranslatePipe],\n      styles: [\".group_user[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;overflow-y:auto;margin-top:1rem}.group_user[_ngcontent-%COMP%]:lang(ar){text-align:right}.group_user[_ngcontent-%COMP%]:lang(en){text-align:left}.group_user[_ngcontent-%COMP%]   .label_sreach[_ngcontent-%COMP%]{color:#333;font-size:1.125rem;padding:.5rem;font-weight:700}.group_user[_ngcontent-%COMP%]   .m-t-15[_ngcontent-%COMP%]{margin-top:1.25rem}.group_user[_ngcontent-%COMP%]   .p-t-15[_ngcontent-%COMP%]{padding-top:1rem}.group_user[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:66vh;max-height:66vh;overflow-y:auto}.group_user[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:100%;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.group_user[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:100%;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.group_user[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}.group_user[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{width:95%;border-color:transparent}.group_user[_ngcontent-%COMP%]   .asend_desend_btn[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff;border:.063rem solid var(--second_color);width:auto;font-size:.75rem;height:2rem;border-radius:.5rem;padding:0 1rem;margin:0 .3rem}.group_user[_ngcontent-%COMP%]   .default_btn[_ngcontent-%COMP%]{background-color:#fff;color:var(--second_color);border:.063rem solid var(--second_color);width:auto;font-size:.75rem;height:2.125rem;border-radius:.5rem;padding:0 1rem;margin:0 .3rem}.group_user[_ngcontent-%COMP%]   .asend_btn[_ngcontent-%COMP%]{transform:rotate(180deg)}.group_user[_ngcontent-%COMP%]   .asend_btn[_ngcontent-%COMP%], .group_user[_ngcontent-%COMP%]   .desend_btn[_ngcontent-%COMP%]{margin:0 .3rem}.group_user[_ngcontent-%COMP%]   .text-right[_ngcontent-%COMP%]:lang(ar){text-align:left!important}.group_user[_ngcontent-%COMP%]   .text-right[_ngcontent-%COMP%]:lang(en){text-align:right!important}.group_user[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%]{color:var(--main_color)}.group_user[_ngcontent-%COMP%]   a.disabled[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}.group_user[_ngcontent-%COMP%]   .fa-check[_ngcontent-%COMP%]{color:var(--main_color)}.group_user[_ngcontent-%COMP%]   .default_btn[_ngcontent-%COMP%]{padding:0 .5rem}.group_user[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}\"]\n    });\n  }\n  return UserGroupManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}