{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { ECPolygon } from '../line/poly.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { bind } from 'zrender/lib/core/util.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar ThemeRiverView = /** @class */function (_super) {\n  __extends(ThemeRiverView, _super);\n  function ThemeRiverView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ThemeRiverView.type;\n    _this._layers = [];\n    return _this;\n  }\n  ThemeRiverView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var self = this;\n    var group = this.group;\n    var layersSeries = seriesModel.getLayerSeries();\n    var layoutInfo = data.getLayout('layoutInfo');\n    var rect = layoutInfo.rect;\n    var boundaryGap = layoutInfo.boundaryGap;\n    group.x = 0;\n    group.y = rect.y + boundaryGap[0];\n    function keyGetter(item) {\n      return item.name;\n    }\n    var dataDiffer = new DataDiffer(this._layersSeries || [], layersSeries, keyGetter, keyGetter);\n    var newLayersGroups = [];\n    dataDiffer.add(bind(process, this, 'add')).update(bind(process, this, 'update')).remove(bind(process, this, 'remove')).execute();\n    function process(status, idx, oldIdx) {\n      var oldLayersGroups = self._layers;\n      if (status === 'remove') {\n        group.remove(oldLayersGroups[idx]);\n        return;\n      }\n      var points0 = [];\n      var points1 = [];\n      var style;\n      var indices = layersSeries[idx].indices;\n      var j = 0;\n      for (; j < indices.length; j++) {\n        var layout = data.getItemLayout(indices[j]);\n        var x = layout.x;\n        var y0 = layout.y0;\n        var y = layout.y;\n        points0.push(x, y0);\n        points1.push(x, y0 + y);\n        style = data.getItemVisual(indices[j], 'style');\n      }\n      var polygon;\n      var textLayout = data.getItemLayout(indices[0]);\n      var labelModel = seriesModel.getModel('label');\n      var margin = labelModel.get('margin');\n      var emphasisModel = seriesModel.getModel('emphasis');\n      if (status === 'add') {\n        var layerGroup = newLayersGroups[idx] = new graphic.Group();\n        polygon = new ECPolygon({\n          shape: {\n            points: points0,\n            stackedOnPoints: points1,\n            smooth: 0.4,\n            stackedOnSmooth: 0.4,\n            smoothConstraint: false\n          },\n          z2: 0\n        });\n        layerGroup.add(polygon);\n        group.add(layerGroup);\n        if (seriesModel.isAnimationEnabled()) {\n          polygon.setClipPath(createGridClipShape(polygon.getBoundingRect(), seriesModel, function () {\n            polygon.removeClipPath();\n          }));\n        }\n      } else {\n        var layerGroup = oldLayersGroups[oldIdx];\n        polygon = layerGroup.childAt(0);\n        group.add(layerGroup);\n        newLayersGroups[idx] = layerGroup;\n        graphic.updateProps(polygon, {\n          shape: {\n            points: points0,\n            stackedOnPoints: points1\n          }\n        }, seriesModel);\n        saveOldStyle(polygon);\n      }\n      setLabelStyle(polygon, getLabelStatesModels(seriesModel), {\n        labelDataIndex: indices[j - 1],\n        defaultText: data.getName(indices[j - 1]),\n        inheritColor: style.fill\n      }, {\n        normal: {\n          verticalAlign: 'middle'\n          // align: 'right'\n        }\n      });\n      polygon.setTextConfig({\n        position: null,\n        local: true\n      });\n      var labelEl = polygon.getTextContent();\n      // TODO More label position options.\n      if (labelEl) {\n        labelEl.x = textLayout.x - margin;\n        labelEl.y = textLayout.y0 + textLayout.y / 2;\n      }\n      polygon.useStyle(style);\n      data.setItemGraphicEl(idx, polygon);\n      setStatesStylesFromModel(polygon, seriesModel);\n      toggleHoverEmphasis(polygon, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n    }\n    this._layersSeries = layersSeries;\n    this._layers = newLayersGroups;\n  };\n  ThemeRiverView.type = 'themeRiver';\n  return ThemeRiverView;\n}(ChartView);\n;\n// add animation to the view\nfunction createGridClipShape(rect, seriesModel, cb) {\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x - 10,\n      y: rect.y - 10,\n      width: 0,\n      height: rect.height + 20\n    }\n  });\n  graphic.initProps(rectEl, {\n    shape: {\n      x: rect.x - 50,\n      width: rect.width + 100,\n      height: rect.height + 20\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nexport default ThemeRiverView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}