{"ast": null, "code": "import * as moment from 'moment-hijri';\nimport { moveItemInArray, transferArrayItem } from \"@angular/cdk/drag-drop\";\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ScientificProblemUsersEnum } from 'src/app/core/enums/scientific-problem-users-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = a0 => [a0];\nconst _c1 = () => ({\n  initialCountry: \"in\"\n});\nfunction SharedMaterialComponent_ng_template_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum. Why do we use it? It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like). \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \" Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SharedMaterialComponent_mat_expansion_panel_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 72)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-panel-description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 73);\n    i0.ɵɵtext(6, \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, .\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r2.title, \" \");\n  }\n}\nfunction SharedMaterialComponent_mat_expansion_panel_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 74)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-panel-description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"This is the primary content of the panel.\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r3.title, \" \");\n  }\n}\nfunction SharedMaterialComponent_div_171_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"app-custome-card\", 75);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", item_r4.title)(\"content\", item_r4.content)(\"imgPath\", item_r4.imgPath);\n  }\n}\nfunction SharedMaterialComponent_div_177_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"app-card-student-scientific-problem\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const card_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scientificProblem\", card_r5);\n  }\n}\nfunction SharedMaterialComponent_div_180_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵelement(1, \"app-card-admin-scientific-problem\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const Admincard_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scientificProblem\", Admincard_r6);\n  }\n}\nfunction SharedMaterialComponent_div_187_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r7.id, \" \");\n  }\n}\nfunction SharedMaterialComponent_div_207_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-question-template\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const q_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionTemplate\", q_r8);\n  }\n}\nfunction SharedMaterialComponent_div_213_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-question-template\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const q_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionTemplate\", q_r9);\n  }\n}\nfunction SharedMaterialComponent_div_213_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SharedMaterialComponent_div_213_div_2_Template, 2, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.examJson, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.exam.questions);\n  }\n}\nexport let SharedMaterialComponent = /*#__PURE__*/(() => {\n  class SharedMaterialComponent {\n    dialog;\n    domSanitizer;\n    attachmentService;\n    imagesPathesService;\n    hijri = false;\n    milady = false;\n    dataPinding;\n    higriPinding;\n    MiladyPinding;\n    exam = {\n      questions: []\n    };\n    submitExam = false;\n    examJson;\n    checked = false;\n    indeterminate = false;\n    // labelPosition: 'before' | 'after' = 'after';\n    disabled = false;\n    panelOpenState = false;\n    pp = '222222';\n    telInputParam = {\n      // phoneNumber:'+201062100486',\n      isRequired: true,\n      countryIsoCode: '{\"initialCountry\": \"sa\"}'\n    };\n    passdata = new Date();\n    voiceUrl;\n    adminCard = ScientificProblemUsersEnum.Admin;\n    constructor(dialog, domSanitizer, attachmentService, imagesPathesService) {\n      this.dialog = dialog;\n      this.domSanitizer = domSanitizer;\n      this.attachmentService = attachmentService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.setScssImages();\n    }\n    setScssImages() {\n      this.imagesPathesService.setDownloadInStyle();\n    }\n    unassignedTasks = [{\n      id: '1',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from'\n      // description: 'This is description of tasks 1'\n    }, {\n      id: '2',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from'\n      // description: 'This is description of tasks 2'\n    }, {\n      id: '3',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from'\n      // description: 'This is description of tasks 3'\n    }];\n    assignedTasks = [{\n      id: '4',\n      title: 'Task 4'\n      // description: 'This is description of tasks 4'\n    }, {\n      id: '5',\n      title: 'Task 5'\n      // description: 'This is description of tasks 5'\n    }, {\n      id: '6',\n      title: 'Task 6'\n      // description: 'This is description of tasks 6'\n    }];\n    drop(event) {\n      if (event.previousContainer === event.container) {\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      } else {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      }\n    }\n    /*\n     * custome pop-up\n     */\n    result = '';\n    confirmDialog() {\n      const message = `Are you sure you want to do this?`;\n      const dialogData = new ConfirmDialogModel(\"Confirm Action\", message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n      });\n    }\n    /*\n    * custome card\n    */\n    cardLst = [{\n      title: 'title ABC',\n      content: 'content ABC',\n      imgPath: this.imagesPathesService.book\n    }, {\n      title: 'title CDE',\n      content: 'content CDE',\n      imgPath: this.imagesPathesService.mic\n    }, {\n      title: 'title EFG',\n      content: 'content EFG',\n      imgPath: this.imagesPathesService.book\n    }];\n    student_card_scientificProblem = [{\n      question: 'gsgs sfsegf arfawr 1',\n      reply: 'gry qr qarq ',\n      huffazNo: 4150,\n      scCreationDate: '03-05-1442'\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content content erfawera ',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442'\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content se aerfarf content',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442'\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content se aerfarf content',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442'\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content se aerfarf content',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442'\n    }];\n    admin_card_scientificProblem = [{\n      question: 'aerar ey wywww t1',\n      reply: 'content content erfawera ',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442',\n      usrId: \"سيف الدين ابراهيم\",\n      progId: \"اسم البرنام1\",\n      day: \"الواجب اليومي - يوم \"\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content content erfawera ',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442',\n      usrId: \"سيف الدين ابراهيم\",\n      progId: \"اسم البرنام1\",\n      day: \"الواجب اليومي - يوم \"\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content content erfawera ',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442',\n      usrId: \"سيف الدين ابراهيم\",\n      progId: \"اسم البرنام1\",\n      day: \"الواجب اليومي - يوم \"\n    }, {\n      question: 'aerar ey wywww t1',\n      reply: 'content content erfawera ',\n      huffazNo: 4050,\n      scCreationDate: '03-05-1442',\n      usrId: \"سيف الدين ابراهيم\",\n      progId: \"اسم البرنام1\",\n      day: \"الواجب اليومي - يوم \"\n    }];\n    /*\n     * custome accordion\n     */\n    items = [{\n      id: '1',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test test test test test test test test test test test test test test test test test test test test \"\n      // description: 'This is description of tasks 1'\n    }, {\n      id: '2',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test341 test341 test341 test341 test341 test341 test341 test341 test341 test341 \"\n      // description: 'This is description of tasks 2'\n    }, {\n      id: '3',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 \"\n      // description: 'This is description of tasks 3'\n    }, {\n      id: '4',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 \"\n      // description: 'This is description of tasks 3'\n    }, {\n      id: '5',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 \"\n      // description: 'This is description of tasks 3'\n    }, {\n      id: '6',\n      title: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from',\n      paragraph: \"test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 test12234 \"\n      // description: 'This is description of tasks 3'\n    }];\n    hasError(event) {}\n    getNumber(event) {}\n    telInputObject(event) {}\n    onCountryChange(event) {}\n    savePhonNumber(event) {\n      this.telInputParam.phoneNumber = event;\n    }\n    // date \n    dateToString = date => {\n      date.year + '/' + date.month + '/' + date.day;\n    };\n    getCalenderData(date) {\n      this.HijriTOMilady(date);\n    }\n    Hijri(date) {\n      date = date.year + '/' + date.month + '/' + date.day;\n      this.higriPinding = date;\n    }\n    Milady(date) {\n      date = date.year + '/' + date.month + '/' + date.day;\n      this.MiladyPinding = date;\n    }\n    HijriTOMilady(date) {\n      var currentDate = date.year + '/' + date.month + '/' + date.day;\n      moment.locale('en');\n      var m = moment.utc(currentDate, 'iYYYY/iM/iD'); // Parse a Hijri date.\n      var hijriDate = m.format('YYYY/M/D');\n      this.dataPinding = hijriDate;\n    }\n    //questin:IQuestion |undefined;\n    addQuestion() {\n      if (Object.keys(this.exam).length === 0) {\n        let id = BaseConstantModel.newGuid();\n        this.exam = {\n          id: id,\n          questions: []\n        };\n      }\n      let qid = BaseConstantModel.newGuid();\n      let ques = {\n        questionId: qid,\n        questionNo: this.exam?.questions?.length + 1,\n        answers: []\n      };\n      this.exam.questions?.push(ques);\n    }\n    saveExam() {\n      this.submitExam = true;\n      this.examJson = JSON.stringify(this.exam);\n    }\n    /////recording/////\n    saveVoiceUrl(event) {\n      this.voiceUrl = event;\n    }\n    /////end recording////\n    chartOption = {\n      xAxis: {\n        type: 'category',\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n      },\n      yAxis: {\n        type: 'value'\n      },\n      series: [{\n        data: [820, 932, 901, 934, 1290, 1330, 1320],\n        type: 'line'\n      }]\n    };\n    static ɵfac = function SharedMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedMaterialComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.DomSanitizer), i0.ɵɵdirectiveInject(i3.AttachmentsService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SharedMaterialComponent,\n      selectors: [[\"app-shared-material\"]],\n      decls: 222,\n      vars: 42,\n      consts: [[\"unassignedList\", \"cdkDropList\"], [\"assignedList\", \"cdkDropList\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"LoginBackground_left_img\", \"pl-0\", \"pr-0\"], [1, \"col-12\", \"identity_img\", \"pl-0\", \"pr-0\"], [1, \"row\", \"justify-content-center\"], [1, \"box\"], [1, \"form-group\", \"UserLogin__LoginForm\"], [1, \"content_form\"], [1, \"col-lg-12\", \"mb-4\", \"mt-2\"], [1, \"font-weight-bold\"], [1, \"UserLogin__Label\"], [1, \"hint_yellow\", \"px-2\"], [1, \"col-12\", \"mb-3\", \"d-flex\"], [1, \"form-control\", \"bg-white\", \"border-md\", \"input-sm\", \"w-75\", \"valid_num\"], [1, \"col-12\"], [\"for\", \"UserEmail\", 1, \"UserLogin__Label\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\", \"bg-white\", \"pl-2\", \"pr-2\", \"border-md\", \"border-right-0\"], [1, \"UserLogin_icon_input\", 3, \"src\"], [\"pInputText\", \"\", \"placeholder\", \"search\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-left-0\", \"border-md\", \"input-sm\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\", \"mb-0\", \"mt-2\"], [\"type\", \"submit\", 1, \"btn\", \"UserLogin__Submit\"], [1, \"col-6\", \"mt-5\"], [1, \"example-h2\"], [1, \"example-section\"], [1, \"example-margin\"], [1, \"example-margin\", \"ml-5\"], [\"aria-label\", \"Select an option\", 1, \"example-radio-group\"], [\"value\", \"1\", 1, \"example-radio-button\"], [\"value\", \"2\", 1, \"example-radio-button\"], [3, \"sendDate\", \"hijri\", \"milady\"], [1, \"container\", \"mb-5\", \"mt-5\"], [1, \"col-4\", \"mb-3\", \"mt-3\"], [1, \"example-card\"], [\"mat-card-avatar\", \"\", 1, \"example-header-image\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"content_card\"], [1, \"align-self-baseline\", 3, \"src\"], [1, \"col-9\", \"mb-5\"], [\"hideToggle\", \"\"], [\"matExpansionPanelContent\", \"\"], [1, \"container-fluid\", \"mb-5\"], [1, \"col-md-6\", \"mb-5\"], [\"cdkDropList\", \"\", 3, \"cdkDropListDropped\", \"cdkDropListData\", \"cdkDropListConnectedTo\"], [\"hideToggle\", \"\", \"cdkDrag\", \"\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", \"hideToggle\", \"\", 4, \"ngFor\", \"ngForOf\"], [1, \"container\", \"border\", \"overlay_container\"], [1, \"col-7\", \"first_container\", \"border\"], [1, \"btn\", \"btn-primary\"], [1, \"col-1\"], [1, \"col-4\", \"sec_container\", \"border\"], [\"type\", \"text\", \"ng2TelInput\", \"\", 3, \"hasError\", \"ng2TelOutput\", \"intlTelInputObject\", \"countryChange\", \"value\", \"ng2TelInputOptions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"col-4 mb-3 mt-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-3\", \"col-md-3\", \"col-sm-12\", 2, \"background-color\", \"#e6e6e6\"], [1, \"col-lg-9\", \"col-md-9\", \"col-sm-12\", 2, \"background-color\", \"#e6e6e6\"], [\"class\", \"col-4 mt-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", 2, \"background-color\", \"bisque\", \"height\", \"25rem\"], [\"class\", \"col-3 mt-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-8\", \"mb-5\"], [3, \"items\"], [4, \"ngFor\", \"ngForOf\"], [3, \"getPhonNumber\", \"telInputParam\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\"], [\"class\", \"input-group \", 4, \"ngIf\"], [3, \"getVoiceUrl\"], [3, \"items\", \"numberPerRow\", \"userMode\"], [\"echarts\", \"\", 1, \"demo-chart\", 3, \"options\"], [\"hideToggle\", \"\", \"cdkDrag\", \"\"], [1, \"mt-2\"], [\"cdkDrag\", \"\", \"hideToggle\", \"\"], [3, \"title\", \"content\", \"imgPath\"], [1, \"col-4\", \"mt-3\"], [3, \"scientificProblem\"], [1, \"col-3\", \"mt-3\"], [3, \"questionTemplate\"]],\n      template: function SharedMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"div\", 5);\n          i0.ɵɵelementStart(4, \"section\", 6)(5, \"div\", 7)(6, \"form\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"h1\", 11);\n          i0.ɵɵtext(10, \" Login Number \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 12);\n          i0.ɵɵtext(12, \"The access code has been sent to your email\");\n          i0.ɵɵelementStart(13, \"small\", 13);\n          i0.ɵɵtext(14, \"<EMAIL>\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14);\n          i0.ɵɵelement(16, \"input\", 15)(17, \"input\", 15)(18, \"input\", 15)(19, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"p\", 12);\n          i0.ɵɵtext(22, \"I did not receive the passcode.\");\n          i0.ɵɵelementStart(23, \"small\", 13);\n          i0.ɵɵtext(24, \"Please Resend\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 16)(26, \"label\", 17);\n          i0.ɵɵtext(27, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19)(30, \"span\", 20);\n          i0.ɵɵelement(31, \"img\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(32, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 23)(34, \"button\", 24);\n          i0.ɵɵtext(35, \"Send\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"mat-card\")(38, \"mat-card-content\")(39, \"h2\", 26);\n          i0.ɵɵtext(40, \"Checkbox configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"h2\", 26);\n          i0.ɵɵtext(42, \"git color from TS file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"section\", 27)(44, \"mat-checkbox\", 28);\n          i0.ɵɵtext(45, \"Checked\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-checkbox\", 29);\n          i0.ɵɵtext(47, \"Indeterminate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"section\")(49, \"label\", 17);\n          i0.ɵɵtext(50, \"Radio-button\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-radio-group\", 30)(52, \"mat-radio-button\", 31);\n          i0.ɵɵtext(53, \"Option 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-radio-button\", 32);\n          i0.ɵɵtext(55, \"Option 2\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(56, \"hr\")(57, \"br\");\n          i0.ɵɵelementStart(58, \"mat-card\")(59, \"div\", 3)(60, \"div\", 16)(61, \"label\");\n          i0.ɵɵtext(62, \" hijri and milady \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\")(64, \"app-milady-hijri-calendar\", 33);\n          i0.ɵɵlistener(\"sendDate\", function SharedMaterialComponent_Template_app_milady_hijri_calendar_sendDate_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.HijriTOMilady($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"p\");\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 16)(68, \"label\");\n          i0.ɵɵtext(69, \" hijri \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\")(71, \"app-milady-hijri-calendar\", 33);\n          i0.ɵɵlistener(\"sendDate\", function SharedMaterialComponent_Template_app_milady_hijri_calendar_sendDate_71_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.Hijri($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"p\");\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 16)(75, \"label\");\n          i0.ɵɵtext(76, \" milady \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\")(78, \"app-milady-hijri-calendar\", 33);\n          i0.ɵɵlistener(\"sendDate\", function SharedMaterialComponent_Template_app_milady_hijri_calendar_sendDate_78_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.Milady($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"p\");\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(81, \"div\", 34)(82, \"div\", 3)(83, \"div\", 16)(84, \"h1\");\n          i0.ɵɵtext(85, \"CARDS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 35)(87, \"mat-card\", 36)(88, \"mat-card-header\");\n          i0.ɵɵelement(89, \"div\", 37);\n          i0.ɵɵelementStart(90, \"mat-card-title\");\n          i0.ɵɵtext(91, \"The name of the scientific article\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"mat-card-content\")(93, \"div\", 38)(94, \"p\", 39);\n          i0.ɵɵtext(95, \" The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan. A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally bred for hunting. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(96, \"img\", 40);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"div\", 35)(98, \"mat-card\", 36)(99, \"mat-card-header\");\n          i0.ɵɵelement(100, \"div\", 37);\n          i0.ɵɵelementStart(101, \"mat-card-title\");\n          i0.ɵɵtext(102, \"The name of the scientific article\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"mat-card-content\")(104, \"div\", 38)(105, \"p\", 39);\n          i0.ɵɵtext(106, \" The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan. A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally bred for hunting. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(107, \"img\", 40);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(108, \"div\", 35)(109, \"mat-card\", 36)(110, \"mat-card-header\");\n          i0.ɵɵelement(111, \"div\", 37);\n          i0.ɵɵelementStart(112, \"mat-card-title\");\n          i0.ɵɵtext(113, \"The name of the scientific article\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"mat-card-content\")(115, \"div\", 38)(116, \"p\", 39);\n          i0.ɵɵtext(117, \" The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan. A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally bred for hunting. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(118, \"img\", 40);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(119, \"div\", 34)(120, \"div\", 3)(121, \"div\", 16)(122, \"h1\");\n          i0.ɵɵtext(123, \"EXPENDED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(124, \"div\", 41)(125, \"mat-accordion\")(126, \"mat-expansion-panel\", 42)(127, \"mat-expansion-panel-header\")(128, \"mat-panel-title\");\n          i0.ɵɵtext(129, \" Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(130, SharedMaterialComponent_ng_template_130_Template, 4, 0, \"ng-template\", 43);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(131, \"hr\");\n          i0.ɵɵelementStart(132, \"div\", 44)(133, \"h1\");\n          i0.ɵɵtext(134, \" Accordion drag and drop\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"p\");\n          i0.ɵɵtext(136, \"Just drag and drop unassigned tasks to assigned tasks and magic will happen!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 3)(138, \"div\", 45)(139, \"h5\");\n          i0.ɵɵtext(140, \" Unassigned tasks \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"mat-accordion\", 46, 0);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function SharedMaterialComponent_Template_mat_accordion_cdkDropListDropped_141_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.drop($event));\n          });\n          i0.ɵɵtemplate(143, SharedMaterialComponent_mat_expansion_panel_143_Template, 7, 1, \"mat-expansion-panel\", 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(144, \"div\", 45)(145, \"h5\");\n          i0.ɵɵtext(146, \" Assigned tasks \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"mat-accordion\", 46, 1);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function SharedMaterialComponent_Template_mat_accordion_cdkDropListDropped_147_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.drop($event));\n          });\n          i0.ɵɵtemplate(149, SharedMaterialComponent_mat_expansion_panel_149_Template, 7, 1, \"mat-expansion-panel\", 48);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(150, \"div\", 49)(151, \"div\", 3)(152, \"div\", 50)(153, \"button\", 51);\n          i0.ɵɵtext(154, \" OVERLAY_DIV\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(155, \"div\", 52)(156, \"div\", 53);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(157, \"div\", 34)(158, \"div\", 3)(159, \"input\", 54);\n          i0.ɵɵlistener(\"hasError\", function SharedMaterialComponent_Template_input_hasError_159_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hasError($event));\n          })(\"ng2TelOutput\", function SharedMaterialComponent_Template_input_ng2TelOutput_159_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getNumber($event));\n          })(\"intlTelInputObject\", function SharedMaterialComponent_Template_input_intlTelInputObject_159_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.telInputObject($event));\n          })(\"countryChange\", function SharedMaterialComponent_Template_input_countryChange_159_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCountryChange($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(160, \"div\", 34)(161, \"div\", 3)(162, \"button\", 55);\n          i0.ɵɵlistener(\"click\", function SharedMaterialComponent_Template_button_click_162_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.confirmDialog());\n          });\n          i0.ɵɵtext(163, \"Confirm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(164, \"br\");\n          i0.ɵɵtext(165);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"div\", 34)(167, \"div\", 3)(168, \"div\", 16)(169, \"h1\");\n          i0.ɵɵtext(170, \"CARDS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(171, SharedMaterialComponent_div_171_Template, 2, 3, \"div\", 56);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(172, \"div\", 2)(173, \"div\", 3);\n          i0.ɵɵelement(174, \"div\", 57);\n          i0.ɵɵelementStart(175, \"div\", 58)(176, \"div\", 3);\n          i0.ɵɵtemplate(177, SharedMaterialComponent_div_177_Template, 2, 1, \"div\", 59);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(178, \"div\", 60)(179, \"div\", 3);\n          i0.ɵɵtemplate(180, SharedMaterialComponent_div_180_Template, 2, 1, \"div\", 61);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(181, \"div\", 34)(182, \"div\", 3)(183, \"div\", 62)(184, \"h5\");\n          i0.ɵɵtext(185, \" List \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(186, \"app-custom-accordion\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(187, SharedMaterialComponent_div_187_Template, 2, 1, \"div\", 64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(188, \"mat-accordion\")(189, \"mat-expansion-panel\")(190, \"mat-expansion-panel-header\")(191, \"mat-panel-title\");\n          i0.ɵɵtext(192, \" Welcome \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(193, \"p\");\n          i0.ɵɵtext(194, \"I am the content!\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(195, \"app-tel-input\", 65);\n          i0.ɵɵlistener(\"getPhonNumber\", function SharedMaterialComponent_Template_app_tel_input_getPhonNumber_195_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.savePhonNumber($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(196);\n          i0.ɵɵelementStart(197, \"div\", 2);\n          i0.ɵɵelement(198, \"app-students-rating\")(199, \"app-users-counter\")(200, \"app-khatmeen-students\")(201, \"app-student-numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(202, \"div\", 18)(203, \"button\", 66);\n          i0.ɵɵlistener(\"click\", function SharedMaterialComponent_Template_button_click_203_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addQuestion());\n          });\n          i0.ɵɵtext(204, \"Add question\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(205, \"br\");\n          i0.ɵɵelementStart(206, \"div\", 18);\n          i0.ɵɵtemplate(207, SharedMaterialComponent_div_207_Template, 2, 1, \"div\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(208, \"br\");\n          i0.ɵɵelementStart(209, \"div\", 18)(210, \"button\", 67);\n          i0.ɵɵlistener(\"click\", function SharedMaterialComponent_Template_button_click_210_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.saveExam());\n          });\n          i0.ɵɵtext(211, \"save\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(212, \"br\");\n          i0.ɵɵtemplate(213, SharedMaterialComponent_div_213_Template, 3, 2, \"div\", 68);\n          i0.ɵɵelementStart(214, \"div\")(215, \"app-voice-recording\", 69);\n          i0.ɵɵlistener(\"getVoiceUrl\", function SharedMaterialComponent_Template_app_voice_recording_getVoiceUrl_215_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.saveVoiceUrl($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(216);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(217, \"app-scientific-problems-grid\", 63)(218, \"app-scientific-problems-grid\", 70);\n          i0.ɵɵelementStart(219, \"div\", 2);\n          i0.ɵɵelement(220, \"app-not-auth\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(221, \"div\", 71);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const unassignedList_r11 = i0.ɵɵreference(142);\n          const assignedList_r12 = i0.ɵɵreference(148);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.search, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"hijri\", ctx.hijri)(\"milady\", ctx.milady);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.dataPinding);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hijri\", ctx.hijri)(\"milady\", !ctx.milady);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.higriPinding);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hijri\", !ctx.hijri)(\"milady\", ctx.milady);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.MiladyPinding);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.book, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.audio, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.audio, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"cdkDropListData\", ctx.unassignedTasks)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(37, _c0, assignedList_r12));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.unassignedTasks);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cdkDropListData\", ctx.assignedTasks)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(39, _c0, unassignedList_r11));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.assignedTasks);\n          i0.ɵɵadvance(10);\n          i0.ɵɵpropertyInterpolate(\"value\", ctx.pp);\n          i0.ɵɵproperty(\"ng2TelInputOptions\", i0.ɵɵpureFunction0(41, _c1));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" Response is: \", ctx.result, \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cardLst);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.student_card_scientificProblem);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.admin_card_scientificProblem);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"items\", ctx.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"telInputParam\", ctx.telInputParam);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.telInputParam.phoneNumber, \" \");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.exam.questions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitExam);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.voiceUrl, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", ctx.student_card_scientificProblem);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", ctx.admin_card_scientificProblem)(\"numberPerRow\", 4)(\"userMode\", ctx.adminCard);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.chartOption);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}[_ngcontent-%COMP%]:root{--download: \\\"\\\"}.form-control.valid_num[_ngcontent-%COMP%]{border-bottom:.125rem solid var(--main_color);border-bottom-color:var(--main_color);margin-right:1rem;margin-left:1rem}.form-control.valid_num[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:focus{border:.125rem solid #b3b3b3;border-bottom-color:var(--second_color)!important;outline:0;box-shadow:none!important}.example-radio-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin:1rem 0}.example-radio-button[_ngcontent-%COMP%]{margin:.313rem}.example-header-image[_ngcontent-%COMP%]{background-image:var(--download);background-repeat:no-repeat}.mat-card-avatar[_ngcontent-%COMP%]{height:2rem;width:2rem;flex-shrink:0;object-fit:cover;border-radius:0}.mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{font-size:1.063rem}.content_card[_ngcontent-%COMP%]{font-size:.75rem;color:#000}.mat-expansion-panel-header.mat-expanded[_ngcontent-%COMP%]:focus, .mat-expansion-panel-header.mat-expanded[_ngcontent-%COMP%]:hover, .mat-expansion-panel-header._mat-animation-noopable[_ngcontent-%COMP%]{background-color:#d6d7d8}.mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{background-color:#d6d7d8}.mat-expansion-panel-header-description[_ngcontent-%COMP%]{color:#fff;font-size:.875rem;font-weight:700}.mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded):not([aria-disabled=true])   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover{background-color:#d6d7d8}.mat-expansion-panel-header-title[_ngcontent-%COMP%]{color:#fff;font-size:.875rem;font-weight:700}.overlay_container[_ngcontent-%COMP%]{background-color:var(--main_color)}.first_container[_ngcontent-%COMP%], .sec_container[_ngcontent-%COMP%]{background-color:#fbfbfb;height:50vh}.demo-chart[_ngcontent-%COMP%]{height:25rem}\"]\n    });\n  }\n  return SharedMaterialComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}