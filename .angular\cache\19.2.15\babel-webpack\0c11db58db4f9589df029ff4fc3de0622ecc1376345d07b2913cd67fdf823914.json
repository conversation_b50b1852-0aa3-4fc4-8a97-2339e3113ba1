{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { isNumber, map } from 'zrender/lib/core/util.js';\nexport function prepareDataCoordInfo(coordSys, data, valueOrigin) {\n  var baseAxis = coordSys.getBaseAxis();\n  var valueAxis = coordSys.getOtherAxis(baseAxis);\n  var valueStart = getValueStart(valueAxis, valueOrigin);\n  var baseAxisDim = baseAxis.dim;\n  var valueAxisDim = valueAxis.dim;\n  var valueDim = data.mapDimension(valueAxisDim);\n  var baseDim = data.mapDimension(baseAxisDim);\n  var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n  var dims = map(coordSys.dimensions, function (coordDim) {\n    return data.mapDimension(coordDim);\n  });\n  var stacked = false;\n  var stackResultDim = data.getCalculationInfo('stackResultDimension');\n  if (isDimensionStacked(data, dims[0] /* , dims[1] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[0] = stackResultDim;\n  }\n  if (isDimensionStacked(data, dims[1] /* , dims[0] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[1] = stackResultDim;\n  }\n  return {\n    dataDimsForPoint: dims,\n    valueStart: valueStart,\n    valueAxisDim: valueAxisDim,\n    baseAxisDim: baseAxisDim,\n    stacked: !!stacked,\n    valueDim: valueDim,\n    baseDim: baseDim,\n    baseDataOffset: baseDataOffset,\n    stackedOverDimension: data.getCalculationInfo('stackedOverDimension')\n  };\n}\nfunction getValueStart(valueAxis, valueOrigin) {\n  var valueStart = 0;\n  var extent = valueAxis.scale.getExtent();\n  if (valueOrigin === 'start') {\n    valueStart = extent[0];\n  } else if (valueOrigin === 'end') {\n    valueStart = extent[1];\n  }\n  // If origin is specified as a number, use it as\n  // valueStart directly\n  else if (isNumber(valueOrigin) && !isNaN(valueOrigin)) {\n    valueStart = valueOrigin;\n  }\n  // auto\n  else {\n    // Both positive\n    if (extent[0] > 0) {\n      valueStart = extent[0];\n    }\n    // Both negative\n    else if (extent[1] < 0) {\n      valueStart = extent[1];\n    }\n    // If is one positive, and one negative, onZero shall be true\n  }\n  return valueStart;\n}\nexport function getStackedOnPoint(dataCoordInfo, coordSys, data, idx) {\n  var value = NaN;\n  if (dataCoordInfo.stacked) {\n    value = data.get(data.getCalculationInfo('stackedOverDimension'), idx);\n  }\n  if (isNaN(value)) {\n    value = dataCoordInfo.valueStart;\n  }\n  var baseDataOffset = dataCoordInfo.baseDataOffset;\n  var stackedData = [];\n  stackedData[baseDataOffset] = data.get(dataCoordInfo.baseDim, idx);\n  stackedData[1 - baseDataOffset] = value;\n  return coordSys.dataToPoint(stackedData);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}