{"ast": null, "code": "import * as matrix from './matrix.js';\nimport Point from './Point.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar lt = new Point();\nvar rb = new Point();\nvar lb = new Point();\nvar rt = new Point();\nvar minTv = new Point();\nvar maxTv = new Point();\nvar BoundingRect = function () {\n  function BoundingRect(x, y, width, height) {\n    if (width < 0) {\n      x = x + width;\n      width = -width;\n    }\n    if (height < 0) {\n      y = y + height;\n      height = -height;\n    }\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n  BoundingRect.prototype.union = function (other) {\n    var x = mathMin(other.x, this.x);\n    var y = mathMin(other.y, this.y);\n    if (isFinite(this.x) && isFinite(this.width)) {\n      this.width = mathMax(other.x + other.width, this.x + this.width) - x;\n    } else {\n      this.width = other.width;\n    }\n    if (isFinite(this.y) && isFinite(this.height)) {\n      this.height = mathMax(other.y + other.height, this.y + this.height) - y;\n    } else {\n      this.height = other.height;\n    }\n    this.x = x;\n    this.y = y;\n  };\n  BoundingRect.prototype.applyTransform = function (m) {\n    BoundingRect.applyTransform(this, this, m);\n  };\n  BoundingRect.prototype.calculateTransform = function (b) {\n    var a = this;\n    var sx = b.width / a.width;\n    var sy = b.height / a.height;\n    var m = matrix.create();\n    matrix.translate(m, m, [-a.x, -a.y]);\n    matrix.scale(m, m, [sx, sy]);\n    matrix.translate(m, m, [b.x, b.y]);\n    return m;\n  };\n  BoundingRect.prototype.intersect = function (b, mtv) {\n    if (!b) {\n      return false;\n    }\n    if (!(b instanceof BoundingRect)) {\n      b = BoundingRect.create(b);\n    }\n    var a = this;\n    var ax0 = a.x;\n    var ax1 = a.x + a.width;\n    var ay0 = a.y;\n    var ay1 = a.y + a.height;\n    var bx0 = b.x;\n    var bx1 = b.x + b.width;\n    var by0 = b.y;\n    var by1 = b.y + b.height;\n    var overlap = !(ax1 < bx0 || bx1 < ax0 || ay1 < by0 || by1 < ay0);\n    if (mtv) {\n      var dMin = Infinity;\n      var dMax = 0;\n      var d0 = Math.abs(ax1 - bx0);\n      var d1 = Math.abs(bx1 - ax0);\n      var d2 = Math.abs(ay1 - by0);\n      var d3 = Math.abs(by1 - ay0);\n      var dx = Math.min(d0, d1);\n      var dy = Math.min(d2, d3);\n      if (ax1 < bx0 || bx1 < ax0) {\n        if (dx > dMax) {\n          dMax = dx;\n          if (d0 < d1) {\n            Point.set(maxTv, -d0, 0);\n          } else {\n            Point.set(maxTv, d1, 0);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d0 < d1) {\n            Point.set(minTv, d0, 0);\n          } else {\n            Point.set(minTv, -d1, 0);\n          }\n        }\n      }\n      if (ay1 < by0 || by1 < ay0) {\n        if (dy > dMax) {\n          dMax = dy;\n          if (d2 < d3) {\n            Point.set(maxTv, 0, -d2);\n          } else {\n            Point.set(maxTv, 0, d3);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d2 < d3) {\n            Point.set(minTv, 0, d2);\n          } else {\n            Point.set(minTv, 0, -d3);\n          }\n        }\n      }\n    }\n    if (mtv) {\n      Point.copy(mtv, overlap ? minTv : maxTv);\n    }\n    return overlap;\n  };\n  BoundingRect.prototype.contain = function (x, y) {\n    var rect = this;\n    return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height;\n  };\n  BoundingRect.prototype.clone = function () {\n    return new BoundingRect(this.x, this.y, this.width, this.height);\n  };\n  BoundingRect.prototype.copy = function (other) {\n    BoundingRect.copy(this, other);\n  };\n  BoundingRect.prototype.plain = function () {\n    return {\n      x: this.x,\n      y: this.y,\n      width: this.width,\n      height: this.height\n    };\n  };\n  BoundingRect.prototype.isFinite = function () {\n    return isFinite(this.x) && isFinite(this.y) && isFinite(this.width) && isFinite(this.height);\n  };\n  BoundingRect.prototype.isZero = function () {\n    return this.width === 0 || this.height === 0;\n  };\n  BoundingRect.create = function (rect) {\n    return new BoundingRect(rect.x, rect.y, rect.width, rect.height);\n  };\n  BoundingRect.copy = function (target, source) {\n    target.x = source.x;\n    target.y = source.y;\n    target.width = source.width;\n    target.height = source.height;\n  };\n  BoundingRect.applyTransform = function (target, source, m) {\n    if (!m) {\n      if (target !== source) {\n        BoundingRect.copy(target, source);\n      }\n      return;\n    }\n    if (m[1] < 1e-5 && m[1] > -1e-5 && m[2] < 1e-5 && m[2] > -1e-5) {\n      var sx = m[0];\n      var sy = m[3];\n      var tx = m[4];\n      var ty = m[5];\n      target.x = source.x * sx + tx;\n      target.y = source.y * sy + ty;\n      target.width = source.width * sx;\n      target.height = source.height * sy;\n      if (target.width < 0) {\n        target.x += target.width;\n        target.width = -target.width;\n      }\n      if (target.height < 0) {\n        target.y += target.height;\n        target.height = -target.height;\n      }\n      return;\n    }\n    lt.x = lb.x = source.x;\n    lt.y = rt.y = source.y;\n    rb.x = rt.x = source.x + source.width;\n    rb.y = lb.y = source.y + source.height;\n    lt.transform(m);\n    rt.transform(m);\n    rb.transform(m);\n    lb.transform(m);\n    target.x = mathMin(lt.x, rb.x, lb.x, rt.x);\n    target.y = mathMin(lt.y, rb.y, lb.y, rt.y);\n    var maxX = mathMax(lt.x, rb.x, lb.x, rt.x);\n    var maxY = mathMax(lt.y, rb.y, lb.y, rt.y);\n    target.width = maxX - target.x;\n    target.height = maxY - target.y;\n  };\n  return BoundingRect;\n}();\nexport default BoundingRect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}