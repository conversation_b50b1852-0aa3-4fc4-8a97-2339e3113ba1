{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { assert, bind, each, eqNaN, extend, hasOwn, indexOf, isArrayLike, keys, reduce } from 'zrender/lib/core/util.js';\nimport { cloneValue } from 'zrender/lib/animation/Animator.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { Path } from '../util/graphic.js';\nimport { warn } from '../util/log.js';\nimport { TRANSFORMABLE_PROPS } from 'zrender/lib/core/Transformable.js';\nvar LEGACY_TRANSFORM_PROPS_MAP = {\n  position: ['x', 'y'],\n  scale: ['scaleX', 'scaleY'],\n  origin: ['originX', 'originY']\n};\nvar LEGACY_TRANSFORM_PROPS = keys(LEGACY_TRANSFORM_PROPS_MAP);\nvar TRANSFORM_PROPS_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = 1;\n  return obj;\n}, {});\nvar transformPropNamesStr = TRANSFORMABLE_PROPS.join(', ');\n// '' means root\nexport var ELEMENT_ANIMATABLE_PROPS = ['', 'style', 'shape', 'extra'];\n;\nvar transitionInnerStore = makeInner();\n;\nfunction getElementAnimationConfig(animationType, el, elOption, parentModel, dataIndex) {\n  var animationProp = animationType + \"Animation\";\n  var config = getAnimationConfig(animationType, parentModel, dataIndex) || {};\n  var userDuring = transitionInnerStore(el).userDuring;\n  // Only set when duration is > 0 and it's need to be animated.\n  if (config.duration > 0) {\n    // For simplicity, if during not specified, the previous during will not work any more.\n    config.during = userDuring ? bind(duringCall, {\n      el: el,\n      userDuring: userDuring\n    }) : null;\n    config.setToFinal = true;\n    config.scope = animationType;\n  }\n  extend(config, elOption[animationProp]);\n  return config;\n}\nexport function applyUpdateTransition(el, elOption, animatableModel, opts) {\n  opts = opts || {};\n  var dataIndex = opts.dataIndex,\n    isInit = opts.isInit,\n    clearStyle = opts.clearStyle;\n  var hasAnimation = animatableModel.isAnimationEnabled();\n  // Save the meta info for further morphing. Like apply on the sub morphing elements.\n  var store = transitionInnerStore(el);\n  var styleOpt = elOption.style;\n  store.userDuring = elOption.during;\n  var transFromProps = {};\n  var propsToSet = {};\n  prepareTransformAllPropsFinal(el, elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('shape', elOption, propsToSet);\n  prepareShapeOrExtraAllPropsFinal('extra', elOption, propsToSet);\n  if (!isInit && hasAnimation) {\n    prepareTransformTransitionFrom(el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('shape', el, elOption, transFromProps);\n    prepareShapeOrExtraTransitionFrom('extra', el, elOption, transFromProps);\n    prepareStyleTransitionFrom(el, elOption, styleOpt, transFromProps);\n  }\n  propsToSet.style = styleOpt;\n  applyPropsDirectly(el, propsToSet, clearStyle);\n  applyMiscProps(el, elOption);\n  if (hasAnimation) {\n    if (isInit) {\n      var enterFromProps_1 = {};\n      each(ELEMENT_ANIMATABLE_PROPS, function (propName) {\n        var prop = propName ? elOption[propName] : elOption;\n        if (prop && prop.enterFrom) {\n          if (propName) {\n            enterFromProps_1[propName] = enterFromProps_1[propName] || {};\n          }\n          extend(propName ? enterFromProps_1[propName] : enterFromProps_1, prop.enterFrom);\n        }\n      });\n      var config = getElementAnimationConfig('enter', el, elOption, animatableModel, dataIndex);\n      if (config.duration > 0) {\n        el.animateFrom(enterFromProps_1, config);\n      }\n    } else {\n      applyPropsTransition(el, elOption, dataIndex || 0, animatableModel, transFromProps);\n    }\n  }\n  // Store leave to be used in leave transition.\n  updateLeaveTo(el, elOption);\n  styleOpt ? el.dirty() : el.markRedraw();\n}\nexport function updateLeaveTo(el, elOption) {\n  // Try merge to previous set leaveTo\n  var leaveToProps = transitionInnerStore(el).leaveToProps;\n  for (var i = 0; i < ELEMENT_ANIMATABLE_PROPS.length; i++) {\n    var propName = ELEMENT_ANIMATABLE_PROPS[i];\n    var prop = propName ? elOption[propName] : elOption;\n    if (prop && prop.leaveTo) {\n      if (!leaveToProps) {\n        leaveToProps = transitionInnerStore(el).leaveToProps = {};\n      }\n      if (propName) {\n        leaveToProps[propName] = leaveToProps[propName] || {};\n      }\n      extend(propName ? leaveToProps[propName] : leaveToProps, prop.leaveTo);\n    }\n  }\n}\nexport function applyLeaveTransition(el, elOption, animatableModel, onRemove) {\n  if (el) {\n    var parent_1 = el.parent;\n    var leaveToProps = transitionInnerStore(el).leaveToProps;\n    if (leaveToProps) {\n      // TODO TODO use leave after leaveAnimation in series is introduced\n      // TODO Data index?\n      var config = getElementAnimationConfig('update', el, elOption, animatableModel, 0);\n      config.done = function () {\n        parent_1.remove(el);\n        onRemove && onRemove();\n      };\n      el.animateTo(leaveToProps, config);\n    } else {\n      parent_1.remove(el);\n      onRemove && onRemove();\n    }\n  }\n}\nexport function isTransitionAll(transition) {\n  return transition === 'all';\n}\nfunction applyPropsDirectly(el,\n// Can be null/undefined\nallPropsFinal, clearStyle) {\n  var styleOpt = allPropsFinal.style;\n  if (!el.isGroup && styleOpt) {\n    if (clearStyle) {\n      el.useStyle({});\n      // When style object changed, how to trade the existing animation?\n      // It is probably complicated and not needed to cover all the cases.\n      // But still need consider the case:\n      // (1) When using init animation on `style.opacity`, and before the animation\n      //     ended users triggers an update by mousewhel. At that time the init\n      //     animation should better be continued rather than terminated.\n      //     So after `useStyle` called, we should change the animation target manually\n      //     to continue the effect of the init animation.\n      // (2) PENDING: If the previous animation targeted at a `val1`, and currently we need\n      //     to update the value to `val2` and no animation declared, should be terminate\n      //     the previous animation or just modify the target of the animation?\n      //     Therotically That will happen not only on `style` but also on `shape` and\n      //     `transfrom` props. But we haven't handle this case at present yet.\n      // (3) PENDING: Is it proper to visit `animators` and `targetName`?\n      var animators = el.animators;\n      for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        // targetName is the \"topKey\".\n        if (animator.targetName === 'style') {\n          animator.changeTarget(el.style);\n        }\n      }\n    }\n    el.setStyle(styleOpt);\n  }\n  if (allPropsFinal) {\n    // Not set style here.\n    allPropsFinal.style = null;\n    // Set el to the final state firstly.\n    allPropsFinal && el.attr(allPropsFinal);\n    allPropsFinal.style = styleOpt;\n  }\n}\nfunction applyPropsTransition(el, elOption, dataIndex, model,\n// Can be null/undefined\ntransFromProps) {\n  if (transFromProps) {\n    var config = getElementAnimationConfig('update', el, elOption, model, dataIndex);\n    if (config.duration > 0) {\n      el.animateFrom(transFromProps, config);\n    }\n  }\n}\nfunction applyMiscProps(el, elOption) {\n  // Merge by default.\n  hasOwn(elOption, 'silent') && (el.silent = elOption.silent);\n  hasOwn(elOption, 'ignore') && (el.ignore = elOption.ignore);\n  if (el instanceof Displayable) {\n    hasOwn(elOption, 'invisible') && (el.invisible = elOption.invisible);\n  }\n  if (el instanceof Path) {\n    hasOwn(elOption, 'autoBatch') && (el.autoBatch = elOption.autoBatch);\n  }\n}\n// Use it to avoid it be exposed to user.\nvar tmpDuringScope = {};\nvar transitionDuringAPI = {\n  // Usually other props do not need to be changed in animation during.\n  setTransform: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `setTransform`.');\n    }\n    tmpDuringScope.el[key] = val;\n    return this;\n  },\n  getTransform: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(hasOwn(TRANSFORM_PROPS_MAP, key), 'Only ' + transformPropNamesStr + ' available in `getTransform`.');\n    }\n    return tmpDuringScope.el[key];\n  },\n  setShape: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var shape = el.shape || (el.shape = {});\n    shape[key] = val;\n    el.dirtyShape && el.dirtyShape();\n    return this;\n  },\n  getShape: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var shape = tmpDuringScope.el.shape;\n    if (shape) {\n      return shape[key];\n    }\n  },\n  setStyle: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var el = tmpDuringScope.el;\n    var style = el.style;\n    if (style) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (eqNaN(val)) {\n          warn('style.' + key + ' must not be assigned with NaN.');\n        }\n      }\n      style[key] = val;\n      el.dirtyStyle && el.dirtyStyle();\n    }\n    return this;\n  },\n  getStyle: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var style = tmpDuringScope.el.style;\n    if (style) {\n      return style[key];\n    }\n  },\n  setExtra: function (key, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra || (tmpDuringScope.el.extra = {});\n    extra[key] = val;\n    return this;\n  },\n  getExtra: function (key) {\n    if (process.env.NODE_ENV !== 'production') {\n      assertNotReserved(key);\n    }\n    var extra = tmpDuringScope.el.extra;\n    if (extra) {\n      return extra[key];\n    }\n  }\n};\nfunction assertNotReserved(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (key === 'transition' || key === 'enterFrom' || key === 'leaveTo') {\n      throw new Error('key must not be \"' + key + '\"');\n    }\n  }\n}\nfunction duringCall() {\n  // Do not provide \"percent\" until some requirements come.\n  // Because consider thies case:\n  // enterFrom: {x: 100, y: 30}, transition: 'x'.\n  // And enter duration is different from update duration.\n  // Thus it might be confused about the meaning of \"percent\" in during callback.\n  var scope = this;\n  var el = scope.el;\n  if (!el) {\n    return;\n  }\n  // If el is remove from zr by reason like legend, during still need to called,\n  // because el will be added back to zr and the prop value should not be incorrect.\n  var latestUserDuring = transitionInnerStore(el).userDuring;\n  var scopeUserDuring = scope.userDuring;\n  // Ensured a during is only called once in each animation frame.\n  // If a during is called multiple times in one frame, maybe some users' calculation logic\n  // might be wrong (not sure whether this usage exists).\n  // The case of a during might be called twice can be: by default there is a animator for\n  // 'x', 'y' when init. Before the init animation finished, call `setOption` to start\n  // another animators for 'style'/'shape'/'extra'.\n  if (latestUserDuring !== scopeUserDuring) {\n    // release\n    scope.el = scope.userDuring = null;\n    return;\n  }\n  tmpDuringScope.el = el;\n  // Give no `this` to user in \"during\" calling.\n  scopeUserDuring(transitionDuringAPI);\n  // FIXME: if in future meet the case that some prop will be both modified in `during` and `state`,\n  // consider the issue that the prop might be incorrect when return to \"normal\" state.\n}\nfunction prepareShapeOrExtraTransitionFrom(mainAttr, fromEl, elOption, transFromProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var elPropsInAttr = fromEl[mainAttr];\n  var transFromPropsInAttr;\n  if (elPropsInAttr) {\n    var transition = elOption.transition;\n    var attrTransition = attrOpt.transition;\n    if (attrTransition) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      if (isTransitionAll(attrTransition)) {\n        extend(transFromPropsInAttr, elPropsInAttr);\n      } else {\n        var transitionKeys = normalizeToArray(attrTransition);\n        for (var i = 0; i < transitionKeys.length; i++) {\n          var key = transitionKeys[i];\n          var elVal = elPropsInAttr[key];\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    } else if (isTransitionAll(transition) || indexOf(transition, mainAttr) >= 0) {\n      !transFromPropsInAttr && (transFromPropsInAttr = transFromProps[mainAttr] = {});\n      var elPropsInAttrKeys = keys(elPropsInAttr);\n      for (var i = 0; i < elPropsInAttrKeys.length; i++) {\n        var key = elPropsInAttrKeys[i];\n        var elVal = elPropsInAttr[key];\n        if (isNonStyleTransitionEnabled(attrOpt[key], elVal)) {\n          transFromPropsInAttr[key] = elVal;\n        }\n      }\n    }\n  }\n}\nfunction prepareShapeOrExtraAllPropsFinal(mainAttr, elOption, allProps) {\n  var attrOpt = elOption[mainAttr];\n  if (!attrOpt) {\n    return;\n  }\n  var allPropsInAttr = allProps[mainAttr] = {};\n  var keysInAttr = keys(attrOpt);\n  for (var i = 0; i < keysInAttr.length; i++) {\n    var key = keysInAttr[i];\n    // To avoid share one object with different element, and\n    // to avoid user modify the object inexpectedly, have to clone.\n    allPropsInAttr[key] = cloneValue(attrOpt[key]);\n  }\n}\nfunction prepareTransformTransitionFrom(el, elOption, transFromProps) {\n  var transition = elOption.transition;\n  var transitionKeys = isTransitionAll(transition) ? TRANSFORMABLE_PROPS : normalizeToArray(transition || []);\n  for (var i = 0; i < transitionKeys.length; i++) {\n    var key = transitionKeys[i];\n    if (key === 'style' || key === 'shape' || key === 'extra') {\n      continue;\n    }\n    var elVal = el[key];\n    if (process.env.NODE_ENV !== 'production') {\n      checkTransformPropRefer(key, 'el.transition');\n    }\n    // Do not clone, animator will perform that clone.\n    transFromProps[key] = elVal;\n  }\n}\nfunction prepareTransformAllPropsFinal(el, elOption, allProps) {\n  for (var i = 0; i < LEGACY_TRANSFORM_PROPS.length; i++) {\n    var legacyName = LEGACY_TRANSFORM_PROPS[i];\n    var xyName = LEGACY_TRANSFORM_PROPS_MAP[legacyName];\n    var legacyArr = elOption[legacyName];\n    if (legacyArr) {\n      allProps[xyName[0]] = legacyArr[0];\n      allProps[xyName[1]] = legacyArr[1];\n    }\n  }\n  for (var i = 0; i < TRANSFORMABLE_PROPS.length; i++) {\n    var key = TRANSFORMABLE_PROPS[i];\n    if (elOption[key] != null) {\n      allProps[key] = elOption[key];\n    }\n  }\n}\nfunction prepareStyleTransitionFrom(fromEl, elOption, styleOpt, transFromProps) {\n  if (!styleOpt) {\n    return;\n  }\n  var fromElStyle = fromEl.style;\n  var transFromStyleProps;\n  if (fromElStyle) {\n    var styleTransition = styleOpt.transition;\n    var elTransition = elOption.transition;\n    if (styleTransition && !isTransitionAll(styleTransition)) {\n      var transitionKeys = normalizeToArray(styleTransition);\n      !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n      for (var i = 0; i < transitionKeys.length; i++) {\n        var key = transitionKeys[i];\n        var elVal = fromElStyle[key];\n        // Do not clone, see `checkNonStyleTansitionRefer`.\n        transFromStyleProps[key] = elVal;\n      }\n    } else if (fromEl.getAnimationStyleProps && (isTransitionAll(elTransition) || isTransitionAll(styleTransition) || indexOf(elTransition, 'style') >= 0)) {\n      var animationProps = fromEl.getAnimationStyleProps();\n      var animationStyleProps = animationProps ? animationProps.style : null;\n      if (animationStyleProps) {\n        !transFromStyleProps && (transFromStyleProps = transFromProps.style = {});\n        var styleKeys = keys(styleOpt);\n        for (var i = 0; i < styleKeys.length; i++) {\n          var key = styleKeys[i];\n          if (animationStyleProps[key]) {\n            var elVal = fromElStyle[key];\n            transFromStyleProps[key] = elVal;\n          }\n        }\n      }\n    }\n  }\n}\nfunction isNonStyleTransitionEnabled(optVal, elVal) {\n  // The same as `checkNonStyleTansitionRefer`.\n  return !isArrayLike(optVal) ? optVal != null && isFinite(optVal) : optVal !== elVal;\n}\nvar checkTransformPropRefer;\nif (process.env.NODE_ENV !== 'production') {\n  checkTransformPropRefer = function (key, usedIn) {\n    if (!hasOwn(TRANSFORM_PROPS_MAP, key)) {\n      warn('Prop `' + key + '` is not a permitted in `' + usedIn + '`. ' + 'Only `' + keys(TRANSFORM_PROPS_MAP).join('`, `') + '` are permitted.');\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}