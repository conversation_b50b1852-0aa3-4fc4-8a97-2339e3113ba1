{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, SPECIAL_STATES, DISPLAY_STATES } from '../../util/states.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { isRadianAroundZero } from '../../util/number.js';\nvar DEFAULT_SECTOR_Z = 2;\nvar DEFAULT_TEXT_Z = 4;\n/**\r\n * Sunburstce of Sunburst including Sector, Label, LabelLine\r\n */\nvar SunburstPiece = /** @class */function (_super) {\n  __extends(SunburstPiece, _super);\n  function SunburstPiece(node, seriesModel, ecModel, api) {\n    var _this = _super.call(this) || this;\n    _this.z2 = DEFAULT_SECTOR_Z;\n    _this.textConfig = {\n      inside: true\n    };\n    getECData(_this).seriesIndex = seriesModel.seriesIndex;\n    var text = new graphic.Text({\n      z2: DEFAULT_TEXT_Z,\n      silent: node.getModel().get(['label', 'silent'])\n    });\n    _this.setTextContent(text);\n    _this.updateData(true, node, seriesModel, ecModel, api);\n    return _this;\n  }\n  SunburstPiece.prototype.updateData = function (firstCreate, node,\n  // state: 'emphasis' | 'normal' | 'highlight' | 'downplay',\n  seriesModel, ecModel, api) {\n    this.node = node;\n    node.piece = this;\n    seriesModel = seriesModel || this._seriesModel;\n    ecModel = ecModel || this._ecModel;\n    var sector = this;\n    getECData(sector).dataIndex = node.dataIndex;\n    var itemModel = node.getModel();\n    var emphasisModel = itemModel.getModel('emphasis');\n    var layout = node.getLayout();\n    var sectorShape = zrUtil.extend({}, layout);\n    sectorShape.label = null;\n    var normalStyle = node.getVisual('style');\n    normalStyle.lineJoin = 'bevel';\n    var decal = node.getVisual('decal');\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, api);\n    }\n    var cornerRadius = getSectorCornerRadius(itemModel.getModel('itemStyle'), sectorShape, true);\n    zrUtil.extend(sectorShape, cornerRadius);\n    zrUtil.each(SPECIAL_STATES, function (stateName) {\n      var state = sector.ensureState(stateName);\n      var itemStyleModel = itemModel.getModel([stateName, 'itemStyle']);\n      state.style = itemStyleModel.getItemStyle();\n      // border radius\n      var cornerRadius = getSectorCornerRadius(itemStyleModel, sectorShape);\n      if (cornerRadius) {\n        state.shape = cornerRadius;\n      }\n    });\n    if (firstCreate) {\n      sector.setShape(sectorShape);\n      sector.shape.r = layout.r0;\n      graphic.initProps(sector, {\n        shape: {\n          r: layout.r\n        }\n      }, seriesModel, node.dataIndex);\n    } else {\n      // Disable animation for gradient since no interpolation method\n      // is supported for gradient\n      graphic.updateProps(sector, {\n        shape: sectorShape\n      }, seriesModel);\n      saveOldStyle(sector);\n    }\n    sector.useStyle(normalStyle);\n    this._updateLabel(seriesModel);\n    var cursorStyle = itemModel.getShallow('cursor');\n    cursorStyle && sector.attr('cursor', cursorStyle);\n    this._seriesModel = seriesModel || this._seriesModel;\n    this._ecModel = ecModel || this._ecModel;\n    var focus = emphasisModel.get('focus');\n    var focusOrIndices = focus === 'relative' ? zrUtil.concatArray(node.getAncestorsIndices(), node.getDescendantIndices()) : focus === 'ancestor' ? node.getAncestorsIndices() : focus === 'descendant' ? node.getDescendantIndices() : focus;\n    toggleHoverEmphasis(this, focusOrIndices, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  SunburstPiece.prototype._updateLabel = function (seriesModel) {\n    var _this = this;\n    var itemModel = this.node.getModel();\n    var normalLabelModel = itemModel.getModel('label');\n    var layout = this.node.getLayout();\n    var angle = layout.endAngle - layout.startAngle;\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var dx = Math.cos(midAngle);\n    var dy = Math.sin(midAngle);\n    var sector = this;\n    var label = sector.getTextContent();\n    var dataIndex = this.node.dataIndex;\n    var labelMinAngle = normalLabelModel.get('minAngle') / 180 * Math.PI;\n    var isNormalShown = normalLabelModel.get('show') && !(labelMinAngle != null && Math.abs(angle) < labelMinAngle);\n    label.ignore = !isNormalShown;\n    // TODO use setLabelStyle\n    zrUtil.each(DISPLAY_STATES, function (stateName) {\n      var labelStateModel = stateName === 'normal' ? itemModel.getModel('label') : itemModel.getModel([stateName, 'label']);\n      var isNormal = stateName === 'normal';\n      var state = isNormal ? label : label.ensureState(stateName);\n      var text = seriesModel.getFormattedLabel(dataIndex, stateName);\n      if (isNormal) {\n        text = text || _this.node.name;\n      }\n      state.style = createTextStyle(labelStateModel, {}, null, stateName !== 'normal', true);\n      if (text) {\n        state.style.text = text;\n      }\n      // Not displaying text when angle is too small\n      var isShown = labelStateModel.get('show');\n      if (isShown != null && !isNormal) {\n        state.ignore = !isShown;\n      }\n      var labelPosition = getLabelAttr(labelStateModel, 'position');\n      var sectorState = isNormal ? sector : sector.states[stateName];\n      var labelColor = sectorState.style.fill;\n      sectorState.textConfig = {\n        outsideFill: labelStateModel.get('color') === 'inherit' ? labelColor : null,\n        inside: labelPosition !== 'outside'\n      };\n      var r;\n      var labelPadding = getLabelAttr(labelStateModel, 'distance') || 0;\n      var textAlign = getLabelAttr(labelStateModel, 'align');\n      var rotateType = getLabelAttr(labelStateModel, 'rotate');\n      var flipStartAngle = Math.PI * 0.5;\n      var flipEndAngle = Math.PI * 1.5;\n      var midAngleNormal = normalizeRadian(rotateType === 'tangential' ? Math.PI / 2 - midAngle : midAngle);\n      // For text that is up-side down, rotate 180 degrees to make sure\n      // it's readable\n      var needsFlip = midAngleNormal > flipStartAngle && !isRadianAroundZero(midAngleNormal - flipStartAngle) && midAngleNormal < flipEndAngle;\n      if (labelPosition === 'outside') {\n        r = layout.r + labelPadding;\n        textAlign = needsFlip ? 'right' : 'left';\n      } else {\n        if (!textAlign || textAlign === 'center') {\n          // Put label in the center if it's a circle\n          if (angle === 2 * Math.PI && layout.r0 === 0) {\n            r = 0;\n          } else {\n            r = (layout.r + layout.r0) / 2;\n          }\n          textAlign = 'center';\n        } else if (textAlign === 'left') {\n          r = layout.r0 + labelPadding;\n          textAlign = needsFlip ? 'right' : 'left';\n        } else if (textAlign === 'right') {\n          r = layout.r - labelPadding;\n          textAlign = needsFlip ? 'left' : 'right';\n        }\n      }\n      state.style.align = textAlign;\n      state.style.verticalAlign = getLabelAttr(labelStateModel, 'verticalAlign') || 'middle';\n      state.x = r * dx + layout.cx;\n      state.y = r * dy + layout.cy;\n      var rotate = 0;\n      if (rotateType === 'radial') {\n        rotate = normalizeRadian(-midAngle) + (needsFlip ? Math.PI : 0);\n      } else if (rotateType === 'tangential') {\n        rotate = normalizeRadian(Math.PI / 2 - midAngle) + (needsFlip ? Math.PI : 0);\n      } else if (zrUtil.isNumber(rotateType)) {\n        rotate = rotateType * Math.PI / 180;\n      }\n      state.rotation = normalizeRadian(rotate);\n    });\n    function getLabelAttr(model, name) {\n      var stateAttr = model.get(name);\n      if (stateAttr == null) {\n        return normalLabelModel.get(name);\n      }\n      return stateAttr;\n    }\n    label.dirtyStyle();\n  };\n  return SunburstPiece;\n}(graphic.Sector);\nexport default SunburstPiece;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}