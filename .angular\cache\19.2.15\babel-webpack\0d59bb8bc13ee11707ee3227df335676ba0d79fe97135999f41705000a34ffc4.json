{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Separate legend and scrollable legend to reduce package size.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport LegendView from './LegendView.js';\nvar Group = graphic.Group;\nvar WH = ['width', 'height'];\nvar XY = ['x', 'y'];\nvar ScrollableLegendView = /** @class */function (_super) {\n  __extends(ScrollableLegendView, _super);\n  function ScrollableLegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScrollableLegendView.type;\n    _this.newlineDisabled = true;\n    _this._currentIndex = 0;\n    return _this;\n  }\n  ScrollableLegendView.prototype.init = function () {\n    _super.prototype.init.call(this);\n    this.group.add(this._containerGroup = new Group());\n    this._containerGroup.add(this.getContentGroup());\n    this.group.add(this._controllerGroup = new Group());\n  };\n  /**\r\n   * @override\r\n   */\n  ScrollableLegendView.prototype.resetInner = function () {\n    _super.prototype.resetInner.call(this);\n    this._controllerGroup.removeAll();\n    this._containerGroup.removeClipPath();\n    this._containerGroup.__rectSize = null;\n  };\n  /**\r\n   * @override\r\n   */\n  ScrollableLegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var self = this;\n    // Render content items.\n    _super.prototype.renderInner.call(this, itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    var controllerGroup = this._controllerGroup;\n    // FIXME: support be 'auto' adapt to size number text length,\n    // e.g., '3/12345' should not overlap with the control arrow button.\n    var pageIconSize = legendModel.get('pageIconSize', true);\n    var pageIconSizeArr = zrUtil.isArray(pageIconSize) ? pageIconSize : [pageIconSize, pageIconSize];\n    createPageButton('pagePrev', 0);\n    var pageTextStyleModel = legendModel.getModel('pageTextStyle');\n    controllerGroup.add(new graphic.Text({\n      name: 'pageText',\n      style: {\n        // Placeholder to calculate a proper layout.\n        text: 'xx/xx',\n        fill: pageTextStyleModel.getTextColor(),\n        font: pageTextStyleModel.getFont(),\n        verticalAlign: 'middle',\n        align: 'center'\n      },\n      silent: true\n    }));\n    createPageButton('pageNext', 1);\n    function createPageButton(name, iconIdx) {\n      var pageDataIndexName = name + 'DataIndex';\n      var icon = graphic.createIcon(legendModel.get('pageIcons', true)[legendModel.getOrient().name][iconIdx], {\n        // Buttons will be created in each render, so we do not need\n        // to worry about avoiding using legendModel kept in scope.\n        onclick: zrUtil.bind(self._pageGo, self, pageDataIndexName, legendModel, api)\n      }, {\n        x: -pageIconSizeArr[0] / 2,\n        y: -pageIconSizeArr[1] / 2,\n        width: pageIconSizeArr[0],\n        height: pageIconSizeArr[1]\n      });\n      icon.name = name;\n      controllerGroup.add(icon);\n    }\n  };\n  /**\r\n   * @override\r\n   */\n  ScrollableLegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n    var hw = WH[1 - orientIdx];\n    var yx = XY[1 - orientIdx];\n    selector && layoutUtil.box(\n    // Buttons in selectorGroup always layout horizontally\n    'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n    var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n    var selectorRect = selectorGroup.getBoundingRect();\n    var selectorPos = [-selectorRect.x, -selectorRect.y];\n    var processMaxSize = zrUtil.clone(maxSize);\n    selector && (processMaxSize[wh] = maxSize[wh] - selectorRect[wh] - selectorButtonGap);\n    var mainRect = this._layoutContentAndController(legendModel, isFirstRender, processMaxSize, orientIdx, wh, hw, yx, xy);\n    if (selector) {\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += mainRect[wh] + selectorButtonGap;\n      } else {\n        var offset = selectorRect[wh] + selectorButtonGap;\n        selectorPos[orientIdx] -= offset;\n        mainRect[xy] -= offset;\n      }\n      mainRect[wh] += selectorRect[wh] + selectorButtonGap;\n      selectorPos[1 - orientIdx] += mainRect[yx] + mainRect[hw] / 2 - selectorRect[hw] / 2;\n      mainRect[hw] = Math.max(mainRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(mainRect[yx], selectorRect[yx] + selectorPos[1 - orientIdx]);\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      selectorGroup.markRedraw();\n    }\n    return mainRect;\n  };\n  ScrollableLegendView.prototype._layoutContentAndController = function (legendModel, isFirstRender, maxSize, orientIdx, wh, hw, yx, xy) {\n    var contentGroup = this.getContentGroup();\n    var containerGroup = this._containerGroup;\n    var controllerGroup = this._controllerGroup;\n    // Place items in contentGroup.\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), !orientIdx ? null : maxSize.width, orientIdx ? null : maxSize.height);\n    layoutUtil.box(\n    // Buttons in controller are layout always horizontally.\n    'horizontal', controllerGroup, legendModel.get('pageButtonItemGap', true));\n    var contentRect = contentGroup.getBoundingRect();\n    var controllerRect = controllerGroup.getBoundingRect();\n    var showController = this._showController = contentRect[wh] > maxSize[wh];\n    // In case that the inner elements of contentGroup layout do not based on [0, 0]\n    var contentPos = [-contentRect.x, -contentRect.y];\n    // Remain contentPos when scroll animation perfroming.\n    // If first rendering, `contentGroup.position` is [0, 0], which\n    // does not make sense and may cause unexepcted animation if adopted.\n    if (!isFirstRender) {\n      contentPos[orientIdx] = contentGroup[xy];\n    }\n    // Layout container group based on 0.\n    var containerPos = [0, 0];\n    var controllerPos = [-controllerRect.x, -controllerRect.y];\n    var pageButtonGap = zrUtil.retrieve2(legendModel.get('pageButtonGap', true), legendModel.get('itemGap', true));\n    // Place containerGroup and controllerGroup and contentGroup.\n    if (showController) {\n      var pageButtonPosition = legendModel.get('pageButtonPosition', true);\n      // controller is on the right / bottom.\n      if (pageButtonPosition === 'end') {\n        controllerPos[orientIdx] += maxSize[wh] - controllerRect[wh];\n      }\n      // controller is on the left / top.\n      else {\n        containerPos[orientIdx] += controllerRect[wh] + pageButtonGap;\n      }\n    }\n    // Always align controller to content as 'middle'.\n    controllerPos[1 - orientIdx] += contentRect[hw] / 2 - controllerRect[hw] / 2;\n    contentGroup.setPosition(contentPos);\n    containerGroup.setPosition(containerPos);\n    controllerGroup.setPosition(controllerPos);\n    // Calculate `mainRect` and set `clipPath`.\n    // mainRect should not be calculated by `this.group.getBoundingRect()`\n    // for sake of the overflow.\n    var mainRect = {\n      x: 0,\n      y: 0\n    };\n    // Consider content may be overflow (should be clipped).\n    mainRect[wh] = showController ? maxSize[wh] : contentRect[wh];\n    mainRect[hw] = Math.max(contentRect[hw], controllerRect[hw]);\n    // `containerRect[yx] + containerPos[1 - orientIdx]` is 0.\n    mainRect[yx] = Math.min(0, controllerRect[yx] + controllerPos[1 - orientIdx]);\n    containerGroup.__rectSize = maxSize[wh];\n    if (showController) {\n      var clipShape = {\n        x: 0,\n        y: 0\n      };\n      clipShape[wh] = Math.max(maxSize[wh] - controllerRect[wh] - pageButtonGap, 0);\n      clipShape[hw] = mainRect[hw];\n      containerGroup.setClipPath(new graphic.Rect({\n        shape: clipShape\n      }));\n      // Consider content may be larger than container, container rect\n      // can not be obtained from `containerGroup.getBoundingRect()`.\n      containerGroup.__rectSize = clipShape[wh];\n    } else {\n      // Do not remove or ignore controller. Keep them set as placeholders.\n      controllerGroup.eachChild(function (child) {\n        child.attr({\n          invisible: true,\n          silent: true\n        });\n      });\n    }\n    // Content translate animation.\n    var pageInfo = this._getPageInfo(legendModel);\n    pageInfo.pageIndex != null && graphic.updateProps(contentGroup, {\n      x: pageInfo.contentPosition[0],\n      y: pageInfo.contentPosition[1]\n    },\n    // When switch from \"show controller\" to \"not show controller\", view should be\n    // updated immediately without animation, otherwise causes weird effect.\n    showController ? legendModel : null);\n    this._updatePageInfoView(legendModel, pageInfo);\n    return mainRect;\n  };\n  ScrollableLegendView.prototype._pageGo = function (to, legendModel, api) {\n    var scrollDataIndex = this._getPageInfo(legendModel)[to];\n    scrollDataIndex != null && api.dispatchAction({\n      type: 'legendScroll',\n      scrollDataIndex: scrollDataIndex,\n      legendId: legendModel.id\n    });\n  };\n  ScrollableLegendView.prototype._updatePageInfoView = function (legendModel, pageInfo) {\n    var controllerGroup = this._controllerGroup;\n    zrUtil.each(['pagePrev', 'pageNext'], function (name) {\n      var key = name + 'DataIndex';\n      var canJump = pageInfo[key] != null;\n      var icon = controllerGroup.childOfName(name);\n      if (icon) {\n        icon.setStyle('fill', canJump ? legendModel.get('pageIconColor', true) : legendModel.get('pageIconInactiveColor', true));\n        icon.cursor = canJump ? 'pointer' : 'default';\n      }\n    });\n    var pageText = controllerGroup.childOfName('pageText');\n    var pageFormatter = legendModel.get('pageFormatter');\n    var pageIndex = pageInfo.pageIndex;\n    var current = pageIndex != null ? pageIndex + 1 : 0;\n    var total = pageInfo.pageCount;\n    pageText && pageFormatter && pageText.setStyle('text', zrUtil.isString(pageFormatter) ? pageFormatter.replace('{current}', current == null ? '' : current + '').replace('{total}', total == null ? '' : total + '') : pageFormatter({\n      current: current,\n      total: total\n    }));\n  };\n  /**\r\n   *  contentPosition: Array.<number>, null when data item not found.\r\n   *  pageIndex: number, null when data item not found.\r\n   *  pageCount: number, always be a number, can be 0.\r\n   *  pagePrevDataIndex: number, null when no previous page.\r\n   *  pageNextDataIndex: number, null when no next page.\r\n   * }\r\n   */\n  ScrollableLegendView.prototype._getPageInfo = function (legendModel) {\n    var scrollDataIndex = legendModel.get('scrollDataIndex', true);\n    var contentGroup = this.getContentGroup();\n    var containerRectSize = this._containerGroup.__rectSize;\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n    var targetItemIndex = this._findTargetItemIndex(scrollDataIndex);\n    var children = contentGroup.children();\n    var targetItem = children[targetItemIndex];\n    var itemCount = children.length;\n    var pCount = !itemCount ? 0 : 1;\n    var result = {\n      contentPosition: [contentGroup.x, contentGroup.y],\n      pageCount: pCount,\n      pageIndex: pCount - 1,\n      pagePrevDataIndex: null,\n      pageNextDataIndex: null\n    };\n    if (!targetItem) {\n      return result;\n    }\n    var targetItemInfo = getItemInfo(targetItem);\n    result.contentPosition[orientIdx] = -targetItemInfo.s;\n    // Strategy:\n    // (1) Always align based on the left/top most item.\n    // (2) It is user-friendly that the last item shown in the\n    // current window is shown at the begining of next window.\n    // Otherwise if half of the last item is cut by the window,\n    // it will have no chance to display entirely.\n    // (3) Consider that item size probably be different, we\n    // have calculate pageIndex by size rather than item index,\n    // and we can not get page index directly by division.\n    // (4) The window is to narrow to contain more than\n    // one item, we should make sure that the page can be fliped.\n    for (var i = targetItemIndex + 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i <= itemCount; ++i) {\n      currItemInfo = getItemInfo(children[i]);\n      if (\n      // Half of the last item is out of the window.\n      !currItemInfo && winEndItemInfo.e > winStartItemInfo.s + containerRectSize\n      // If the current item does not intersect with the window, the new page\n      // can be started at the current item or the last item.\n      || currItemInfo && !intersect(currItemInfo, winStartItemInfo.s)) {\n        if (winEndItemInfo.i > winStartItemInfo.i) {\n          winStartItemInfo = winEndItemInfo;\n        } else {\n          // e.g., when page size is smaller than item size.\n          winStartItemInfo = currItemInfo;\n        }\n        if (winStartItemInfo) {\n          if (result.pageNextDataIndex == null) {\n            result.pageNextDataIndex = winStartItemInfo.i;\n          }\n          ++result.pageCount;\n        }\n      }\n      winEndItemInfo = currItemInfo;\n    }\n    for (var i = targetItemIndex - 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i >= -1; --i) {\n      currItemInfo = getItemInfo(children[i]);\n      if (\n      // If the the end item does not intersect with the window started\n      // from the current item, a page can be settled.\n      (!currItemInfo || !intersect(winEndItemInfo, currItemInfo.s)\n      // e.g., when page size is smaller than item size.\n      ) && winStartItemInfo.i < winEndItemInfo.i) {\n        winEndItemInfo = winStartItemInfo;\n        if (result.pagePrevDataIndex == null) {\n          result.pagePrevDataIndex = winStartItemInfo.i;\n        }\n        ++result.pageCount;\n        ++result.pageIndex;\n      }\n      winStartItemInfo = currItemInfo;\n    }\n    return result;\n    function getItemInfo(el) {\n      if (el) {\n        var itemRect = el.getBoundingRect();\n        var start = itemRect[xy] + el[xy];\n        return {\n          s: start,\n          e: start + itemRect[wh],\n          i: el.__legendDataIndex\n        };\n      }\n    }\n    function intersect(itemInfo, winStart) {\n      return itemInfo.e >= winStart && itemInfo.s <= winStart + containerRectSize;\n    }\n  };\n  ScrollableLegendView.prototype._findTargetItemIndex = function (targetDataIndex) {\n    if (!this._showController) {\n      return 0;\n    }\n    var index;\n    var contentGroup = this.getContentGroup();\n    var defaultIndex;\n    contentGroup.eachChild(function (child, idx) {\n      var legendDataIdx = child.__legendDataIndex;\n      // FIXME\n      // If the given targetDataIndex (from model) is illegal,\n      // we use defaultIndex. But the index on the legend model and\n      // action payload is still illegal. That case will not be\n      // changed until some scenario requires.\n      if (defaultIndex == null && legendDataIdx != null) {\n        defaultIndex = idx;\n      }\n      if (legendDataIdx === targetDataIndex) {\n        index = idx;\n      }\n    });\n    return index != null ? index : defaultIndex;\n  };\n  ScrollableLegendView.type = 'legend.scroll';\n  return ScrollableLegendView;\n}(LegendView);\nexport default ScrollableLegendView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}