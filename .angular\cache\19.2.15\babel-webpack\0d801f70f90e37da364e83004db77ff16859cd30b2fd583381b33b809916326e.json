{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProgramDayTasksService = /*#__PURE__*/(() => {\n  class ProgramDayTasksService {\n    http;\n    CreateProgramDayTasksURL = environment.baseUrl + 'Programs/add-program-day-tasks/';\n    SaveProgramDayTaskDetailsURL = environment.baseUrl + 'Programs/save-program-day-tasks-details/';\n    CopyProgramDayTasksURL = environment.baseUrl + 'Programs/copy-program-day-task/';\n    DeleteProgramDayTasksURL = environment.baseUrl + 'Programs/delete-program-day-task/';\n    GetProgramDayTasksURL = environment.baseUrl + 'Programs/get-day-tasks-by-program-day/';\n    UpdateOrderByProgramDayTasksURL = environment.baseUrl + 'Programs/update-order-program-day-tasks/';\n    getProgramLastFiveHomeWorkToLinkAutoURL = environment.baseUrl + 'Programs/get-program-last-five-work-to-link-auto/';\n    getProgramMemorizeAtDayURL = environment.baseUrl + 'Programs/get-program-momrize-at-day/';\n    constructor(http) {\n      this.http = http;\n    }\n    AddProgramDayTasks(model) {\n      return this.http.post(this.CreateProgramDayTasksURL, model);\n    }\n    SaveProgramDayTaskDetails(model) {\n      return this.http.put(this.SaveProgramDayTaskDetailsURL, model);\n    }\n    CopyProgramDayTasks(id) {\n      return this.http.get(this.CopyProgramDayTasksURL + id);\n    }\n    DeleteProgramDayTasks(id) {\n      return this.http.delete(this.DeleteProgramDayTasksURL + id);\n    }\n    getProgramDayTasks(id) {\n      return this.http.get(this.GetProgramDayTasksURL + id);\n    }\n    UpdateOrderByProgramDayTasks(model) {\n      return this.http.put(this.UpdateOrderByProgramDayTasksURL, model);\n    }\n    GetProgramLastFiveHomeWorkToLinkAuto(model) {\n      return this.http.post(this.getProgramLastFiveHomeWorkToLinkAutoURL, model);\n    }\n    getProgramMemorizeAtDay(dayId) {\n      return this.http.get(this.getProgramMemorizeAtDayURL + dayId);\n    }\n    static ɵfac = function ProgramDayTasksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramDayTasksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProgramDayTasksService,\n      factory: ProgramDayTasksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ProgramDayTasksService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}