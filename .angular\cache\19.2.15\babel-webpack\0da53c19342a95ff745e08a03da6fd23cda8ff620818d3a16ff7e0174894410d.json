{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DropOutRoleEnum } from 'src/app/core/enums/drop-out-request-enums/drop-out-status.enum';\nimport { StudentDropOutRequestStatusEnum } from 'src/app/core/enums/drop-out-request-enums/student-drop-out-request-status.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/student-drop-out-request-services/student-drop-out-request.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-drop-out-grid\", 12);\n    i0.ɵɵlistener(\"acceptStudentDropOutAdminRequest\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_acceptStudentDropOutAdminRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptStudentDropOutRequest($event));\n    })(\"itemStudentDropOutRequestForAdminReject\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_itemStudentDropOutRequestForAdminReject_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectStudentDropOutRequestMethod($event));\n    })(\"acceptAllStudentDropOutRequestAdminChecked\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_acceptAllStudentDropOutRequestAdminChecked_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllStudentDropOutRequestChecked());\n    })(\"rejectStudentDropOutAdminRequest\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_rejectStudentDropOutAdminRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectStudentDropOutRequestEvent($event));\n    })(\"studentDropOutRequestFilterAdminEvent\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentDropOutRequestFilterAdminEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentDropOutRequestPendingChangePage($event));\n    })(\"studentIdToDetails\", function StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentIdToDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"studentDropOutRequestAdminItems\", ctx_r1.studentDropOutRequestList)(\"typeEnum\", ctx_r1.statusEnum.Pending)(\"totalCount\", ctx_r1.totalCount)(\"userMode\", ctx_r1.userMode)(\"studentDropOutRequestFilterRequestAdminModel\", ctx_r1.studentDropOutRequestFilterRequestModel);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StudentDropOutTabRequestComponent_div_0_ng_container_18_app_student_drop_out_grid_1_Template, 1, 5, \"app-student-drop-out-grid\", 11)(2, StudentDropOutTabRequestComponent_div_0_ng_container_18_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList && ctx_r1.studentDropOutRequestList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList.length === 0);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_19_app_student_drop_out_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-drop-out-grid\", 15);\n    i0.ɵɵlistener(\"rejectStudentDropOutAdminRequest\", function StudentDropOutTabRequestComponent_div_0_ng_container_19_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_rejectStudentDropOutAdminRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectStudentDropOutRequestEvent($event));\n    })(\"studentDropOutRequestFilterAdminEvent\", function StudentDropOutTabRequestComponent_div_0_ng_container_19_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentDropOutRequestFilterAdminEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentDropOutRequestAcceptChangePage($event));\n    })(\"studentIdToDetails\", function StudentDropOutTabRequestComponent_div_0_ng_container_19_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentIdToDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"typeEnum\", ctx_r1.statusEnum.Accept)(\"studentDropOutRequestAdminItems\", ctx_r1.studentDropOutRequestList)(\"totalCount\", ctx_r1.totalCount)(\"userMode\", ctx_r1.userMode)(\"studentDropOutRequestFilterRequestAdminModel\", ctx_r1.studentDropOutRequestFilterRequestModel);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StudentDropOutTabRequestComponent_div_0_ng_container_19_app_student_drop_out_grid_1_Template, 1, 5, \"app-student-drop-out-grid\", 14)(2, StudentDropOutTabRequestComponent_div_0_ng_container_19_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList && ctx_r1.studentDropOutRequestList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList.length === 0);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_20_app_student_drop_out_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-drop-out-grid\", 15);\n    i0.ɵɵlistener(\"rejectStudentDropOutAdminRequest\", function StudentDropOutTabRequestComponent_div_0_ng_container_20_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_rejectStudentDropOutAdminRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectStudentDropOutRequestEvent($event));\n    })(\"studentDropOutRequestFilterAdminEvent\", function StudentDropOutTabRequestComponent_div_0_ng_container_20_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentDropOutRequestFilterAdminEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentDropOutRequestRejectChangePage($event));\n    })(\"studentIdToDetails\", function StudentDropOutTabRequestComponent_div_0_ng_container_20_app_student_drop_out_grid_1_Template_app_student_drop_out_grid_studentIdToDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"typeEnum\", ctx_r1.statusEnum.Rejected)(\"studentDropOutRequestAdminItems\", ctx_r1.studentDropOutRequestList)(\"totalCount\", ctx_r1.totalCount)(\"userMode\", ctx_r1.userMode)(\"studentDropOutRequestFilterRequestAdminModel\", ctx_r1.studentDropOutRequestFilterRequestModel);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StudentDropOutTabRequestComponent_div_0_ng_container_20_app_student_drop_out_grid_1_Template, 1, 5, \"app-student-drop-out-grid\", 14)(2, StudentDropOutTabRequestComponent_div_0_ng_container_20_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList && ctx_r1.studentDropOutRequestList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentDropOutRequestList.length === 0);\n  }\n}\nfunction StudentDropOutTabRequestComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"app-search-input\", 4);\n    i0.ɵɵlistener(\"searchTerm\", function StudentDropOutTabRequestComponent_div_0_Template_app_search_input_searchTerm_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function StudentDropOutTabRequestComponent_div_0_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAvancedSearch());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function StudentDropOutTabRequestComponent_div_0_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPendingChange());\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function StudentDropOutTabRequestComponent_div_0_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptChange());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function StudentDropOutTabRequestComponent_div_0_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRejectedChange());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 9);\n    i0.ɵɵtemplate(18, StudentDropOutTabRequestComponent_div_0_ng_container_18_Template, 3, 2, \"ng-container\", 10)(19, StudentDropOutTabRequestComponent_div_0_ng_container_19_Template, 3, 2, \"ng-container\", 10)(20, StudentDropOutTabRequestComponent_div_0_ng_container_20_Template, 3, 2, \"ng-container\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"searchKey\", ctx_r1.studentDropOutRequestFilterRequestModel.usrName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 11, \"STUDENT_SUBSCRIBERS.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Pending));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 13, \"STUDENT_SUBSCRIBERS.NEW_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Accept));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 15, \"STUDENT_SUBSCRIBERS.ACCEPTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Rejected));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 17, \"STUDENT_SUBSCRIBERS.REJECTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Accept);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Rejected);\n  }\n}\nfunction StudentDropOutTabRequestComponent_app_student_details_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-details-view\", 16);\n    i0.ɵɵlistener(\"hideUserDetails\", function StudentDropOutTabRequestComponent_app_student_details_view_1_Template_app_student_details_view_hideUserDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideUserDetailsView($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resiveUserId\", ctx_r1.StudentDropIdInput);\n  }\n}\nexport let StudentDropOutTabRequestComponent = /*#__PURE__*/(() => {\n  class StudentDropOutTabRequestComponent {\n    translate;\n    studentDropOutRequestService;\n    alertify;\n    rejectStudentDropOutRequest = new EventEmitter();\n    advancedSearchEvent = new EventEmitter();\n    itemOfRejectStudentDropOutRequest = new EventEmitter();\n    closePopup = new EventEmitter();\n    StudentDropIdInput;\n    studentDropOutRequestList = [];\n    studentDropOutRequestFilterRequestModel = {\n      statusNum: StudentDropOutRequestStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortOrder: -1,\n      page: 1,\n      sortField: 'requestdate'\n    };\n    resultMessage = {};\n    totalCount = 0;\n    numberItemsPerRow = 3;\n    ids = [];\n    typeEnum = StudentDropOutRequestStatusEnum.Pending;\n    showTap = StudentDropOutRequestStatusEnum.Pending;\n    statusEnum = StudentDropOutRequestStatusEnum;\n    userMode = DropOutRoleEnum.Admin;\n    showUserDetailsView = false;\n    constructor(translate, studentDropOutRequestService, alertify) {\n      this.translate = translate;\n      this.studentDropOutRequestService = studentDropOutRequestService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      // this.studentDropOutRequestFilterRequestModel.sortField = this.translate.currentLang === LanguageEnum.ar ? 'userNameAr' : 'UserNameEn'\n      this.studentDropOutRequestFilterRequestModel.sortField = 'requestdate';\n      this.getStudentDropOutRequests();\n    }\n    studentIdDrop(event) {\n      this.showUserDetailsView = true;\n      this.StudentDropIdInput = event;\n    }\n    searchByText(searchKey) {\n      this.studentDropOutRequestFilterRequestModel.usrName = searchKey;\n      this.getStudentDropOutRequests();\n    }\n    getStudentDropOutRequests() {\n      this.studentDropOutRequestService.studentDropOutRequestAdvFilterAdminView(this.studentDropOutRequestFilterRequestModel).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.studentDropOutRequestList = res.data;\n          this.totalCount = res.count ? res.count : 0;\n          if (this.studentDropOutRequestFilterRequestModel.skip > 0 && (!this.studentDropOutRequestList || this.studentDropOutRequestList.length === 0)) {\n            this.studentDropOutRequestFilterRequestModel.page -= 1;\n            this.studentDropOutRequestFilterRequestModel.skip = (this.studentDropOutRequestFilterRequestModel.page - 1) * this.studentDropOutRequestFilterRequestModel.take;\n            this.getStudentDropOutRequests();\n          }\n        } else {\n          this.resultMessage = {\n            message: response.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onPendingChange() {\n      this.studentDropOutRequestFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentDropOutRequestStatusEnum.Pending,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentDropOutRequestStatusEnum.Pending;\n      this.closeAvancedSearch();\n      this.getStudentDropOutRequests();\n    }\n    onAcceptChange() {\n      this.studentDropOutRequestFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentDropOutRequestStatusEnum.Accept,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentDropOutRequestStatusEnum.Accept;\n      this.closeAvancedSearch();\n      this.getStudentDropOutRequests();\n    }\n    onRejectedChange() {\n      this.studentDropOutRequestFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentDropOutRequestStatusEnum.Rejected,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentDropOutRequestStatusEnum.Rejected;\n      this.closeAvancedSearch();\n      this.getStudentDropOutRequests();\n    }\n    closeAvancedSearch() {\n      this.studentDropOutRequestFilterRequestModel.usrName = '';\n      this.studentDropOutRequestFilterRequestModel.progId = '';\n      this.studentDropOutRequestFilterRequestModel.numberRequest = undefined;\n      this.studentDropOutRequestFilterRequestModel.fromDate = undefined;\n      this.studentDropOutRequestFilterRequestModel.toDate = undefined;\n      this.studentDropOutRequestFilterRequestModel.skip = 0;\n      this.studentDropOutRequestFilterRequestModel.take = 9;\n      this.studentDropOutRequestFilterRequestModel.sortField = 'requestdate';\n      this.studentDropOutRequestFilterRequestModel.sortOrder = -1;\n      this.studentDropOutRequestFilterRequestModel.page = 1;\n      this.closePopup.emit(); // as per issue number 3250\n    }\n    acceptAllStudentDropOutRequestChecked() {\n      this.ids = this.studentDropOutRequestList?.filter(i => i.checked).map(a => a.id || '');\n      this.studentDropOutRequestService.studentDropOutRequestsAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentDropOutRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    studentDropOutRequestPendingChangePage(event) {\n      this.studentDropOutRequestFilterRequestModel.statusNum = StudentDropOutRequestStatusEnum.Pending;\n      this.studentDropOutRequestFilterRequestModel = event;\n      this.getStudentDropOutRequests();\n    }\n    studentDropOutRequestAcceptChangePage(event) {\n      this.studentDropOutRequestFilterRequestModel.statusNum = StudentDropOutRequestStatusEnum.Accept;\n      this.studentDropOutRequestFilterRequestModel = event;\n      this.getStudentDropOutRequests();\n    }\n    studentDropOutRequestRejectChangePage(event) {\n      this.studentDropOutRequestFilterRequestModel.statusNum = StudentDropOutRequestStatusEnum.Rejected;\n      this.studentDropOutRequestFilterRequestModel = event;\n      this.getStudentDropOutRequests();\n    }\n    rejectStudentDropOutRequestMethod(event) {\n      this.itemOfRejectStudentDropOutRequest.emit(event);\n    }\n    rejectStudentDropOutRequestEvent(teacherSubscripModel) {\n      this.rejectStudentDropOutRequest.emit(teacherSubscripModel);\n    }\n    acceptStudentDropOutRequest(teacherSubscripModel) {\n      this.ids?.push(teacherSubscripModel.id || '');\n      this.studentDropOutRequestService.studentDropOutRequestsAcceptance(this.ids).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentDropOutRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    openAvancedSearch() {\n      this.advancedSearchEvent.emit(this.studentDropOutRequestFilterRequestModel);\n    }\n    hideUserDetailsView(event) {\n      this.showUserDetailsView = event;\n    }\n    static ɵfac = function StudentDropOutTabRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentDropOutTabRequestComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.StudentDropOutRequestService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentDropOutTabRequestComponent,\n      selectors: [[\"app-student-drop-out-tab-request\"]],\n      outputs: {\n        rejectStudentDropOutRequest: \"rejectStudentDropOutRequest\",\n        advancedSearchEvent: \"advancedSearchEvent\",\n        itemOfRejectStudentDropOutRequest: \"itemOfRejectStudentDropOutRequest\",\n        closePopup: \"closePopup\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid reuest_container\", 4, \"ngIf\"], [3, \"resiveUserId\", \"hideUserDetails\", 4, \"ngIf\"], [1, \"container-fluid\", \"reuest_container\"], [1, \"col-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", \"mx-2\", 3, \"click\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [4, \"ngIf\"], [3, \"studentDropOutRequestAdminItems\", \"typeEnum\", \"totalCount\", \"userMode\", \"studentDropOutRequestFilterRequestAdminModel\", \"acceptStudentDropOutAdminRequest\", \"itemStudentDropOutRequestForAdminReject\", \"acceptAllStudentDropOutRequestAdminChecked\", \"rejectStudentDropOutAdminRequest\", \"studentDropOutRequestFilterAdminEvent\", \"studentIdToDetails\", 4, \"ngIf\"], [3, \"acceptStudentDropOutAdminRequest\", \"itemStudentDropOutRequestForAdminReject\", \"acceptAllStudentDropOutRequestAdminChecked\", \"rejectStudentDropOutAdminRequest\", \"studentDropOutRequestFilterAdminEvent\", \"studentIdToDetails\", \"studentDropOutRequestAdminItems\", \"typeEnum\", \"totalCount\", \"userMode\", \"studentDropOutRequestFilterRequestAdminModel\"], [1, \"No_data\"], [3, \"typeEnum\", \"studentDropOutRequestAdminItems\", \"totalCount\", \"userMode\", \"studentDropOutRequestFilterRequestAdminModel\", \"rejectStudentDropOutAdminRequest\", \"studentDropOutRequestFilterAdminEvent\", \"studentIdToDetails\", 4, \"ngIf\"], [3, \"rejectStudentDropOutAdminRequest\", \"studentDropOutRequestFilterAdminEvent\", \"studentIdToDetails\", \"typeEnum\", \"studentDropOutRequestAdminItems\", \"totalCount\", \"userMode\", \"studentDropOutRequestFilterRequestAdminModel\"], [3, \"hideUserDetails\", \"resiveUserId\"]],\n      template: function StudentDropOutTabRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StudentDropOutTabRequestComponent_div_0_Template, 21, 25, \"div\", 0)(1, StudentDropOutTabRequestComponent_app_student_details_view_1_Template, 1, 1, \"app-student-details-view\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.showUserDetailsView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserDetailsView);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.TranslatePipe],\n      styles: [\".reuest_container[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:74vh;margin-top:1rem}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.reuest_container[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.reuest_container[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:55vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}.reuest_container[_ngcontent-%COMP%]   .w-60[_ngcontent-%COMP%]{width:60%}.reuest_container[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer;margin:0 .5rem}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(en){margin-left:66.666667%}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(ar){margin-right:66.666667%}\"]\n    });\n  }\n  return StudentDropOutTabRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}