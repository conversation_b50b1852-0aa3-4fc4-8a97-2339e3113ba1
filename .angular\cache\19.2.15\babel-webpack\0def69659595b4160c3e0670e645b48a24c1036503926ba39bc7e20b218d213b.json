{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { BasicInformationComponent } from './basic-information/basic-information.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-services/program.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction ProgramsTabsComponent_div_16_app_basic_information_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-basic-information\", 10);\n    i0.ɵɵlistener(\"refreshProgList\", function ProgramsTabsComponent_div_16_app_basic_information_2_Template_app_basic_information_refreshProgList_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.refreshProgList());\n    })(\"refreshProgDetails\", function ProgramsTabsComponent_div_16_app_basic_information_2_Template_app_basic_information_refreshProgDetails_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getProgramDetails());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progBasicInfoDetails\", ctx_r1.programDetails == null ? null : ctx_r1.programDetails.progBaseInfo);\n  }\n}\nfunction ProgramsTabsComponent_div_16_app_days_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-days\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progs\", ctx_r1.programDetails);\n  }\n}\nfunction ProgramsTabsComponent_div_16_app_join_requests_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-join-requests\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"programDetails\", ctx_r1.programDetails);\n  }\n}\nfunction ProgramsTabsComponent_div_16_app_notify_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-notify\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progId\", ctx_r1.programDetails == null ? null : ctx_r1.programDetails.progBaseInfo == null ? null : ctx_r1.programDetails.progBaseInfo.id);\n  }\n}\nfunction ProgramsTabsComponent_div_16_app_students_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-students\");\n  }\n}\nfunction ProgramsTabsComponent_div_16_app_program_batches_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-program-batches\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"programDetails\", ctx_r1.programDetails);\n  }\n}\nfunction ProgramsTabsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, ProgramsTabsComponent_div_16_app_basic_information_2_Template, 1, 1, \"app-basic-information\", 5)(3, ProgramsTabsComponent_div_16_app_days_3_Template, 1, 1, \"app-days\", 6)(4, ProgramsTabsComponent_div_16_app_join_requests_4_Template, 1, 1, \"app-join-requests\", 7)(5, ProgramsTabsComponent_div_16_app_notify_5_Template, 1, 1, \"app-notify\", 8)(6, ProgramsTabsComponent_div_16_app_students_6_Template, 1, 0, \"app-students\", 9)(7, ProgramsTabsComponent_div_16_app_program_batches_7_Template, 1, 1, \"app-program-batches\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"BASEINFO\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"DAYS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"JOIN\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"NOTIFACATION\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"STUDENS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"BATCHES\");\n  }\n}\nexport let ProgramsTabsComponent = /*#__PURE__*/(() => {\n  class ProgramsTabsComponent {\n    progService;\n    refreshProgListEvent = new EventEmitter();\n    basicInfoCompChild;\n    programModel;\n    programDetails;\n    resMessage = {};\n    showTap = 'BASEINFO';\n    constructor(progService) {\n      this.progService = progService;\n    }\n    ngOnInit() {\n      this.getProgramDetails();\n    }\n    ngOnChanges() {\n      this.getProgramDetails();\n    }\n    getProgramDetails() {\n      if (this.programModel && this.programModel.id) {\n        this.progService.getProgramDetails(this.programModel?.id || '').subscribe(res => {\n          if (res.isSuccess) {\n            this.programDetails = res.data;\n            if (this.basicInfoCompChild && this.basicInfoCompChild.basicInfoDetails) {\n              this.basicInfoCompChild.basicInfoDetails = this.programDetails.progBaseInfo;\n            }\n          } else {\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    refreshProgList() {\n      this.refreshProgListEvent.emit();\n    }\n    static ɵfac = function ProgramsTabsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramsTabsComponent)(i0.ɵɵdirectiveInject(i1.ProgramService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramsTabsComponent,\n      selectors: [[\"app-programs-tabs\"]],\n      viewQuery: function ProgramsTabsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(BasicInformationComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.basicInfoCompChild = _t.first);\n        }\n      },\n      inputs: {\n        programModel: \"programModel\"\n      },\n      outputs: {\n        refreshProgListEvent: \"refreshProgListEvent\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 17,\n      vars: 31,\n      consts: [[1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [\"class\", \"row fix_margin w-100\", 4, \"ngIf\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-0\"], [3, \"progBasicInfoDetails\", \"refreshProgList\", \"refreshProgDetails\", 4, \"ngIf\"], [3, \"progs\", 4, \"ngIf\"], [3, \"programDetails\", 4, \"ngIf\"], [3, \"progId\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"refreshProgList\", \"refreshProgDetails\", \"progBasicInfoDetails\"], [3, \"progs\"], [3, \"programDetails\"], [3, \"progId\"]],\n      template: function ProgramsTabsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function ProgramsTabsComponent_Template_div_click_1_listener() {\n            return ctx.showTap = \"BASEINFO\";\n          });\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function ProgramsTabsComponent_Template_div_click_4_listener() {\n            return ctx.showTap = \"JOIN\";\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function ProgramsTabsComponent_Template_div_click_7_listener() {\n            return ctx.showTap = \"DAYS\";\n          });\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function ProgramsTabsComponent_Template_div_click_10_listener() {\n            return ctx.showTap = \"NOTIFACATION\";\n          });\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function ProgramsTabsComponent_Template_div_click_13_listener() {\n            return ctx.showTap = \"BATCHES\";\n          });\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(16, ProgramsTabsComponent_div_16_Template, 8, 6, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.showTap == \"BASEINFO\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 11, \"PROGRAM_TABS.BASIC_INFO\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx.showTap == \"JOIN\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 13, \"PROGRAM_TABS.JOINING_EXAM\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ctx.showTap == \"DAYS\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 15, \"PROGRAM_TABS.DAYS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx.showTap == \"NOTIFACATION\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 17, \"PROGRAM_TABS.NOTIFACATION\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx.showTap == \"BATCHES\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 19, \"PROGRAM_TABS.BATCHES\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.programDetails);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i3.TranslatePipe],\n      styles: [\".group_header[_ngcontent-%COMP%]{margin-top:1.5%;border-bottom:.063rem solid var(--second_color)}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}\"]\n    });\n  }\n  return ProgramsTabsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}