{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AddScientificMaterialComponent } from './components/scientific-material-view/add-scientific-material/add-scientific-material.component';\nimport { ScientificMaterialViewComponent } from './components/scientific-material-view/scientific-material-view.component';\nimport { TstComponent } from './components/tst/tst.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'add-scientific-material',\n    component: AddScientificMaterialComponent\n  }, {\n    path: 'add-scientific-material/:id',\n    component: AddScientificMaterialComponent\n  }, {\n    path: 'scientific-material-view',\n    component: ScientificMaterialViewComponent\n  }, {\n    path: 'tst',\n    component: TstComponent\n  }]\n}];\nexport let ScientificMaterialRoutingModule = /*#__PURE__*/(() => {\n  class ScientificMaterialRoutingModule {\n    static ɵfac = function ScientificMaterialRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScientificMaterialRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ScientificMaterialRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return ScientificMaterialRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}