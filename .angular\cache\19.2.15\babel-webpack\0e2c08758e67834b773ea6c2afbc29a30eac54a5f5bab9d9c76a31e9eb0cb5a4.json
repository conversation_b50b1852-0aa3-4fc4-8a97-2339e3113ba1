{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport BrushController from '../helper/BrushController.js';\nimport * as brushHelper from '../helper/brushHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport ComponentView from '../../view/Component.js';\nvar elementList = ['axisLine', 'axisTickLabel', 'axisName'];\nvar ParallelAxisView = /** @class */function (_super) {\n  __extends(ParallelAxisView, _super);\n  function ParallelAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisView.type;\n    return _this;\n  }\n  ParallelAxisView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.apply(this, arguments);\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this));\n  };\n  ParallelAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    if (fromAxisAreaSelect(axisModel, ecModel, payload)) {\n      return;\n    }\n    this.axisModel = axisModel;\n    this.api = api;\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var coordSysModel = getCoordSysModel(axisModel, ecModel);\n    var coordSys = coordSysModel.coordinateSystem;\n    var areaSelectStyle = axisModel.getAreaSelectStyle();\n    var areaWidth = areaSelectStyle.width;\n    var dim = axisModel.axis.dim;\n    var axisLayout = coordSys.getAxisLayout(dim);\n    var builderOpt = zrUtil.extend({\n      strokeContainThreshold: areaWidth\n    }, axisLayout);\n    var axisBuilder = new AxisBuilder(axisModel, builderOpt);\n    zrUtil.each(elementList, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    this._refreshBrushController(builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n  };\n  // /**\n  //  * @override\n  //  */\n  // updateVisual(axisModel, ecModel, api, payload) {\n  //     this._brushController && this._brushController\n  //         .updateCovers(getCoverInfoList(axisModel));\n  // }\n  ParallelAxisView.prototype._refreshBrushController = function (builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api) {\n    // After filtering, axis may change, select area needs to be update.\n    var extent = axisModel.axis.getExtent();\n    var extentLen = extent[1] - extent[0];\n    var extra = Math.min(30, Math.abs(extentLen) * 0.1); // Arbitrary value.\n    // width/height might be negative, which will be\n    // normalized in BoundingRect.\n    var rect = graphic.BoundingRect.create({\n      x: extent[0],\n      y: -areaWidth / 2,\n      width: extentLen,\n      height: areaWidth\n    });\n    rect.x -= extra;\n    rect.width += 2 * extra;\n    this._brushController.mount({\n      enableGlobalPan: true,\n      rotation: builderOpt.rotation,\n      x: builderOpt.position[0],\n      y: builderOpt.position[1]\n    }).setPanels([{\n      panelId: 'pl',\n      clipPath: brushHelper.makeRectPanelClipPath(rect),\n      isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, coordSysModel),\n      getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect, 0)\n    }]).enableBrush({\n      brushType: 'lineX',\n      brushStyle: areaSelectStyle,\n      removeOnClick: true\n    }).updateCovers(getCoverInfoList(axisModel));\n  };\n  ParallelAxisView.prototype._onBrush = function (eventParam) {\n    var coverInfoList = eventParam.areas;\n    // Do not cache these object, because the mey be changed.\n    var axisModel = this.axisModel;\n    var axis = axisModel.axis;\n    var intervals = zrUtil.map(coverInfoList, function (coverInfo) {\n      return [axis.coordToData(coverInfo.range[0], true), axis.coordToData(coverInfo.range[1], true)];\n    });\n    // If realtime is true, action is not dispatched on drag end, because\n    // the drag end emits the same params with the last drag move event,\n    // and may have some delay when using touch pad.\n    if (!axisModel.option.realtime === eventParam.isEnd || eventParam.removeOnClick) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'axisAreaSelect',\n        parallelAxisId: axisModel.id,\n        intervals: intervals\n      });\n    }\n  };\n  ParallelAxisView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  ParallelAxisView.type = 'parallelAxis';\n  return ParallelAxisView;\n}(ComponentView);\nfunction fromAxisAreaSelect(axisModel, ecModel, payload) {\n  return payload && payload.type === 'axisAreaSelect' && ecModel.findComponents({\n    mainType: 'parallelAxis',\n    query: payload\n  })[0] === axisModel;\n}\nfunction getCoverInfoList(axisModel) {\n  var axis = axisModel.axis;\n  return zrUtil.map(axisModel.activeIntervals, function (interval) {\n    return {\n      brushType: 'lineX',\n      panelId: 'pl',\n      range: [axis.dataToCoord(interval[0], true), axis.dataToCoord(interval[1], true)]\n    };\n  });\n}\nfunction getCoordSysModel(axisModel, ecModel) {\n  return ecModel.getComponent('parallel', axisModel.get('parallelIndex'));\n}\nexport default ParallelAxisView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}