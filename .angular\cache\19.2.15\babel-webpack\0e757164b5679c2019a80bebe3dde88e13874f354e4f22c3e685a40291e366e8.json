{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { DateType } from 'ngx-hijri-gregorian-datepicker';\nimport { BaseSelectedDateModel } from \"../../../../core/ng-model/base-selected-date-model\";\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nlet UpdateTeacherProfileComponent = class UpdateTeacherProfileComponent {\n  fb;\n  lookupService;\n  teacherService;\n  userProfilePicService;\n  userService;\n  roleService;\n  attachmentService;\n  translate;\n  ProgramService;\n  languageService;\n  dateFormatterService;\n  chatService;\n  authService;\n  dialog;\n  router;\n  imagesPathesService;\n  profileForm = new FormGroup({});\n  currentUser;\n  telInputParam = {};\n  teacherProfileDetails = {};\n  updateTeacherModel = {};\n  teacherProgramModel = {};\n  availabilityDaysModel = {};\n  langEnum = LanguageEnum;\n  collectionOfLookup = {};\n  listOfLookupProfile = ['GENDER', 'EDU_LEVEL', 'NATIONALITY', 'COUNTRY', 'DEGREE', 'EDU_DATE', 'DAYS', 'LANG', 'QUALIFI', 'SPECIAL', 'REWAYAT', 'AGENCY', 'WORKING_PLATFORM'];\n  resMessage = {};\n  selecteddrgreeList = Array();\n  drgreeList__addition = Array();\n  degreeMessage = {};\n  isSubmit = false;\n  hijri = false;\n  milady = false;\n  hijriBinding;\n  hijriBirthDateInputParam; //= { year: 0, day: 0, month: 0 };\n  hijriInterviewDayInputParam; //{ year: 0, day: 0, month: 0 };\n  ProgramsList = [];\n  ProgramFilter = {};\n  rewayatsMessage = {};\n  selectedRewayatsList = Array();\n  languagesMessage = {};\n  selectedLanguagesList = Array();\n  availabilityDaysMessage = {};\n  selectedAvailabilityDaysList = Array();\n  teacherProgramsMessage = {};\n  selectedTeacherProgramsList = Array();\n  fileUploadModel = [];\n  fileList = [];\n  ejazaAttachmentIds = [];\n  programFilterByNameFilterRequest = {};\n  participantModel;\n  event = {\n    eampm: \"AM\"\n  };\n  selectedDateType;\n  selectedInterviewDateType;\n  //selectedDateType_Melady = DateType.Gregorian;  // or DateType.Gregorian\n  //selectedDateType_Hijri = DateType.Hijri;\n  updateCalenderType = new BaseSelectedDateModel();\n  updateInterviewCalenderType = new BaseSelectedDateModel();\n  maxHijriDate;\n  maxGregDate;\n  minHijriInterviewDate;\n  minGregInterviewDate;\n  minGregDate;\n  minHijriDate;\n  constructor(fb, lookupService, teacherService, userProfilePicService, userService, roleService, attachmentService, translate, ProgramService, languageService, dateFormatterService, chatService, authService, dialog, router, imagesPathesService) {\n    this.fb = fb;\n    this.lookupService = lookupService;\n    this.teacherService = teacherService;\n    this.userProfilePicService = userProfilePicService;\n    this.userService = userService;\n    this.roleService = roleService;\n    this.attachmentService = attachmentService;\n    this.translate = translate;\n    this.ProgramService = ProgramService;\n    this.languageService = languageService;\n    this.dateFormatterService = dateFormatterService;\n    this.chatService = chatService;\n    this.authService = authService;\n    this.dialog = dialog;\n    this.router = router;\n    this.imagesPathesService = imagesPathesService;\n  }\n  ngOnInit() {\n    this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n    this.chatService.getParticipantById(this.currentUser?.id || '');\n    this.setCurrentLang();\n    this.getCountryIsoCode();\n    this.buildForm();\n    this.getPrograms();\n    this.getLookupByKey();\n    this.setHijri();\n    this.setGreg();\n  }\n  setScssImages() {\n    this.imagesPathesService.setBackgroundPannerInStyle();\n  }\n  unsavedDataCheck() {\n    return this.profileForm.value.firstAr != this.teacherProfileDetails?.fnameAr || this.profileForm.value.firstNameEn != this.teacherProfileDetails?.faNameEn || this.profileForm.value.middleAr != this.teacherProfileDetails?.mnameAr || this.profileForm.value.middleNameEn != this.teacherProfileDetails?.mnameEn || this.profileForm.value.familyAr != this.teacherProfileDetails?.fanameAr || this.profileForm.value.familyNameEn != this.teacherProfileDetails?.faNameEn || this.profileForm.value.nationality != this.teacherProfileDetails?.nationality\n    // || this.profileForm.value.hijriBirthDate != this.teacherProfileDetails?.hijriBirthDate\n    || this.profileForm.value.gender != this.teacherProfileDetails?.gender || this.profileForm.value.mobile != this.teacherProfileDetails?.mobile || this.profileForm.value.country != this.teacherProfileDetails?.country || this.profileForm.value.city != this.teacherProfileDetails?.city || this.profileForm.value.nationality != this.teacherProfileDetails?.nationality || this.profileForm.value.edulevel != this.teacherProfileDetails?.eduLevel || this.profileForm.value.qualifi != this.teacherProfileDetails?.qualifi || this.profileForm.value.specia != this.teacherProfileDetails?.specia\n    // || this.profileForm.value.eduDate != this.teacherProfileDetails?.eduDate\n    || this.profileForm.value.eduNum != this.teacherProfileDetails?.eduNum || this.profileForm.value.isHasQuranExp != this.teacherProfileDetails?.isHasQuranExp?.toString() || this.profileForm.value.isHasTeachSunnaExp != this.teacherProfileDetails?.isHasTeachSunnaExp?.toString() || this.profileForm.value.isHasInternetTeachExp != this.teacherProfileDetails?.isHasInternetTeachExp?.toString() || this.profileForm.value.isHasTeachForeignerExp != this.teacherProfileDetails?.isHasTeachForeignerExp?.toString() || this.profileForm.value.isHasEjazaHafz != this.teacherProfileDetails?.isHasEjazaHafz?.toString() || this.profileForm.value.workingPlatForm != this.teacherProfileDetails?.workingPlatForm || this.profileForm.value.isHasEjazaTelawa != this.teacherProfileDetails?.isHasEjazaTelawa?.toString() || this.profileForm.value.bankName != this.teacherProfileDetails?.bankName || this.profileForm.value.agency != this.teacherProfileDetails?.agency || this.profileForm.value.address != this.teacherProfileDetails?.address || this.profileForm.value.bankNumber != this.teacherProfileDetails?.bankNumber;\n    //    || this.profileForm.value.ejazaAttachmentIds!= this.teacherProfileDetails?.ejazaAttachments\n  }\n  setCurrentLang() {\n    this.emitHeaderTitle();\n    this.languageService.currentLanguageEvent.subscribe(res => {\n      this.emitHeaderTitle();\n      this.getLookupByKey();\n      this.buildForm();\n      this.PopulateForm();\n    });\n  }\n  emitHeaderTitle() {\n    this.languageService.headerPageNameEvent.emit(this.translate.instant('UPDATE_TEACHER_PG.TITLE'));\n  }\n  getPrograms() {\n    this.programFilterByNameFilterRequest = {\n      name: \"\"\n    };\n    this.ProgramService.getAllPrograms(this.programFilterByNameFilterRequest).subscribe(res => {\n      let response = res;\n      if (response.isSuccess) {\n        this.ProgramsList = response.data;\n      } else {\n        this.ProgramsList = [];\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }, error => {\n      //logging\n    });\n  }\n  getLookupByKey() {\n    this.lookupService.getLookupByKey(this.listOfLookupProfile).subscribe(res => {\n      this.collectionOfLookup = res.data;\n      if (res.isSuccess) {\n        this.getTeacherProfile(this.currentUser?.id);\n      } else {\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    });\n  }\n  getCitiesLookupByCountry(id) {\n    let countryId = this.f['country'].value;\n    this.lookupService.getCitiesByCountryId(countryId || '').subscribe(res => {\n      if (res.isSuccess) {\n        this.collectionOfLookup.CITY = res.data;\n        this.collectionOfLookup && this.collectionOfLookup.CITY ? this.f.city.setValue(this.collectionOfLookup.CITY[0].id) : '';\n      } else {\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    });\n  }\n  get f() {\n    return this.profileForm.controls;\n  }\n  buildForm() {\n    if (this.translate.currentLang === LanguageEnum.ar) {\n      this.profileForm = this.fb.group({\n        firstAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        middleAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        familyAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        hijriBirthDate: [null, Validators.required],\n        gender: [null, Validators.required],\n        mobile: [null, Validators.required],\n        nationality: [null, Validators.required],\n        address: [],\n        country: [null, Validators.required],\n        city: [null, Validators.required],\n        email: [null, Validators.required],\n        qualifi: [null, Validators.required],\n        specia: [null, Validators.required],\n        // eduDate: [null, Validators.required],\n        eduNum: [null, [Validators.min(1)]],\n        entity: [null, Validators.required],\n        agency: [null, Validators.required],\n        edulevel: [null, Validators.required],\n        isHasQuranExp: [null, Validators.required],\n        isHasTeachSunnaExp: [null, Validators.required],\n        isHasInternetTeachExp: [null, Validators.required],\n        isHasTeachForeignerExp: [null, Validators.required],\n        isHasEjazaHafz: [null, Validators.required],\n        isHasEjazaTelawa: [null, Validators.required],\n        workingPlatForm: [null, Validators.required],\n        hijriInterviewDay: [null, Validators.required],\n        bankName: [null],\n        bankNumber: [null],\n        ejazaAttachments: [],\n        teacherPrograms: [],\n        teacherProgramDegrees: [],\n        availabilityDays: [],\n        interviewDay: [],\n        interviewTime: [],\n        fromDayTimeinterview: [],\n        toDayTimeinterview: [],\n        rewayats: [],\n        languages: []\n      });\n    } else {\n      this.profileForm = this.fb.group({\n        firstEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        middleEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        familyEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        hijriBirthDate: [null, Validators.required],\n        gender: [null, Validators.required],\n        mobile: [null, Validators.required],\n        nationality: [null, Validators.required],\n        address: [],\n        country: [null, Validators.required],\n        city: [null, Validators.required],\n        email: [null, Validators.required],\n        qualifi: [null, Validators.required],\n        specia: [null, Validators.required],\n        // eduDate: [null, Validators.required],\n        eduNum: [null, [Validators.min(1)]],\n        entity: [null, Validators.required],\n        agency: [null, Validators.required],\n        edulevel: [null, Validators.required],\n        isHasQuranExp: [null, Validators.required],\n        isHasTeachSunnaExp: [null, Validators.required],\n        isHasInternetTeachExp: [null, Validators.required],\n        isHasTeachForeignerExp: [null, Validators.required],\n        isHasEjazaHafz: [null, Validators.required],\n        isHasEjazaTelawa: [null, Validators.required],\n        workingPlatForm: [null, Validators.required],\n        hijriInterviewDay: [null, Validators.required],\n        bankName: [null],\n        bankNumber: [null],\n        ejazaAttachments: [],\n        teacherPrograms: [],\n        teacherProgramDegrees: [],\n        availabilityDays: [],\n        interviewDay: [],\n        interviewTime: [],\n        fromDayTimeinterview: [],\n        toDayTimeinterview: [],\n        rewayats: [],\n        languages: []\n      });\n    }\n  }\n  getTeacherProfile(id) {\n    this.teacherService.viewTeacherProfileDetails(id || '').subscribe(res => {\n      if (res.isSuccess) {\n        this.teacherProfileDetails = res.data;\n        if (!this.teacherProfileDetails?.proPic) {\n          this.teacherProfileDetails.proPic = this.imagesPathesService.profile;\n        }\n        this.PopulateForm();\n        if (this.teacherProfileDetails.country) {\n          this.getCitiesLookupByCountry(this.teacherProfileDetails.country);\n        }\n      } else {\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }, error => {\n      //logging\n    });\n  }\n  isRtlMode() {\n    return this.translate.currentLang == LanguageEnum.ar ? true : false;\n  }\n  PopulateForm() {\n    if (this.translate.currentLang === LanguageEnum.ar) {\n      this.f.firstAr.setValue(this.teacherProfileDetails?.fnameAr);\n      this.f.middleAr.setValue(this.teacherProfileDetails?.mnameAr);\n      this.f.familyAr.setValue(this.teacherProfileDetails?.fanameAr);\n    }\n    if (this.translate.currentLang === LanguageEnum.en) {\n      this.f.firstEn.setValue(this.teacherProfileDetails?.fnameEn);\n      this.f.middleEn.setValue(this.teacherProfileDetails?.mnameEn);\n      this.f.familyEn.setValue(this.teacherProfileDetails?.faNameEn);\n    }\n    if (this.teacherProfileDetails.birthDispMode == 1) {\n      this.updateCalenderType.selectedDateType = DateType.Hijri;\n      this.selectedDateType = DateType.Hijri;\n      let date = new Date(this.teacherProfileDetails?.birthdate || '');\n      this.hijriBirthDateInputParam = {\n        year: date?.getFullYear(),\n        month: date?.getMonth() + 1,\n        day: date?.getDate()\n      };\n      this.f.hijriBirthDate.setValue(this.teacherProfileDetails.birthdate);\n    } else if (this.teacherProfileDetails.birthDispMode == 2) {\n      this.updateCalenderType.selectedDateType = DateType.Gregorian;\n      this.selectedDateType = DateType.Gregorian;\n      let date = new Date(this.teacherProfileDetails?.birthGregorian || '');\n      this.hijriBirthDateInputParam = {\n        year: date?.getFullYear(),\n        month: date?.getMonth() + 1,\n        day: date?.getDate()\n      };\n      this.f.hijriBirthDate.setValue(this.teacherProfileDetails.birthGregorian);\n    } else {\n      this.updateCalenderType.selectedDateType = DateType.Hijri;\n      this.hijriBirthDateInputParam = {\n        year: NaN,\n        month: NaN,\n        day: NaN\n      };\n    }\n    if (this.teacherProfileDetails.interviewDisplayMode == 1) {\n      this.updateInterviewCalenderType.selectedDateType = DateType.Hijri;\n      this.selectedInterviewDateType = DateType.Hijri;\n      let date = new Date(this.teacherProfileDetails?.interviewHijri || '');\n      this.hijriInterviewDayInputParam = {\n        year: date.getFullYear(),\n        month: date.getMonth() + 1,\n        day: date.getDate()\n      };\n      this.f.hijriInterviewDay.setValue(this.teacherProfileDetails.interviewHijri);\n    } else if (this.teacherProfileDetails.interviewDisplayMode == 2) {\n      this.updateInterviewCalenderType.selectedDateType = DateType.Gregorian;\n      this.selectedInterviewDateType = DateType.Hijri;\n      let date = new Date(this.teacherProfileDetails?.interviewGregorian || '');\n      this.hijriInterviewDayInputParam = {\n        year: date.getFullYear(),\n        month: date.getMonth() + 1,\n        day: date.getDate()\n      };\n      this.f.hijriInterviewDay.setValue(this.teacherProfileDetails.interviewGregorian);\n    } else {\n      this.updateInterviewCalenderType.selectedDateType = DateType.Hijri;\n      this.hijriInterviewDayInputParam = {\n        year: NaN,\n        month: NaN,\n        day: NaN\n      };\n    }\n    this.f.nationality.setValue(this.teacherProfileDetails?.nationality);\n    this.f.country.setValue(this.teacherProfileDetails?.country);\n    this.f.address.setValue(this.teacherProfileDetails?.address);\n    this.f.city.setValue(this.teacherProfileDetails?.city);\n    this.f.email.setValue(this.teacherProfileDetails?.usrEmail);\n    this.f.mobile.setValue(this.teacherProfileDetails?.mobile);\n    this.telInputParam.phoneNumber = this.teacherProfileDetails?.mobile;\n    this.f.gender.setValue(this.teacherProfileDetails?.gender);\n    this.f.qualifi.setValue(this.teacherProfileDetails?.qualifi);\n    this.f.specia.setValue(this.teacherProfileDetails?.specia);\n    //this.f.eduDate.setValue(this.teacherProfileDetails?.eduDate)\n    this.f.edulevel.setValue(this.teacherProfileDetails?.eduLevel);\n    this.f.entity.setValue(this.teacherProfileDetails?.entity);\n    this.f.agency.setValue(this.teacherProfileDetails?.agency);\n    this.f.eduNum.setValue(this.teacherProfileDetails?.eduNum);\n    this.f.isHasQuranExp.setValue(this.teacherProfileDetails.isHasQuranExp?.toString());\n    this.f.isHasTeachSunnaExp.setValue(this.teacherProfileDetails.isHasTeachSunnaExp?.toString());\n    this.f.isHasInternetTeachExp.setValue(this.teacherProfileDetails.isHasInternetTeachExp?.toString());\n    this.f.isHasTeachForeignerExp.setValue(this.teacherProfileDetails.isHasTeachForeignerExp?.toString());\n    this.f.isHasEjazaHafz.setValue(this.teacherProfileDetails.isHasEjazaHafz?.toString());\n    this.f.isHasEjazaTelawa.setValue(this.teacherProfileDetails.isHasEjazaTelawa?.toString());\n    this.f.workingPlatForm.setValue(this.teacherProfileDetails?.workingPlatForm);\n    this.f.bankName.setValue(this.teacherProfileDetails?.bankName);\n    this.f.bankNumber.setValue(this.teacherProfileDetails?.bankNumber);\n    // let dateInterviewDay = new Date(this.teacherProfileDetails?.interviewHijri || '');\n    // this.hijriInterviewDayInputParam = { year: dateInterviewDay.getFullYear(), month: dateInterviewDay.getMonth() + 1, day: dateInterviewDay.getDay() }\n    // this.f.hijriInterviewDay.setValue(dateInterviewDay);\n    this.f.interviewTime.setValue(this.teacherProfileDetails?.interviewTime);\n    this.fileList = this.teacherProfileDetails?.ejazaAttachments;\n    this.teacherProfileDetails?.ejazaAttachments?.forEach(element => {\n      this.ejazaAttachmentIds.push(element.id);\n    });\n    if (this.teacherProfileDetails?.rewayats) {\n      this.selectedRewayatsList = this.teacherProfileDetails?.rewayats;\n    }\n    if (this.teacherProfileDetails?.teacherPrograms) {\n      this.selectedTeacherProgramsList = this.teacherProfileDetails?.teacherPrograms;\n    }\n    if (this.teacherProfileDetails?.availabilityDays) {\n      this.selectedAvailabilityDaysList = this.teacherProfileDetails?.availabilityDays;\n    }\n    if (this.teacherProfileDetails?.languages) {\n      this.selectedLanguagesList = this.teacherProfileDetails?.languages;\n    }\n  }\n  onFileChange(files) {\n    let profImagModel = {\n      usrId: this.currentUser?.id,\n      image: files[0]\n    };\n    this.updateProfilePic(profImagModel);\n  }\n  updateProfilePic(profImagModel) {\n    const formData = new FormData();\n    formData.append('UserProfilePictureModel.UserId', profImagModel.usrId || '');\n    formData.append('UserProfilePictureModel.ProfileImage', profImagModel.image);\n    this.userProfilePicService.updateUserProfilePic(formData).subscribe(res => {\n      if (res.isSuccess) {\n        this.teacherProfileDetails.proPic = res.data;\n      } else {\n        this.resMessage = {\n          message: res.message,\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }, error => {\n      //logging\n    });\n  }\n  onSubmit(value) {\n    this.isSubmit = true;\n    this.resMessage = {};\n    if (this.profileForm.valid) {\n      this.updateTeacherModel = {\n        usrId: this.currentUser?.id,\n        firstAr: this.profileForm.value.firstAr != null ? this.profileForm.value.firstAr : this.teacherProfileDetails.fnameAr,\n        firstEn: this.profileForm.value.firstEn != null ? this.profileForm.value.firstEn : this.teacherProfileDetails.faNameEn,\n        middleAr: this.profileForm.value.middleAr != null ? this.profileForm.value.middleAr : this.teacherProfileDetails.mnameAr,\n        middleEn: this.profileForm.value.middleEn != null ? this.profileForm.value.middleEn : this.teacherProfileDetails.mnameEn,\n        familyAr: this.profileForm.value.familyAr != null ? this.profileForm.value.familyAr : this.teacherProfileDetails.fanameAr,\n        familyEn: this.profileForm.value.familyEn != null ? this.profileForm.value.familyEn : this.teacherProfileDetails.fanameAr,\n        nationality: this.profileForm.value.nationality,\n        birthDate: this.selectedDateType == 1 ? this.profileForm.value.hijriBirthDate : null,\n        birthGregorian: this.selectedDateType == 2 ? this.profileForm.value.hijriBirthDate : null,\n        gender: this.profileForm.value.gender,\n        mobile: this.profileForm.value.mobile,\n        country: this.profileForm.value.country,\n        city: this.profileForm.value.city,\n        edulevel: this.profileForm.value.edulevel,\n        qualifi: this.profileForm.value.qualifi,\n        specia: this.profileForm.value.specia,\n        workingPlatForm: this.profileForm.value.workingPlatForm,\n        entity: this.profileForm.value.entity,\n        //eduDate: this.profileForm.value.eduDate,  commented because client Requests to be education with years only\n        eduNum: this.profileForm.value.eduNum,\n        isHasQuranExp: this.profileForm.value.isHasQuranExp,\n        isHasTeachSunnaExp: this.profileForm.value.isHasTeachSunnaExp,\n        isHasInternetTeachExp: this.profileForm.value.isHasInternetTeachExp,\n        isHasTeachForeignerExp: this.profileForm.value.isHasTeachForeignerExp,\n        isHasEjazaHafz: this.profileForm.value.isHasEjazaHafz,\n        isHasEjazaTelawa: this.profileForm.value.isHasEjazaTelawa,\n        agency: this.profileForm.value.agency,\n        bankName: this.profileForm.value.bankName,\n        bankNumber: this.profileForm.value.bankNumber,\n        // interviewHijri: this.profileForm.value.hijriInterviewDay,\n        interviewHijri: this.selectedInterviewDateType == 1 ? this.profileForm.value.hijriInterviewDay : null,\n        interviewGregorian: this.selectedInterviewDateType == 2 ? this.profileForm.value.hijriInterviewDay : null,\n        interviewTime: this.profileForm.value.interviewTime,\n        address: this.profileForm.value.address,\n        ejazaAttachments: this.ejazaAttachmentIds,\n        birthDispMode: this.selectedDateType,\n        interviewDisplayMode: this.selectedInterviewDateType\n      };\n      this.rewayatsMessage = {};\n      this.updateTeacherModel.rewayats = [];\n      if (this.selectedRewayatsList.length) {\n        Array.from(this.selectedRewayatsList).forEach(elm => {\n          if (this.updateTeacherModel.rewayats) {\n            this.updateTeacherModel.rewayats.push({\n              rewayat: elm.id\n            });\n          }\n        });\n      }\n      this.languagesMessage = {};\n      this.updateTeacherModel.languages = [];\n      if (this.selectedLanguagesList.length) {\n        Array.from(this.selectedLanguagesList).forEach(elm => {\n          if (this.updateTeacherModel.languages) {\n            this.updateTeacherModel.languages.push({\n              language: elm.id\n            });\n          }\n        });\n      }\n      this.availabilityDaysMessage = {};\n      this.updateTeacherModel.availabilityDays = [];\n      if (this.selectedAvailabilityDaysList.length > 0) {\n        Array.from(this.selectedAvailabilityDaysList).forEach(elm => {\n          if (this.updateTeacherModel.availabilityDays) {\n            this.updateTeacherModel.availabilityDays.push({\n              availableDay: elm.id,\n              fromTime: elm.fromTime,\n              toTime: elm.toTime\n            });\n          }\n        });\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.AVAILABLE_TIMES_ERROR'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.teacherProgramsMessage = {};\n      this.updateTeacherModel.teacherPrograms = [];\n      if (this.selectedTeacherProgramsList.length) {\n        Array.from(this.selectedTeacherProgramsList).forEach(elm => {\n          if (this.updateTeacherModel.teacherPrograms) {\n            this.updateTeacherModel.teacherPrograms.push({\n              program: elm.programId,\n              degree: elm.degreeId\n            });\n          }\n        });\n      }\n      this.participantModel = {\n        id: this.updateTeacherModel.usrId,\n        name_ar: this.updateTeacherModel.firstAr + \" \" + this.updateTeacherModel.middleAr + \" \" + this.updateTeacherModel.familyAr,\n        name_en: this.updateTeacherModel.firstEn === null ? '' : this.updateTeacherModel.firstEn + \" \" + this.updateTeacherModel.middleEn === null ? '' : this.updateTeacherModel.middleEn + \" \" + this.updateTeacherModel.familyEn === null ? '' : this.updateTeacherModel.familyEn,\n        key: this.updateTeacherModel.usrId,\n        hoffazId: \"1\",\n        avatar_url: this.teacherProfileDetails.proPic,\n        role: RoleEnum.Teacher,\n        gender: this.updateTeacherModel.gender\n      };\n      if (this.chatService.participantData && this.teacherProfileDetails) {\n        this.chatService.participantData.id = this.updateTeacherModel.usrId;\n        this.chatService.participantData.name_ar = (this.updateTeacherModel.firstAr === null ? this.teacherProfileDetails.fnameAr : this.updateTeacherModel.firstAr) + \" \" + (this.updateTeacherModel.middleAr === null ? this.teacherProfileDetails.mnameAr : this.updateTeacherModel.middleAr) + \" \" + (this.updateTeacherModel.familyAr === null ? this.teacherProfileDetails.fanameAr : this.updateTeacherModel.familyAr);\n        this.chatService.participantData.name_en = (this.updateTeacherModel.firstEn === null ? this.teacherProfileDetails.fnameEn : this.updateTeacherModel.firstEn) + \" \" + (this.updateTeacherModel.middleEn === null ? this.teacherProfileDetails.mnameEn : this.updateTeacherModel.middleEn) + \" \" + (this.updateTeacherModel.familyEn === null ? this.teacherProfileDetails.faNameEn : this.updateTeacherModel.familyEn);\n        this.chatService.participantData.key = this.updateTeacherModel.usrId;\n        this.chatService.participantData.hoffazId = \"1\";\n        this.chatService.participantData.avatar_url = this.teacherProfileDetails.proPic;\n        this.chatService.participantData.role = RoleEnum.Teacher;\n        this.chatService.participantData.gender = this.updateTeacherModel.gender;\n        this.chatService.updateParticipant(this.chatService.participantData);\n      } else {\n        this.chatService.addParticipant(this.participantModel);\n      }\n      this.teacherService.updateTeacher(this.updateTeacherModel).subscribe(res => {\n        if (res.isSuccess) {\n          this.isSubmit = false;\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.SUCCESS_TYPE\n          };\n          this.router.navigateByUrl('teacher/view-teacher-profile-details');\n        } else {\n          this.isSubmit = false;\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    } else {\n      this.resMessage = {\n        message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    }\n  }\n  addTeacherRewayats() {\n    if (!this.profileForm.value.rewayats) {\n      this.rewayatsMessage = {\n        message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_REWAYAT'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n      return;\n    }\n    this.rewayatsMessage = {};\n    var x = this.profileForm.value.rewayats;\n    const exist = this.selectedRewayatsList.some(el => el.id === this.profileForm.value.rewayats);\n    if (!exist) {\n      if (this.collectionOfLookup.REWAYAT) {\n        this.selectedRewayatsList.push(this.collectionOfLookup.REWAYAT.filter(el => el.id == this.profileForm.value.rewayats)[0]);\n      }\n    }\n  }\n  removeItemFromSelectedTeacherRewayats(item) {\n    let index = this.selectedRewayatsList.indexOf(item);\n    this.selectedRewayatsList.splice(index, 1);\n  }\n  addTeacherLanguages() {\n    if (!this.profileForm.value.languages) {\n      this.languagesMessage = {\n        message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_LANGUAGE'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n      return;\n    }\n    this.languagesMessage = {};\n    const exist = this.selectedLanguagesList.some(el => el.id === this.profileForm.value.languages);\n    if (!exist) {\n      if (this.collectionOfLookup.LANG) {\n        this.selectedLanguagesList.push(this.collectionOfLookup.LANG.filter(el => el.id == this.profileForm.value.languages)[0]);\n      }\n    }\n  }\n  removeItemFromSelectedTeacherLanguages(item) {\n    let index = this.selectedLanguagesList.indexOf(item);\n    this.selectedLanguagesList.splice(index, 1);\n  }\n  addAvailabilityDays() {\n    if (!this.profileForm.value.availabilityDays && !this.profileForm.value.timeinterview) {\n      this.availabilityDaysMessage = {\n        message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_AVAILABILITY'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n      return;\n    }\n    this.availabilityDaysMessage = {};\n    const existAvailabilityDays = this.selectedAvailabilityDaysList.some(el => el.id === this.profileForm.value.availabilityDays);\n    const existFromTime = this.profileForm.value.fromDayTimeinterview;\n    const existtoTime = this.profileForm.value.toDayTimeinterview;\n    if (existFromTime != null && existtoTime != null && existtoTime < existFromTime) {\n      this.availabilityDaysMessage = {\n        message: this.translate.instant('TEACHER_APPOINTMENT.VALIDATION_TIME'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    } else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => el.fromTime === existFromTime && el.toTime === existtoTime)) {\n      this.availabilityDaysMessage = {\n        message: this.translate.instant('TEACHER_APPOINTMENT.TIME_EXIST_BEFORE'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    }\n    //overlap = a.start < b.end && b.start < a.end;\n    else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => {\n      if (el.toTime && el.fromTime) {\n        return existFromTime < el.toTime && el.fromTime < existtoTime;\n      }\n      return false;\n    })) {\n      this.availabilityDaysMessage = {\n        message: this.translate.instant('TEACHER_APPOINTMENT.OVERLAP_TIME'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    } else {\n      if (this.profileForm.value.availabilityDays == \"all\" && this.collectionOfLookup.DAYS) {\n        const verifyAllTimes = this.collectionOfLookup.DAYS.some(obj => {\n          let eAvailabilityDay = this.selectedAvailabilityDaysList.some(el => el.id === obj.id);\n          return eAvailabilityDay && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => el.fromTime === existFromTime && el.toTime === existtoTime) || existFromTime != null && existtoTime != null && existtoTime < existFromTime || eAvailabilityDay && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => {\n            if (el.toTime && el.fromTime) {\n              return existFromTime < el.toTime && el.fromTime < existtoTime;\n            }\n            return false;\n          });\n        });\n        if (verifyAllTimes) {\n          this.availabilityDaysMessage = {\n            message: this.translate.instant('TEACHER_APPOINTMENT.INVALID_TIME'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        } else {\n          if (this.collectionOfLookup.DAYS && this.profileForm.value.availabilityDays == \"all\") {\n            this.collectionOfLookup.DAYS.forEach(obj => {\n              this.availabilityDaysModel = {\n                id: obj.id,\n                nameAr: obj.nameAr,\n                nameEn: obj.nameEn,\n                fromTime: this.profileForm.value.fromDayTimeinterview,\n                toTime: this.profileForm.value.toDayTimeinterview\n              };\n              this.selectedAvailabilityDaysList.push(this.availabilityDaysModel);\n            });\n          }\n        }\n      } else {\n        if (this.collectionOfLookup.DAYS) {\n          this.availabilityDaysModel = {\n            id: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].id,\n            nameAr: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].nameAr,\n            nameEn: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].nameEn,\n            fromTime: this.profileForm.value.fromDayTimeinterview,\n            toTime: this.profileForm.value.toDayTimeinterview\n          };\n          this.selectedAvailabilityDaysList.push(this.availabilityDaysModel);\n        }\n      }\n    }\n  }\n  removeItemFromSelectedAvailabilityDays(item) {\n    let index = this.selectedAvailabilityDaysList.indexOf(item);\n    this.selectedAvailabilityDaysList.splice(index, 1);\n  }\n  addTeacherPrograms() {\n    if (!this.profileForm.value.teacherPrograms && !this.profileForm.value.teacherProgramDegrees) {\n      this.teacherProgramsMessage = {\n        message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_PROGRAM'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n      return;\n    }\n    this.teacherProgramsMessage = {};\n    const existDegree = this.selectedTeacherProgramsList.some(el => el.degreeId === this.profileForm.value.teacherProgramDegrees);\n    const existProgram = this.selectedTeacherProgramsList.some(el => el.programId === this.profileForm.value.teacherPrograms);\n    if (!existDegree && !existProgram) {\n      if (this.collectionOfLookup.DEGREE && this.ProgramsList) {\n        this.teacherProgramModel = {\n          programId: this.ProgramsList.filter(el => el.id == this.profileForm.value.teacherPrograms)[0].id,\n          degreeId: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].id,\n          progName: this.ProgramsList.filter(el => el.id == this.profileForm.value.teacherPrograms)[0].progName,\n          degreeNameEn: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].nameEn,\n          degreeNameAr: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].nameAr\n        };\n        this.selectedTeacherProgramsList.push(this.teacherProgramModel);\n      }\n    }\n  }\n  removeItemFromSelectedTeacherPrograms(item) {\n    let index = this.selectedTeacherProgramsList.indexOf(item);\n    this.selectedTeacherProgramsList.splice(index, 1);\n  }\n  applyPhoneNumber(phoneNumber) {\n    this.f.mobile.setValue(phoneNumber);\n  }\n  getCountryIsoCode() {\n    this.userService.getCountryIsoCode().subscribe(res => {\n      let code = res.data;\n      this.telInputParam = {\n        // phoneNumber:'+201062100486',\n        isRequired: true,\n        countryIsoCode: '{\"initialCountry\": \"' + code.toLowerCase() + '\"}'\n      };\n      // this.telInputParam.countryIsoCode = '{\"initialCountry\": \"' + code.toLowerCase() +'\"}';\n    });\n  }\n  SendData(data) {\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.hijriBinding = data.selectedDateValue;\n    this.selectedDateType = data.selectedDateType;\n    this.f.hijriBirthDate.setValue(data.selectedDateValue);\n  }\n  HijriInterviewDay(data) {\n    // date = date.year + '/' + date.month + '/' + date.day;\n    // this.hijriBinding = date\n    // this.f.hijriInterviewDay.setValue(date);\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.hijriBinding = data.selectedDateValue;\n    this.selectedInterviewDateType = data.selectedDateType;\n    this.f.hijriInterviewDay.setValue(data.selectedDateValue);\n  }\n  addDrgree() {\n    if (!this.profileForm.value.proficiencyDegree) {\n      this.degreeMessage = {\n        message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_DEGREE'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    } else {\n      this.degreeMessage = {};\n      const exist = this.selecteddrgreeList.some(el => el.id === this.profileForm.value.proficiencyDegree);\n      if (!exist) {\n        if (this.collectionOfLookup.DEGREE) {\n          this.selecteddrgreeList.push(this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.proficiencyDegree)[0]);\n        }\n      }\n    }\n  }\n  removeItemFromselecteddrgreeList(item) {\n    let index = this.selecteddrgreeList.indexOf(item);\n    this.selecteddrgreeList.splice(index, 1);\n  }\n  DeleteAttachment(index, id) {\n    this.fileList?.splice(index, 1);\n    this.ejazaAttachmentIds = this.ejazaAttachmentIds.filter(a => a !== id);\n  }\n  listExt = [\"jpg\", \"png\", \"jpeg\", \"gif\", \"bmp\", \"tif\", \"tiff\", \"docx\", \"doc\"];\n  onEjazaFileChange(files) {\n    if (files.length > 0) {\n      if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.EXTENTION_FILE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      Array.from(files).forEach(element => {\n        var fileUploadObj = {\n          containerNameIndex: 4,\n          // need to be changed based on file type\n          file: element\n        };\n        this.fileUploadModel?.push(fileUploadObj);\n      });\n      this.UploadFiles(this.fileUploadModel);\n    }\n  }\n  UploadFiles(files) {\n    if (files.length === 0) {\n      return;\n    }\n    this.attachmentService.upload(files).subscribe(res => {\n      Array.from(res.data).forEach(elm => {\n        this.ejazaAttachmentIds.push(elm.id);\n        this.fileList?.push(elm);\n      });\n      this.fileUploadModel = [];\n    }, error => {\n      this.fileUploadModel = [];\n      //logging\n    });\n  }\n  onDragOver(event) {\n    event.preventDefault();\n  }\n  // From drag and drop\n  onDropSuccess(event) {\n    event.preventDefault();\n    this.onFileChange(event.dataTransfer.files);\n  }\n  // From attachment link\n  onChange(event) {\n    this.onFileChange(event.target.files);\n  }\n  setHijri() {\n    let toDayHijriDate = this.dateFormatterService.GetTodayHijri();\n    //  toDayHijriDate.day= toDayHijriDate.day;\n    toDayHijriDate.day = toDayHijriDate.day - 1;\n    this.maxHijriDate = toDayHijriDate;\n    this.minHijriDate = this.dateFormatterService.GetTodayHijri();\n    this.minHijriDate.year = this.minHijriDate.year - 150;\n    let toDayHijriInterviewDate = this.dateFormatterService.GetTodayHijri();\n    //  toDayHijriInterviewDate.day= toDayHijriInterviewDate.day + 1 ;\n    //  toDayHijriInterviewDate.month= toDayHijriInterviewDate.month + 1 ;\n    //  toDayHijriInterviewDate.year= toDayHijriInterviewDate.year;\n    this.minHijriInterviewDate = toDayHijriInterviewDate;\n  }\n  setGreg() {\n    let toDayGreDate = this.dateFormatterService.GetTodayGregorian();\n    // toDayGreDate.day= toDayGreDate.day;\n    toDayGreDate.day = toDayGreDate.day - 1;\n    this.maxGregDate = toDayGreDate;\n    this.minGregDate = this.dateFormatterService.GetTodayGregorian();\n    this.minGregDate.year = this.maxGregDate.year - 150;\n    let toDayGreInterviewDate = this.dateFormatterService.GetTodayGregorian();\n    // toDayGreInterviewDate.day= toDayGreInterviewDate.day + 1 ;\n    // toDayGreInterviewDate.month= toDayGreInterviewDate.month + 1 ;\n    // toDayGreInterviewDate.year= toDayGreInterviewDate.year  ;\n    this.minGregInterviewDate = toDayGreInterviewDate;\n  }\n};\nUpdateTeacherProfileComponent = __decorate([Component({\n  selector: 'app-update-teacher-profile',\n  templateUrl: './update-teacher-profile.component.html',\n  styleUrls: ['./update-teacher-profile.component.scss']\n})], UpdateTeacherProfileComponent);\nexport { UpdateTeacherProfileComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}