{"ast": null, "code": "import { StudentSubscriptionEmun } from 'src/app/core/enums/programs/stu-subscription-enum/student-subscription-emun.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction StuRequestDetailsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-stu-join-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StuRequestDetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-stu-vacations-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StuRequestDetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-student-admin-drop-out-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StuRequestDetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-stu-moving-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let StuRequestDetailsComponent = /*#__PURE__*/(() => {\n  class StuRequestDetailsComponent {\n    selectedStuRequest;\n    studentSubscriptionEmun = StudentSubscriptionEmun;\n    constructor() {}\n    ngOnInit() {}\n    static ɵfac = function StuRequestDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StuRequestDetailsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StuRequestDetailsComponent,\n      selectors: [[\"app-stu-request-details\"]],\n      inputs: {\n        selectedStuRequest: \"selectedStuRequest\"\n      },\n      decls: 4,\n      vars: 4,\n      consts: [[4, \"ngIf\"]],\n      template: function StuRequestDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StuRequestDetailsComponent_div_0_Template, 2, 0, \"div\", 0)(1, StuRequestDetailsComponent_div_1_Template, 2, 0, \"div\", 0)(2, StuRequestDetailsComponent_div_2_Template, 2, 0, \"div\", 0)(3, StuRequestDetailsComponent_div_3_Template, 2, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.studentSubscriptionEmun.joinRequest === ctx.selectedStuRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.studentSubscriptionEmun.vacationRequest === ctx.selectedStuRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.studentSubscriptionEmun.quitRequest === ctx.selectedStuRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.studentSubscriptionEmun.movingRequest === ctx.selectedStuRequest);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return StuRequestDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}