{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { DoughnutChartDegreeCountComponent } from 'src/app/shared/components/admin-dash-board-widgets/doughnut-chart-degree-count/doughnut-chart-degree-count.component';\nlet AdminDashBoardStudTotalTaskDegreeWidgetsComponent = class AdminDashBoardStudTotalTaskDegreeWidgetsComponent {\n  programBatchesService;\n  adminDashBoardService;\n  translate;\n  alertfy;\n  dateFormatterService;\n  studTotalTaskDegreeEchart;\n  allProgBatchs = [];\n  batId;\n  adminDashBoardStudTotalTaskDegreeReq;\n  adminDashBoardTotalTaskDegree = {\n    percentage: 0\n  };\n  maxHijriDate;\n  maxGregDate;\n  fromDateInputParam; //= {year: 0, day: 0, month: 0};\n  toDateInputParam; //= {year: 0, day: 0, month: 0};\n  hijri = false;\n  milady = false;\n  hijriBinding;\n  selectedDateType;\n  fromDate;\n  toDate;\n  langEnum = LanguageEnum;\n  constructor(programBatchesService, adminDashBoardService, translate, alertfy, dateFormatterService) {\n    this.programBatchesService = programBatchesService;\n    this.adminDashBoardService = adminDashBoardService;\n    this.translate = translate;\n    this.alertfy = alertfy;\n    this.dateFormatterService = dateFormatterService;\n  }\n  ngOnInit() {\n    this.fromDate = new Date();\n    this.toDate = new Date();\n    this.fromDateInputParam = {\n      year: this.fromDate?.getFullYear(),\n      month: this.fromDate?.getMonth() + 1,\n      day: this.fromDate?.getDate()\n    };\n    this.toDateInputParam = {\n      year: this.fromDate?.getFullYear(),\n      month: this.fromDate?.getMonth() + 1,\n      day: this.fromDate?.getDate()\n    };\n    this.getAllProgs();\n    this.getAdminDashBoardStudTotalTaskDegree();\n  }\n  getAllProgs() {\n    this.programBatchesService.getAllProgramBatches().subscribe(res => {\n      if (res.isSuccess) {\n        this.allProgBatchs = res.data;\n      } else {\n        this.alertfy.error(res.message || \"\");\n      }\n    }, error => {\n      //logging\n    });\n  }\n  onChangeBatch() {\n    this.adminDashBoardTotalTaskDegree = {\n      percentage: 0\n    };\n    this.getAdminDashBoardStudTotalTaskDegree();\n  }\n  getAdminDashBoardStudTotalTaskDegree() {\n    this.adminDashBoardStudTotalTaskDegreeReq = {\n      batId: this.batId,\n      fromDate: this.fromDate,\n      toDate: this.toDate\n    };\n    if (this.fromDate && this.toDate) {\n      if (new Date(this.toDate) < new Date(this.fromDate)) {\n        this.translate.instant('ADMIN_DASH_BORD.DATE_FROM_IS_BIGGER_THAN_DATE_TO');\n        this.alertfy.error(this.translate.instant('ADMIN_DASH_BORD.DATE_FROM_IS_BIGGER_THAN_DATE_TO') || \"\");\n        this.adminDashBoardTotalTaskDegree = {\n          percentage: 0\n        };\n        this.studTotalTaskDegreeEchart?.initiate(this.adminDashBoardTotalTaskDegree);\n        return;\n      }\n    }\n    this.adminDashBoardService.getAdminDashBoardStudTotalTaskDegree(this.adminDashBoardStudTotalTaskDegreeReq || {}).subscribe(res => {\n      if (res.isSuccess) {\n        this.adminDashBoardTotalTaskDegree = res.data;\n        this.studTotalTaskDegreeEchart?.initiate(this.adminDashBoardTotalTaskDegree);\n      } else {\n        this.alertfy.error(res.message || \"\");\n      }\n    }, error => {\n      //logging\n    });\n  }\n  setHijri() {\n    let toDayHijriDate = this.dateFormatterService.GetTodayHijri();\n    toDayHijriDate.day = toDayHijriDate.day;\n    this.maxHijriDate = toDayHijriDate;\n  }\n  setGreg() {\n    let toDayGreDate = this.dateFormatterService.GetTodayGregorian();\n    toDayGreDate.day = toDayGreDate.day;\n    this.maxGregDate = toDayGreDate;\n  }\n  changFromDate(data) {\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.fromDate = data.selectedDateValue;\n    this.getAdminDashBoardStudTotalTaskDegree();\n  }\n  chanToDate(data) {\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.toDate = data.selectedDateValue;\n    this.getAdminDashBoardStudTotalTaskDegree();\n  }\n};\n__decorate([ViewChild(DoughnutChartDegreeCountComponent)], AdminDashBoardStudTotalTaskDegreeWidgetsComponent.prototype, \"studTotalTaskDegreeEchart\", void 0);\nAdminDashBoardStudTotalTaskDegreeWidgetsComponent = __decorate([Component({\n  selector: 'app-admin-dash-board-stud-total-task-degree-widgets',\n  templateUrl: './admin-dash-board-stud-total-task-degree-widgets.component.html',\n  styleUrls: ['./admin-dash-board-stud-total-task-degree-widgets.component.scss']\n})], AdminDashBoardStudTotalTaskDegreeWidgetsComponent);\nexport { AdminDashBoardStudTotalTaskDegreeWidgetsComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}