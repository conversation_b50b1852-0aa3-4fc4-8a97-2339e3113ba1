{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/language-services/language.service\";\nimport * as i3 from \"@angular/common\";\nfunction ExamFormViewComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"app-add-exam\", 8);\n    i0.ɵɵlistener(\"closeExamForm\", function ExamFormViewComponent_div_6_Template_app_add_exam_closeExamForm_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAddExamForm());\n    })(\"addExamForm\", function ExamFormViewComponent_div_6_Template_app_add_exam_addExamForm_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadListAfterAddExam($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputExamId\", ctx_r1.inputExamId);\n  }\n}\nexport let ExamFormViewComponent = /*#__PURE__*/(() => {\n  class ExamFormViewComponent {\n    translate;\n    languageService;\n    selectedExamFormId = {\n      id: '',\n      arabExamName: '',\n      engExamName: ''\n    };\n    examId;\n    inputExamId;\n    closeExamForm;\n    showAddExamForm = false;\n    addExamForm = false;\n    constructor(translate, languageService) {\n      this.translate = translate;\n      this.languageService = languageService;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('EXAM_FORM.TITLE'));\n    }\n    setSelectedExam(event) {\n      this.selectedExamFormId = {\n        id: event.id,\n        arabExamName: event.arabExamName,\n        engExamName: event.engExamName\n      };\n      this.examId = event.id;\n      this.addExamForm = false;\n    }\n    setInputExamId(event) {\n      this.inputExamId = event;\n      this.showAddExamForm = true;\n    }\n    loadListAfterAddExam(event) {\n      this.addExamForm = event;\n    }\n    closeAddExamForm() {\n      this.showAddExamForm = false;\n    }\n    static ɵfac = function ExamFormViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ExamFormViewComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.LanguageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExamFormViewComponent,\n      selectors: [[\"app-exam-form-view\"]],\n      inputs: {\n        closeExamForm: \"closeExamForm\"\n      },\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-12\", \"col-xs-12\", \"pl-0\"], [3, \"selectedExamFormId\", \"inputExamId\", \"addExamForm\"], [1, \"col-xl-9\", \"col-lg-9\", \"col-md-9\", \"col-sm-12\", \"col-xs-12\", \"pl-0\"], [3, \"selectedExamFormId\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeExamForm\", \"addExamForm\", \"inputExamId\"]],\n      template: function ExamFormViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-exam-view\", 3);\n          i0.ɵɵlistener(\"selectedExamFormId\", function ExamFormViewComponent_Template_app_exam_view_selectedExamFormId_3_listener($event) {\n            return ctx.setSelectedExam($event);\n          })(\"inputExamId\", function ExamFormViewComponent_Template_app_exam_view_inputExamId_3_listener($event) {\n            return ctx.setInputExamId($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-attache-exam-template\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ExamFormViewComponent_div_6_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"addExamForm\", ctx.addExamForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"selectedExamFormId\", ctx.selectedExamFormId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddExamForm);\n        }\n      },\n      dependencies: [i3.NgIf],\n      encapsulation: 2\n    });\n  }\n  return ExamFormViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}