{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/core/services/content-management-services/content-management.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"red-border-class\": a0\n});\nfunction ContentManagementSystemComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.REQUIRED\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.SHORT_DESCRIPTION_AR_MAX_LENGHT\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ContentManagementSystemComponent_div_10_div_1_Template, 3, 3, \"div\", 16)(2, ContentManagementSystemComponent_div_10_div_2_Template, 3, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.shortDescriptionAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.shortDescriptionAr.errors == null ? null : ctx_r0.f.shortDescriptionAr.errors.maxlength);\n  }\n}\nfunction ContentManagementSystemComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.REQUIRED\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.SHORT_DESCRIPTION_EN_MAX_LENGHT\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ContentManagementSystemComponent_div_16_div_1_Template, 3, 3, \"div\", 16)(2, ContentManagementSystemComponent_div_16_div_2_Template, 3, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.shortDescriptionEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.shortDescriptionEn.errors == null ? null : ctx_r0.f.shortDescriptionEn.errors.maxlength);\n  }\n}\nfunction ContentManagementSystemComponent_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.REQUIRED\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.LONG_DESCRIPTION_AR_MAX_LENGHT\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ContentManagementSystemComponent_div_22_div_1_Template, 3, 3, \"div\", 16)(2, ContentManagementSystemComponent_div_22_div_2_Template, 3, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.longDescriptionAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.longDescriptionAr.errors == null ? null : ctx_r0.f.longDescriptionAr.errors.maxlength);\n  }\n}\nfunction ContentManagementSystemComponent_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.REQUIRED\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_28_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONTENT_MANEGMENT_SYSTEM.LONG_DESCRIPTION_EN_MAX_LENGHT\"), \" \");\n  }\n}\nfunction ContentManagementSystemComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ContentManagementSystemComponent_div_28_div_1_Template, 3, 3, \"div\", 16)(2, ContentManagementSystemComponent_div_28_div_2_Template, 3, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.longDescriptionEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.longDescriptionEn.errors == null ? null : ctx_r0.f.longDescriptionEn.errors.maxlength);\n  }\n}\nfunction ContentManagementSystemComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \" \");\n  }\n}\nexport let ContentManagementSystemComponent = /*#__PURE__*/(() => {\n  class ContentManagementSystemComponent {\n    route;\n    fb;\n    contentmanagementService;\n    translate;\n    contentmanagementsystem = {};\n    createCMS = false;\n    updateCMS = false;\n    errorMessage;\n    contentManagementId;\n    routeParams;\n    isSubmit = false;\n    selectedcmsTypeId = {\n      id: '',\n      nameAr: '',\n      nameEn: ''\n    };\n    currentForm = new FormGroup({});\n    resultMessage = {};\n    disableSaveButtons = false;\n    cmsId = '';\n    isAdd = true;\n    typeId = '';\n    contentmanagementsystemUpdate = {};\n    contentmanagementCreat = {};\n    langEnum = LanguageEnum;\n    constructor(route, fb, contentmanagementService, translate) {\n      this.route = route;\n      this.fb = fb;\n      this.contentmanagementService = contentmanagementService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.currentForm.reset();\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n      if (this.selectedcmsTypeId.id !== undefined) {\n        this.isAdd = false;\n        this.loadContentManagementSystemByType();\n      } else {\n        this.isAdd = true;\n        this.currentForm.reset();\n      }\n      this.buildForm();\n    }\n    ngOnChanges(changes) {\n      this.currentForm.reset();\n      //this.cmsType.typeId=this.selectedcmsTypeId||\"\";\n      if (this.selectedcmsTypeId.id !== \"\") {\n        this.isAdd = false;\n        this.loadContentManagementSystemByType();\n      } else {\n        this.isAdd = true;\n        this.currentForm.reset();\n      }\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n    }\n    // @HostListener('window:beforeunload', ['$event'])\n    // public onPageUnload($event: BeforeUnloadEvent) {\n    //   if (this.unsavedDataCheck()) {\n    //     $event.returnValue = true;\n    //     // return \"message\";\n    //   }\n    //   else{\n    //     $event.returnValue = false;\n    //     // return '';\n    //   }\n    // }\n    // @HostListener('window:popstate', ['$event'])\n    // onPopState(event:any) {\n    //   this.contentmanagementService.setCanDeActivate(this.unsavedDataCheck());\n    // }\n    unsavedDataCheck() {\n      return this.contentmanagementsystem.shortDesAr != this.f.shortDescriptionAr.value || this.contentmanagementsystem.shortDesEn != this.f.shortDescriptionEn.value || this.contentmanagementsystem.longDesAr != this.f.longDescriptionAr.value || this.contentmanagementsystem.longDesEn != this.f.longDescriptionEn.value;\n    }\n    get f() {\n      {\n        return this.currentForm?.controls;\n      }\n    }\n    buildForm() {\n      // const arabicWordPattern = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9]+$\";\n      // const englishWordPattern =\"^[a-zA-Z0-9' '-'\\s]{1,40}$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+\\\\-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}A-Za-z 0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[ A-Za-z0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      this.currentForm = this.fb.group({\n        shortDescriptionAr: ['', [Validators.required, Validators.maxLength(200)]],\n        shortDescriptionEn: ['', [Validators.required, Validators.maxLength(200)]],\n        longDescriptionAr: ['', [Validators.required, Validators.maxLength(1000)]],\n        longDescriptionEn: ['', [Validators.required, Validators.maxLength(1000)]]\n        //typeId: ['', Validators.required]\n      });\n    }\n    loadContentManagementSystemByType() {\n      if (this.selectedcmsTypeId.id !== undefined && this.selectedcmsTypeId.id !== \"\") {\n        this.cmsId = \"\";\n        this.contentmanagementsystem = {};\n        this.contentmanagementService.getContentManagementSystemByTypeCms(this.selectedcmsTypeId.id).subscribe(res => {\n          if (res.isSuccess && res.data) {\n            this.contentmanagementsystem = res.data;\n            this.cmsId = this.contentmanagementsystem.id;\n            if (this.cmsId != '') {\n              this.isAdd = false;\n            } else {\n              this.isAdd = true;\n            }\n            this.f.shortDescriptionAr.setValue(this.contentmanagementsystem.shortDesAr);\n            this.f.shortDescriptionEn.setValue(this.contentmanagementsystem.shortDesEn);\n            this.f.longDescriptionAr.setValue(this.contentmanagementsystem.longDesAr);\n            this.f.longDescriptionEn.setValue(this.contentmanagementsystem.longDesEn);\n            // this.f.typeId.setValue(this.contentmanagementsystem.typeId);\n            this.disableSaveButtons = false;\n            this.resultMessage = {\n              message: '',\n              type: ''\n            };\n          } else {\n            // this.errorMessage = response.message;\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    Submit() {\n      this.isSubmit = true;\n      this.resultMessage = {\n        message: ''\n      };\n      if (this.currentForm.valid) {\n        this.contentmanagementCreat.shortDesAr = this.f.shortDescriptionAr.value;\n        this.contentmanagementCreat.shortDesEn = this.f.shortDescriptionEn.value;\n        this.contentmanagementCreat.longDesAr = this.f.longDescriptionAr.value;\n        this.contentmanagementCreat.longDesEn = this.f.longDescriptionEn.value;\n        this.contentmanagementCreat.cmsType = this.selectedcmsTypeId.id;\n        this.contentmanagementService.createContentManagementSystem(this.contentmanagementCreat).subscribe(res => {\n          this.isSubmit = false;\n          if (res.isSuccess) {\n            this.disableSaveButtons = true;\n            this.resultMessage = {\n              message: res.message || \"\",\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n            setTimeout(() => {\n              this.loadContentManagementSystemByType();\n            }, 2000);\n          } else {\n            this.disableSaveButtons = false;\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    reset() {\n      this.currentForm.reset();\n    }\n    static ɵfac = function ContentManagementSystemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementSystemComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ContentManagementService), i0.ɵɵdirectiveInject(i4.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementSystemComponent,\n      selectors: [[\"app-content-management-system\"]],\n      inputs: {\n        selectedcmsTypeId: \"selectedcmsTypeId\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 35,\n      vars: 34,\n      consts: [[1, \"content_management\"], [1, \"row\"], [1, \"form-group\", \"internal_scroll_list_group\", 3, \"submit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"comment\"], [\"cols\", \"2\", \"rows\", \"5\", \"id\", \"InputShortDescriptionAr\", \"formControlName\", \"shortDescriptionAr\", 1, \"form-control\", \"text-arabic\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"for\", \"comment\", 1, \"labl_dirc\"], [\"cols\", \"2\", \"rows\", \"5\", \"id\", \"InputShortDescriptionEn\", \"formControlName\", \"shortDescriptionEn\", 1, \"form-control\", \"text-english\", 3, \"ngClass\"], [\"for\", \"exampleFormControlTextarea1\"], [\"cols\", \"2\", \"rows\", \"10\", \"id\", \"InputLongDescriptionAr\", \"formControlName\", \"longDescriptionAr\", 1, \"form-control\", \"text-arabic\", 3, \"ngClass\"], [\"cols\", \"2\", \"rows\", \"10\", \"id\", \"InputlongDescriptionEn\", \"formControlName\", \"longDescriptionEn\", 1, \"form-control\", \"text-english\", 3, \"ngClass\"], [1, \"row\", \"col-6\"], [\"type\", \"submit\", 1, \"save-btn\", \"btn\", \"btn-danger\"], [3, \"class\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"]],\n      template: function ContentManagementSystemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"form\", 2);\n          i0.ɵɵlistener(\"submit\", function ContentManagementSystemComponent_Template_form_submit_4_listener() {\n            return ctx.Submit();\n          });\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"textarea\", 5);\n          i0.ɵɵtemplate(10, ContentManagementSystemComponent_div_10_Template, 3, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"label\", 7);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"textarea\", 8);\n          i0.ɵɵtemplate(16, ContentManagementSystemComponent_div_16_Template, 3, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 3)(18, \"label\", 9);\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"textarea\", 10);\n          i0.ɵɵtemplate(22, ContentManagementSystemComponent_div_22_Template, 3, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 3)(24, \"label\", 9);\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"textarea\", 11);\n          i0.ɵɵtemplate(28, ContentManagementSystemComponent_div_28_Template, 3, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"section\")(30, \"div\", 12)(31, \"button\", 13);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(34, ContentManagementSystemComponent_div_34_Template, 2, 4, \"div\", 14);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.selectedcmsTypeId.nameEn : ctx.selectedcmsTypeId.nameAr, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.currentForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 16, \"CONTENT_MANEGMENT_SYSTEM.SHORTDESCRIPTIONAR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(26, _c0, ctx.f.shortDescriptionAr.errors && (ctx.f.shortDescriptionAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.shortDescriptionAr.errors && (ctx.f.shortDescriptionAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 18, \"CONTENT_MANEGMENT_SYSTEM.SHORTDESCRIPTIONEN\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c0, ctx.f.shortDescriptionEn.errors && (ctx.f.shortDescriptionEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.shortDescriptionEn.errors && (ctx.f.shortDescriptionEn.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 20, \"CONTENT_MANEGMENT_SYSTEM.LONGDESCRIPTIONAR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c0, ctx.f.longDescriptionAr.errors && (ctx.f.longDescriptionAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.longDescriptionAr.errors && (ctx.f.longDescriptionAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 22, \"CONTENT_MANEGMENT_SYSTEM.LONGDESCRIPTIONEN\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c0, ctx.f.longDescriptionEn.errors && (ctx.f.longDescriptionEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.longDescriptionEn.errors && (ctx.f.longDescriptionEn.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 24, \"CONTENT_MANEGMENT_SYSTEM.SAVE\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i5.NgClass, i5.NgIf, i4.TranslatePipe],\n      styles: [\".content_management[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;margin-top:1rem;margin-bottom:0}.content_management[_ngcontent-%COMP%]:lang(en){margin-right:0rem}.content_management[_ngcontent-%COMP%]:lang(ar){margin-left:1rem}.content_management[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:74vh;overflow-y:auto;padding-left:.5rem}.content_management[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:74vh;overflow-y:auto;padding-right:.5rem}.content_management[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--main_color)}.content_management[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]{font-size:.875rem;color:gray}.content_management[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]:lang(en){float:left}.content_management[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]:lang(ar){float:right!important}.content_management[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;margin-bottom:1rem}.content_management[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:lang(ar){float:right!important;padding-right:.75rem}.content_management[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:lang(en){float:left;padding-left:.5rem}.content_management[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.content_management[_ngcontent-%COMP%]   .alert-danger[_ngcontent-%COMP%]:lang(ar){text-align:right}.content_management[_ngcontent-%COMP%]   .alert-danger[_ngcontent-%COMP%]:lang(en){text-align:left}\"]\n    });\n  }\n  return ContentManagementSystemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}