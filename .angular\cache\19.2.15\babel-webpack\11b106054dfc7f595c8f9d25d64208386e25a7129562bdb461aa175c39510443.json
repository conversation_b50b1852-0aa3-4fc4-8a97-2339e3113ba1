{"ast": null, "code": "var Eventful = function () {\n  function Eventful(eventProcessors) {\n    if (eventProcessors) {\n      this._$eventProcessor = eventProcessors;\n    }\n  }\n  Eventful.prototype.on = function (event, query, handler, context) {\n    if (!this._$handlers) {\n      this._$handlers = {};\n    }\n    var _h = this._$handlers;\n    if (typeof query === 'function') {\n      context = handler;\n      handler = query;\n      query = null;\n    }\n    if (!handler || !event) {\n      return this;\n    }\n    var eventProcessor = this._$eventProcessor;\n    if (query != null && eventProcessor && eventProcessor.normalizeQuery) {\n      query = eventProcessor.normalizeQuery(query);\n    }\n    if (!_h[event]) {\n      _h[event] = [];\n    }\n    for (var i = 0; i < _h[event].length; i++) {\n      if (_h[event][i].h === handler) {\n        return this;\n      }\n    }\n    var wrap = {\n      h: handler,\n      query: query,\n      ctx: context || this,\n      callAtLast: handler.zrEventfulCallAtLast\n    };\n    var lastIndex = _h[event].length - 1;\n    var lastWrap = _h[event][lastIndex];\n    lastWrap && lastWrap.callAtLast ? _h[event].splice(lastIndex, 0, wrap) : _h[event].push(wrap);\n    return this;\n  };\n  Eventful.prototype.isSilent = function (eventName) {\n    var _h = this._$handlers;\n    return !_h || !_h[eventName] || !_h[eventName].length;\n  };\n  Eventful.prototype.off = function (eventType, handler) {\n    var _h = this._$handlers;\n    if (!_h) {\n      return this;\n    }\n    if (!eventType) {\n      this._$handlers = {};\n      return this;\n    }\n    if (handler) {\n      if (_h[eventType]) {\n        var newList = [];\n        for (var i = 0, l = _h[eventType].length; i < l; i++) {\n          if (_h[eventType][i].h !== handler) {\n            newList.push(_h[eventType][i]);\n          }\n        }\n        _h[eventType] = newList;\n      }\n      if (_h[eventType] && _h[eventType].length === 0) {\n        delete _h[eventType];\n      }\n    } else {\n      delete _h[eventType];\n    }\n    return this;\n  };\n  Eventful.prototype.trigger = function (eventType) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[eventType];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(eventType, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(hItem.ctx);\n            break;\n          case 1:\n            hItem.h.call(hItem.ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(hItem.ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(hItem.ctx, args);\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(eventType);\n    return this;\n  };\n  Eventful.prototype.triggerWithContext = function (type) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[type];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var ctx = args[argLen - 1];\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(type, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(ctx);\n            break;\n          case 1:\n            hItem.h.call(ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(ctx, args.slice(1, argLen - 1));\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(type);\n    return this;\n  };\n  return Eventful;\n}();\nexport default Eventful;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}