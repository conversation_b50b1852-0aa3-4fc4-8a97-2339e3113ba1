{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/scientific-problem-services/scientific-problem.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction AddScientifiProblemReplyComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \"\");\n  }\n}\nexport let AddScientifiProblemReplyComponent = /*#__PURE__*/(() => {\n  class AddScientifiProblemReplyComponent {\n    tranlste;\n    scientificProblemService;\n    alertify;\n    closeAddReplyToScProblem = new EventEmitter();\n    scProbObjForAddReplyView = {};\n    resultMessage = {};\n    constructor(tranlste, scientificProblemService, alertify) {\n      this.tranlste = tranlste;\n      this.scientificProblemService = scientificProblemService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {}\n    closeAddReplyEvent() {\n      this.closeAddReplyToScProblem.emit();\n    }\n    saveReplyToScientificProble() {\n      let model = {\n        id: this.scProbObjForAddReplyView?.id,\n        reply: this.scProbObjForAddReplyView?.repText\n      };\n      if (model.reply) {\n        this.scientificProblemService.addScientificProblemReply(model).subscribe(res => {\n          if (res.isSuccess) {\n            this.alertify.success(res.message || '');\n            this.closeAddReplyToScProblem.emit();\n          } else {\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resultMessage = {\n          message: this.tranlste.instant('SCIENTIFIC_PROBLEM.ENTER_REPLAY'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    static ɵfac = function AddScientifiProblemReplyComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddScientifiProblemReplyComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ScientificProblemService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddScientifiProblemReplyComponent,\n      selectors: [[\"app-add-scientifi-problem-reply\"]],\n      inputs: {\n        scProbObjForAddReplyView: \"scProbObjForAddReplyView\"\n      },\n      outputs: {\n        closeAddReplyToScProblem: \"closeAddReplyToScProblem\"\n      },\n      decls: 23,\n      vars: 20,\n      consts: [[1, \"form-group\", \"DataForm\", \"pl-3\", \"pr-3\"], [1, \"text-right\", \"head\", \"bold\"], [1, \"form-group\"], [1, \"label\"], [1, \"data_label\"], [\"cols\", \"1\", \"rows\", \"5\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-3\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-3\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"bold\"]],\n      template: function AddScientifiProblemReplyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"label\", 4);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"label\", 3);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"textarea\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddScientifiProblemReplyComponent_Template_textarea_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.scProbObjForAddReplyView.repText, $event) || (ctx.scProbObjForAddReplyView.repText = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, AddScientifiProblemReplyComponent_div_14_Template, 3, 4, \"div\", 6);\n          i0.ɵɵelementStart(15, \"section\", 7)(16, \"div\", 8)(17, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AddScientifiProblemReplyComponent_Template_button_click_17_listener() {\n            return ctx.saveReplyToScientificProble();\n          });\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AddScientifiProblemReplyComponent_Template_button_click_20_listener() {\n            return ctx.closeAddReplyEvent();\n          });\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 9, \"SCIENTIFIC_PROBLEM.RESPONSE_SCIENTIFIC_PROBLEM\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 11, \"SCIENTIFIC_PROBLEM.USER_QUESTION\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.scProbObjForAddReplyView == null ? null : ctx.scProbObjForAddReplyView.questText, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 13, \"SCIENTIFIC_PROBLEM.REPLY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.scProbObjForAddReplyView.repText);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(19, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 15, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 17, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.NgModel, i5.NgForm, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;text-align:right;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;text-align:right;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{margin:1rem}\"]\n    });\n  }\n  return AddScientifiProblemReplyComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}