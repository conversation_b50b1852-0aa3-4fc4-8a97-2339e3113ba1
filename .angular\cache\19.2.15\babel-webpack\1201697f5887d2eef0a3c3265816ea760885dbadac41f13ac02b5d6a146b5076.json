{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { FlatTreeControl } from '@angular/cdk/tree';\nimport { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/tree\";\nimport * as i5 from \"@angular/material/icon\";\nconst _c0 = a0 => ({\n  \"d-none\": a0\n});\nfunction RoleComponent_mat_card_content_1_section_1_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 10);\n    i0.ɵɵlistener(\"click\", function RoleComponent_mat_card_content_1_section_1_i_1_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const role_r3 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(role_r3.show = !role_r3.show);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleComponent_mat_card_content_1_section_1_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 11);\n    i0.ɵɵlistener(\"click\", function RoleComponent_mat_card_content_1_section_1_i_2_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const role_r3 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(role_r3.show = !role_r3.show);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RoleComponent_mat_card_content_1_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 4);\n    i0.ɵɵtemplate(1, RoleComponent_mat_card_content_1_section_1_i_1_Template, 1, 0, \"i\", 5)(2, RoleComponent_mat_card_content_1_section_1_i_2_Template, 1, 0, \"i\", 6);\n    i0.ɵɵelementStart(3, \"mat-checkbox\", 7);\n    i0.ɵɵlistener(\"change\", function RoleComponent_mat_card_content_1_section_1_Template_mat_checkbox_change_3_listener($event) {\n      const role_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.changeValue($event, role_r3));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleComponent_mat_card_content_1_section_1_Template_mat_checkbox_ngModelChange_3_listener($event) {\n      const role_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(role_r3.checked, $event) || (role_r3.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelement(4, \"i\", 8);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"app-role\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r3 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", role_r3.children.length > 0 && role_r3.show == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", role_r3.children.length > 0 && role_r3.show == true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"indeterminate\", ctx_r4.someChecked(role_r3));\n    i0.ɵɵtwoWayProperty(\"ngModel\", role_r3.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.translate.currentLang === ctx_r4.langEnum.en ? role_r3.nodeNameEn : role_r3.nodeNameAr, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, !role_r3.show))(\"selectedRoles\", ctx_r4.selectedRoles)(\"listRoles\", role_r3);\n  }\n}\nfunction RoleComponent_mat_card_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card-content\");\n    i0.ɵɵtemplate(1, RoleComponent_mat_card_content_1_section_1_Template, 7, 10, \"section\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listRoles.children);\n  }\n}\nfunction RoleComponent_mat_tree_2_mat_tree_node_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tree-node\", 15);\n    i0.ɵɵelement(1, \"button\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", node_r6.name, \" \");\n  }\n}\nfunction RoleComponent_mat_tree_2_mat_tree_node_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tree-node\", 15)(1, \"button\", 17)(2, \"mat-icon\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Toggle \" + node_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.treeControl.isExpanded(node_r7) ? \"expand_more\" : \"chevron_right\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r7.name, \" \");\n  }\n}\nfunction RoleComponent_mat_tree_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tree\", 12);\n    i0.ɵɵtemplate(1, RoleComponent_mat_tree_2_mat_tree_node_1_Template, 3, 1, \"mat-tree-node\", 13)(2, RoleComponent_mat_tree_2_mat_tree_node_2_Template, 5, 3, \"mat-tree-node\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dataSource\", ctx_r4.dataSource)(\"treeControl\", ctx_r4.treeControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matTreeNodeDefWhen\", ctx_r4.hasChild);\n  }\n}\nexport let RoleComponent = /*#__PURE__*/(() => {\n  class RoleComponent {\n    translate;\n    show = false;\n    listRoles;\n    selectedRoles;\n    allComplete = false;\n    langEnum = LanguageEnum;\n    _transformer = (node, level) => {\n      return {\n        expandable: !!node.children && node.children.length > 0,\n        name: node.nodeNameAr,\n        level: level\n      };\n    };\n    treeControl = new FlatTreeControl(node => node.level, node => node.expandable);\n    treeFlattener = new MatTreeFlattener(this._transformer, node => node.level, node => node.expandable, node => node.children);\n    dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);\n    hasChild = (_, node) => node.expandable;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    ngOnChanges(changes) {\n      if (this.listRoles?.children.length > 0) {\n        this.selected(this.listRoles.children);\n      }\n    }\n    ngOnInit() {\n      this.dataSource.data = this.listRoles.children;\n      // this.selected(this.listRoles.children);\n    }\n    changeValue(event, role) {\n      let value = event.checked;\n      this.allComplete = value;\n      this.CheckAllArray(role.children, value);\n      if (value) {\n        const isAllChecked = currentValue => currentValue.checked == true;\n        this.listRoles.checked = this.listRoles.children.every(isAllChecked);\n      }\n    }\n    restTree(arr) {\n      if (arr && arr.length > 0) {\n        for (var i = 0; i < arr.length; i++) {\n          if (arr[i].children instanceof Array) {\n            arr[i].checked = false;\n            arr[i].show = false;\n            this.selected(arr[i].children);\n          } else {}\n        }\n      }\n    }\n    selected(arr) {\n      this.restTree(arr);\n      if (this.selectedRoles && this.selectedRoles.length > 0) {\n        this.selectedRoles.forEach(element => {\n          for (var i = 0; i < arr.length; i++) {\n            if (arr[i].children instanceof Array) {\n              // arr[i].checked=false;\n              if (element.permId == arr[i].id) {\n                arr[i].checked = true;\n                this.allComplete = false;\n              }\n              //this.selected(arr[i].children);\n            } else {}\n          }\n        });\n      } else {\n        this.restTree(arr);\n      }\n    }\n    CheckAllArray(arr, value) {\n      for (var i = 0; i < arr.length; i++) {\n        if (arr[i].children instanceof Array) {\n          arr[i].checked = value;\n          this.CheckAllArray(arr[i].children, value);\n        } else {}\n      }\n    }\n    getChecked(arr) {\n      let checkedIndeterminate = false;\n      for (var i = 0; i < arr.length; i++) {\n        if (arr[i].children instanceof Array) {\n          // arr[i].checked=false;\n          if (arr[i].checked == true) {\n            checkedIndeterminate = true;\n            arr[i].checkedIndeterminate = true;\n          }\n          this.getChecked(arr[i].children);\n        } else {\n          checkedIndeterminate = false;\n        }\n      }\n      return checkedIndeterminate;\n    }\n    someChecked(role) {\n      if (role.children == null) {\n        return false;\n      }\n      let isAllChildren;\n      const isAllChecked = currentValue => currentValue.checked == true;\n      isAllChildren = role.children.every(isAllChecked);\n      return this.getChecked(role.children) && !isAllChildren;\n    }\n    static ɵfac = function RoleComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoleComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleComponent,\n      selectors: [[\"app-role\"]],\n      inputs: {\n        listRoles: \"listRoles\",\n        selectedRoles: \"selectedRoles\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"result\", \"w-100\", \"py-0\"], [4, \"ngIf\"], [3, \"dataSource\", \"treeControl\", 4, \"ngIf\"], [\"class\", \"role-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"role-section\"], [\"class\", \"fas fa-sort-down children-down\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fas fa-caret-left children-left\", 3, \"click\", 4, \"ngIf\"], [3, \"change\", \"ngModelChange\", \"indeterminate\", \"ngModel\"], [1, \"far\", \"fa-folder-open\"], [3, \"ngClass\", \"selectedRoles\", \"listRoles\"], [1, \"fas\", \"fa-sort-down\", \"children-down\", 3, \"click\"], [1, \"fas\", \"fa-caret-left\", \"children-left\", 3, \"click\"], [3, \"dataSource\", \"treeControl\"], [\"matTreeNodePadding\", \"\", 4, \"matTreeNodeDef\"], [\"matTreeNodePadding\", \"\", 4, \"matTreeNodeDef\", \"matTreeNodeDefWhen\"], [\"matTreeNodePadding\", \"\"], [\"mat-icon-button\", \"\", \"disabled\", \"\"], [\"mat-icon-button\", \"\", \"matTreeNodeToggle\", \"\"], [1, \"mat-icon-rtl-mirror\"]],\n      template: function RoleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\", 0);\n          i0.ɵɵtemplate(1, RoleComponent_mat_card_content_1_Template, 2, 1, \"mat-card-content\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, RoleComponent_mat_tree_2_Template, 3, 3, \"mat-tree\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.listRoles == null ? null : ctx.listRoles.children.length) > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", false);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.NgControlStatus, i3.NgModel, i4.MatTreeNodeDef, i4.MatTreeNodePadding, i4.MatTreeNodeToggle, i4.MatTree, i4.MatTreeNode, i5.MatIcon],\n      styles: [\".result[_ngcontent-%COMP%]{width:100%;box-shadow:none;font-weight:700;padding:0 2.188rem}.result[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:1.125rem}.result[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]:lang(ar){text-align:right}.result[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]:lang(en){text-align:left}.result[_ngcontent-%COMP%]   .role-section[_ngcontent-%COMP%]:lang(ar){text-align:right}.result[_ngcontent-%COMP%]   .role-section[_ngcontent-%COMP%]:lang(en){text-align:left}.result[_ngcontent-%COMP%]   .role-section-child[_ngcontent-%COMP%]{padding:0 1.875rem}.result[_ngcontent-%COMP%]   .role-section[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .result[_ngcontent-%COMP%]   .role-section-child[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem;margin:0 .375rem;color:var(--second_color)}.result[_ngcontent-%COMP%]   .children-down[_ngcontent-%COMP%]{cursor:pointer;color:#000!important;position:relative;top:-.313rem;right:-.125rem}.result[_ngcontent-%COMP%]   .children-left[_ngcontent-%COMP%]:lang(ar){cursor:pointer;color:#000!important;position:relative;top:-.125rem;right:-.25rem}.result[_ngcontent-%COMP%]   .children-left[_ngcontent-%COMP%]:lang(en){cursor:pointer;color:#000!important;position:relative;top:-.125rem;transform:rotate(180deg);right:.25rem}\"]\n    });\n  }\n  return RoleComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}