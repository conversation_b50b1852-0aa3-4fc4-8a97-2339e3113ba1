{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-services/program.service\";\nimport * as i2 from \"src/app/core/services/admin-dash-bord-services/admin-dash-board.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"src/app/core/services/environment-config-services/environment-config.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/forms\";\nfunction AdminDashBoardTaskCountWidgetsComponent_ng_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const prog_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", prog_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", prog_r1.progName, \" \");\n  }\n}\nfunction AdminDashBoardTaskCountWidgetsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.taskCountOption);\n  }\n}\nexport let AdminDashBoardTaskCountWidgetsComponent = /*#__PURE__*/(() => {\n  class AdminDashBoardTaskCountWidgetsComponent {\n    programService;\n    adminDashBoardService;\n    translate;\n    alertfy;\n    _environmentConfig;\n    allProgs = [];\n    id;\n    adminDashBoardTaskCountReq;\n    adminDashBoardTaskCountList = [];\n    taskCountOption;\n    tenantThemeColors;\n    constructor(programService, adminDashBoardService, translate, alertfy, _environmentConfig) {\n      this.programService = programService;\n      this.adminDashBoardService = adminDashBoardService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n      this._environmentConfig = _environmentConfig;\n    }\n    ngOnInit() {\n      this.getTenantThemeColors();\n      this.getAllProgs();\n      this.getAdminDashBoardTaskCount();\n    }\n    getTenantThemeColors() {\n      this.tenantThemeColors = this._environmentConfig.getTenantThemeColors();\n    }\n    getAllProgs() {\n      this.programService.getProgramAdmin().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgs = res.data;\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.adminDashBoardTaskCountReq = {\n        progId: this.id\n      };\n      this.getAdminDashBoardTaskCount();\n    }\n    getAdminDashBoardTaskCount() {\n      this.adminDashBoardService.getAdminDashBoardTaskCount(this.adminDashBoardTaskCountReq || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.adminDashBoardTaskCountList = res.data;\n          this.worldPopulationChart();\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    worldPopulationChart() {\n      this.taskCountOption = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          boundaryGap: [0, 0.01]\n        },\n        yAxis: {\n          type: 'category',\n          data: this.translate.currentLang === 'ar-SA' ? this.adminDashBoardTaskCountList.map(({\n            taskTypNameAr\n          }) => taskTypNameAr || \"\") : this.adminDashBoardTaskCountList.map(({\n            taskTypNameEn\n          }) => taskTypNameEn || \"\"),\n          position: 'left'\n        },\n        series: [{\n          name: this.translate.instant('ADMIN_DASH_BORD.COUNT_TASK'),\n          type: 'bar',\n          data: this.adminDashBoardTaskCountList.map(({\n            countTask\n          }) => countTask || \"\"),\n          itemStyle: {\n            color: this.tenantThemeColors?.mainColor\n          },\n          label: {\n            show: true,\n            position: 'right',\n            valueAnimation: true\n          }\n        }]\n      };\n    }\n    static ɵfac = function AdminDashBoardTaskCountWidgetsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardTaskCountWidgetsComponent)(i0.ɵɵdirectiveInject(i1.ProgramService), i0.ɵɵdirectiveInject(i2.AdminDashBoardService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService), i0.ɵɵdirectiveInject(i5.EnvironmentConfigService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashBoardTaskCountWidgetsComponent,\n      selectors: [[\"app-admin-dash-board-task-count-widgets\"]],\n      decls: 13,\n      vars: 12,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\", \"pt-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"head\", \"mx-2\"], [1, \"w-100\", \"select\"], [\"bindValue\", \"id\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12  mb-2 px-1\", 4, \"ngIf\"], [1, \"col-12\", \"mb-2\", \"px-1\"], [1, \"shape\"], [\"echarts\", \"\", 1, \"demo-chart\", 3, \"options\"]],\n      template: function AdminDashBoardTaskCountWidgetsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"ng-select\", 4);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardTaskCountWidgetsComponent_Template_ng_select_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.id, $event) || (ctx.id = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminDashBoardTaskCountWidgetsComponent_Template_ng_select_change_6_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementStart(8, \"ng-option\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, AdminDashBoardTaskCountWidgetsComponent_ng_option_11_Template, 2, 2, \"ng-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, AdminDashBoardTaskCountWidgetsComponent_div_12_Template, 3, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, \"ADMIN_DASH_BORD.PRORAMS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 8, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.id);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 10, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allProgs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.taskCountOption);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.NgSelectComponent, i7.NgOptionComponent, i8.NgControlStatus, i8.NgModel, i3.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}\"]\n    });\n  }\n  return AdminDashBoardTaskCountWidgetsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}