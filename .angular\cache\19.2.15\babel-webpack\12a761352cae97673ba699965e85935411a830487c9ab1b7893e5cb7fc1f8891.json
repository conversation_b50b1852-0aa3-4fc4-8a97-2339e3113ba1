{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SettingRoutingModule } from './setting-routing.module';\nimport { SettingDashboardComponent } from './components/setting-dashboard/setting-dashboard.component';\nimport { ProgramConditionSettingComponent } from './components/program-condition-setting/program-condition-setting.component';\nimport { ProgramCategoriesComponent } from './components/program-categories/program-categories.component';\nimport { BankAccountViewComponent } from './components/bank-account-setting/bank-account-view/bank-account-view.component';\nimport * as i0 from \"@angular/core\";\nexport let SettingModule = /*#__PURE__*/(() => {\n  class SettingModule {\n    static ɵfac = function SettingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SettingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SettingRoutingModule, ReactiveFormsModule, FormsModule, TranslateModule, SettingDashboardComponent, ProgramConditionSettingComponent, ProgramCategoriesComponent, BankAccountViewComponent\n      // SharedModule - temporarily removed to test build\n      ]\n    });\n  }\n  return SettingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}