{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';\nimport { ReservationAvailableAppointmentsComponent } from './reservation-available-appointments/reservation-available-appointments.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nfunction StudentFreeRecitationWrapperComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-view-available-teacher-for-free-recitation-in-student\", 7);\n    i0.ɵɵlistener(\"ShedualAsOutPut\", function StudentFreeRecitationWrapperComponent_div_0_div_2_Template_app_view_available_teacher_for_free_recitation_in_student_ShedualAsOutPut_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openSchedualForm($event));\n    })(\"openSchedule\", function StudentFreeRecitationWrapperComponent_div_0_div_2_Template_app_view_available_teacher_for_free_recitation_in_student_openSchedule_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openSchedule());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StudentFreeRecitationWrapperComponent_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"app-schedule-free-recitation\", 8);\n    i0.ɵɵlistener(\"teacherCallPhonEvent\", function StudentFreeRecitationWrapperComponent_div_0_ng_container_3_Template_app_schedule_free_recitation_teacherCallPhonEvent_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.teacherCall($event));\n    })(\"backSchedule\", function StudentFreeRecitationWrapperComponent_div_0_ng_container_3_Template_app_schedule_free_recitation_backSchedule_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.backSchedule());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction StudentFreeRecitationWrapperComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-reservation-available-appointments\", 10);\n    i0.ɵɵlistener(\"closeShedual\", function StudentFreeRecitationWrapperComponent_div_0_div_5_Template_app_reservation_available_appointments_closeShedual_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeSchedualForm());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"objectAddSchedualForm\", ctx_r1.objectAddSchedualForm);\n  }\n}\nfunction StudentFreeRecitationWrapperComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 3);\n    i0.ɵɵtemplate(2, StudentFreeRecitationWrapperComponent_div_0_div_2_Template, 2, 0, \"div\", 4)(3, StudentFreeRecitationWrapperComponent_div_0_ng_container_3_Template, 3, 0, \"ng-container\", 2);\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵtemplate(5, StudentFreeRecitationWrapperComponent_div_0_div_5_Template, 2, 1, \"div\", 6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.schedule);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.schedule);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.schedualForm);\n  }\n}\nfunction StudentFreeRecitationWrapperComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-student-free-recitation-call\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"freeRecitationStuCallConnector\", ctx_r1.freeRecitationStuCallConnector);\n  }\n}\nexport let StudentFreeRecitationWrapperComponent = /*#__PURE__*/(() => {\n  class StudentFreeRecitationWrapperComponent {\n    languageService;\n    translate;\n    reservationAvailableAppointments;\n    teacherCallPhonEvent = new EventEmitter();\n    // @Input() \n    freeRecitationStuCallConnector;\n    isShowCall = false;\n    schedualForm = false;\n    schedule = false;\n    role = CallTypesEnum.FreeRecitation;\n    objectAddSchedualForm;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.SARD_TEC'));\n    }\n    openSchedualForm(event) {\n      this.schedualForm = !this.schedualForm;\n      this.objectAddSchedualForm = event;\n    }\n    closeSchedualForm() {\n      this.schedualForm = !this.schedualForm;\n    }\n    openSchedule() {\n      this.schedule = true;\n    }\n    backSchedule() {\n      this.schedule = false;\n    }\n    teacherCall(event) {\n      this.freeRecitationStuCallConnector = event;\n      this.isShowCall = true;\n      // this.role= CallTypesEnum.FreeRecitation;\n    }\n    static ɵfac = function StudentFreeRecitationWrapperComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentFreeRecitationWrapperComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentFreeRecitationWrapperComponent,\n      selectors: [[\"app-student-free-recitation-wrapper\"]],\n      viewQuery: function StudentFreeRecitationWrapperComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ReservationAvailableAppointmentsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reservationAvailableAppointments = _t.first);\n        }\n      },\n      outputs: {\n        teacherCallPhonEvent: \"teacherCallPhonEvent\"\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid\", 4, \"ngIf\"], [1, \"container-fluid\"], [4, \"ngIf\"], [1, \"row\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"col-12\"], [\"class\", \"overlay\", 4, \"ngIf\"], [3, \"ShedualAsOutPut\", \"openSchedule\"], [3, \"teacherCallPhonEvent\", \"backSchedule\"], [1, \"overlay\"], [3, \"closeShedual\", \"objectAddSchedualForm\"], [3, \"freeRecitationStuCallConnector\"]],\n      template: function StudentFreeRecitationWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StudentFreeRecitationWrapperComponent_div_0_Template, 6, 3, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, StudentFreeRecitationWrapperComponent_ng_container_2_Template, 2, 1, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isShowCall);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowCall);\n        }\n      },\n      dependencies: [i3.NgIf],\n      encapsulation: 2\n    });\n  }\n  return StudentFreeRecitationWrapperComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}