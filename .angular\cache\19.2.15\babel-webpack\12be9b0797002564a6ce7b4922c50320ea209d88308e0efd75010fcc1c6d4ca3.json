{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { TeacherDropOutRequestStatusEnum } from 'src/app/core/enums/drop-out-request-enums/teacher-drop-out-request-status.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = (a0, a1, a2, a3) => ({\n  \"accept\": a0,\n  \"pending\": a1,\n  \"rejected\": a2,\n  \"cancel\": a3\n});\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.user_profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r0.teacherDropOutRequestModel.avatarLink, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL_DROP_OUT_REQUEST.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL_DROP_OUT_REQUEST.REJECT_REASON\"), \" : \");\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL_DROP_OUT_REQUEST.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.teacherDropOutRequestModel.reasonReject);\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\")(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function TeacherDropOutRequestTeacherCardComponent_div_0_ng_container_38_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.cancelRequestOfTeacherEvent());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"TEACHER_DROP_OUT.CANCEL\"));\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6);\n    i0.ɵɵelement(11, \"p\", 7);\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"p\", 9);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"span\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13);\n    i0.ɵɵtemplate(19, TeacherDropOutRequestTeacherCardComponent_div_0_img_19_Template, 1, 1, \"img\", 14)(20, TeacherDropOutRequestTeacherCardComponent_div_0_img_20_Template, 1, 1, \"img\", 14);\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"p\", 16);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 17);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(26, \"div\", 18)(27, \"p\", 19)(28, \"span\", 20);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TeacherDropOutRequestTeacherCardComponent_div_0_span_31_Template, 3, 3, \"span\", 21);\n    i0.ɵɵelementStart(32, \"span\", 22);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"p\", 19);\n    i0.ɵɵtemplate(35, TeacherDropOutRequestTeacherCardComponent_div_0_span_35_Template, 3, 3, \"span\", 23)(36, TeacherDropOutRequestTeacherCardComponent_div_0_span_36_Template, 3, 3, \"span\", 24)(37, TeacherDropOutRequestTeacherCardComponent_div_0_span_37_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, TeacherDropOutRequestTeacherCardComponent_div_0_ng_container_38_Template, 5, 3, \"ng-container\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 16, \"TEACHER_DROP_OUT.TIME\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 18, ctx_r0.teacherDropOutRequestModel.requestDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enTechStatusName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arTechStatusName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(23, _c0, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Accept, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Pending, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Rejected, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Canceled));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.teacherDropOutRequestModel.avatarLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestModel.avatarLink);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.no);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enProgBatName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arProgBatName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enProgBatName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arProgBatName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 21, \"GENERAL_DROP_OUT_REQUEST.REQUEST_REASON\"), \" : \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestModel.reason === null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherDropOutRequestModel.reason);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestStatus.Rejected === ctx_r0.teacherDropOutRequestModel.drpStat);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestStatus.Rejected === ctx_r0.teacherDropOutRequestModel.drpStat);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestModel.reasonReject && ctx_r0.teacherDropOutRequestStatus.Rejected === ctx_r0.teacherDropOutRequestModel.drpStat);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestStatus.Pending && (ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.canCancel));\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_1_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL_DROP_OUT_REQUEST.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_1_p_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL_DROP_OUT_REQUEST.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherDropOutRequestTeacherCardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 3)(2, \"div\", 4)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6);\n    i0.ɵɵelement(11, \"p\", 33);\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"p\", 9);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"span\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 11)(17, \"p\", 34);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, TeacherDropOutRequestTeacherCardComponent_div_1_p_22_Template, 3, 3, \"p\", 35);\n    i0.ɵɵelementStart(23, \"p\", 20);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, TeacherDropOutRequestTeacherCardComponent_div_1_p_28_Template, 3, 3, \"p\", 36);\n    i0.ɵɵelementStart(29, \"p\", 20);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 12, \"TEACHER_DROP_OUT.TIME\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 14, ctx_r0.teacherDropOutRequestModel.requestDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enTechStatusName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arTechStatusName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(21, _c0, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Accept, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Pending, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Rejected, ctx_r0.teacherDropOutRequestModel.drpStat == ctx_r0.teacherDropOutRequestStatus.Canceled));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enProgBatName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arProgBatName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.enProgBatName : ctx_r0.teacherDropOutRequestModel == null ? null : ctx_r0.teacherDropOutRequestModel.arProgBatName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(21, 17, \"GENERAL_DROP_OUT_REQUEST.REQUEST_REASON\"), \" : \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherDropOutRequestModel.reason === null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherDropOutRequestModel.reason);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(27, 19, \"TEACHER_DROP_OUT.ADMIN_ANSWER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.teacherDropOutRequestModel.reasonReject);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherDropOutRequestModel.reasonReject);\n  }\n}\nexport let TeacherDropOutRequestTeacherCardComponent = /*#__PURE__*/(() => {\n  class TeacherDropOutRequestTeacherCardComponent {\n    translate;\n    imagesPathesService;\n    rejectTeacherDropOutRequest = new EventEmitter();\n    acceptTeacherDropOutRequest = new EventEmitter();\n    cancelRequestOfTeacher = new EventEmitter();\n    teacherDropOutRequestModel = {\n      totalRows: 0\n    };\n    adminView = false;\n    typeEnum = TeacherDropOutRequestStatusEnum.Pending;\n    // typeDropOutRequestEnum = TeacherDropOutRequestStatusEnum;\n    teacherDropOutRequestStatus = TeacherDropOutRequestStatusEnum;\n    teacherDropOutRequestIds;\n    langEnum = LanguageEnum;\n    requestDate;\n    x = 1;\n    constructor(translate, imagesPathesService) {\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      if (this.teacherDropOutRequestModel?.requestDate) {\n        let requestDateValue = new Date(this.teacherDropOutRequestModel.requestDate || '');\n        this.requestDate = new Date(requestDateValue.setDate(requestDateValue.getDate() + 1)).toISOString().slice(0, 10);\n      }\n      if (!this.teacherDropOutRequestModel?.avatarLink) {\n        this.teacherDropOutRequestModel.avatarLink = this.imagesPathesService.profile;\n      }\n      this.x += 1;\n    }\n    rejectTeacherDropOutRequestEvent(teacherDropOutRequestAdminViewModel) {\n      this.rejectTeacherDropOutRequest.emit(teacherDropOutRequestAdminViewModel);\n    }\n    acceptTeacherDropOutRequestEvent() {\n      this.acceptTeacherDropOutRequest.emit(this.teacherDropOutRequestModel);\n    }\n    cancelRequestOfTeacherEvent() {\n      this.cancelRequestOfTeacher.emit(this.teacherDropOutRequestModel);\n    }\n    static ɵfac = function TeacherDropOutRequestTeacherCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherDropOutRequestTeacherCardComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherDropOutRequestTeacherCardComponent,\n      selectors: [[\"app-teacher-drop-out-request-teacher-card\"]],\n      inputs: {\n        teacherDropOutRequestModel: \"teacherDropOutRequestModel\",\n        adminView: \"adminView\",\n        typeEnum: \"typeEnum\"\n      },\n      outputs: {\n        rejectTeacherDropOutRequest: \"rejectTeacherDropOutRequest\",\n        acceptTeacherDropOutRequest: \"acceptTeacherDropOutRequest\",\n        cancelRequestOfTeacher: \"cancelRequestOfTeacher\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"card-request  taecherViewCard\", 4, \"ngIf\"], [\"class\", \"card-request adminViewCard\", 4, \"ngIf\"], [1, \"card-request\", \"taecherViewCard\"], [1, \"card_header\"], [1, \"col-6\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-0\", \"m-0\"], [1, \"card_body\", \"program\"], [1, \"d-flex\", \"align-items-center\", \"program__header\", \"justify-content-between\"], [1, \"program__num\", \"mb-0\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"program__num\", \"mb-0\", \"mr-3\", \"ml-3\"], [1, \"status\", \"mb-0\", 3, \"ngClass\"], [1, \"mx-1\"], [1, \"d-flex\", \"justify-content-between\", \"mb-3\"], [1, \"d-flex\", \"mb-0\"], [\"class\", \"img_user\", 3, \"src\", 4, \"ngIf\"], [1, \"mx-1\", \"container-width\"], [1, \"number_request\", \"mb-0\"], [1, \"program__name\", \"ellipsis\", \"p-0\", \"mb-0\", 3, \"title\"], [1, \"program_reason\"], [1, \"mb-0\", \"pb-2\"], [1, \"program_user\", \"bold\", \"mb-0\"], [\"class\", \" mb-0\", 4, \"ngIf\"], [1, \"mb-0\"], [\"class\", \" program_user bold mx-1\", 4, \"ngIf\"], [\"class\", \"program__admin  mb-0 \", 4, \"ngIf\"], [\"class\", \"program__answer  mb-0 \", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"img_user\", 3, \"src\"], [1, \"program_user\", \"bold\", \"mx-1\"], [1, \"program__admin\", \"mb-0\"], [1, \"program__answer\", \"mb-0\"], [1, \"program__btn\", \"cancel-btn\", 3, \"click\"], [1, \"card-request\", \"adminViewCard\"], [1, \"program__num\", \"mb-2\"], [1, \"program__name\", \"ellipsis\", \"mb-0\", \"p-0\", 3, \"title\"], [\"class\", \"program_user bold mb-0\", 4, \"ngIf\"], [\"class\", \"program_user bold mb-0 \", 4, \"ngIf\"]],\n      template: function TeacherDropOutRequestTeacherCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TeacherDropOutRequestTeacherCardComponent_div_0_Template, 39, 28, \"div\", 0)(1, TeacherDropOutRequestTeacherCardComponent_div_1_Template, 31, 26, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.adminView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminView);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.h-90[_ngcontent-%COMP%]{height:90%}.card-request[_ngcontent-%COMP%]{background:#fff;margin:0 .2rem;box-shadow:0 .188rem 1rem #f2f1f1de;border-radius:.75rem;padding:0;overflow:hidden;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .bold[_ngcontent-%COMP%]{font-weight:700;font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_header[_ngcontent-%COMP%]{background-color:var(--main_color);color:#fff;display:flex;justify-content:space-between;align-items:center;padding:.313rem 1.25rem;font-size:.75rem}.card-request[_ngcontent-%COMP%]   .call[_ngcontent-%COMP%], .card-request[_ngcontent-%COMP%]   .vedio[_ngcontent-%COMP%]{width:.93rem;height:.93rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;height:32vh;padding:.625rem 1.25rem 1.25rem;color:#4d4d4d}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(en){text-align:left}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(ar){text-align:right}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(ar){text-align:center}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(en){text-align:center}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{color:var(--main_color);font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .number_request[_ngcontent-%COMP%]{color:#d6d7d8;font-weight:700;font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:3.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .program_user[_ngcontent-%COMP%]{color:#333;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:0 1.25rem;margin-bottom:.7rem;margin-top:.5rem}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:0rem;height:2rem;width:46%;min-width:5.5rem;font-size:.7rem}.card-request[_ngcontent-%COMP%]   .container-width[_ngcontent-%COMP%]{flex-basis:12.5rem}.card-request[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:10.625rem}.card-request[_ngcontent-%COMP%]   .program_rejected[_ngcontent-%COMP%]{height:10vh;overflow-y:auto}@media (max-width: 48rem){.ellipsis[_ngcontent-%COMP%]{width:7.813rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;height:25vh}}.card-request.taecherViewCard[_ngcontent-%COMP%]{height:95%}.card_body[_ngcontent-%COMP%]{height:38vh!important}.program__btn[_ngcontent-%COMP%]{text-align:center!important;position:absolute}.program__btn[_ngcontent-%COMP%]:lang(ar){left:1.375rem}.program__btn[_ngcontent-%COMP%]:lang(en){right:1.375rem}.program__btn[disabled][_ngcontent-%COMP%]{background-color:#d6d7d8;color:#fff}.program__header[_ngcontent-%COMP%]{margin-top:.438rem}.program__num[_ngcontent-%COMP%]{color:#d6d7d8}.program__name[_ngcontent-%COMP%]{cursor:auto;color:#333;font-weight:600;font-size:1rem}.program__admin[_ngcontent-%COMP%]{font-size:.875rem;font-weight:400;color:var(--main_color);margin:0}.program__answer[_ngcontent-%COMP%]{font-weight:400;font-size:.875rem;color:#333}.ellipsis[_ngcontent-%COMP%]{width:97%}.program_reason[_ngcontent-%COMP%]{height:16vh;overflow-y:auto}\"]\n    });\n  }\n  return TeacherDropOutRequestTeacherCardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}