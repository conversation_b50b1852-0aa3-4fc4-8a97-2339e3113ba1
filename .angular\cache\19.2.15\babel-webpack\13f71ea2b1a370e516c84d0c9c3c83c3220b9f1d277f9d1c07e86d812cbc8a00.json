{"ast": null, "code": "import { WalkThroughPagesComponent } from './walk-through-pages/walk-through-pages.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/walk-through-services/walk-through-services\";\nimport * as i3 from \"src/app/core/services/language-services/language.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nfunction ViewAllWalkThroughComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"app-add-walk-through\", 8);\n    i0.ɵɵlistener(\"closeWalkThrough\", function ViewAllWalkThroughComponent_div_6_Template_app_add_walk_through_closeWalkThrough_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeWalkThrough($event));\n    })(\"refreshWalkThroughPages\", function ViewAllWalkThroughComponent_div_6_Template_app_add_walk_through_refreshWalkThroughPages_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshWalkthroughPagesList($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ViewAllWalkThroughComponent = /*#__PURE__*/(() => {\n  class ViewAllWalkThroughComponent {\n    router;\n    route;\n    walkThroughService;\n    languageService;\n    translate;\n    walkthroughPagesChild;\n    routeParams;\n    walkThroughId;\n    allWalkThroughData;\n    isSuccess;\n    selectedWalkThroughPageId;\n    showAddWalkThroughForm = false;\n    constructor(router, route, walkThroughService, languageService, translate) {\n      this.router = router;\n      this.route = route;\n      this.walkThroughService = walkThroughService;\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n      // this.routeParams = this.router.url;\n      // this.walkThroughId = this.route.snapshot.params.id;\n      // this.walkThroughService.getAllWalkThrough().subscribe(res =>{\n      //   this.allWalkThroughData = res.data;\n      // });\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('WALKTHROUGH.TITLE'));\n    }\n    // deleteWalkThrough(Id: any)\n    // {\n    //   this.walkThroughService.deleteWalkThrough(Id).subscribe(res =>{\n    //     this.isSuccess = res.isSuccess;\n    //     this.successMessage = res.message;\n    //   });\n    // }\n    setSelectedPageId(event) {\n      this.selectedWalkThroughPageId = event;\n    }\n    openWalkThrough(event) {\n      this.showAddWalkThroughForm = event;\n    }\n    closeWalkThrough(event) {\n      this.showAddWalkThroughForm = event;\n    }\n    refreshWalkthroughPagesList(event) {\n      this.walkthroughPagesChild?.loadWalkThroughPages();\n    }\n    static ɵfac = function ViewAllWalkThroughComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewAllWalkThroughComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.WalkThroughService), i0.ɵɵdirectiveInject(i3.LanguageService), i0.ɵɵdirectiveInject(i4.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAllWalkThroughComponent,\n      selectors: [[\"app-view-all-walk-through\"]],\n      viewQuery: function ViewAllWalkThroughComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(WalkThroughPagesComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.walkthroughPagesChild = _t.first);\n        }\n      },\n      decls: 7,\n      vars: 2,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-12\", \"col-xs-12\"], [3, \"selectedWalkThroughPageId\", \"openWalkThrough\"], [1, \"col-xl-9\", \"col-lg-9\", \"col-md-9\", \"col-sm-12\", \"col-xs-12\"], [3, \"selectedWalkThroughPageId\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeWalkThrough\", \"refreshWalkThroughPages\"]],\n      template: function ViewAllWalkThroughComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-walk-through-pages\", 3);\n          i0.ɵɵlistener(\"selectedWalkThroughPageId\", function ViewAllWalkThroughComponent_Template_app_walk_through_pages_selectedWalkThroughPageId_3_listener($event) {\n            return ctx.setSelectedPageId($event);\n          })(\"openWalkThrough\", function ViewAllWalkThroughComponent_Template_app_walk_through_pages_openWalkThrough_3_listener($event) {\n            return ctx.openWalkThrough($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-walk-through\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, ViewAllWalkThroughComponent_div_6_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"selectedWalkThroughPageId\", ctx.selectedWalkThroughPageId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddWalkThroughForm);\n        }\n      },\n      dependencies: [i5.NgIf],\n      encapsulation: 2\n    });\n  }\n  return ViewAllWalkThroughComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}