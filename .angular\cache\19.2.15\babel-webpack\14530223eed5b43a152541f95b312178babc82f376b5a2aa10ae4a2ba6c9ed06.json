{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isArray } from 'zrender/lib/core/util.js';\n/**\r\n * @return label string. Not null/undefined\r\n */\nexport function getDefaultLabel(data, dataIndex) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  var len = labelDims.length;\n  // Simple optimization (in lots of cases, label dims length is 1)\n  if (len === 1) {\n    var rawVal = retrieveRawValue(data, dataIndex, labelDims[0]);\n    return rawVal != null ? rawVal + '' : null;\n  } else if (len) {\n    var vals = [];\n    for (var i = 0; i < labelDims.length; i++) {\n      vals.push(retrieveRawValue(data, dataIndex, labelDims[i]));\n    }\n    return vals.join(' ');\n  }\n}\nexport function getDefaultInterpolatedLabel(data, interpolatedValue) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  if (!isArray(interpolatedValue)) {\n    return interpolatedValue + '';\n  }\n  var vals = [];\n  for (var i = 0; i < labelDims.length; i++) {\n    var dimIndex = data.getDimensionIndex(labelDims[i]);\n    if (dimIndex >= 0) {\n      vals.push(interpolatedValue[dimIndex]);\n    }\n  }\n  return vals.join(' ');\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}