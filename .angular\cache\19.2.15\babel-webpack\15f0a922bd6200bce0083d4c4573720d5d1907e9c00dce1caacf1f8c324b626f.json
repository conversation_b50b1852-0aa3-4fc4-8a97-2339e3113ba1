{"ast": null, "code": "import { ProgramDayTaskEncouragementLetterType } from 'src/app/core/enums/program-day-task-encouragement-letter-type.enum';\nimport { ProgramDutyDaysTaskViewMoodEnum } from 'src/app/core/enums/programs/program-duty-days-task-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = (a0, a1) => ({\n  \" border-transparent\": a0,\n  \"input_with-voice\": a1\n});\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 10);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵlistener(\"input\", function ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_input_8_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onQuestionTextChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_input_8_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.encouragementLetterDetailsModel.text, $event) || (ctx_r2.encouragementLetterDetailsModel.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 4, \"EXAM_FORM.ADD_QUESTION\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r2.viewMode);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.encouragementLetterDetailsModel.text);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c0, !ctx_r2.encouragementLetterDetailsModel.text, !ctx_r2.encouragementLetterDetailsModel.text));\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_app_voice_recording_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recording\", 11);\n    i0.ɵɵlistener(\"getVoiceUrl\", function ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_app_voice_recording_9_Template_app_voice_recording_getVoiceUrl_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.saveVoiceUrl($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"viewMode\", ctx_r2.viewMode)(\"voiceUrl\", ctx_r2.encouragementLetterDetailsModel == null ? null : ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 5);\n    i0.ɵɵtemplate(8, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_input_8_Template, 2, 9, \"input\", 6)(9, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_app_voice_recording_9_Template, 1, 2, \"app-voice-recording\", 7);\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"img\", 9);\n    i0.ɵɵlistener(\"click\", function ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_Template_img_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.DeleteMSG());\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"PROGRAM_DAY_TASK_ENCOURAGEMENT.MESSAGE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.trash_2, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_app_voice_recording_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recording\", 11);\n    i0.ɵɵlistener(\"getVoiceUrl\", function ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_app_voice_recording_1_Template_app_voice_recording_getVoiceUrl_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.saveVoiceUrl($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"viewMode\", true)(\"voiceUrl\", ctx_r2.encouragementLetterDetailsModel == null ? null : ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"p\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.encouragementLetterDetailsModel.text);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_app_voice_recording_1_Template, 1, 2, \"app-voice-recording\", 7)(2, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_ng_container_2_Template, 4, 1, \"ng-container\", 0);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_1_Template, 12, 6, \"ng-container\", 0)(2, ProgramDayTaskEncouragementLetterComponent_div_0_ng_container_2_Template, 3, 2, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isView);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isView);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_1_app_voice_recording_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recording\", 11);\n    i0.ɵɵlistener(\"getVoiceUrl\", function ProgramDayTaskEncouragementLetterComponent_div_1_app_voice_recording_2_Template_app_voice_recording_getVoiceUrl_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.saveVoiceUrl($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"viewMode\", true)(\"voiceUrl\", ctx_r2.encouragementLetterDetailsModel == null ? null : ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"p\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.encouragementLetterDetailsModel.text);\n  }\n}\nfunction ProgramDayTaskEncouragementLetterComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, ProgramDayTaskEncouragementLetterComponent_div_1_app_voice_recording_2_Template, 1, 2, \"app-voice-recording\", 7)(3, ProgramDayTaskEncouragementLetterComponent_div_1_ng_container_3_Template, 4, 1, \"ng-container\", 0);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.encouragementLetterDetailsModel.voiceUrl);\n  }\n}\nexport let ProgramDayTaskEncouragementLetterComponent = /*#__PURE__*/(() => {\n  class ProgramDayTaskEncouragementLetterComponent {\n    activeroute;\n    dialog;\n    router;\n    translate;\n    fb;\n    imagesPathesService;\n    isView = false;\n    viewMode = false;\n    programDayTaskDetails = {};\n    resultMessage = {};\n    selectedTaskId;\n    encouragementLetterDetailsModel = {};\n    dutyDaysTaskViewMood = ProgramDutyDaysTaskViewMoodEnum.admin;\n    programDutyDaysTaskViewMoodEnum = ProgramDutyDaysTaskViewMoodEnum;\n    constructor(activeroute, dialog, router, translate, fb, imagesPathesService) {\n      this.activeroute = activeroute;\n      this.dialog = dialog;\n      this.router = router;\n      this.translate = translate;\n      this.fb = fb;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {}\n    onQuestionTextChange() {\n      this.encouragementLetterDetailsModel.text ? this.encouragementLetterDetailsModel.letterType = ProgramDayTaskEncouragementLetterType.text : null;\n    }\n    saveVoiceUrl(event) {\n      this.encouragementLetterDetailsModel.voiceUrl = event;\n      this.encouragementLetterDetailsModel.voiceUrl ? this.encouragementLetterDetailsModel.letterType = ProgramDayTaskEncouragementLetterType.voice : null;\n    }\n    DeleteMSG() {\n      this.encouragementLetterDetailsModel.voiceUrl = \"\";\n      this.encouragementLetterDetailsModel.text = \"\";\n    }\n    static ɵfac = function ProgramDayTaskEncouragementLetterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramDayTaskEncouragementLetterComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramDayTaskEncouragementLetterComponent,\n      selectors: [[\"app-program-day-task-encouragement-letter\"]],\n      inputs: {\n        isView: \"isView\",\n        viewMode: \"viewMode\",\n        selectedTaskId: \"selectedTaskId\",\n        encouragementLetterDetailsModel: \"encouragementLetterDetailsModel\",\n        dutyDaysTaskViewMood: \"dutyDaysTaskViewMood\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [1, \"form-group\", \"add-question-template\", \"dynamic-radio-button-with-input-text-model\", \"mb-0\"], [1, \"form-group\", \"row\", \"question\", \"px-2\"], [1, \"col-sm-8\", \"mt-3\"], [1, \"label_item_question\"], [1, \"input-group\", \"input-sm\", \"input_search_voice\"], [\"type\", \"text\", \"class\", \"form-control  border-md \", 3, \"disabled\", \"ngModel\", \"ngClass\", \"placeholder\", \"input\", \"ngModelChange\", 4, \"ngIf\"], [3, \"viewMode\", \"voiceUrl\", \"getVoiceUrl\", 4, \"ngIf\"], [1, \"row\"], [3, \"click\", \"src\"], [\"type\", \"text\", 1, \"form-control\", \"border-md\", 3, \"input\", \"ngModelChange\", \"disabled\", \"ngModel\", \"ngClass\", \"placeholder\"], [3, \"getVoiceUrl\", \"viewMode\", \"voiceUrl\"], [1, \"cardRecourd\", \"mb-2\"], [1, \"mr-2\", \"mb-0\"]],\n      template: function ProgramDayTaskEncouragementLetterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProgramDayTaskEncouragementLetterComponent_div_0_Template, 3, 2, \"div\", 0)(1, ProgramDayTaskEncouragementLetterComponent_div_1_Template, 4, 2, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.dutyDaysTaskViewMood === ctx.programDutyDaysTaskViewMoodEnum.admin);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dutyDaysTaskViewMood === ctx.programDutyDaysTaskViewMoodEnum.student);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.list-group_program-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;margin-top:1rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:57vh;overflow-y:auto;padding-left:.5rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:57vh;overflow-y:auto;padding-right:.5rem}.list-group_program-list[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:pointer}.list-group_program-list[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.list-group_program-list[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-family:Almarai!important;font-weight:700;font-size:1rem;justify-content:space-between}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-copy[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-copy[_ngcontent-%COMP%]{color:#fff}@media (min-width: 37.5rem) and (max-width: 63.938rem){.list-group_program-list[_ngcontent-%COMP%]{padding:.9rem}}.tab_page[_ngcontent-%COMP%]{height:79vh;display:flex;justify-content:space-between;padding-bottom:1rem;background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:1.188rem;height:74vh;overflow-y:auto;overflow-x:hidden;margin-left:.5rem;margin-right:.5rem;margin-top:2rem;padding:1rem .5rem}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]   .name_prog[_ngcontent-%COMP%]{font-size:.875rem;color:gray}.tab_page[_ngcontent-%COMP%]   .part1[_ngcontent-%COMP%]{flex:0 0 30.333333%;max-width:30.333333%}.tab_page[_ngcontent-%COMP%]   .part2[_ngcontent-%COMP%]{flex:0 0 64.666667%;max-width:64.666667%}.tab_page[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;color:#333}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]{background:transparent;border-radius:0;padding:0;height:auto;margin-top:0}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color)}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-size:1.12rem}.list_exams[_ngcontent-%COMP%]{overflow-x:hidden!important;height:75vh!important}.tab_page[_ngcontent-%COMP%]{height:80vh}.tab_page-2[_ngcontent-%COMP%]{height:80vh;background:#fbfbfb;border-radius:0 0 .75rem .75rem}p.name[_ngcontent-%COMP%]{font-weight:700;font-size:1.125rem;letter-spacing:0;color:#333;opacity:1}.btn-container[_ngcontent-%COMP%]:lang(en){margin-right:2.3rem}.btn-container[_ngcontent-%COMP%]:lang(ar){margin-left:2.3rem}.back-btn[_ngcontent-%COMP%]{color:var(--second_color);background:#b3b3b3;border:.125rem solid var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.internal_scroll_list_group.order[_ngcontent-%COMP%]{height:25.9375rem!important}.show-exam[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;text-decoration:none;box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color);height:10%;background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;margin-bottom:1rem;opacity:1;margin-top:1.4rem .1rem;font-weight:700;font-size:1rem;justify-content:space-between;padding:.4rem}.show-exam[_ngcontent-%COMP%]   .show-space[_ngcontent-%COMP%]{padding:.5rem 0rem}.show-exam[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{min-width:3rem}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(ar){float:left}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(en){float:right}.cardRecourd[_ngcontent-%COMP%]{margin:1rem;justify-content:space-between;align-items:center;display:flex;padding:.5rem 1rem;background-color:#fff;border-radius:.438rem;border:.063rem solid rgba(242,241,241,.8705882353);box-shadow:0 .125rem .188rem #f2f1f1de}.redPoint[_ngcontent-%COMP%]{top:16.813rem;left:32.813rem;width:1.25rem;height:1.25rem;background:#ea5455 0% 0% no-repeat padding-box;opacity:1;border-radius:50%}.label_item_question[_ngcontent-%COMP%]{font-weight:700;font-size:.875rem}\"]\n    });\n  }\n  return ProgramDayTaskEncouragementLetterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}