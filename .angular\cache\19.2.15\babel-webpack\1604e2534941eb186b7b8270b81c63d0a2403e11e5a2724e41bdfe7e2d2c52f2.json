{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function radarLayout(ecModel) {\n  ecModel.eachSeriesByType('radar', function (seriesModel) {\n    var data = seriesModel.getData();\n    var points = [];\n    var coordSys = seriesModel.coordinateSystem;\n    if (!coordSys) {\n      return;\n    }\n    var axes = coordSys.getIndicatorAxes();\n    zrUtil.each(axes, function (axis, axisIndex) {\n      data.each(data.mapDimension(axes[axisIndex].dim), function (val, dataIndex) {\n        points[dataIndex] = points[dataIndex] || [];\n        var point = coordSys.dataToPoint(val, axisIndex);\n        points[dataIndex][axisIndex] = isValidPoint(point) ? point : getValueMissingPoint(coordSys);\n      });\n    });\n    // Close polygon\n    data.each(function (idx) {\n      // TODO\n      // Is it appropriate to connect to the next data when some data is missing?\n      // Or, should trade it like `connectNull` in line chart?\n      var firstPoint = zrUtil.find(points[idx], function (point) {\n        return isValidPoint(point);\n      }) || getValueMissingPoint(coordSys);\n      // Copy the first actual point to the end of the array\n      points[idx].push(firstPoint.slice());\n      data.setItemLayout(idx, points[idx]);\n    });\n  });\n}\nfunction isValidPoint(point) {\n  return !isNaN(point[0]) && !isNaN(point[1]);\n}\nfunction getValueMissingPoint(coordSys) {\n  // It is error-prone to input [NaN, NaN] into polygon, polygon.\n  // (probably cause problem when refreshing or animating)\n  return [coordSys.cx, coordSys.cy];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}