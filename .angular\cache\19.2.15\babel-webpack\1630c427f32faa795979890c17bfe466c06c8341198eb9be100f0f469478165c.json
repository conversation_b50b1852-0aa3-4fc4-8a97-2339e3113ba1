{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ProgramDutiesEnum } from 'src/app/core/enums/programs/program-duties-enum.enum';\nimport { ProgramDayTaskRecitationType } from 'src/app/core/enums/program-day-task-recitation-type.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/program-services/program-basic-info.service\";\nimport * as i4 from \"src/app/core/services/lookup-services/lookup.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i7 from \"src/app/core/services/program-categories-services/program-categories.service\";\nimport * as i8 from \"src/app/core/services/program-services/program.service\";\nimport * as i9 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i10 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i11 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"dimmed\": a0\n});\nconst _c1 = () => ({\n  standalone: true\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"ml-1 mr-1\": a0,\n  \"ml-5 mr-5\": a1,\n  \"ml-3 mr-3\": a2\n});\nfunction ProgramBasicInfoComponent_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.quran, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProgramBasicInfoComponent_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.progPic, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProgramBasicInfoComponent_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.PROG_NAME_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_18_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_18_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.progName.errors == null ? null : ctx_r1.f.progName.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.progName.errors == null ? null : ctx_r1.f.progName.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"option\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", item_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r4.nameEn : item_r4.nameAr, \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_26_div_1_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.shareWith.errors == null ? null : ctx_r1.f.shareWith.errors.required);\n  }\n}\nfunction ProgramBasicInfoComponent_mat_radio_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 62);\n    i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_mat_radio_button_34_Template_mat_radio_button_change_0_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProgTypeToList(item_r6, true));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"value\", item_r6.id);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c2, i_r7 == 0, i_r7 == 1, i_r7 == 2));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r6.nameEn : item_r6.nameAr, \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_ng_container_42_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63);\n    i0.ɵɵtemplate(2, ProgramBasicInfoComponent_ng_container_42_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.durationProg.errors == null ? null : ctx_r1.f.durationProg.errors.required);\n  }\n}\nfunction ProgramBasicInfoComponent_div_49_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_49_div_1_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.dutyTime.errors == null ? null : ctx_r1.f.dutyTime.errors.required);\n  }\n}\nfunction ProgramBasicInfoComponent_div_56_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_56_div_1_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.availableDuty.errors == null ? null : ctx_r1.f.availableDuty.errors.required);\n  }\n}\nfunction ProgramBasicInfoComponent_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_63_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.PROG_IDEA_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_63_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_63_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.ideaProg.errors == null ? null : ctx_r1.f.ideaProg.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.ideaProg.errors == null ? null : ctx_r1.f.ideaProg.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.PROG_GOAL_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_70_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_70_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.goalProg.errors == null ? null : ctx_r1.f.goalProg.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.goalProg.errors == null ? null : ctx_r1.f.goalProg.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.PROG_VISION_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_80_div_1_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.visionProg.errors == null ? null : ctx_r1.f.visionProg.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_87_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.PATH_PROG_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_87_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_87_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.pathProg.errors == null ? null : ctx_r1.f.pathProg.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.pathProg.errors == null ? null : ctx_r1.f.pathProg.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_div_94_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_94_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.ADVANTAGE_PROG_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_94_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_94_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.advantageProg.errors == null ? null : ctx_r1.f.advantageProg.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.advantageProg.errors == null ? null : ctx_r1.f.advantageProg.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_div_101_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_101_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.TEXT_PLEDGE_MAX_LENGTH\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_101_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_101_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textPledge.errors == null ? null : ctx_r1.f.textPledge.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textPledge.errors == null ? null : ctx_r1.f.textPledge.errors.maxlength);\n  }\n}\nfunction ProgramBasicInfoComponent_ng_container_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-radio-button\", 64);\n    i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_ng_container_109_Template_mat_radio_button_click_1_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.getDutyDayType(item_r9.id));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", item_r9.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r9.nameEn : item_r9.nameAr);\n  }\n}\nfunction ProgramBasicInfoComponent_input_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 65);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \"\", i0.ɵɵpipeBind1(1, 3, \"PROGRAM_BASIC_INFO.WRITE_DAY_COUNT\"), \" \");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, (ctx_r1.progBasicInfoDetails == null ? null : ctx_r1.progBasicInfoDetails.prgPasuDate) != null));\n  }\n}\nfunction ProgramBasicInfoComponent_div_111_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_111_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BASIC_INFO.DAY_COUNT_MAX\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_111_div_1_Template, 3, 3, \"div\", 24)(2, ProgramBasicInfoComponent_div_111_div_2_Template, 3, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.progDutyDaysFreeDaysSelection && (ctx_r1.f.dayCount.errors == null ? null : ctx_r1.f.dayCount.errors.required));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.progDutyDaysFreeDaysSelection && (ctx_r1.f.dayCount.errors == null ? null : ctx_r1.f.dayCount.errors.max));\n  }\n}\nfunction ProgramBasicInfoComponent_div_112_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-checkbox\", 67);\n    i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_div_112_ng_container_1_Template_mat_checkbox_change_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addProgWeeklyDayToList(item_r11, $event));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.progWeeklyDaysChecked(item_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r11.nameEn : item_r11.nameAr, \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, ProgramBasicInfoComponent_div_112_ng_container_1_Template, 3, 2, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, (ctx_r1.progBasicInfoDetails == null ? null : ctx_r1.progBasicInfoDetails.prgPasuDate) != null));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.DAYS);\n  }\n}\nfunction ProgramBasicInfoComponent_div_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_ng_container_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"option\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", item_r12.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r12.enCatName : item_r12.arCatName);\n  }\n}\nfunction ProgramBasicInfoComponent_div_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_span_135_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_span_135_Template_a_click_2_listener() {\n      const p_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeSelectedProgramTypesList(p_r14));\n    });\n    i0.ɵɵelement(3, \"i\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const p_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? p_r14.enCatName : p_r14.arCatName, \" \");\n  }\n}\nfunction ProgramBasicInfoComponent_div_149_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-radio-button\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", item_r16.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? item_r16.nameEn : item_r16.nameAr);\n  }\n}\nfunction ProgramBasicInfoComponent_div_149_div_10_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 82)(2, \"p\", 83);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_div_149_div_10_div_23_Template_a_click_6_listener() {\n      const item_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeRecitationTimeToList(item_r19));\n    });\n    i0.ɵɵelement(7, \"i\", 84);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate4(\" \", i0.ɵɵpipeBind1(4, 4, \"PROGRAM_BASIC_INFO.FROM\"), \" : \", item_r19.progRecFrom, \" , \", i0.ɵɵpipeBind1(5, 6, \"PROGRAM_BASIC_INFO.TO\"), \" : \", item_r19.progRecTo, \"\");\n  }\n}\nfunction ProgramBasicInfoComponent_div_149_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 4)(2, \"div\", 76)(3, \"div\", 12)(4, \"label\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProgramBasicInfoComponent_div_149_div_10_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.recitFrom, $event) || (ctx_r1.recitFrom = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 76)(9, \"div\", 12)(10, \"label\", 13);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProgramBasicInfoComponent_div_149_div_10_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.recitTo, $event) || (ctx_r1.recitTo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 76)(15, \"label\", 78);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_div_149_div_10_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addRecitationTimeToList());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 41)(22, \"div\", 80);\n    i0.ɵɵtemplate(23, ProgramBasicInfoComponent_div_149_div_10_div_23_Template, 8, 8, \"div\", 81);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c0, (ctx_r1.progBasicInfoDetails == null ? null : ctx_r1.progBasicInfoDetails.prgPasuDate) != null));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(6, 10, \"PROGRAM_BASIC_INFO.FROM_HOUR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.recitFrom);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c1));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(12, 12, \"PROGRAM_BASIC_INFO.TO_HOUR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.recitTo);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(17, 14, \"PROGRAM_BASIC_INFO.RECITTYPE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 16, \"PROGRAM_BASIC_INFO.ADD\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.progRecitationTimes);\n  }\n}\nfunction ProgramBasicInfoComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 4)(2, \"div\", 72)(3, \"div\", 12)(4, \"div\")(5, \"label\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-radio-group\", 73);\n    i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_div_149_Template_mat_radio_group_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.isSardTimesChange($event));\n    });\n    i0.ɵɵtemplate(9, ProgramBasicInfoComponent_div_149_ng_container_9_Template, 3, 2, \"ng-container\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(10, ProgramBasicInfoComponent_div_149_div_10_Template, 24, 22, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, (ctx_r1.progBasicInfoDetails == null ? null : ctx_r1.progBasicInfoDetails.prgPasuDate) != null));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"PROGRAM_BASIC_INFO.RECITTYPE\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.SARD_TYPES);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSardTimesEnabled);\n  }\n}\nfunction ProgramBasicInfoComponent_div_150_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \" \");\n  }\n}\nexport let ProgramBasicInfoComponent = /*#__PURE__*/(() => {\n  class ProgramBasicInfoComponent {\n    fb;\n    translate;\n    BasicInfoService;\n    lookupService;\n    router;\n    alert;\n    ProgramCategoriesService;\n    programService;\n    attachmentService;\n    imagesPathesService;\n    // programTypes = ['برنامج حفظ', 'برنامج قراءة', 'برنامج شرح'];\n    // ratingTypes = ['من الطالب للمعلم', 'من المعلم للطالب']\n    baseInfoForm = new FormGroup({});\n    isSubmit = false;\n    //baseicInfoProgrmInputs = {} as IProgramBasicInfoModel;\n    progBasicInfoDetails;\n    resultMessage = {};\n    baseicInfoProgrmModel;\n    baseicInfoProgrmEditModel;\n    collectionOfLookup = {};\n    listOfLookup = ['PROG_TYPES', 'SARD_TYPES', 'SHARED_TYPES', 'DUTY_TYPES', 'DAYS', 'RATING'];\n    progDutyDaysFreeDaysSelection = true;\n    isSardEnabled = false;\n    isSardTimesEnabled = false;\n    langEnum = LanguageEnum;\n    allPrograms = [];\n    programTypesList = [];\n    selectedProgramTypesList = [];\n    programRatingList = [];\n    progWeeklyDayList = [];\n    progRecitationTimes = [];\n    recitFrom = '';\n    recitTo = '';\n    resMessage = {};\n    rewayatsMessage = {};\n    dutyDayTypeId = ' ';\n    selectedTypeProg; //: BaseLookupModel |undefined ;\n    programsCategoryFilterRequestModel = {};\n    fileUploadModel = [];\n    progPic;\n    listExt = [\"jpg\", \"png\", \"jpeg\", \"gif\", \"bmp\", \"svg\"];\n    constructor(fb, translate, BasicInfoService, lookupService, router, alert, ProgramCategoriesService, programService, attachmentService, imagesPathesService) {\n      this.fb = fb;\n      this.translate = translate;\n      this.BasicInfoService = BasicInfoService;\n      this.lookupService = lookupService;\n      this.router = router;\n      this.alert = alert;\n      this.ProgramCategoriesService = ProgramCategoriesService;\n      this.programService = programService;\n      this.attachmentService = attachmentService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getLookupByKey();\n      this.buildForm();\n      this.getAllCategories();\n      this.setScssImages();\n    }\n    setScssImages() {\n      this.imagesPathesService.setBackgroundPannerInStyle();\n    }\n    getAllCategories() {\n      this.ProgramCategoriesService.getProgramCategories(this.programsCategoryFilterRequestModel).subscribe(res => {\n        if (res.isSuccess) {\n          this.allPrograms = res.data;\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    addSection() {\n      // if (!this.baseInfoForm.value.addSection) {\n      //   this.rewayatsMessage = {\n      //     message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_REWAYAT'),\n      //     type: BaseConstantModel.DANGER_TYPE\n      //   };\n      //   return;\n      // }\n      if (this.selectedProgramTypesList.length === 1) {\n        this.alert.error(this.translate.instant('PROGRAM_BASIC_INFO.CATEGORY_LIST_ONE_ITEM_VALIDATION'));\n        return;\n      }\n      const exist = this.selectedProgramTypesList.some(el => el.id === this.baseInfoForm.value.addSection);\n      if (!exist) {\n        if (this.allPrograms) {\n          this.selectedProgramTypesList.push(this.allPrograms.filter(el => el.id == this.baseInfoForm.value.addSection)[0]);\n        }\n      }\n      // this.selectedProgramTypesList.push();\n    }\n    removeSelectedProgramTypesList(item) {\n      let index = this.selectedProgramTypesList.indexOf(item);\n      this.selectedProgramTypesList.splice(index, 1);\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookup).subscribe(res => {\n        if (res.isSuccess) {\n          this.collectionOfLookup = res.data;\n          //set default value for duty days lookups\n          this.f.dutiesDayType.setValue(this.collectionOfLookup?.DUTY_TYPES ? this.collectionOfLookup?.DUTY_TYPES[0]?.id : '');\n          if (this.progBasicInfoDetails) {\n            this.PopulateForm();\n          }\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    get f() {\n      return this.baseInfoForm.controls;\n    }\n    buildForm() {\n      this.baseInfoForm = this.fb.group({\n        progName: ['', [Validators.required, Validators.maxLength(100)]],\n        shareWith: ['', [Validators.required]],\n        durationProg: ['', [Validators.required]],\n        dutyTime: ['', [Validators.required]],\n        availableDuty: ['', [Validators.required]],\n        ideaProg: ['', [Validators.required, Validators.maxLength(2000)]],\n        goalProg: ['', [Validators.required, Validators.maxLength(2000)]],\n        visionProg: ['', [Validators.maxLength(2000)]],\n        pathProg: ['', [Validators.required, Validators.maxLength(2000)]],\n        advantageProg: ['', [Validators.required, Validators.maxLength(300)]],\n        textPledge: ['', [Validators.required, Validators.maxLength(300)]],\n        dutiesDayType: ['', [Validators.required]],\n        dayCount: ['1', [Validators.required, Validators.max(7)]],\n        examPass: [''],\n        rectMand: [''],\n        isAlsard: [false],\n        recitType: [''],\n        addSection: [null]\n        //progType:[[] /*, [Validators.required]*/]\n      });\n    }\n    PopulateForm() {\n      this.f.progName.setValue(this.progBasicInfoDetails?.prgName);\n      this.f.shareWith.setValue(this.progBasicInfoDetails?.prgSharType);\n      this.f.durationProg.setValue(this.progBasicInfoDetails?.prgDura);\n      this.f.dutyTime.setValue(this.progBasicInfoDetails?.prgAvailaDutyTime);\n      this.f.availableDuty.setValue(this.progBasicInfoDetails?.prgAllowDutyDays);\n      this.f.ideaProg.setValue(this.progBasicInfoDetails?.prgIda);\n      this.f.goalProg.setValue(this.progBasicInfoDetails?.prgGoal);\n      this.f.visionProg.setValue(this.progBasicInfoDetails?.prgVisi);\n      this.f.pathProg.setValue(this.progBasicInfoDetails?.prgMeth);\n      this.f.advantageProg.setValue(this.progBasicInfoDetails?.prgAdvan);\n      this.f.textPledge.setValue(this.progBasicInfoDetails?.prgPledgTxt);\n      this.f.dutiesDayType.setValue(this.progBasicInfoDetails?.prgDutiDayType);\n      this.dutyDaysChange({\n        value: this.progBasicInfoDetails?.prgDutiDayType\n      });\n      this.f.dayCount.setValue(this.progBasicInfoDetails?.prgNoDutyDays);\n      this.dutyDayTypeId = this.progBasicInfoDetails?.prgDutiDayType;\n      this.f.examPass.setValue(this.progBasicInfoDetails?.prgIsPassExaRequ);\n      this.f.rectMand.setValue(this.progBasicInfoDetails?.prgIsRecitTimeMand);\n      this.f.isAlsard.setValue(this.progBasicInfoDetails?.prgIsSard);\n      this.isSardChange({\n        value: this.progBasicInfoDetails?.prgIsSard\n      });\n      this.f.recitType.setValue(this.progBasicInfoDetails?.prgRecitType);\n      this.isSardTimesChange({\n        value: this.progBasicInfoDetails?.prgRecitType\n      });\n      this.progRecitationTimes = this.progBasicInfoDetails?.prgRecitTms ? this.progBasicInfoDetails?.prgRecitTms.filter(i => i.huffno !== ProgramDayTaskRecitationType.limited).map(item => ({\n        progRecFrom: item.recitFrom,\n        progRecTo: item.recitTo\n      })) : [];\n      this.programTypesList = this.progBasicInfoDetails?.prgTps ? this.progBasicInfoDetails?.prgTps.map(item => ({\n        progTypeId: item.id\n      })) : [];\n      if (this.programTypesList && this.programTypesList.length > 0 && this.programTypesList) {\n        this.selectedTypeProg = this.collectionOfLookup.PROG_TYPES?.find(x => x.id == this.programTypesList[0].progTypeId)?.id;\n      }\n      this.progWeeklyDayList = this.progBasicInfoDetails?.prgWeekDutiDas ? this.progBasicInfoDetails?.prgWeekDutiDas.map(item => ({\n        progWeeklyDay: item.id\n      })) : [];\n      this.programRatingList = this.progBasicInfoDetails?.prgRats ? this.progBasicInfoDetails?.prgRats.map(item => ({\n        progRatId: item.id\n      })) : [];\n      this.selectedProgramTypesList = this.progBasicInfoDetails && this.progBasicInfoDetails.progCats ? this.progBasicInfoDetails?.progCats : [];\n      this.progPic = this.progBasicInfoDetails?.progPic;\n    }\n    onSubmit() {\n      this.isSubmit = true;\n      this.resultMessage = {};\n      if (!this.progDutyDaysFreeDaysSelection && this.progWeeklyDayList.length === 0) {\n        return;\n      }\n      //form is valid\n      if (this.baseInfoForm.valid && this.programTypesList.length > 0 && this.selectedProgramTypesList.length > 0) {\n        // 1- fill EDit model\n        if (this.progBasicInfoDetails) {\n          this.baseicInfoProgrmEditModel = {\n            progId: this.progBasicInfoDetails?.id,\n            progSharedWith: this.baseInfoForm.value.shareWith,\n            progAvableDtyTime: this.baseInfoForm.value.dutyTime,\n            progAllowedDtyDay: this.baseInfoForm.value.availableDuty,\n            progIdea: this.baseInfoForm.value.ideaProg,\n            progGoal: this.baseInfoForm.value.goalProg,\n            progVision: this.baseInfoForm.value.visionProg,\n            progMthd: this.baseInfoForm.value.pathProg,\n            progAdva: this.baseInfoForm.value.advantageProg,\n            progPldgtxt: this.baseInfoForm.value.textPledge,\n            progIsPasJinExm: this.baseInfoForm.value.examPass,\n            progIsRecTimeMand: this.baseInfoForm.value.rectMand,\n            progRecType: this.baseInfoForm.value.recitType,\n            proRatTyps: this.programRatingList,\n            progRecitTimes: this.progRecitationTimes,\n            progCats: this.selectedProgramTypesList?.map(item => ({\n              progCatId: item.catId || item.id\n            })),\n            prgDura: this.baseInfoForm.value.durationProg,\n            progDtyDays: this.progWeeklyDayList,\n            progTypes: this.programTypesList,\n            progName: this.baseInfoForm.value.progName,\n            progCountDtyDay: this.baseInfoForm.value.dayCount,\n            dutyDayTypeId: this.dutyDayTypeId,\n            progIsRecitationEna: this.baseInfoForm.value.isAlsard,\n            progPic: this.progPic\n          };\n          if (this.baseicInfoProgrmEditModel?.prgDura && this.progBasicInfoDetails?.prgDura) if (this.baseicInfoProgrmEditModel?.prgDura < this.progBasicInfoDetails?.prgDura && this.progBasicInfoDetails.prgPasuDate != null) {\n            this.resultMessage = {\n              message: this.translate.instant('PROGRAM_BASIC_INFO.DURATION_VALIDATION'),\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          } else {\n            this.editBasicInfoProgrm();\n          }\n          // send edit model to api\n        } else {\n          // 1- fill add model\n          this.baseicInfoProgrmModel = {\n            progName: this.baseInfoForm.value.progName,\n            progSharedWith: this.baseInfoForm.value.shareWith,\n            progTypes: this.programTypesList,\n            progDura: this.baseInfoForm.value.durationProg,\n            progAvableDtyTime: this.baseInfoForm.value.dutyTime,\n            progAllowedDtyDay: this.baseInfoForm.value.availableDuty,\n            progIdea: this.baseInfoForm.value.ideaProg,\n            progGoal: this.baseInfoForm.value.goalProg,\n            progVision: this.baseInfoForm.value.visionProg,\n            progMthd: this.baseInfoForm.value.pathProg,\n            progAdva: this.baseInfoForm.value.advantageProg,\n            progPldgtxt: this.baseInfoForm.value.textPledge,\n            progDtyDaytyp: this.baseInfoForm.value.dutiesDayType,\n            progCountDtyDay: this.baseInfoForm.value.dayCount,\n            progIsPasJinExm: this.baseInfoForm.value.examPass,\n            progIsRecTimeMand: this.baseInfoForm.value.rectMand,\n            progIsRecitationEna: this.baseInfoForm.value.isAlsard,\n            progRecType: this.baseInfoForm.value.recitType,\n            proRatTyps: this.programRatingList,\n            progDtyDays: this.progWeeklyDayList,\n            progRecitTimes: this.progRecitationTimes,\n            progCats: this.selectedProgramTypesList?.map(item => ({\n              progCatId: item.id\n            })),\n            progPic: this.progPic\n          };\n          this.addBasicInfoProgrm();\n        }\n        // 2- send add  model to api\n      }\n      // form is invalid\n      else {\n        this.resultMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } //close else if in case form not invalid\n    }\n    addBasicInfoProgrm() {\n      this.BasicInfoService.addBasicIfoProgram(this.baseicInfoProgrmModel || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.router.navigate(['/program/edit-program/' + res.data]);\n        } else {\n          this.alert.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    editBasicInfoProgrm() {\n      if (this.progBasicInfoDetails?.prgIsConSarOrTas == true && this.baseInfoForm.value.isAlsard == false) {\n        this.alert.alert(this.translate.currentLang === LanguageEnum.en ? \"Can not edit\" : \"لا تستطيع التعديل\", this.translate.currentLang === LanguageEnum.en ? \"You can not change program to no sard and programe contains of task tasmea or sard stuent\" : \"لا يمكن تعديل البرنامج الى لايوجد سرد مع احتوائة على مهمة سرد طلاب او تسميع\");\n        return;\n      }\n      this.BasicInfoService.updateBasicIfoProgram(this.baseicInfoProgrmEditModel || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.alert.success(res.message || '');\n        } else {\n          this.alert.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onCancel() {\n      this.router.navigate([\"/program\"]);\n    }\n    dutyDaysChange(event) {\n      this.progDutyDaysFreeDaysSelection = this.collectionOfLookup.DUTY_TYPES?.filter(i => i.id === event.value)[0]?.huffazId === ProgramDutiesEnum.FreeDays;\n    }\n    isSardChange(event) {\n      this.isSardEnabled = event.value;\n      this.changSard();\n    }\n    isSardTimesChange(event) {\n      this.isSardTimesEnabled = this.collectionOfLookup.SARD_TYPES?.filter(i => i.id === event.value)[0]?.huffazId === ProgramDayTaskRecitationType.unlimited;\n      if (!this.isSardTimesEnabled) {\n        this.progRecitationTimes = [];\n      }\n    }\n    addProgTypeToList(item, event) {\n      this.programTypesList = [];\n      this.programTypesList?.push({\n        progTypeId: item.id\n      });\n      // this.programTypesList?.push({progTypeId:item.id});\n      // if (event.checked  ){\n      //   this.programTypesList?.push({progTypeId:item.id});\n      // }\n      // else{\n      //   let it = this.programTypesList.filter(i => i.progTypeId === item.id)[0];\n      //   const ind = this.programTypesList?.indexOf(it);\n      //   if (ind > -1) {\n      //     this.programTypesList?.splice(ind, 1);\n      //   }\n      // }\n    }\n    progTypeChecked(item) {\n      return this.programTypesList.some(it => it.progTypeId === item.id);\n    }\n    addProgRatingToList(item, event) {\n      if (event.checked) {\n        this.programRatingList?.push({\n          progRatId: item.id\n        });\n      } else {\n        let it = this.programRatingList.filter(i => i.progRatId === item.id)[0];\n        const ind = this.programRatingList?.indexOf(it);\n        if (ind > -1) {\n          this.programRatingList?.splice(ind, 1);\n        }\n      }\n    }\n    progProgRatingChecked(item) {\n      return this.programRatingList.some(it => it.progRatId === item.id);\n    }\n    addProgWeeklyDayToList(item, event) {\n      if (event.checked) {\n        this.progWeeklyDayList?.push({\n          progWeeklyDay: item.id\n        });\n      } else {\n        let it = this.progWeeklyDayList.filter(i => i.progWeeklyDay === item.id)[0];\n        const ind = this.progWeeklyDayList?.indexOf(it);\n        if (ind > -1) {\n          this.progWeeklyDayList?.splice(ind, 1);\n        }\n      }\n    }\n    progWeeklyDaysChecked(item) {\n      return this.progWeeklyDayList.some(it => it.progWeeklyDay === item.id);\n    }\n    addRecitationTimeToList() {\n      var timeExist = this.progRecitationTimes.filter(i => i.progRecFrom === this.recitFrom && i.progRecTo === this.recitTo);\n      if (timeExist.length <= 0) {\n        var start = new Date();\n        var end = new Date();\n        start.setHours(Number(this.recitFrom.split(':')[0]));\n        start.setMinutes(Number(this.recitFrom.split(':')[1]));\n        end.setHours(Number(this.recitTo.split(':')[0]));\n        end.setMinutes(Number(this.recitTo.split(':')[1]));\n        if (this.recitFrom && this.recitTo && start < end) this.progRecitationTimes.push({\n          progRecFrom: this.recitFrom,\n          progRecTo: this.recitTo\n        });\n      }\n    }\n    removeRecitationTimeToList(item) {\n      let it = this.progRecitationTimes.filter(i => i.progRecFrom === item.progRecFrom && i.progRecTo === item.progRecTo)[0];\n      const ind = this.progRecitationTimes?.indexOf(it);\n      if (ind > -1) {\n        this.progRecitationTimes?.splice(ind, 1);\n      }\n    }\n    getDutyDayType(id) {\n      this.dutyDayTypeId = id;\n    }\n    changSard() {\n      this.programService.getCountContainSardStudOrTasmeaTaskInProg(this.progBasicInfoDetails?.id || '').subscribe(res => {\n        //need check on success\n        if (this.progBasicInfoDetails) this.progBasicInfoDetails.prgIsConSarOrTas = res.data;\n      }, error => {\n        //logging\n      });\n    }\n    onFileChange(files) {\n      if (files.length > 0) {\n        if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {\n          this.resultMessage = {\n            message: this.translate.instant('GENERAL.EXTENTION_FILE'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 7,\n            file: element\n          };\n          this.fileUploadModel?.push(fileUploadObj);\n        });\n        this.UploadFiles(this.fileUploadModel);\n      }\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        this.progPic = res.data[0].url;\n        this.fileUploadModel = [];\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    static ɵfac = function ProgramBasicInfoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramBasicInfoComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ProgramBasicInfoService), i0.ɵɵdirectiveInject(i4.LookupService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AlertifyService), i0.ɵɵdirectiveInject(i7.ProgramCategoriesService), i0.ɵɵdirectiveInject(i8.ProgramService), i0.ɵɵdirectiveInject(i9.AttachmentsService), i0.ɵɵdirectiveInject(i10.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramBasicInfoComponent,\n      selectors: [[\"app-program-basic-info\"]],\n      inputs: {\n        progBasicInfoDetails: \"progBasicInfoDetails\"\n      },\n      decls: 158,\n      vars: 148,\n      consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"baseInfoForm\", 3, \"ngSubmit\", \"formGroup\"], [1, \"tab_page\", \"basICinfo_page\"], [1, \"col-md-12\", \"max_h\"], [1, \"row\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"profile\", \"p-0\", \"mb-50\"], [1, \"progpic_background\"], [\"class\", \"img_prgpic\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"file\", 2, \"display\", \"none\", 3, \"change\"], [\"id\", \"upload_link\", 1, \"custom_file_input\", 2, \"text-decoration\", \"none\", 3, \"click\"], [3, \"src\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-6\"], [1, \"form-group\"], [1, \"header_input\"], [\"type\", \"text\", \"formControlName\", \"progName\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"alert alert-danger my-2 \", 4, \"ngIf\"], [\"formControlName\", \"shareWith\", 1, \"form-control\", 3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"example-section\"], [1, \"header_input\", \"mb-3\"], [\"aria-label\", \"Select an option\", 1, \"input-group\", 3, \"ngModel\", \"ngModelOptions\", \"ngClass\"], [3, \"value\", \"ngClass\", \"change\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-3\", \"col-md-6\", \"col-sm-6\"], [\"type\", \"number\", \"formControlName\", \"durationProg\", 1, \"form-control\"], [4, \"ngIf\"], [\"type\", \"time\", \"formControlName\", \"dutyTime\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"number\", \"formControlName\", \"availableDuty\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-6\", \"col-sm-6\"], [\"for\", \"comment\", 1, \"header_input\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"ideaProg\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"goalProg\", 1, \"form-control\", 3, \"ngClass\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"visionProg\", 1, \"form-control\", 3, \"ngClass\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"pathProg\", 1, \"form-control\", 3, \"ngClass\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"advantageProg\", 1, \"form-control\", 3, \"ngClass\"], [\"cols\", \"2\", \"rows\", \"2\", \"formControlName\", \"textPledge\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-xl-6\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"dutiesDayType\", 1, \"example-radio-group\", 3, \"change\", \"ngClass\"], [\"type\", \"number\", \"class\", \"form-control w-50\", \"formControlName\", \"dayCount\", 3, \"placeholder\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [1, \"col-12\", \"my-2\"], [1, \"col-12\"], [1, \"col-xl-5\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [\"formControlName\", \"addSection\", 1, \"form-control\", 3, \"ngClass\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"ngValue\"], [\"type\", \"button\", 1, \"cancel-btn\", \"mx-3\", \"add_sec\", 3, \"click\", \"ngClass\"], [1, \"col-xl-4\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [1, \"badges\", 3, \"ngClass\"], [\"class\", \"badge badge-info p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isAlsard\", 1, \"example-radio-group\", 3, \"change\", \"ngClass\"], [1, \"example-radio-button\", \"ml-2\", \"mr-2\", 3, \"value\"], [\"checked\", \"true\", 1, \"example-radio-button\", \"ml-1\", \"mr-1\", 3, \"value\"], [\"class\", \"col-8\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [1, \"row\", \"justify-content-end\", \"mt-3\", \"m-start\"], [\"type\", \"submit\", 1, \"save-btn\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"img_prgpic\", 3, \"src\"], [1, \"alert\", \"alert-danger\", \"my-2\"], [3, \"value\"], [3, \"change\", \"value\", \"ngClass\"], [1, \"alert\", \"alert-danger\"], [1, \"example-radio-button\", \"ml-2\", \"mr-2\", 3, \"click\", \"value\"], [\"type\", \"number\", \"formControlName\", \"dayCount\", 1, \"form-control\", \"w-50\", 3, \"placeholder\", \"ngClass\"], [3, \"ngClass\"], [1, \"example-margin\", \"ml-1\", \"mr-1\", 3, \"change\", \"checked\"], [1, \"badge\", \"badge-info\", \"p-2\"], [3, \"click\"], [1, \"fas\", \"cursor-pointer\", \"fa-times-circle\"], [1, \"col-8\", 3, \"ngClass\"], [1, \"col-xl-4\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"recitType\", 1, \"example-radio-group\", 3, \"change\"], [\"class\", \"col-xl-8 col-lg-12 col-md-12 col-sm-12\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"col-xl-8\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", 3, \"ngClass\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\"], [\"type\", \"time\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [1, \"header_input\", \"invisible\"], [\"type\", \"button\", 1, \"saveButton\", \"btn\", 3, \"click\"], [1, \"badges\", \"mt-3\"], [\"class\", \"badge badge-info p-2 \", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"ellipsis\", \"mb-0\"], [1, \"fas\", \"fa-times-circle\"]],\n      template: function ProgramBasicInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ProgramBasicInfoComponent_Template_form_ngSubmit_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵelement(5, \"div\", 6);\n          i0.ɵɵtemplate(6, ProgramBasicInfoComponent_img_6_Template, 1, 1, \"img\", 7)(7, ProgramBasicInfoComponent_img_7_Template, 1, 1, \"img\", 7);\n          i0.ɵɵelementStart(8, \"input\", 8, 0);\n          i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_Template_input_change_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileChange($event.target.files));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"a\", 9);\n          i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_Template_a_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r3 = i0.ɵɵreference(9);\n            return i0.ɵɵresetView(fileInput_r3.click());\n          });\n          i0.ɵɵelement(11, \"img\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, ProgramBasicInfoComponent_div_18_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"div\", 12)(21, \"label\", 13);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"select\", 16);\n          i0.ɵɵtemplate(25, ProgramBasicInfoComponent_ng_container_25_Template, 3, 2, \"ng-container\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, ProgramBasicInfoComponent_div_26_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 12)(29, \"div\")(30, \"label\", 19);\n          i0.ɵɵtext(31);\n          i0.ɵɵpipe(32, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"mat-radio-group\", 20);\n          i0.ɵɵtemplate(34, ProgramBasicInfoComponent_mat_radio_button_34_Template, 2, 7, \"mat-radio-button\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(35, ProgramBasicInfoComponent_div_35_Template, 4, 3, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 12)(38, \"label\", 13);\n          i0.ɵɵtext(39);\n          i0.ɵɵpipe(40, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"input\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ProgramBasicInfoComponent_ng_container_42_Template, 3, 1, \"ng-container\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 22)(44, \"div\", 12)(45, \"label\", 13);\n          i0.ɵɵtext(46);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"input\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, ProgramBasicInfoComponent_div_49_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 22)(51, \"div\", 12)(52, \"label\", 13);\n          i0.ɵɵtext(53);\n          i0.ɵɵpipe(54, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(55, \"input\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, ProgramBasicInfoComponent_div_56_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 27)(58, \"div\", 12)(59, \"label\", 28);\n          i0.ɵɵtext(60);\n          i0.ɵɵpipe(61, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(62, \"textarea\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(63, ProgramBasicInfoComponent_div_63_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 30)(65, \"div\", 12)(66, \"label\", 28);\n          i0.ɵɵtext(67);\n          i0.ɵɵpipe(68, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"textarea\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(70, ProgramBasicInfoComponent_div_70_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 30)(72, \"div\", 12)(73, \"label\", 28);\n          i0.ɵɵtext(74);\n          i0.ɵɵpipe(75, \"translate\");\n          i0.ɵɵelementStart(76, \"small\");\n          i0.ɵɵtext(77);\n          i0.ɵɵpipe(78, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(79, \"textarea\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(80, ProgramBasicInfoComponent_div_80_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 30)(82, \"div\", 12)(83, \"label\", 28);\n          i0.ɵɵtext(84);\n          i0.ɵɵpipe(85, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(86, \"textarea\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(87, ProgramBasicInfoComponent_div_87_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 30)(89, \"div\", 12)(90, \"label\", 28);\n          i0.ɵɵtext(91);\n          i0.ɵɵpipe(92, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(93, \"textarea\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(94, ProgramBasicInfoComponent_div_94_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 30)(96, \"div\", 12)(97, \"label\", 13);\n          i0.ɵɵtext(98);\n          i0.ɵɵpipe(99, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(100, \"textarea\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(101, ProgramBasicInfoComponent_div_101_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"div\", 36)(103, \"div\", 12)(104, \"div\")(105, \"label\", 13);\n          i0.ɵɵtext(106);\n          i0.ɵɵpipe(107, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"mat-radio-group\", 37);\n          i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_Template_mat_radio_group_change_108_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.dutyDaysChange($event));\n          });\n          i0.ɵɵtemplate(109, ProgramBasicInfoComponent_ng_container_109_Template, 3, 2, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(110, ProgramBasicInfoComponent_input_110_Template, 2, 7, \"input\", 38)(111, ProgramBasicInfoComponent_div_111_Template, 3, 2, \"div\", 15)(112, ProgramBasicInfoComponent_div_112_Template, 2, 4, \"div\", 39)(113, ProgramBasicInfoComponent_div_113_Template, 4, 3, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(114, \"div\", 40);\n          i0.ɵɵelementStart(115, \"div\", 41)(116, \"div\", 4)(117, \"div\", 42)(118, \"div\", 12)(119, \"label\", 13);\n          i0.ɵɵtext(120);\n          i0.ɵɵpipe(121, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"div\", 43)(123, \"select\", 44)(124, \"option\", 45);\n          i0.ɵɵtext(125);\n          i0.ɵɵpipe(126, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(127, ProgramBasicInfoComponent_ng_container_127_Template, 3, 2, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_Template_button_click_128_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addSection());\n          });\n          i0.ɵɵtext(129);\n          i0.ɵɵpipe(130, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(131, ProgramBasicInfoComponent_div_131_Template, 4, 3, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(132, \"div\", 47)(133, \"div\", 4)(134, \"div\", 48);\n          i0.ɵɵtemplate(135, ProgramBasicInfoComponent_span_135_Template, 4, 1, \"span\", 49);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(136, \"div\", 50)(137, \"div\", 12)(138, \"div\")(139, \"label\", 13);\n          i0.ɵɵtext(140);\n          i0.ɵɵpipe(141, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(142, \"mat-radio-group\", 51);\n          i0.ɵɵlistener(\"change\", function ProgramBasicInfoComponent_Template_mat_radio_group_change_142_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.isSardChange($event));\n          });\n          i0.ɵɵelementStart(143, \"mat-radio-button\", 52);\n          i0.ɵɵtext(144);\n          i0.ɵɵpipe(145, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"mat-radio-button\", 53);\n          i0.ɵɵtext(147);\n          i0.ɵɵpipe(148, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(149, ProgramBasicInfoComponent_div_149_Template, 11, 8, \"div\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(150, ProgramBasicInfoComponent_div_150_Template, 2, 4, \"div\", 55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(151, \"div\", 56)(152, \"button\", 57);\n          i0.ɵɵtext(153);\n          i0.ɵɵpipe(154, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"button\", 58);\n          i0.ɵɵlistener(\"click\", function ProgramBasicInfoComponent_Template_button_click_155_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵtext(156);\n          i0.ɵɵpipe(157, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.baseInfoForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", !ctx.progPic);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.progPic);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.camera, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(16, 71, \"PROGRAM_BASIC_INFO.PROG_NAME\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(115, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.progName.errors && (ctx.f.progName.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(23, 73, \"PROGRAM_BASIC_INFO.SHARE_WITH\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(117, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.SHARED_TYPES);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.shareWith.errors && (ctx.f.shareWith.touched || ctx.isSubmit));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(32, 75, \"PROGRAM_BASIC_INFO.PROG_TYPE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedTypeProg)(\"ngModelOptions\", i0.ɵɵpureFunction0(119, _c1))(\"ngClass\", i0.ɵɵpureFunction1(120, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.PROG_TYPES);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmit && ctx.programTypesList.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(40, 77, \"PROGRAM_BASIC_INFO.PROG_DURATION\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.durationProg.errors && (ctx.f.durationProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(47, 79, \"PROGRAM_BASIC_INFO.PROG_DUTY_TIME\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(122, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.dutyTime.errors && (ctx.f.dutyTime.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(54, 81, \"PROGRAM_BASIC_INFO.PROG_AVAILABLE_DUTY\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(124, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.availableDuty.errors && (ctx.f.availableDuty.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(61, 83, \"PROGRAM_BASIC_INFO.PROG_IDEA\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(126, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.ideaProg.errors && (ctx.f.ideaProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(68, 85, \"PROGRAM_BASIC_INFO.PROG_GOAL\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(128, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.goalProg.errors && (ctx.f.goalProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(75, 87, \"PROGRAM_BASIC_INFO.PROG_VISION\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(78, 89, \"PROGRAM_BASIC_INFO.PROG_VISION_OPTIONAL\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(130, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.visionProg.errors && (ctx.f.visionProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(85, 91, \"PROGRAM_BASIC_INFO.PATH_PROG\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(132, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.pathProg.errors && (ctx.f.pathProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(92, 93, \"PROGRAM_BASIC_INFO.ADVANTAGE_PROG\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(134, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.advantageProg.errors && (ctx.f.advantageProg.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(99, 95, \"PROGRAM_BASIC_INFO.TEXT_PLEDGE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(136, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.textPledge.errors && (ctx.f.textPledge.touched || ctx.isSubmit));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(107, 97, \"PROGRAM_BASIC_INFO.DAY_COUNT\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(138, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.DUTY_TYPES);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.progDutyDaysFreeDaysSelection);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.progDutyDaysFreeDaysSelection && ctx.f.dayCount.errors && (ctx.f.dayCount.touched || ctx.isSubmit));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.progDutyDaysFreeDaysSelection);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.progDutyDaysFreeDaysSelection && ctx.isSubmit && ctx.progWeeklyDayList.length === 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(121, 99, \"PROGRAM_BASIC_INFO.SECTION\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(140, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(126, 101, \"PROGRAM_BASIC_INFO.CHOOSE_SECTION\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allPrograms);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(142, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(130, 103, \"PROGRAM_BASIC_INFO.ADD_SECTION\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmit && ctx.selectedProgramTypesList.length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(144, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.selectedProgramTypesList);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(141, 105, \"PROGRAM_BASIC_INFO.ISALSARD\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(146, _c0, (ctx.progBasicInfoDetails == null ? null : ctx.progBasicInfoDetails.prgPasuDate) != null));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(145, 107, \"PROGRAM_BASIC_INFO.YES\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(148, 109, \"PROGRAM_BASIC_INFO.NO\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSardEnabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(154, 111, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(157, 113, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i11.NgClass, i11.NgForOf, i11.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}[_ngcontent-%COMP%]:root{--background_panner: \\\"\\\"}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page.tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;margin-top:1rem;border-radius:.625rem;padding:1rem}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:65vh;max-height:65vh;overflow-y:auto}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]:lang(en){text-align:left}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .header_input[_ngcontent-%COMP%]{font-size:.875rem;color:#333;font-weight:700}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-size:.875rem;font-weight:700}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .container_haeder[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .saveButton[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:7.5rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .haeder[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto!important}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{margin:0 1rem}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .progpic_background[_ngcontent-%COMP%]{background:var(--background_panner);background-size:cover;height:11.9375rem;width:100%;border-radius:1.875rem;margin-bottom:4rem}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .img_prgpic[_ngcontent-%COMP%]{position:absolute;top:0;left:42.5%!important;top:20%;width:12.5rem;height:12.5rem;border-radius:1rem;z-index:1}.baseInfoForm[_ngcontent-%COMP%]   .basICinfo_page[_ngcontent-%COMP%]   .custom_file_input[_ngcontent-%COMP%]:lang(ar){left:55%!important;position:absolute;bottom:-.063rem;cursor:pointer;z-index:1}.baseInfoForm[_ngcontent-%COMP%]   .m-start[_ngcontent-%COMP%]:lang(en){margin-right:.1rem}.baseInfoForm[_ngcontent-%COMP%]   .m-start[_ngcontent-%COMP%]:lang(ar){margin-left:.1rem}.baseInfoForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;padding:.5rem;border:none;color:#fff;display:block}.baseInfoForm[_ngcontent-%COMP%]   .add_sec[_ngcontent-%COMP%]{width:50%}.baseInfoForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{margin:0 1rem}.baseInfoForm[_ngcontent-%COMP%]   .dimmed-btn[_ngcontent-%COMP%]{opacity:.5!important}\"]\n    });\n  }\n  return ProgramBasicInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}