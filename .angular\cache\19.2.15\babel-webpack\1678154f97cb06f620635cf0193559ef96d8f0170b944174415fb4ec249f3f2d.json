{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let MoveNextByMaxLengthDirective = /*#__PURE__*/(() => {\n  class MoveNextByMaxLengthDirective {\n    _el;\n    constructor(_el) {\n      this._el = _el;\n    }\n    onKeyDown(e) {\n      if (e.srcElement.maxLength === e.srcElement.value.length) {\n        e.preventDefault();\n        let nextControl = e.srcElement.nextElementSibling;\n        // Searching for next similar control to set it focus\n        while (true) {\n          if (nextControl) {\n            if (nextControl.type === e.srcElement.type) {\n              nextControl.focus();\n              return;\n            } else {\n              nextControl = nextControl.nextElementSibling;\n            }\n          } else {\n            return;\n          }\n        }\n      }\n    }\n    static ɵfac = function MoveNextByMaxLengthDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MoveNextByMaxLengthDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: MoveNextByMaxLengthDirective,\n      selectors: [[\"\", \"appMoveNextByMaxLength\", \"\"]],\n      hostBindings: function MoveNextByMaxLengthDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keyup\", function MoveNextByMaxLengthDirective_keyup_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n      }\n    });\n  }\n  return MoveNextByMaxLengthDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}