{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BankAccountListComponent } from './bank-account-list/bank-account-list.component';\nimport { AddEditBankAccountComponent } from './add-edit-bank-account/add-edit-bank-account.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction BankAccountViewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"app-add-edit-bank-account\", 4);\n    i0.ɵɵlistener(\"closeBankAccountForm\", function BankAccountViewComponent_div_3_Template_app_add_edit_bank_account_closeBankAccountForm_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForm($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"idBankAccount\", ctx_r1.idBankAccount);\n  }\n}\nexport let BankAccountViewComponent = /*#__PURE__*/(() => {\n  class BankAccountViewComponent {\n    bankAccountList;\n    openBankAccount = false;\n    idBankAccount;\n    constructor() {}\n    ngOnInit() {}\n    editCardBankaccount(event) {\n      this.openBankAccount = true;\n      this.idBankAccount = event;\n    }\n    closeForm(event) {\n      this.openBankAccount = event;\n      this.bankAccountList?.getListBankAccount();\n    }\n    static ɵfac = function BankAccountViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BankAccountViewComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BankAccountViewComponent,\n      selectors: [[\"app-bank-account-view\"]],\n      viewQuery: function BankAccountViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(BankAccountListComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bankAccountList = _t.first);\n        }\n      },\n      decls: 4,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [3, \"idBankAccount\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeBankAccountForm\", \"idBankAccount\"]],\n      template: function BankAccountViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\")(2, \"app-bank-account-list\", 1);\n          i0.ɵɵlistener(\"idBankAccount\", function BankAccountViewComponent_Template_app_bank_account_list_idBankAccount_2_listener($event) {\n            return ctx.editCardBankaccount($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(3, BankAccountViewComponent_div_3_Template, 2, 1, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.openBankAccount);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, BankAccountListComponent, AddEditBankAccountComponent],\n      encapsulation: 2\n    });\n  }\n  return BankAccountViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}