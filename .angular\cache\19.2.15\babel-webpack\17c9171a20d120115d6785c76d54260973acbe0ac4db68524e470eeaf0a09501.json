{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { StudentDropOutRequestStatusEnum } from 'src/app/core/enums/drop-out-request-enums/student-drop-out-request-status.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nfunction StudentDropOutRequestAdminCardComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 16)(1, \"mat-checkbox\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StudentDropOutRequestAdminCardComponent_section_2_Template_mat_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.studentDropOutRequestFilterRequestAdminModel.checked, $event) || (ctx_r1.studentDropOutRequestFilterRequestAdminModel.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function StudentDropOutRequestAdminCardComponent_section_2_Template_mat_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateAllItemsCheckedCall());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.studentDropOutRequestFilterRequestAdminModel.checked);\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_img_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 18);\n    i0.ɵɵlistener(\"click\", function StudentDropOutRequestAdminCardComponent_img_18_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.studentDetails(ctx_r1.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx_r1.studentDropOutRequestFilterRequestAdminModel.usrId, ctx_r1.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx_r1.studentDropOutRequestFilterRequestAdminModel.progName));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.user_profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 18);\n    i0.ɵɵlistener(\"click\", function StudentDropOutRequestAdminCardComponent_img_19_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.studentDetails(ctx_r1.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx_r1.studentDropOutRequestFilterRequestAdminModel.usrId, ctx_r1.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx_r1.studentDropOutRequestFilterRequestAdminModel.progName));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.studentDropOutRequestFilterRequestAdminModel.avatarLink, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"STUDENT_SUBSCRIBERS.NO_DATA\"), \" \");\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_p_33_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"STUDENT_SUBSCRIBERS.NO_DATA\"), \" \");\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_p_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 10)(1, \"span\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StudentDropOutRequestAdminCardComponent_p_33_span_4_Template, 3, 3, \"span\", 20);\n    i0.ɵɵelementStart(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 3, \"STUDENT_SUBSCRIBERS.REASON_REJECT\"), \" :\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studentDropOutRequestFilterRequestAdminModel.reasonReject);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.studentDropOutRequestFilterRequestAdminModel.reasonReject, \" \");\n  }\n}\nfunction StudentDropOutRequestAdminCardComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function StudentDropOutRequestAdminCardComponent_ng_container_34_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.acceptStudentAdminDropOutRequestEvent());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function StudentDropOutRequestAdminCardComponent_ng_container_34_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rejectStudentAdminDropOutRequestEvent(ctx_r1.studentDropOutRequestFilterRequestAdminModel));\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, \"STUDENT_SUBSCRIBERS.ACCEPT\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"disabled\", ctx_r1.studentDropOutRequestFilterRequestAdminModel.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 5, \"STUDENT_SUBSCRIBERS.REJECT\"), \" \");\n  }\n}\nexport let StudentDropOutRequestAdminCardComponent = /*#__PURE__*/(() => {\n  class StudentDropOutRequestAdminCardComponent {\n    translate;\n    imagesPathesService;\n    rejectStudentDropOutAdminRequest = new EventEmitter();\n    acceptStudentDropOutAdminRequest = new EventEmitter();\n    studentIdToGrid = new EventEmitter();\n    updateAllItemsChecked = new EventEmitter();\n    studentDropOutRequestFilterRequestAdminModel = {\n      totalRows: 0\n    };\n    typeEnum = StudentDropOutRequestStatusEnum.Pending;\n    typeDropOutRequestEnum = StudentDropOutRequestStatusEnum;\n    studentAdminDropOutRequestIds;\n    langEnum = LanguageEnum;\n    requestDate;\n    studentDropOutRequestStatusEnum = StudentDropOutRequestStatusEnum;\n    constructor(translate, imagesPathesService) {\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      if (this.studentDropOutRequestFilterRequestAdminModel?.requestDate) {\n        let requestDateValue = new Date(this.studentDropOutRequestFilterRequestAdminModel.requestDate || '');\n        this.requestDate = new Date(requestDateValue.setDate(requestDateValue.getDate() + 1)).toISOString().slice(0, 10);\n      }\n      if (!this.studentDropOutRequestFilterRequestAdminModel?.avatarLink) {\n        this.studentDropOutRequestFilterRequestAdminModel.avatarLink = this.imagesPathesService.profile;\n      }\n    }\n    studentDetails(id, JoinedProgName) {\n      let UserModel = {\n        progName: JoinedProgName,\n        usrId: id\n      };\n      this.studentIdToGrid.emit(UserModel);\n    }\n    rejectStudentAdminDropOutRequestEvent(studentAdminDropOutRequest) {\n      this.rejectStudentDropOutAdminRequest.emit(studentAdminDropOutRequest);\n    }\n    acceptStudentAdminDropOutRequestEvent() {\n      this.acceptStudentDropOutAdminRequest.emit(this.studentDropOutRequestFilterRequestAdminModel);\n    }\n    updateAllItemsCheckedCall() {\n      this.updateAllItemsChecked.emit(true);\n    }\n    checkNameSpace(str) {\n      let reg = new RegExp(/^ *$/);\n      return str.match(reg) === null;\n    }\n    static ɵfac = function StudentDropOutRequestAdminCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentDropOutRequestAdminCardComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentDropOutRequestAdminCardComponent,\n      selectors: [[\"app-student-drop-out-request-admin-card\"]],\n      inputs: {\n        studentDropOutRequestFilterRequestAdminModel: \"studentDropOutRequestFilterRequestAdminModel\",\n        typeEnum: \"typeEnum\"\n      },\n      outputs: {\n        rejectStudentDropOutAdminRequest: \"rejectStudentDropOutAdminRequest\",\n        acceptStudentDropOutAdminRequest: \"acceptStudentDropOutAdminRequest\",\n        studentIdToGrid: \"studentIdToGrid\",\n        updateAllItemsChecked: \"updateAllItemsChecked\"\n      },\n      decls: 35,\n      vars: 23,\n      consts: [[1, \"card-request\"], [1, \"card_header\"], [\"class\", \"example-section mt-2\", 4, \"ngIf\"], [1, \"card_body\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [\"class\", \"img_user\", 3, \"src\", \"click\", 4, \"ngIf\"], [1, \"mx-1\"], [1, \"name_user\", \"bold\", \"mb-2\", \"ellipsis\", \"p-0\", 3, \"click\", \"title\"], [1, \"program_user\", \"bold\", \"mb-2\", \"ellipsis\", \"p-0\", 3, \"title\"], [1, \"program_reason\"], [1, \"mb-0\", \"pb-2\"], [1, \"program_user\", \"bold\", \"mb-0\"], [\"class\", \" mb-0 \", 4, \"ngIf\"], [1, \"mb-0\"], [\"class\", \" mb-0 pb-2\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"example-section\", \"mt-2\"], [1, \"example-margin\", 3, \"ngModelChange\", \"ngModel\"], [1, \"img_user\", 3, \"click\", \"src\"], [1, \"program_user\", \"bold\", \"mx-1\"], [\"class\", \"  mb-0 \", 4, \"ngIf\"], [1, \"body_footer\"], [1, \"btn\", \"cancel-btn\", 3, \"click\"], [1, \"btn\", \"save-btn\", 3, \"click\"]],\n      template: function StudentDropOutRequestAdminCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, StudentDropOutRequestAdminCardComponent_section_2_Template, 2, 1, \"section\", 2);\n          i0.ɵɵelementStart(3, \"div\")(4, \"span\");\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\");\n          i0.ɵɵtext(10, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 3)(15, \"span\");\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 4);\n          i0.ɵɵtemplate(18, StudentDropOutRequestAdminCardComponent_img_18_Template, 1, 1, \"img\", 5)(19, StudentDropOutRequestAdminCardComponent_img_19_Template, 1, 1, \"img\", 5);\n          i0.ɵɵelementStart(20, \"div\", 6)(21, \"p\", 7);\n          i0.ɵɵlistener(\"click\", function StudentDropOutRequestAdminCardComponent_Template_p_click_21_listener() {\n            return ctx.studentDetails(ctx.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx.studentDropOutRequestFilterRequestAdminModel.usrId, ctx.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx.studentDropOutRequestFilterRequestAdminModel.progName);\n          });\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 8);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"p\", 10)(27, \"span\", 11);\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, StudentDropOutRequestAdminCardComponent_span_30_Template, 3, 3, \"span\", 12);\n          i0.ɵɵelementStart(31, \"span\", 13);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, StudentDropOutRequestAdminCardComponent_p_33_Template, 7, 5, \"p\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, StudentDropOutRequestAdminCardComponent_ng_container_34_Template, 8, 7, \"ng-container\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeDropOutRequestEnum.Pending === ctx.typeEnum);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 16, \"STUDENT_SUBSCRIBERS.TIME\"), \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 18, ctx.studentDropOutRequestFilterRequestAdminModel.requestDate, \"dd/MM/yyyy\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.studentDropOutRequestFilterRequestAdminModel == null ? null : ctx.studentDropOutRequestFilterRequestAdminModel.no);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.studentDropOutRequestFilterRequestAdminModel.avatarLink);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.studentDropOutRequestFilterRequestAdminModel.avatarLink);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.translate.currentLang == ctx.langEnum.en ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn && ctx.checkNameSpace(ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn) ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr && ctx.checkNameSpace(ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr) ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang == ctx.langEnum.en ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn && ctx.checkNameSpace(ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn) ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr && ctx.checkNameSpace(ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr) ? ctx.studentDropOutRequestFilterRequestAdminModel.usrNameAr : ctx.studentDropOutRequestFilterRequestAdminModel.usrNameEn, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate1(\"title\", \"  \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.studentDropOutRequestFilterRequestAdminModel.enProgBatchName : ctx.studentDropOutRequestFilterRequestAdminModel.arProgBatchName, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.studentDropOutRequestFilterRequestAdminModel.enProgBatchName : ctx.studentDropOutRequestFilterRequestAdminModel.arProgBatchName, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 21, \"STUDENT_SUBSCRIBERS.REQUEST_REASON\"), \" : \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.studentDropOutRequestFilterRequestAdminModel.reason);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.studentDropOutRequestFilterRequestAdminModel.reason);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.typeDropOutRequestEnum.Rejected === ctx.typeEnum);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.typeEnum === ctx.typeDropOutRequestEnum.Pending);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.h-90[_ngcontent-%COMP%]{height:90%}.card-request[_ngcontent-%COMP%]{background:#fff;margin:0 .2rem;box-shadow:0 .188rem 1rem #f2f1f1de;border-radius:.75rem;padding:0;overflow:hidden;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .bold[_ngcontent-%COMP%]{font-weight:700;font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_header[_ngcontent-%COMP%]{background-color:var(--main_color);color:#fff;display:flex;justify-content:space-between;align-items:center;padding:.313rem 1.25rem;font-size:.75rem}.card-request[_ngcontent-%COMP%]   .call[_ngcontent-%COMP%], .card-request[_ngcontent-%COMP%]   .vedio[_ngcontent-%COMP%]{width:.93rem;height:.93rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;height:32vh;padding:.625rem 1.25rem 1.25rem;color:#4d4d4d}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(en){text-align:left}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(ar){text-align:right}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(ar){text-align:center}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(en){text-align:center}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{color:var(--main_color);font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .number_request[_ngcontent-%COMP%]{color:#d6d7d8;font-weight:700;font-size:1rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:3.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .program_user[_ngcontent-%COMP%]{color:#333;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:0 1.25rem;margin-bottom:.7rem;margin-top:.5rem}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:0rem;height:2rem;width:46%;min-width:5.5rem;font-size:.7rem}.card-request[_ngcontent-%COMP%]   .container-width[_ngcontent-%COMP%]{flex-basis:12.5rem}.card-request[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:10.625rem}.card-request[_ngcontent-%COMP%]   .program_rejected[_ngcontent-%COMP%]{height:10vh;overflow-y:auto}@media (max-width: 48rem){.ellipsis[_ngcontent-%COMP%]{width:7.813rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;height:25vh}}.ellipsis[_ngcontent-%COMP%]{width:12.5rem}.program_reason[_ngcontent-%COMP%]{max-height:10vh;overflow-y:auto}.card_body[_ngcontent-%COMP%]{padding-bottom:0}.card-request[_ngcontent-%COMP%]{height:38vh}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]{padding:0 .625rem}.card-request[_ngcontent-%COMP%]   .body_footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-align:center}@media (max-width: 48rem){.card-request[_ngcontent-%COMP%]{height:30vh}.program_reason[_ngcontent-%COMP%]{max-height:6vh}}\"]\n    });\n  }\n  return StudentDropOutRequestAdminCardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}