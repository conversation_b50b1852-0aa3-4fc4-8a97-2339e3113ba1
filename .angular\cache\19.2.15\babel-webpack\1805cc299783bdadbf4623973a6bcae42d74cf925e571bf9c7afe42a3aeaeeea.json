{"ast": null, "code": "import { StudentDropOutRequestStatusEnum } from 'src/app/core/enums/drop-out-request-enums/student-drop-out-request-status.enum';\nimport { StudentDropOutTabRequestComponent } from './student-drop-out-tab-request/student-drop-out-tab-request.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction StudentAdminDropOutRequestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-student-drop-out-request-rejected\", 6);\n    i0.ɵɵlistener(\"closePopup\", function StudentAdminDropOutRequestComponent_div_4_Template_app_student_drop_out_request_rejected_closePopup_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForms());\n    })(\"closeRejectedRequest\", function StudentAdminDropOutRequestComponent_div_4_Template_app_student_drop_out_request_rejected_closeRejectedRequest_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeRejectedRequest());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"itemStudentDropOutRequestForReject\", ctx_r1.itemStudentDropOutRequestForReject);\n  }\n}\nfunction StudentAdminDropOutRequestComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-student-advanced-search\", 7);\n    i0.ɵɵlistener(\"closeAdvancedSearch\", function StudentAdminDropOutRequestComponent_div_5_Template_app_student_advanced_search_closeAdvancedSearch_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStudentDropOutRequestAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"filter\", ctx_r1.filter);\n  }\n}\nexport let StudentAdminDropOutRequestComponent = /*#__PURE__*/(() => {\n  class StudentAdminDropOutRequestComponent {\n    studentDropOutTabRequestComponent;\n    filter = {\n      statusNum: StudentDropOutRequestStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    itemStudentDropOutRequestForReject = {};\n    openStudentDropOutRequestRejectOverlay = false;\n    openStudentDropOutRequestAdvancedSearch = false;\n    constructor() {}\n    ngOnInit() {}\n    openRejectRequest(event) {\n      this.itemStudentDropOutRequestForReject = event;\n      this.openStudentDropOutRequestRejectOverlay = !this.openStudentDropOutRequestRejectOverlay;\n    }\n    closeStudentDropOutRequestAdvancedSearch(event) {\n      this.openStudentDropOutRequestAdvancedSearch = false;\n      this.filter = event;\n      this.studentDropOutTabRequestComponent?.getStudentDropOutRequests();\n    }\n    openStudentDropOutRequestAdvancedSearchPopup(event) {\n      this.openStudentDropOutRequestAdvancedSearch = true;\n      this.filter = event;\n    }\n    closeRejectedRequest() {\n      this.openStudentDropOutRequestRejectOverlay = false;\n      this.studentDropOutTabRequestComponent?.getStudentDropOutRequests();\n    }\n    // closeOverlay() {\n    //   this.openStudentDropOutRequestAdvancedSearch = false;\n    //   this.openStudentDropOutRequestAdvancedSearch = false;\n    // }\n    // as per issue number 3250\n    closeForms() {\n      this.openStudentDropOutRequestAdvancedSearch = false;\n      this.openStudentDropOutRequestRejectOverlay = false;\n    }\n    static ɵfac = function StudentAdminDropOutRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentAdminDropOutRequestComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentAdminDropOutRequestComponent,\n      selectors: [[\"app-student-admin-drop-out-request\"]],\n      viewQuery: function StudentAdminDropOutRequestComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StudentDropOutTabRequestComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.studentDropOutTabRequestComponent = _t.first);\n        }\n      },\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\", \"px-0\"], [3, \"closePopup\", \"advancedSearchEvent\", \"itemOfRejectStudentDropOutRequest\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closePopup\", \"closeRejectedRequest\", \"itemStudentDropOutRequestForReject\"], [3, \"closeAdvancedSearch\", \"filter\"]],\n      template: function StudentAdminDropOutRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-student-drop-out-tab-request\", 3);\n          i0.ɵɵlistener(\"closePopup\", function StudentAdminDropOutRequestComponent_Template_app_student_drop_out_tab_request_closePopup_3_listener() {\n            return ctx.closeForms();\n          })(\"advancedSearchEvent\", function StudentAdminDropOutRequestComponent_Template_app_student_drop_out_tab_request_advancedSearchEvent_3_listener($event) {\n            return ctx.openStudentDropOutRequestAdvancedSearchPopup($event);\n          })(\"itemOfRejectStudentDropOutRequest\", function StudentAdminDropOutRequestComponent_Template_app_student_drop_out_tab_request_itemOfRejectStudentDropOutRequest_3_listener($event) {\n            return ctx.openRejectRequest($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, StudentAdminDropOutRequestComponent_div_4_Template, 2, 1, \"div\", 4)(5, StudentAdminDropOutRequestComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.openStudentDropOutRequestRejectOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openStudentDropOutRequestAdvancedSearch);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return StudentAdminDropOutRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}