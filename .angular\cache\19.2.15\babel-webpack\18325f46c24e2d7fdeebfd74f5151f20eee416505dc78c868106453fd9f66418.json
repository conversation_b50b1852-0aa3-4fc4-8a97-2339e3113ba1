{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AdminDashBoardService = /*#__PURE__*/(() => {\n  class AdminDashBoardService {\n    http;\n    getAdminDashBoardStudHonorBoardURL = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-stud-honor-board/';\n    getAdminDashBoardStudStatisticURL = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-stud-statistic/';\n    getAdminDashBoardTimeTasmeaCountURL = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-time-tasmea-count/';\n    getAdminDashBoardStudTotalExamTaskDegreeUrl = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-stud-total-exam-task-degree';\n    getAdminDashBoardStudTotalTaskDegreeUrl = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-stud-total-task-degree';\n    getAdminDashBoardTaskCountUrl = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-task-count';\n    getAdminDashBoardCountMatrialProgUrl = environment.baseUrl + 'AdminDashBoard/get-admin-dash-board-count-matrial-prog';\n    constructor(http) {\n      this.http = http;\n    }\n    getAdminDashBoardStudHonorBoard() {\n      return this.http.get(this.getAdminDashBoardStudHonorBoardURL);\n    }\n    getAdminDashBoardStudStatistic() {\n      return this.http.get(this.getAdminDashBoardStudStatisticURL);\n    }\n    getAdminDashBoardTimeTasmeaCount() {\n      return this.http.get(this.getAdminDashBoardTimeTasmeaCountURL);\n    }\n    getAdminDashBoardStudTotalExamTaskDegree(model) {\n      return this.http.post(this.getAdminDashBoardStudTotalExamTaskDegreeUrl, model);\n    }\n    getAdminDashBoardStudTotalTaskDegree(model) {\n      return this.http.post(this.getAdminDashBoardStudTotalTaskDegreeUrl, model);\n    }\n    getAdminDashBoardTaskCount(model) {\n      return this.http.post(this.getAdminDashBoardTaskCountUrl, model);\n    }\n    getAdminDashBoardCountMatrialProg(model) {\n      return this.http.post(this.getAdminDashBoardCountMatrialProgUrl, model);\n    }\n    static ɵfac = function AdminDashBoardService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminDashBoardService,\n      factory: AdminDashBoardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AdminDashBoardService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}