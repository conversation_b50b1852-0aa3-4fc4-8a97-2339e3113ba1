{"ast": null, "code": "import * as eventUtil from './event.js';\nvar GestureMgr = function () {\n  function GestureMgr() {\n    this._track = [];\n  }\n  GestureMgr.prototype.recognize = function (event, target, root) {\n    this._doTrack(event, target, root);\n    return this._recognize(event);\n  };\n  GestureMgr.prototype.clear = function () {\n    this._track.length = 0;\n    return this;\n  };\n  GestureMgr.prototype._doTrack = function (event, target, root) {\n    var touches = event.touches;\n    if (!touches) {\n      return;\n    }\n    var trackItem = {\n      points: [],\n      touches: [],\n      target: target,\n      event: event\n    };\n    for (var i = 0, len = touches.length; i < len; i++) {\n      var touch = touches[i];\n      var pos = eventUtil.clientToLocal(root, touch, {});\n      trackItem.points.push([pos.zrX, pos.zrY]);\n      trackItem.touches.push(touch);\n    }\n    this._track.push(trackItem);\n  };\n  GestureMgr.prototype._recognize = function (event) {\n    for (var eventName in recognizers) {\n      if (recognizers.hasOwnProperty(eventName)) {\n        var gestureInfo = recognizers[eventName](this._track, event);\n        if (gestureInfo) {\n          return gestureInfo;\n        }\n      }\n    }\n  };\n  return GestureMgr;\n}();\nexport { GestureMgr };\nfunction dist(pointPair) {\n  var dx = pointPair[1][0] - pointPair[0][0];\n  var dy = pointPair[1][1] - pointPair[0][1];\n  return Math.sqrt(dx * dx + dy * dy);\n}\nfunction center(pointPair) {\n  return [(pointPair[0][0] + pointPair[1][0]) / 2, (pointPair[0][1] + pointPair[1][1]) / 2];\n}\nvar recognizers = {\n  pinch: function (tracks, event) {\n    var trackLen = tracks.length;\n    if (!trackLen) {\n      return;\n    }\n    var pinchEnd = (tracks[trackLen - 1] || {}).points;\n    var pinchPre = (tracks[trackLen - 2] || {}).points || pinchEnd;\n    if (pinchPre && pinchPre.length > 1 && pinchEnd && pinchEnd.length > 1) {\n      var pinchScale = dist(pinchEnd) / dist(pinchPre);\n      !isFinite(pinchScale) && (pinchScale = 1);\n      event.pinchScale = pinchScale;\n      var pinchCenter = center(pinchEnd);\n      event.pinchX = pinchCenter[0];\n      event.pinchY = pinchCenter[1];\n      return {\n        type: 'pinch',\n        target: tracks[0].target,\n        event: event\n      };\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}