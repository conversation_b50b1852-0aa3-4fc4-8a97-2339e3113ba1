{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/list\";\nconst _c0 = a0 => ({\n  \"red-border-class\": a0\n});\nfunction AddGroupComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, AddGroupComponent_div_10_div_1_Template, 3, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fc.arRoleName.errors.required);\n  }\n}\nfunction AddGroupComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, AddGroupComponent_div_16_div_1_Template, 3, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fc.enRoleName.errors.required);\n  }\n}\nfunction AddGroupComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"label\", 4);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"app-input-search-list\", 16);\n    i0.ɵɵlistener(\"addSearchItem\", function AddGroupComponent_div_17_Template_app_input_search_list_addSearchItem_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addUser($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"Role_Management.SELECT_GROUP_MEMBERS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"searchList\", ctx_r0.SearchItemList);\n  }\n}\nfunction AddGroupComponent_mat_list_18_mat_list_item_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddGroupComponent_mat_list_18_mat_list_item_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n  if (rf & 2) {\n    const SelectedUser_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", SelectedUser_r4 == null ? null : SelectedUser_r4.usrAvatarUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddGroupComponent_mat_list_18_mat_list_item_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\");\n    i0.ɵɵtemplate(1, AddGroupComponent_mat_list_18_mat_list_item_1_img_1_Template, 1, 1, \"img\", 18)(2, AddGroupComponent_mat_list_18_mat_list_item_1_img_2_Template, 1, 1, \"img\", 18);\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 20);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_mat_list_18_mat_list_item_1_Template_span_click_8_listener() {\n      const SelectedUser_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.delete(SelectedUser_r4));\n    });\n    i0.ɵɵelement(9, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const SelectedUser_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(SelectedUser_r4 == null ? null : SelectedUser_r4.usrAvatarUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", SelectedUser_r4 == null ? null : SelectedUser_r4.usrAvatarUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? SelectedUser_r4.enUsrName : SelectedUser_r4.arUsrName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, SelectedUser_r4.createdOn), \" \");\n  }\n}\nfunction AddGroupComponent_mat_list_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list\");\n    i0.ɵɵtemplate(1, AddGroupComponent_mat_list_18_mat_list_item_1_Template, 11, 6, \"mat-list-item\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.listSelectedUser);\n  }\n}\nfunction AddGroupComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \" \");\n  }\n}\nfunction AddGroupComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.EditData());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"Role_Management.EDIT\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveData());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"Role_Management.SAVE\"), \" \");\n  }\n}\nexport let AddGroupComponent = /*#__PURE__*/(() => {\n  class AddGroupComponent {\n    formBuilder;\n    RoleManagement;\n    _alertify;\n    translate;\n    imagesPathesService;\n    DataForm;\n    isSubmit = false;\n    hideform = new EventEmitter();\n    dataEdit = {\n      arRoleName: '',\n      id: '',\n      enRoleName: ''\n    };\n    listSelectedUser = [];\n    resultMessage = {};\n    usersExceptStudent = [];\n    SearchItemList = [];\n    langEnum = LanguageEnum;\n    constructor(formBuilder, RoleManagement, _alertify, translate, imagesPathesService) {\n      this.formBuilder = formBuilder;\n      this.RoleManagement = RoleManagement;\n      this._alertify = _alertify;\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngAfterViewInit() {\n      if (this.dataEdit.id != '') {\n        this.DataForm.patchValue(this.dataEdit);\n      }\n    }\n    ngOnChanges(changes) {\n      if (changes.dataEdit && this.dataEdit.id != '') {\n        this.DataForm.patchValue(this.dataEdit);\n      }\n    }\n    ngOnInit() {\n      this.buildForm();\n      this.getUsers();\n    }\n    // get-users-except-students\n    getUsers() {\n      this.RoleManagement.getUsersExceptStudents().subscribe(res => {\n        this.usersExceptStudent = res.data;\n        // example for map to can run shared\n        this.usersExceptStudent.forEach(element => {\n          this.SearchItemList.push({\n            enUsrName: element.userName,\n            //element.usrFullNameEn,\n            arUsrName: element.userName,\n            usrAvatarUrl: element.avatarUrl,\n            usrEmail: '',\n            usrId: element.id,\n            createdOn: element.createdOn\n          });\n        });\n      });\n    }\n    addUser(event) {\n      this.listSelectedUser.push(event);\n    }\n    delete(event) {\n      let ind = this.listSelectedUser.indexOf(event);\n      this.listSelectedUser.splice(ind, 1);\n      this.SearchItemList.push(event);\n    }\n    buildForm() {\n      this.DataForm = this.formBuilder.group({\n        arRoleName: ['', Validators.required],\n        enRoleName: ['', Validators.required]\n      });\n    }\n    get fc() {\n      return this.DataForm.controls;\n    }\n    saveData() {\n      this.isSubmit = true;\n      if (this.DataForm.invalid) {\n        return;\n      }\n      let createModel = this.DataForm.value;\n      //statment need to updated based on models as it's recorded as type any\n      createModel.usrs = this.listSelectedUser.map(i => ({\n        usrId: i.usrId\n      }));\n      this.RoleManagement.createRole(this.DataForm.value).subscribe(res => {\n        if (res.isSuccess) {\n          this._alertify.success(res.message || \"\");\n          this.backList();\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    EditData() {\n      this.isSubmit = true;\n      if (this.DataForm.invalid) {\n        return;\n      }\n      if (this.dataEdit.id != '') {\n        this.RoleManagement.editRole({\n          roleId: this.dataEdit.id,\n          ...this.DataForm.value\n        }).subscribe(res => {\n          if (res.isSuccess) {\n            this._alertify.success(res.message || \"\");\n            this.backList();\n          } else {\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    backList() {\n      this.hideform?.emit(false);\n    }\n    static ɵfac = function AddGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddGroupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.RoleManagementService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddGroupComponent,\n      selectors: [[\"app-add-group\"]],\n      inputs: {\n        dataEdit: \"dataEdit\"\n      },\n      outputs: {\n        hideform: \"hideform\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 27,\n      vars: 26,\n      consts: [[1, \"col-md-12\"], [1, \"DataForm\", 3, \"formGroup\"], [1, \"title_add\"], [1, \"form-group\"], [\"for\", \"exampleInputEmail1\", 1, \"label\"], [\"type\", \"text\", \"id\", \"InputNamAr\", \"formControlName\", \"arRoleName\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"type\", \"text\", \"id\", \"InputNamAr\", \"formControlName\", \"enRoleName\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"form-group\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [1, \"row\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [1, \"col-md-6\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [3, \"addSearchItem\", \"searchList\"], [4, \"ngFor\", \"ngForOf\"], [\"mat-list-icon\", \"\", \"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [\"mat-line\", \"\"], [1, \"mat-delete\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [\"mat-list-icon\", \"\", \"alt\", \"\", 3, \"src\"], [1, \"save-btn\", 3, \"click\"]],\n      template: function AddGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1)(2, \"p\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"input\", 5);\n          i0.ɵɵtemplate(10, AddGroupComponent_div_10_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"label\", 4);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 7);\n          i0.ɵɵtemplate(16, AddGroupComponent_div_16_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, AddGroupComponent_div_17_Template, 5, 4, \"div\", 8)(18, AddGroupComponent_mat_list_18_Template, 2, 1, \"mat-list\", 9)(19, AddGroupComponent_div_19_Template, 2, 4, \"div\", 10);\n          i0.ɵɵelementStart(20, \"div\", 11);\n          i0.ɵɵtemplate(21, AddGroupComponent_div_21_Template, 4, 3, \"div\", 12)(22, AddGroupComponent_div_22_Template, 4, 3, \"div\", 12);\n          i0.ɵɵelementStart(23, \"div\", 13)(24, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function AddGroupComponent_Template_button_click_24_listener() {\n            return ctx.backList();\n          });\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.DataForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 14, \"Role_Management.CREATENEWGROUP\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 16, \"Role_Management.GROUPNAMEARABIC\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c0, ctx.fc.arRoleName.errors && (ctx.fc.arRoleName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fc.arRoleName.errors && (ctx.fc.arRoleName.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 18, \"Role_Management.GROUPNAMEENGLISH\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c0, ctx.fc.enRoleName.errors && (ctx.fc.enRoleName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fc.enRoleName.errors && (ctx.fc.enRoleName.touched || ctx.isSubmit));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataEdit.id == \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataEdit.id == \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.dataEdit.id != \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataEdit.id == \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 20, \"QUESTION_BANK.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.MatList, i7.MatListItem, i7.MatDivider, i6.DatePipe, i4.TranslatePipe],\n      styles: [\".DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:1rem}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:100%;padding:.5rem;border:none;color:#fff;display:block}.DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:100%;padding:.5rem;border:none;color:#fff;display:block}.DataForm[_ngcontent-%COMP%]   .red-border-class[_ngcontent-%COMP%]{border:.063rem solid red}.DataForm[_ngcontent-%COMP%]   .mat-list-icon[_ngcontent-%COMP%]{width:2.5rem!important;height:2.5rem!important;border-radius:.625rem!important}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:.75rem!important}.DataForm[_ngcontent-%COMP%]   .mat-delete[_ngcontent-%COMP%]{color:red;cursor:pointer}\"]\n    });\n  }\n  return AddGroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}