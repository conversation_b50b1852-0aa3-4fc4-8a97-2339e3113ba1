{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PeriodicExameComponent = /*#__PURE__*/(() => {\n  class PeriodicExameComponent {\n    constructor() {}\n    ngOnInit() {}\n    static ɵfac = function PeriodicExameComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PeriodicExameComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PeriodicExameComponent,\n      selectors: [[\"app-periodic-exame\"]],\n      decls: 2,\n      vars: 0,\n      template: function PeriodicExameComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"periodic-exame works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return PeriodicExameComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}