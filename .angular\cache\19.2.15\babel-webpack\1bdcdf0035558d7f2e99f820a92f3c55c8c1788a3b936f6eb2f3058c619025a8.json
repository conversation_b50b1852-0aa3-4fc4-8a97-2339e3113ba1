{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar EllipseShape = function () {\n  function EllipseShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.rx = 0;\n    this.ry = 0;\n  }\n  return EllipseShape;\n}();\nexport { EllipseShape };\nvar Ellipse = function (_super) {\n  __extends(Ellipse, _super);\n  function Ellipse(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Ellipse.prototype.getDefaultShape = function () {\n    return new EllipseShape();\n  };\n  Ellipse.prototype.buildPath = function (ctx, shape) {\n    var k = 0.5522848;\n    var x = shape.cx;\n    var y = shape.cy;\n    var a = shape.rx;\n    var b = shape.ry;\n    var ox = a * k;\n    var oy = b * k;\n    ctx.moveTo(x - a, y);\n    ctx.bezierCurveTo(x - a, y - oy, x - ox, y - b, x, y - b);\n    ctx.bezierCurveTo(x + ox, y - b, x + a, y - oy, x + a, y);\n    ctx.bezierCurveTo(x + a, y + oy, x + ox, y + b, x, y + b);\n    ctx.bezierCurveTo(x - ox, y + b, x - a, y + oy, x - a, y);\n    ctx.closePath();\n  };\n  return Ellipse;\n}(Path);\nEllipse.prototype.type = 'ellipse';\nexport default Ellipse;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}