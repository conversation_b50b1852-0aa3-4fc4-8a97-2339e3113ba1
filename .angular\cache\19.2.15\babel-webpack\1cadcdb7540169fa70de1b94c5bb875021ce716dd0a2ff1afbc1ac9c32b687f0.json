{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\r\n * Transform markPoint data item to format used in List by do the following\r\n * 1. Calculate statistic like `max`, `min`, `average`\r\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\r\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\r\n * Filter data which is out of coordinateSystem range\r\n * [dataFilter description]\r\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}