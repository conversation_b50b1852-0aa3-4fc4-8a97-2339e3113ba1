{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as zrUtil from '../core/util.js';\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar Group = function (_super) {\n  __extends(Group, _super);\n  function Group(opts) {\n    var _this = _super.call(this) || this;\n    _this.isGroup = true;\n    _this._children = [];\n    _this.attr(opts);\n    return _this;\n  }\n  Group.prototype.childrenRef = function () {\n    return this._children;\n  };\n  Group.prototype.children = function () {\n    return this._children.slice();\n  };\n  Group.prototype.childAt = function (idx) {\n    return this._children[idx];\n  };\n  Group.prototype.childOfName = function (name) {\n    var children = this._children;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i].name === name) {\n        return children[i];\n      }\n    }\n  };\n  Group.prototype.childCount = function () {\n    return this._children.length;\n  };\n  Group.prototype.add = function (child) {\n    if (child) {\n      if (child !== this && child.parent !== this) {\n        this._children.push(child);\n        this._doAdd(child);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (child.__hostTarget) {\n          throw 'This elemenet has been used as an attachment';\n        }\n      }\n    }\n    return this;\n  };\n  Group.prototype.addBefore = function (child, nextSibling) {\n    if (child && child !== this && child.parent !== this && nextSibling && nextSibling.parent === this) {\n      var children = this._children;\n      var idx = children.indexOf(nextSibling);\n      if (idx >= 0) {\n        children.splice(idx, 0, child);\n        this._doAdd(child);\n      }\n    }\n    return this;\n  };\n  Group.prototype.replace = function (oldChild, newChild) {\n    var idx = zrUtil.indexOf(this._children, oldChild);\n    if (idx >= 0) {\n      this.replaceAt(newChild, idx);\n    }\n    return this;\n  };\n  Group.prototype.replaceAt = function (child, index) {\n    var children = this._children;\n    var old = children[index];\n    if (child && child !== this && child.parent !== this && child !== old) {\n      children[index] = child;\n      old.parent = null;\n      var zr = this.__zr;\n      if (zr) {\n        old.removeSelfFromZr(zr);\n      }\n      this._doAdd(child);\n    }\n    return this;\n  };\n  Group.prototype._doAdd = function (child) {\n    if (child.parent) {\n      child.parent.remove(child);\n    }\n    child.parent = this;\n    var zr = this.__zr;\n    if (zr && zr !== child.__zr) {\n      child.addSelfToZr(zr);\n    }\n    zr && zr.refresh();\n  };\n  Group.prototype.remove = function (child) {\n    var zr = this.__zr;\n    var children = this._children;\n    var idx = zrUtil.indexOf(children, child);\n    if (idx < 0) {\n      return this;\n    }\n    children.splice(idx, 1);\n    child.parent = null;\n    if (zr) {\n      child.removeSelfFromZr(zr);\n    }\n    zr && zr.refresh();\n    return this;\n  };\n  Group.prototype.removeAll = function () {\n    var children = this._children;\n    var zr = this.__zr;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      if (zr) {\n        child.removeSelfFromZr(zr);\n      }\n      child.parent = null;\n    }\n    children.length = 0;\n    return this;\n  };\n  Group.prototype.eachChild = function (cb, context) {\n    var children = this._children;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      cb.call(context, child, i);\n    }\n    return this;\n  };\n  Group.prototype.traverse = function (cb, context) {\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      var stopped = cb.call(context, child);\n      if (child.isGroup && !stopped) {\n        child.traverse(cb, context);\n      }\n    }\n    return this;\n  };\n  Group.prototype.addSelfToZr = function (zr) {\n    _super.prototype.addSelfToZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.addSelfToZr(zr);\n    }\n  };\n  Group.prototype.removeSelfFromZr = function (zr) {\n    _super.prototype.removeSelfFromZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.removeSelfFromZr(zr);\n    }\n  };\n  Group.prototype.getBoundingRect = function (includeChildren) {\n    var tmpRect = new BoundingRect(0, 0, 0, 0);\n    var children = includeChildren || this._children;\n    var tmpMat = [];\n    var rect = null;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      if (child.ignore || child.invisible) {\n        continue;\n      }\n      var childRect = child.getBoundingRect();\n      var transform = child.getLocalTransform(tmpMat);\n      if (transform) {\n        BoundingRect.applyTransform(tmpRect, childRect, transform);\n        rect = rect || tmpRect.clone();\n        rect.union(tmpRect);\n      } else {\n        rect = rect || childRect.clone();\n        rect.union(childRect);\n      }\n    }\n    return rect || tmpRect;\n  };\n  return Group;\n}(Element);\nGroup.prototype.type = 'group';\nexport default Group;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}