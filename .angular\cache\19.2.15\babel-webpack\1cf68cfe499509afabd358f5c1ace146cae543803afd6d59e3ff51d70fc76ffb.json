{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport { WhiskerBoxCommonMixin } from '../helper/whiskerBoxCommon.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar CandlestickSeriesModel = /** @class */function (_super) {\n  __extends(CandlestickSeriesModel, _super);\n  function CandlestickSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CandlestickSeriesModel.type;\n    _this.defaultValueDimensions = [{\n      name: 'open',\n      defaultTooltip: true\n    }, {\n      name: 'close',\n      defaultTooltip: true\n    }, {\n      name: 'lowest',\n      defaultTooltip: true\n    }, {\n      name: 'highest',\n      defaultTooltip: true\n    }];\n    return _this;\n  }\n  /**\r\n   * Get dimension for shadow in dataZoom\r\n   * @return dimension name\r\n   */\n  CandlestickSeriesModel.prototype.getShadowDim = function () {\n    return 'open';\n  };\n  CandlestickSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    var itemLayout = data.getItemLayout(dataIndex);\n    return itemLayout && selectors.rect(itemLayout.brushRect);\n  };\n  CandlestickSeriesModel.type = 'series.candlestick';\n  CandlestickSeriesModel.dependencies = ['xAxis', 'yAxis', 'grid'];\n  CandlestickSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    layout: null,\n    clip: true,\n    itemStyle: {\n      color: '#eb5454',\n      color0: '#47b262',\n      borderColor: '#eb5454',\n      borderColor0: '#47b262',\n      borderColorDoji: null,\n      // borderColor: '#d24040',\n      // borderColor0: '#398f4f',\n      borderWidth: 1\n    },\n    emphasis: {\n      itemStyle: {\n        borderWidth: 2\n      }\n    },\n    barMaxWidth: null,\n    barMinWidth: null,\n    barWidth: null,\n    large: true,\n    largeThreshold: 600,\n    progressive: 3e3,\n    progressiveThreshold: 1e4,\n    progressiveChunkMode: 'mod',\n    animationEasing: 'linear',\n    animationDuration: 300\n  };\n  return CandlestickSeriesModel;\n}(SeriesModel);\nmixin(CandlestickSeriesModel, WhiskerBoxCommonMixin, true);\nexport default CandlestickSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}