{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from '../../../../core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active_group_item: a0\n});\nfunction UserManagementRoleListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function UserManagementRoleListComponent_div_6_Template_div_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getDetails(item_r2.id));\n    });\n    i0.ɵɵelementStart(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, item_r2.id == ctx_r2.selectedRoleId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.translate.currentLang === ctx_r2.langEnum.en ? item_r2.enRoleName : item_r2.arRoleName, \" \");\n  }\n}\nexport let UserManagementRoleListComponent = /*#__PURE__*/(() => {\n  class UserManagementRoleListComponent {\n    translate;\n    listRole;\n    selectedRoleId;\n    filterRole = new EventEmitter();\n    getRoleDetails = new EventEmitter();\n    langEnum = LanguageEnum;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    ngOnInit() {}\n    loadRoles(event) {\n      this.filterRole?.emit(event);\n    }\n    getDetails(id) {\n      this.getRoleDetails?.emit(id);\n    }\n    static ɵfac = function UserManagementRoleListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserManagementRoleListComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserManagementRoleListComponent,\n      selectors: [[\"app-user-management-role-list\"]],\n      inputs: {\n        listRole: \"listRole\",\n        selectedRoleId: \"selectedRoleId\"\n      },\n      outputs: {\n        filterRole: \"filterRole\",\n        getRoleDetails: \"getRoleDetails\"\n      },\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"col-12\", \"group-list\"], [1, \"mb-4\"], [3, \"searchTerm\"], [1, \"scroll_list_group\"], [\"class\", \"group_item\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"group_item\", 3, \"click\", \"ngClass\"]],\n      template: function UserManagementRoleListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"p\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"app-search-input\", 2);\n          i0.ɵɵlistener(\"searchTerm\", function UserManagementRoleListComponent_Template_app_search_input_searchTerm_4_listener($event) {\n            return ctx.loadRoles($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, UserManagementRoleListComponent_div_6_Template, 3, 4, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"Role_Management.TITLE\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listRole);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i1.TranslatePipe],\n      styles: [\".group-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;overflow-y:auto;margin-top:1rem}.group-list[_ngcontent-%COMP%]:lang(en){text-align:left}.group-list[_ngcontent-%COMP%]:lang(ar){text-align:right}.group-list[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;letter-spacing:0;color:#333;opacity:1}.group-list[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1}.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]{overflow-y:auto}.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .group_item[_ngcontent-%COMP%]{cursor:pointer;width:100%;padding:.625rem 1rem;background:#f2f1f14d 0% 0% no-repeat padding-box;border-radius:.75rem;margin:1rem 0;overflow:hidden}.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .group_item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;color:gray;margin:0;display:inline-block;width:80%}.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .active_group_item[_ngcontent-%COMP%]{background:var(--second_color) 0% 0% no-repeat padding-box;box-shadow:0 .063rem .25rem #4d4d4d}.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .active_group_item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .active_group_item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fff}@media (max-width: 64rem){.group-list[_ngcontent-%COMP%]   .scroll_list_group[_ngcontent-%COMP%]   .group_item[_ngcontent-%COMP%]{padding:.438rem .563rem}}\"]\n    });\n  }\n  return UserManagementRoleListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}