{"ast": null, "code": "import { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/common\";\nfunction InterviewCallDetailsComponent_ng_container_3_app_jitsi_call_interview_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-jitsi-call-interview\", 5);\n    i0.ɵɵlistener(\"endCallEvent\", function InterviewCallDetailsComponent_ng_container_3_app_jitsi_call_interview_2_Template_app_jitsi_call_interview_endCallEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.endCall($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"callJoinModel\", ctx_r1.callJoinModel)(\"jitsiSettingOptions\", ctx_r1.jitsiSettingOptions);\n  }\n}\nfunction InterviewCallDetailsComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵtemplate(2, InterviewCallDetailsComponent_ng_container_3_app_jitsi_call_interview_2_Template, 1, 2, \"app-jitsi-call-interview\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.callJoinModel);\n  }\n}\nexport let InterviewCallDetailsComponent = /*#__PURE__*/(() => {\n  class InterviewCallDetailsComponent {\n    route;\n    translate;\n    dialog;\n    router;\n    callId;\n    usrId;\n    isMod;\n    callJoinModel;\n    jitsiSettingOptions;\n    langEnum = LanguageEnum;\n    // not used\n    callEntityId;\n    callModuleTypehuffazId;\n    modId;\n    isVideoMute;\n    constructor(route, translate, dialog, router) {\n      this.route = route;\n      this.translate = translate;\n      this.dialog = dialog;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.callId = this.route.snapshot.params.callId;\n      this.usrId = this.route.snapshot.params.usrId;\n      this.callModuleTypehuffazId = this.route.snapshot.params.CallType;\n      this.modId = this.route.snapshot.params.modId;\n      this.isVideoMute = this.route.snapshot.params.isVideoMute;\n      this.callEntityId = this.route.snapshot.params.entityId;\n      this.initCall();\n    }\n    initCall() {\n      this.callJoinModel = {\n        callId: this.callId,\n        usrId: this.usrId,\n        //JSON.parse(localStorage.getItem(\"user\") || '{}').id,\n        isMod: false\n      };\n      this.jitsiSettingOptions = {\n        isVideoMute: this.isVideoMute,\n        startCallType: StartCallTypesEnum.JoinMode,\n        endCallType: EndCallTypesEnum.InterviewTeacherUsrEndCall\n      };\n    }\n    endCall(event) {\n      this.router.navigateByUrl('/teacher/view-teacher-profile-details');\n    }\n    static ɵfac = function InterviewCallDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InterviewCallDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InterviewCallDetailsComponent,\n      selectors: [[\"app-interview-call-details\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"mt-3\", \"d-flex\", \"justify-content-center\"], [1, \"call-integ\"], [4, \"ngIf\"], [3, \"callJoinModel\", \"jitsiSettingOptions\", \"endCallEvent\", 4, \"ngIf\"], [3, \"endCallEvent\", \"callJoinModel\", \"jitsiSettingOptions\"]],\n      template: function InterviewCallDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, InterviewCallDetailsComponent_ng_container_3_Template, 3, 1, \"ng-container\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.callJoinModel);\n        }\n      },\n      dependencies: [i4.NgIf],\n      encapsulation: 2\n    });\n  }\n  return InterviewCallDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}