{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ProgramDayTasksDetails } from 'src/app/core/enums/programs/program-day-tasks-details.enum';\nimport { ProgramDutyDaysTaskViewMoodEnum } from 'src/app/core/enums/programs/program-duty-days-task-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/certificatesAndExamResults-services/certificates-and-exam-results.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"STUD_AGENDA.TEST_DONE\"), \" \");\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"STUD_AGENDA.NOT_TEDTED\"), \" \");\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-question-template\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const question_r6 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionTemplate\", question_r6)(\"viewMode\", true)(\"questionViewMood\", ctx_r4.questionViewMoodEnum);\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_div_18_div_1_Template, 2, 3, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.questionItems);\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 13);\n    i0.ɵɵlistener(\"opened\", function DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_Template_mat_expansion_panel_opened_0_listener() {\n      const ctx_r1 = i0.ɵɵrestoreView(_r1);\n      const examResult_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setOpened(i_r4, examResult_r3));\n    })(\"closed\", function DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_Template_mat_expansion_panel_closed_0_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setClosed(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\", 14)(3, \"div\", 15)(4, \"div\", 16)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 16);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 16)(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtemplate(15, DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_ng_container_15_Template, 4, 3, \"ng-container\", 10)(16, DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_ng_container_16_Template, 4, 3, \"ng-container\", 10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 17);\n    i0.ɵɵtemplate(18, DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_div_18_Template, 2, 1, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const examResult_r3 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(7, 9, \"PROGRAM_DAY_TASKS.DAY\"), \"-\", examResult_r3.order, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", examResult_r3.subTaskDate, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(13, 11, \"STUD_AGENDA.DEGREE\"), \" \", examResult_r3.gradTask, \" / \", examResult_r3.studGrad, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (examResult_r3 == null ? null : examResult_r3.isAnsw) && (examResult_r3 == null ? null : examResult_r3.isprev));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(examResult_r3 == null ? null : examResult_r3.isAnsw) && !(examResult_r3 == null ? null : examResult_r3.isprev));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.questionItems && ctx_r4.questionItems.length > 0);\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"mat-accordion\");\n    i0.ɵɵtemplate(3, DashboardStudentsProgramResultComponent_ng_container_18_mat_expansion_panel_3_Template, 19, 13, \"mat-expansion-panel\", 12);\n    i0.ɵɵpipe(4, \"myfilter\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(4, 1, ctx_r4.examStudBatchNotCompletedReponse, ctx_r4.filterargs));\n  }\n}\nfunction DashboardStudentsProgramResultComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"div\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let MyFilterPipe = /*#__PURE__*/(() => {\n  class MyFilterPipe {\n    transform(items, filter) {\n      if (!items || !filter || !filter.order) {\n        return items;\n      }\n      return items.filter(item => item.order === filter.order);\n    }\n    static ɵfac = function MyFilterPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MyFilterPipe)();\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"myfilter\",\n      type: MyFilterPipe,\n      pure: false\n    });\n  }\n  return MyFilterPipe;\n})();\nexport let DashboardStudentsProgramResultComponent = /*#__PURE__*/(() => {\n  class DashboardStudentsProgramResultComponent {\n    examResultsService;\n    translate;\n    alertfy;\n    examResultDetails = new EventEmitter();\n    allBatches = [];\n    batchRequest = {\n      studId: '',\n      skip: 0,\n      take: 2147483647\n    };\n    examStudBatchNotCompletedReponse = [];\n    searchKey = 0;\n    filterargs = {\n      order: undefined\n    };\n    examStudBatchNotCompletedRequest = {\n      studId: '',\n      skip: 0,\n      take: 2147483647\n    };\n    langEnum = LanguageEnum;\n    currentUser;\n    batId;\n    showTap = ProgramDayTasksDetails.TaskDailyTest;\n    statusEnum = ProgramDayTasksDetails;\n    // selectedIndex = 0;\n    questionItems = [];\n    questionViewMoodEnum = ProgramDutyDaysTaskViewMoodEnum.student;\n    currentlyOpenedItemIndex = -1;\n    constructor(examResultsService, translate, alertfy) {\n      this.examResultsService = examResultsService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem('user') || '{}');\n      this.getAllBatches();\n    }\n    // get-all-batch\n    getAllBatches() {\n      this.batchRequest.studId = this.currentUser?.id || '';\n      this.examResultsService.getStudBatchNotCompleted(this.batchRequest).subscribe(res => {\n        if (res.isSuccess) {\n          this.allBatches = res.data;\n          if (this.allBatches && this.allBatches.length > 0) {\n            this.batId = this.allBatches[0].batId;\n          }\n        } else {}\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      if (this.showTap == ProgramDayTasksDetails.TaskTestPhased) {\n        this.onTestPhasedStatusChange();\n      }\n      if (this.showTap == ProgramDayTasksDetails.TaskDailyTest) {\n        this.onDailyStatusChange();\n      }\n    }\n    onTestPhasedStatusChange() {\n      this.showTap = ProgramDayTasksDetails.TaskTestPhased;\n      this.getAllExamList();\n    }\n    onDailyStatusChange() {\n      this.showTap = ProgramDayTasksDetails.TaskDailyTest;\n      this.getAllExamList();\n    }\n    getAllExamList() {\n      this.examStudBatchNotCompletedRequest.studId = this.currentUser?.id;\n      this.examStudBatchNotCompletedRequest.batId = this.batId;\n      this.examStudBatchNotCompletedRequest.taskTyp = this.showTap;\n      this.examResultsService.getExamStudBatchNotCompleted(this.examStudBatchNotCompletedRequest).subscribe(res => {\n        if (res.isSuccess) {\n          this.examStudBatchNotCompletedReponse = res.data;\n          // this.questionItems = JSON.parse(this.examResultDetails?.answ || \"{}\");\n          // if (this.examStudBatchNotCompletedReponse && this.examStudBatchNotCompletedReponse.length > 0) {\n          //   let firstItemSelected = this.examStudBatchNotCompletedReponse[0];\n          //    this.loadExamDetilas(firstItemSelected)\n          // }\n          // else {\n          //   this.examResultDetails.emit()\n          // }\n        } else {\n          this.alertfy.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    setOpened(itemIndex, item) {\n      this.currentlyOpenedItemIndex = itemIndex;\n      this.questionItems = JSON.parse(item?.answ || \"{}\").questions;\n    }\n    setClosed(itemIndex) {\n      if (this.currentlyOpenedItemIndex === itemIndex) {\n        this.currentlyOpenedItemIndex = -1;\n      }\n    }\n    static ɵfac = function DashboardStudentsProgramResultComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardStudentsProgramResultComponent)(i0.ɵɵdirectiveInject(i1.CertificatesAndExamResultsService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardStudentsProgramResultComponent,\n      selectors: [[\"app-dashboard-students-program-result\"]],\n      outputs: {\n        examResultDetails: \"examResultDetails\"\n      },\n      decls: 20,\n      vars: 23,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"pt-4\"], [1, \"header\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\"], [\"bindLabel\", \"enProgBatchName\", \"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"items\", \"ngModel\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\", \"group_header\", \"mt-2\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\", \"mt-3\"], [\"type\", \"number\", 1, \"px-3\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"-md\", \"input-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"my-3\"], [4, \"ngIf\"], [1, \"card_container\", \"w-100\"], [\"class\", \"my-3\", 3, \"opened\", \"closed\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-3\", 3, \"opened\", \"closed\"], [1, \"qestionHeader\"], [1, \"row\", \"w-100\", \"my-2\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-3\", \"col-xs-3\", \"py-2\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"user__height\"], [4, \"ngFor\", \"ngForOf\"], [3, \"questionTemplate\", \"viewMode\", \"questionViewMood\"], [1, \"No_data\", \"px-5\"]],\n      template: function DashboardStudentsProgramResultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"ng-select\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function DashboardStudentsProgramResultComponent_Template_ng_select_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function DashboardStudentsProgramResultComponent_Template_ng_select_change_6_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardStudentsProgramResultComponent_Template_div_click_8_listener() {\n            return ctx.onDailyStatusChange();\n          });\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardStudentsProgramResultComponent_Template_div_click_11_listener() {\n            return ctx.onTestPhasedStatusChange();\n          });\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"input\", 8);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function DashboardStudentsProgramResultComponent_Template_input_ngModelChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filterargs.order, $event) || (ctx.filterargs.order = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 9);\n          i0.ɵɵtemplate(18, DashboardStudentsProgramResultComponent_ng_container_18_Template, 5, 4, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, DashboardStudentsProgramResultComponent_ng_container_19_Template, 5, 3, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 11, \"STU_RESULT_PROGRAM.EXAM_RESULTS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"items\", ctx.allBatches);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.showTap == ctx.statusEnum.TaskDailyTest));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 13, \"STU_RESULT_PROGRAM.DAILY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.showTap == ctx.statusEnum.TaskTestPhased));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 15, \"STU_RESULT_PROGRAM.PHASE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 17, \"GENERAL.SEARCH\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filterargs.order);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.examStudBatchNotCompletedReponse && ctx.examStudBatchNotCompletedReponse.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.examStudBatchNotCompletedReponse || ctx.examStudBatchNotCompletedReponse && ctx.examStudBatchNotCompletedReponse.length == 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectComponent, i6.DefaultValueAccessor, i6.NumberValueAccessor, i6.NgControlStatus, i6.NgModel, i2.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background-color:#fbfbfb;border-radius:1.25rem;height:85vh!important}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(ar):lang(er){text-align:left}.program_result[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:1.3rem;color:#333;font-weight:700}.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{display:flex;width:100%}.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;color:#333;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem;width:50%;font-size:1rem;font-weight:700}.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--main_color);color:#fff}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]{height:53vh;overflow-y:auto}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]{display:flex!important;align-items:center;justify-content:space-between;padding:.5rem;background:#fff;border-radius:.625rem;text-decoration:none}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]   .info_user[_ngcontent-%COMP%]{text-decoration:none}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]   .prog_name[_ngcontent-%COMP%]{color:gray;font-size:.75rem;font-family:Almarai-Light}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]   .day_name[_ngcontent-%COMP%]{color:#666;font-size:1.0625rem;font-weight:700}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]   .degree[_ngcontent-%COMP%]{color:#666;font-size:1.5rem;font-weight:700}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result.active[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none;cursor:pointer}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result.active[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result.active[_ngcontent-%COMP%]   .prog_name[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result.active[_ngcontent-%COMP%]   .day_name[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result.active[_ngcontent-%COMP%]   .degree[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:hover   .date[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:hover   .prog_name[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:hover   .day_name[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]   .card_result[_ngcontent-%COMP%]:hover   .degree[_ngcontent-%COMP%]{color:#fff}.program_result[_ngcontent-%COMP%]{height:auto}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]{height:62vh;overflow-y:auto}.program_result[_ngcontent-%COMP%]   .qestionHeader[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .qestionHeader[_ngcontent-%COMP%]:lang(ar){text-align:right}@media screen and (max-width: 64rem){.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{padding:.25rem .625rem}.qestionHeader[_ngcontent-%COMP%]{font-size:.9rem}}@media screen and (min-width: 100rem){.program_result[_ngcontent-%COMP%]{height:90vh!important}.program_result[_ngcontent-%COMP%]   .card_container[_ngcontent-%COMP%]{height:67vh!important}}\"]\n    });\n  }\n  return DashboardStudentsProgramResultComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}