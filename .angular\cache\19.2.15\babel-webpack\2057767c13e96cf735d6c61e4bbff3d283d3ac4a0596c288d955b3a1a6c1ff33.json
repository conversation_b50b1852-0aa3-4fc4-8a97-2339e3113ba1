{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/bank-account/bank-account.service\";\nimport * as i3 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction AddEditBankAccountComponent_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"BANKACCOUNT.ADD_BANK_ACC\"));\n  }\n}\nfunction AddEditBankAccountComponent_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"BANKACCOUNT.EDIT_BANK_ACC\"), \" \");\n  }\n}\nfunction AddEditBankAccountComponent_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.bank, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddEditBankAccountComponent_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.bankAccountDetails.lgo, i0.ɵɵsanitizeUrl);\n  }\n}\nexport let AddEditBankAccountComponent = /*#__PURE__*/(() => {\n  class AddEditBankAccountComponent {\n    translate;\n    bankAccountService;\n    attachmentService;\n    alertify;\n    imagesPathesService;\n    closeBankAccountForm = new EventEmitter();\n    idBankAccount;\n    bankAccountDetails = {};\n    bankAccountCreatModel;\n    bankAccountUpdateModel;\n    resultMessage = {};\n    fileUploadModel = [];\n    fileList = [];\n    logoIds = [];\n    currentUser;\n    constructor(translate, bankAccountService, attachmentService, alertify, imagesPathesService) {\n      this.translate = translate;\n      this.bankAccountService = bankAccountService;\n      this.attachmentService = attachmentService;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.setScssImages();\n      if (this.idBankAccount) {\n        this.getDetails();\n      }\n    }\n    setScssImages() {\n      this.imagesPathesService.setBackgroundPannerInStyle();\n    }\n    getDetails() {\n      this.bankAccountService.detailsBankAccount(this.idBankAccount || \"\").subscribe(res => {\n        if (res.isSuccess) {\n          this.bankAccountDetails = res.data;\n          this.bankAccountDetails.lgo = res.data.lgo;\n          if (!this.bankAccountDetails.lgo) {\n            this.bankAccountDetails.lgo = this.imagesPathesService.bank;\n          }\n        } else {}\n      }, error => {\n        //logging\n      });\n    }\n    listExt = [\"jpg\", \"png\", \"jpeg\", \"gif\", \"bmp\", \"tif\", \"tiff\", \"docx\", \"svg\", \"jfif\"];\n    onFileChange(files) {\n      if (files.length > 0) {\n        if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {\n          this.resultMessage = {\n            message: this.translate.instant('GENERAL.EXTENTION_FILE'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 3,\n            // need to be changed based on file type\n            file: element\n          };\n          this.fileUploadModel?.push(fileUploadObj);\n        });\n        this.UploadFiles(this.fileUploadModel);\n      }\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        if (this.bankAccountDetails) {\n          this.bankAccountDetails.lgo = res.data[0].url;\n          // Array.from(res.data).forEach((elm: any) => {\n          //   this.fileList?.push(elm);\n          //   this.bankAccountDetails.lgo = elm.url\n          // })\n          this.fileUploadModel = [];\n        }\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    savaBankAccount() {\n      if (!this.idBankAccount) {\n        this.bankAccountCreatModel = {\n          arabBnkName: this.bankAccountDetails?.arabBnkName,\n          engBnkName: this.bankAccountDetails?.engBnkName,\n          bnkNum: this.bankAccountDetails?.bnkNum,\n          lgo: this.bankAccountDetails?.lgo,\n          iBAN: this.bankAccountDetails?.iBAN\n        };\n        if (this.bankAccountDetails.lgo == null) {\n          this.bankAccountCreatModel.lgo = this.imagesPathesService.bank;\n        }\n        this.bankAccountService.addBankAccount(this.bankAccountCreatModel).subscribe(res => {\n          if (res.isSuccess) {\n            this.closeForm();\n            this.alertify.success(res.message || '');\n          } else {\n            this.alertify.error(res.message || '');\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.bankAccountUpdateModel = {\n          id: this.idBankAccount,\n          arabBnkName: this.bankAccountDetails?.arabBnkName,\n          engBnkName: this.bankAccountDetails?.engBnkName,\n          bnkNum: this.bankAccountDetails?.bnkNum,\n          lgo: this.bankAccountDetails?.lgo,\n          iBAN: this.bankAccountDetails?.iBAN\n        };\n        this.bankAccountService.UpdateBankAccount(this.bankAccountUpdateModel).subscribe(res => {\n          if (res.isSuccess) {\n            this.closeForm();\n            this.alertify.success(res.message || '');\n          } else {\n            this.alertify.error(res.message || '');\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    closeForm() {\n      this.closeBankAccountForm.emit(false);\n    }\n    static ɵfac = function AddEditBankAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddEditBankAccountComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.BankAccountService), i0.ɵɵdirectiveInject(i3.AttachmentsService), i0.ɵɵdirectiveInject(i4.AlertifyService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddEditBankAccountComponent,\n      selectors: [[\"app-add-edit-bank-account\"]],\n      inputs: {\n        idBankAccount: \"idBankAccount\"\n      },\n      outputs: {\n        closeBankAccountForm: \"closeBankAccountForm\"\n      },\n      decls: 46,\n      vars: 39,\n      consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"bankAccountForm\", \"mt-3\", \"pl-3\", \"pr-3\"], [\"class\", \" head  bold\", 4, \"ngIf\"], [\"class\", \" head  bold \", 4, \"ngIf\"], [1, \"form-group\"], [1, \"row\"], [1, \"col-12\", \"my-2\"], [1, \"col-12\", \"profile\", \"p-0\", \"mb-50\"], [1, \"profile_background\"], [\"class\", \"img_profile\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"file\", 2, \"display\", \"none\", 3, \"change\"], [\"id\", \"upload_link\", 1, \"custom_file_input\", 2, \"text-decoration\", \"none\", 3, \"click\"], [1, \"UploadImage\", 3, \"src\"], [1, \"col-12\", \"my-2\", \"mt-5\"], [1, \"text-right\", \"header_input\"], [\"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"head\", \"bold\"], [1, \"img_profile\", 3, \"src\"]],\n      template: function AddEditBankAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, AddEditBankAccountComponent_h3_1_Template, 3, 3, \"h3\", 2)(2, AddEditBankAccountComponent_h3_2_Template, 3, 3, \"h3\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7);\n          i0.ɵɵelement(7, \"div\", 8);\n          i0.ɵɵtemplate(8, AddEditBankAccountComponent_img_8_Template, 1, 1, \"img\", 9)(9, AddEditBankAccountComponent_img_9_Template, 1, 1, \"img\", 9);\n          i0.ɵɵelementStart(10, \"input\", 10, 0);\n          i0.ɵɵlistener(\"change\", function AddEditBankAccountComponent_Template_input_change_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileChange($event.target.files));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"a\", 11);\n          i0.ɵɵlistener(\"click\", function AddEditBankAccountComponent_Template_a_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r3 = i0.ɵɵreference(11);\n            return i0.ɵɵresetView(fileInput_r3.click());\n          });\n          i0.ɵɵelement(13, \"img\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"label\", 14);\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 15);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditBankAccountComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.bankAccountDetails.arabBnkName, $event) || (ctx.bankAccountDetails.arabBnkName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 6)(21, \"label\", 14);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"input\", 15);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditBankAccountComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.bankAccountDetails.engBnkName, $event) || (ctx.bankAccountDetails.engBnkName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 6)(27, \"label\", 14);\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"input\", 15);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditBankAccountComponent_Template_input_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.bankAccountDetails.bnkNum, $event) || (ctx.bankAccountDetails.bnkNum = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 6)(33, \"label\", 14);\n          i0.ɵɵtext(34);\n          i0.ɵɵpipe(35, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"input\", 15);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditBankAccountComponent_Template_input_ngModelChange_36_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.bankAccountDetails.iBAN, $event) || (ctx.bankAccountDetails.iBAN = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"section\", 16)(39, \"div\", 17)(40, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function AddEditBankAccountComponent_Template_button_click_40_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.savaBankAccount());\n          });\n          i0.ɵɵtext(41);\n          i0.ɵɵpipe(42, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function AddEditBankAccountComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeForm());\n          });\n          i0.ɵɵtext(44);\n          i0.ɵɵpipe(45, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.idBankAccount);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.idBankAccount);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", !ctx.bankAccountDetails.lgo);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.bankAccountDetails.lgo);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.camera, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 19, \"BANKACCOUNT.ARABIC_NAME\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(19, 21, \"BANKACCOUNT.ARABIC_NAME\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.bankAccountDetails.arabBnkName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 23, \"BANKACCOUNT.ENG_NAME\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(25, 25, \"BANKACCOUNT.ENG_NAME\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.bankAccountDetails.engBnkName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 27, \"BANKACCOUNT.BANK_NUM\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(31, 29, \"BANKACCOUNT.BANK_NUM\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.bankAccountDetails.bnkNum);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(35, 31, \"BANKACCOUNT.IBAN\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(37, 33, \"BANKACCOUNT.IBAN\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.bankAccountDetails.iBAN);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(42, 35, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(45, 37, \"GENERAL.BACK\"), \" \");\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, ReactiveFormsModule, TranslateModule, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}[_ngcontent-%COMP%]:root{--background_panner: \\\"\\\"}.bankAccountForm[_ngcontent-%COMP%]{height:70vh}.bankAccountForm[_ngcontent-%COMP%]:lang(en){text-align:left}.bankAccountForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.bankAccountForm[_ngcontent-%COMP%]   .header_input[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.bankAccountForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.bankAccountForm[_ngcontent-%COMP%]   .custom-file[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border:.188rem dashed rgba(242,241,241,.**********);border-radius:.75rem;opacity:1;height:6.25rem;width:100%;margin:0rem 0rem .2rem;padding:2rem;cursor:pointer;text-align:center}.bankAccountForm[_ngcontent-%COMP%]   .custom-file__text[_ngcontent-%COMP%]{color:var(--unnamed-color-3a3a3a);font-size:1.25rem;letter-spacing:-.016rem;color:#333;opacity:.5}.bankAccountForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-size:.875rem}.bankAccountForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;text-align:right;font-size:.875rem}.bankAccountForm[_ngcontent-%COMP%]   .img-type[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.75rem;float:left}.bankAccountForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.bankAccountForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .bankAccountForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{width:10.25rem;height:2.6875rem;margin:.5rem}.bankAccountForm[_ngcontent-%COMP%]   .cardRecourd[_ngcontent-%COMP%]{margin:1rem;justify-content:space-between;align-items:center;display:flex;padding:.5rem 1rem;background-color:#fff;border-radius:.438rem;border:.063rem solid rgba(242,241,241,.**********);box-shadow:0 .125rem .188rem #f2f1f1de}.bankAccountForm[_ngcontent-%COMP%]   .profile_background[_ngcontent-%COMP%]{background:var(--background_panner);height:12.9375rem;width:100%;border-radius:1.875rem;opacity:0}.bankAccountForm[_ngcontent-%COMP%]   .img_profile[_ngcontent-%COMP%]{position:absolute;left:30%;top:15%;width:13.125rem;height:13.125rem;border-radius:1rem}.bankAccountForm[_ngcontent-%COMP%]   .profile_edit[_ngcontent-%COMP%]{color:#333;font-weight:700;font-size:.7rem;cursor:pointer}.bankAccountForm[_ngcontent-%COMP%]   .UploadImage[_ngcontent-%COMP%]{position:absolute}.bankAccountForm[_ngcontent-%COMP%]   .UploadImage[_ngcontent-%COMP%]:lang(ar){right:16%}.bankAccountForm[_ngcontent-%COMP%]   .UploadImage[_ngcontent-%COMP%]:lang(en){left:30%}@media (max-width: 64rem){.bankAccountForm[_ngcontent-%COMP%]   .img_profile[_ngcontent-%COMP%]{left:17%;width:13.125rem;height:13.125rem;border-radius:1rem}.bankAccountForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .bankAccountForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{width:8.25rem!important;margin:.5rem!important}}\"]\n    });\n  }\n  return AddEditBankAccountComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}