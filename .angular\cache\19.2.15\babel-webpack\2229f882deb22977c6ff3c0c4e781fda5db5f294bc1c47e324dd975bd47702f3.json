{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/walk-through-services/walk-through-services\";\nimport * as i7 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i8 from \"@angular/common\";\nfunction WalkThroughComponent_form_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 15);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.group_18093, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"WALKTHROUGH.UPLOAD_PIC\"), \"\");\n  }\n}\nfunction WalkThroughComponent_form_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.fileList[0].url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"WALKTHROUGH.UPLOAD_PIC\"), \"\");\n  }\n}\nfunction WalkThroughComponent_form_1_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"WALKTHROUGH.REQUIRED\"), \" \");\n  }\n}\nfunction WalkThroughComponent_form_1_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"WALKTHROUGH.AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction WalkThroughComponent_form_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, WalkThroughComponent_form_1_div_11_div_1_Template, 3, 3, \"div\", 5)(2, WalkThroughComponent_form_1_div_11_div_2_Template, 3, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textAr.errors == null ? null : ctx_r1.f.textAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textAr.errors == null ? null : ctx_r1.f.textAr.errors.maxlength);\n  }\n}\nfunction WalkThroughComponent_form_1_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"WALKTHROUGH.REQUIRED\"), \" \");\n  }\n}\nfunction WalkThroughComponent_form_1_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"WALKTHROUGH.EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction WalkThroughComponent_form_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, WalkThroughComponent_form_1_div_17_div_1_Template, 3, 3, \"div\", 5)(2, WalkThroughComponent_form_1_div_17_div_2_Template, 3, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textEn.errors == null ? null : ctx_r1.f.textEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textEn.errors == null ? null : ctx_r1.f.textEn.errors.maxlength);\n  }\n}\nfunction WalkThroughComponent_form_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nfunction WalkThroughComponent_form_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 2);\n    i0.ɵɵlistener(\"submit\", function WalkThroughComponent_form_1_Template_form_submit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵlistener(\"dragover\", function WalkThroughComponent_form_1_Template_div_dragover_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragOver($event));\n    })(\"drop\", function WalkThroughComponent_form_1_Template_div_drop_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDropSuccess($event));\n    });\n    i0.ɵɵelementStart(2, \"label\", 4);\n    i0.ɵɵtemplate(3, WalkThroughComponent_form_1_span_3_Template, 5, 4, \"span\", 5)(4, WalkThroughComponent_form_1_span_4_Template, 5, 4, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 6);\n    i0.ɵɵlistener(\"change\", function WalkThroughComponent_form_1_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event.target.files));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"label\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"textarea\", 9);\n    i0.ɵɵtemplate(11, WalkThroughComponent_form_1_div_11_Template, 3, 2, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 7)(13, \"label\", 8);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"textarea\", 11);\n    i0.ɵɵtemplate(17, WalkThroughComponent_form_1_div_17_Template, 3, 2, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"section\")(19, \"div\", 12)(20, \"button\", 13);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, WalkThroughComponent_form_1_div_23_Template, 2, 4, \"div\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.currentForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileList.length == 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(9, 10, \"WALKTHROUGH.ARABIC_MESSAGE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textAr.errors && (ctx_r1.f.textAr.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 12, \"WALKTHROUGH.ENGLISH_MESSAGE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.textEn.errors && (ctx_r1.f.textEn.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disableSaveButtons);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 14, \"GENERAL.SAVE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage.message);\n  }\n}\nexport let WalkThroughComponent = /*#__PURE__*/(() => {\n  class WalkThroughComponent {\n    route;\n    fb;\n    attachmentService;\n    translate;\n    alertify;\n    walkThroughService;\n    imagesPathesService;\n    currentForm = new FormGroup({});\n    walkThrough = {};\n    createWalkThrough = false;\n    updateWalkThrough = false;\n    walkThroughId;\n    routeParams;\n    isSubmit = false;\n    selectedWalkThroughPageId;\n    attachmentIds = [];\n    fileUploadModel = [];\n    fileList = [];\n    disableSaveButtons = false;\n    resMessage = {};\n    constructor(route, fb, attachmentService, translate, alertify, walkThroughService, imagesPathesService) {\n      this.route = route;\n      this.fb = fb;\n      this.attachmentService = attachmentService;\n      this.translate = translate;\n      this.alertify = alertify;\n      this.walkThroughService = walkThroughService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      // this.walkThroughId = this.route.snapshot.params.id;\n      // if (this.routeParams === '/walk-through/create-walk-through' && this.walkThroughId == null) {\n      //     this.createWalkThrough = true;\n      //     this.updateWalkThrough = false;\n      // } else if (this.routeParams.includes('/user/update-walk-through') && this.walkThroughId != null) {\n      //     this.walkThroughService.getWalkThroughById(this.walkThroughId).subscribe(res => {\n      //       this.walkThrough = res.data;\n      //     })\n      //     this.createWalkThrough = false;\n      //     this.updateWalkThrough = true;\n      // }\n      this.buildForm();\n      // this.loadWalkThrough(this.selectedWalkThroughPageId);\n    }\n    // @HostListener('window:beforeunload', ['$event'])\n    // public onPageUnload($event: BeforeUnloadEvent) {\n    //   if (this.unsavedDataCheck()) {\n    //     $event.returnValue = true;\n    //     // return \"message\";\n    //   }\n    //   else{\n    //     $event.returnValue = false;\n    //     // return '';\n    //   }\n    // }\n    // @HostListener('window:popstate', ['$event'])\n    // onPopState(event:any) {\n    //   this.walkThroughService.setCanDeActivate(this.unsavedDataCheck());\n    // }\n    unsavedDataCheck() {\n      return this.walkThrough.textAr != this.f.textAr.value || this.walkThrough.textEn != this.f.textEn.value;\n    }\n    ngOnChanges(changes) {\n      this.loadWalkThrough(changes.selectedWalkThroughPageId.currentValue);\n    }\n    loadWalkThrough(selectedPageId) {\n      this.currentForm.reset();\n      this.walkThrough = {};\n      this.fileList = [];\n      this.attachmentIds = [];\n      this.fileUploadModel = [];\n      if (selectedPageId) {\n        this.walkThroughService.getWalkThroughByPageId(selectedPageId).subscribe(res => {\n          if (res.data) {\n            this.walkThrough = res.data;\n            this.populateForm();\n          }\n          // else {\n          //   this.currentForm.reset();\n          //   this.walkThrough = {};\n          //   this.fileList = [];\n          //   this.attachmentIds = [];\n          //   this.fileUploadModel = [];\n          // }\n        });\n      }\n      // else {\n      //   this.currentForm.reset();\n      //   this.walkThrough = {};\n      //   this.fileList = [];\n      //   this.attachmentIds = [];\n      //   this.fileUploadModel = [];\n      // }\n    }\n    deleteAttachment(index, id) {\n      this.fileList.splice(index, 1);\n      this.attachmentIds = this.attachmentIds.filter(a => a !== id);\n    }\n    submit() {\n      this.isSubmit = true;\n      this.resMessage = {};\n      this.mappModel();\n      if (this.currentForm.valid && this.walkThrough.attachmentId) {\n        // if (this.attachmentIds.length ==0) {\n        //   return;\n        // }\n        this.clearMessage();\n        if (this.walkThrough.id) {\n          this.walkThroughService.updateWalkThrough(this.walkThrough).subscribe(res => {\n            this.isSubmit = false;\n            if (res.isSuccess) {\n              this.resMessage = {\n                message: res.message,\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.clearSuccessMessage();\n              this.loadWalkThrough(this.selectedWalkThroughPageId);\n            } else {\n              this.resMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            this.isSubmit = false;\n            //logging\n          });\n        } else {\n          this.walkThroughService.createWalkThrough(this.walkThrough).subscribe(res => {\n            if (res.isSuccess) {\n              this.isSubmit = false;\n              this.resMessage = {\n                message: res.message,\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.clearSuccessMessage();\n              this.loadWalkThrough(this.selectedWalkThroughPageId);\n            } else {\n              this.isSubmit = false;\n              this.resMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        }\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('WALKTHROUGH.COMPLETE_FIELDS_ADD_ATTACHMENT'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    mappModel() {\n      // if (this.walkThrough.id) {\n      //   this.walkThrough.id =  this.f.id.value;\n      // }\n      this.walkThrough.textAr = this.f.textAr.value;\n      this.walkThrough.textEn = this.f.textEn.value;\n      ;\n      this.walkThrough.pageId = this.selectedWalkThroughPageId;\n      this.walkThrough.attachmentId = this.attachmentIds[0];\n    }\n    get f() {\n      return this.currentForm.controls;\n    }\n    buildForm() {\n      const arabicPattern = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9]+$\";\n      const englishPattern = \"^[a-zA-Z0-9' '-'\\s]{1,40}$\";\n      const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+\\\\-~؛)(÷*/'/!/$]+$\";\n      const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}A-Za-z 0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+-~؛)(÷*/'/!/$]+$\";\n      const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[ A-Za-z0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      this.currentForm = this.fb.group({\n        textAr: ['', [Validators.required, Validators.maxLength(1000)]],\n        textEn: ['', [Validators.required, Validators.maxLength(1000)]]\n      });\n    }\n    populateForm() {\n      this.f.textAr.setValue(this.walkThrough.textAr);\n      this.f.textEn.setValue(this.walkThrough.textEn);\n      if (this.walkThrough.attachmentId) {\n        this.attachmentIds.push(this.walkThrough.attachmentId);\n        let attchment = {\n          id: this.walkThrough.attachmentId,\n          content: \"\",\n          contentType: \"\",\n          fileName: this.walkThrough.fileName != null ? this.walkThrough.fileName : '',\n          url: this.walkThrough.fileUrl != null ? this.walkThrough.fileUrl : ''\n        };\n        this.fileList.push(attchment);\n      }\n    }\n    listExt = [\"jpg\", \"png\", \"jpeg\", \"gif\", \"bmp\", \"tif\", \"tiff\"];\n    onFileChange(files) {\n      if (files.length > 0) {\n        if (files[0].size >= 10485760) {\n          this.alertify.error(this.translate.instant('GENERAL.FILE_SIZE'));\n          return;\n        }\n        if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {\n          this.alertify.error(this.translate.instant('GENERAL.EXTENTION_FILE'));\n          return;\n        }\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 3,\n            // need to be changed based on file type\n            file: element\n          };\n          this.fileUploadModel.push(fileUploadObj);\n        });\n        this.uploadFiles(this.fileUploadModel);\n      } else {\n        return;\n      }\n    }\n    // onFileChange(files: FileList) {\n    //   if (files.length > 0) {\n    //     Array.from(files).forEach(element => {\n    //       var fileUploadObj: IFileUpload = {\n    //         containerNameIndex: 1, // need to be changed based on file type\n    //         file: element\n    //       }\n    //       this.fileUploadModel.push(fileUploadObj)\n    //     });\n    //     this.uploadFiles(this.fileUploadModel);\n    //   }\n    // }\n    uploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        this.fileList = [];\n        this.attachmentIds = [];\n        Array.from(res.data).forEach(elm => {\n          this.attachmentIds.push(elm.id);\n          this.fileList.push(elm);\n        });\n        this.fileUploadModel = [];\n      }, error => {\n        //logging\n        this.fileUploadModel = [];\n      });\n    }\n    clearMessage() {\n      this.resMessage = {};\n    }\n    clearSuccessMessage() {\n      setTimeout(() => {\n        this.resMessage = {};\n      }, 2000);\n    }\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    // From drag and drop\n    onDropSuccess(event) {\n      event.preventDefault();\n      this.onFileChange(event.dataTransfer.files); // notice the \"dataTransfer\" used instead of \"target\"\n    }\n    // From attachment link\n    onChange(event) {\n      this.onFileChange(event.target.files); // \"target\" is correct here\n    }\n    static ɵfac = function WalkThroughComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WalkThroughComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AttachmentsService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.WalkThroughService), i0.ɵɵdirectiveInject(i7.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WalkThroughComponent,\n      selectors: [[\"app-walk-through\"]],\n      inputs: {\n        selectedWalkThroughPageId: \"selectedWalkThroughPageId\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"walk-through\"], [\"class\", \"form-group walk-through-form  internal_scroll_list_group\", 3, \"formGroup\", \"submit\", 4, \"ngIf\"], [1, \"form-group\", \"walk-through-form\", \"internal_scroll_list_group\", 3, \"submit\", \"formGroup\"], [1, \"form-group\", 3, \"dragover\", \"drop\"], [\"for\", \"importFile\", 1, \"custom-file\", \"custom-file__text\"], [4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", \"id\", \"importFile\", \"accept\", \"application/.jpg,.png,.jpeg,.gif,.bmp,.tif,.tiff\", 1, \"custom-file-input\", 3, \"change\"], [1, \"form-group\"], [\"for\", \"comment\"], [\"cols\", \"2\", \"rows\", \"10\", \"id\", \"InputtextAr\", \"formControlName\", \"textAr\", 1, \"form-control\", \"text-arabic\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"cols\", \"2\", \"rows\", \"10\", \"id\", \"InputtextEn\", \"formControlName\", \"textEn\", 1, \"form-control\", \"text-english\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\", \"mb-0\", \"mt-4\", \"direction-rtl\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"disabled\"], [3, \"class\", 4, \"ngIf\"], [\"calss\", \"upload-icon\", \"alt\", \"\", 3, \"src\"], [\"calss\", \"page-image\", \"alt\", \"\", 2, \"width\", \"95%\", \"height\", \"25rem\", 3, \"src\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"]],\n      template: function WalkThroughComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, WalkThroughComponent_form_1_Template, 24, 16, \"form\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedWalkThroughPageId);\n        }\n      },\n      dependencies: [i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i4.TranslatePipe],\n      styles: [\".walk-through[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;margin-top:1rem;margin-bottom:0}.walk-through[_ngcontent-%COMP%]:lang(en){text-align:left}.walk-through[_ngcontent-%COMP%]:lang(ar){text-align:right}.walk-through[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:77vh;overflow-y:auto;padding-left:.5rem}.walk-through[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:77vh;overflow-y:auto;padding-right:.5rem}.walk-through[_ngcontent-%COMP%]   .walk-through-form[_ngcontent-%COMP%]:lang(en){margin-right:1rem;text-align:left}.walk-through[_ngcontent-%COMP%]   .walk-through-form[_ngcontent-%COMP%]:lang(ar){margin-left:1rem;text-align:right}.walk-through[_ngcontent-%COMP%]   .custom-file[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border:.188rem dashed rgba(242,241,241,.8705882353);border-radius:.75rem;opacity:1;height:30%;width:100%;text-align:center}.walk-through[_ngcontent-%COMP%]   .custom-file__text[_ngcontent-%COMP%]{color:var(--unnamed-color-363636);text-align:center;font-size:1.25rem;letter-spacing:-.018rem;color:#4d4d4d;opacity:1;padding:1rem}.walk-through[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]{margin-bottom:.625rem;width:3rem;height:3rem}.walk-through[_ngcontent-%COMP%]   .page-image[_ngcontent-%COMP%]{width:100%;height:25rem}.walk-through[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]{font-size:.875rem;color:gray}.walk-through[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1}.walk-through[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:7rem;margin:2rem;height:2.31rem;border:none;color:#fff;display:block}\"]\n    });\n  }\n  return WalkThroughComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}