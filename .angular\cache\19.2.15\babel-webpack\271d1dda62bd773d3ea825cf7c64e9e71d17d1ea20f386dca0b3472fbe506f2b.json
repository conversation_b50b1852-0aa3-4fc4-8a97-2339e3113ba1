{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { StudentProgramVacationStatusEnum } from '../../../../../../../core/enums/StudentProgramVacationStatus/student-program-vacation-status.enum';\nimport { DropOutRoleEnum } from 'src/app/core/enums/drop-out-request-enums/drop-out-status.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"../../../../../../../core/services/student-program-vacation-services/student-program-vacation-services.service\";\nimport * as i3 from \"../../../../../../../core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-program-vacation-grid\", 14);\n    i0.ɵɵlistener(\"studentProgramVacationStudentViewModel\", function StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_studentProgramVacationStudentViewModel_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectStuRequestMethod($event));\n    })(\"acceptStudentProgramVacation\", function StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptStudentProgramVacation_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.AcceptStudentProgramVacation($event));\n    })(\"acceptAllStudentProgramVacationChecked\", function StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptAllStudentProgramVacationChecked_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllStudentVacationRequestsChecked());\n    })(\"studentVacationFilterEvent\", function StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_studentVacationFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.StudentProgramVacationPendingChangePage($event));\n    })(\"sendStudentVacationId\", function StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_sendStudentVacationId_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"studentVacationItems\", ctx_r1.studentProgramVacationRequestsList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"vacationTypeEnum\", ctx_r1.statusEnum.Pending)(\"totalCount\", ctx_r1.totalCount)(\"studentVacationFilterRequestModel\", ctx_r1.studentProgramVacationFilterRequestModel)(\"userMode\", ctx_r1.userMode);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, StudentVacationRequestTabComponent_div_0_div_20_app_student_program_vacation_grid_1_Template, 1, 6, \"app-student-program-vacation-grid\", 13)(2, StudentVacationRequestTabComponent_div_0_div_20_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length === 0);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-program-vacation-grid\", 17);\n    i0.ɵɵlistener(\"acceptStudentProgramVacation\", function StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptStudentProgramVacation_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.AcceptStudentProgramVacation($event));\n    })(\"acceptAllStudentProgramVacationChecked\", function StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptAllStudentProgramVacationChecked_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllStudentVacationRequestsChecked());\n    })(\"studentVacationFilterEvent\", function StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_studentVacationFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.StudentProgramVacationAcceptChangePage($event));\n    })(\"sendStudentVacationId\", function StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_sendStudentVacationId_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"studentVacationItems\", ctx_r1.studentProgramVacationRequestsList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"vacationTypeEnum\", ctx_r1.statusEnum.Accept)(\"totalCount\", ctx_r1.totalCount)(\"studentVacationFilterRequestModel\", ctx_r1.studentProgramVacationFilterRequestModel)(\"userMode\", ctx_r1.userMode);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, StudentVacationRequestTabComponent_div_0_div_21_app_student_program_vacation_grid_1_Template, 1, 6, \"app-student-program-vacation-grid\", 16)(2, StudentVacationRequestTabComponent_div_0_div_21_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length === 0);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-program-vacation-grid\", 17);\n    i0.ɵɵlistener(\"acceptStudentProgramVacation\", function StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptStudentProgramVacation_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.AcceptStudentProgramVacation($event));\n    })(\"acceptAllStudentProgramVacationChecked\", function StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_acceptAllStudentProgramVacationChecked_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllStudentVacationRequestsChecked());\n    })(\"studentVacationFilterEvent\", function StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_studentVacationFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.StudentProgramVacationRejectedChangePage($event));\n    })(\"sendStudentVacationId\", function StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template_app_student_program_vacation_grid_sendStudentVacationId_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.studentIdDrop($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"studentVacationItems\", ctx_r1.studentProgramVacationRequestsList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"vacationTypeEnum\", ctx_r1.statusEnum.Rejected)(\"totalCount\", ctx_r1.totalCount)(\"studentVacationFilterRequestModel\", ctx_r1.studentProgramVacationFilterRequestModel)(\"userMode\", ctx_r1.userMode);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, StudentVacationRequestTabComponent_div_0_div_22_app_student_program_vacation_grid_1_Template, 1, 6, \"app-student-program-vacation-grid\", 16)(2, StudentVacationRequestTabComponent_div_0_div_22_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProgramVacationRequestsList.length === 0);\n  }\n}\nfunction StudentVacationRequestTabComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"app-search-input\", 5);\n    i0.ɵɵlistener(\"searchTerm\", function StudentVacationRequestTabComponent_div_0_Template_app_search_input_searchTerm_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function StudentVacationRequestTabComponent_div_0_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAdvancedSearch());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function StudentVacationRequestTabComponent_div_0_Template_div_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPendingChange());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function StudentVacationRequestTabComponent_div_0_Template_div_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptChange());\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function StudentVacationRequestTabComponent_div_0_Template_div_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRejectedChange());\n    });\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11);\n    i0.ɵɵtemplate(20, StudentVacationRequestTabComponent_div_0_div_20_Template, 3, 2, \"div\", 12)(21, StudentVacationRequestTabComponent_div_0_div_21_Template, 3, 2, \"div\", 12)(22, StudentVacationRequestTabComponent_div_0_div_22_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"searchKey\", ctx_r1.studentProgramVacationFilterRequestModel.usrName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 11, \"STUDENT_VACATION.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Pending));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 13, \"STUDENT_VACATION.NEW_VACATION_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Accept));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 15, \"STUDENT_VACATION.ACCEPTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Rejected));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 17, \"STUDENT_VACATION.REJECTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Accept);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Rejected);\n  }\n}\nfunction StudentVacationRequestTabComponent_app_student_details_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-details-view\", 18);\n    i0.ɵɵlistener(\"hideUserDetails\", function StudentVacationRequestTabComponent_app_student_details_view_1_Template_app_student_details_view_hideUserDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideUserDetailsView($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resiveUserId\", ctx_r1.studentDropIdInput);\n  }\n}\nexport let StudentVacationRequestTabComponent = /*#__PURE__*/(() => {\n  class StudentVacationRequestTabComponent {\n    translate;\n    programVacationServicesService;\n    alertify;\n    advancedSearchEvent = new EventEmitter();\n    itemStuReq = new EventEmitter();\n    closePopup = new EventEmitter();\n    studentDropIdInput;\n    studentProgramVacationRequestsList = [];\n    studentProgramVacationFilterRequestModel = {\n      statusNum: StudentProgramVacationStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortOrder: -1,\n      page: 1,\n      sortField: 'requestdate'\n    };\n    totalCount = 0;\n    numberItemsPerRow = 3;\n    ids = [];\n    typeEnum = StudentProgramVacationStatusEnum.Pending;\n    showTap = StudentProgramVacationStatusEnum.Pending;\n    statusEnum = StudentProgramVacationStatusEnum;\n    userMode = DropOutRoleEnum.Admin;\n    showUserDetailsView = false;\n    constructor(translate, programVacationServicesService, alertify) {\n      this.translate = translate;\n      this.programVacationServicesService = programVacationServicesService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      // this.studentProgramVacationFilterRequestModel.sortField = this.translate.currentLang === LanguageEnum.ar ? 'userNameAr' : 'UserNameEn'\n      this.studentProgramVacationFilterRequestModel.sortField = 'requestdate';\n      this.getStudentProgramVacationRequests();\n    }\n    studentIdDrop(event) {\n      this.showUserDetailsView = true;\n      this.studentDropIdInput = event;\n    }\n    hideUserDetailsView(event) {\n      this.showUserDetailsView = event;\n    }\n    searchByText(searchKey) {\n      this.studentProgramVacationFilterRequestModel.usrName = searchKey;\n      this.getStudentProgramVacationRequests();\n    }\n    getStudentProgramVacationRequests() {\n      this.programVacationServicesService.getStudentsProgramsVacationFilterAdminView(this.studentProgramVacationFilterRequestModel || {}).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.studentProgramVacationRequestsList = res.data;\n          this.studentProgramVacationRequestsList?.forEach(function (item) {});\n          this.totalCount = res.count ? res.count : 0;\n          if (this.studentProgramVacationFilterRequestModel.skip > 0 && (!this.studentProgramVacationRequestsList || this.studentProgramVacationRequestsList.length === 0)) {\n            this.studentProgramVacationFilterRequestModel.page -= 1;\n            this.studentProgramVacationFilterRequestModel.skip = (this.studentProgramVacationFilterRequestModel.page - 1) * this.studentProgramVacationFilterRequestModel.take;\n            this.getStudentProgramVacationRequests();\n          }\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onPendingChange() {\n      this.studentProgramVacationFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentProgramVacationStatusEnum.Pending,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentProgramVacationStatusEnum.Pending;\n      this.closeAvancedSearch();\n      this.getStudentProgramVacationRequests();\n    }\n    onAcceptChange() {\n      this.studentProgramVacationFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentProgramVacationStatusEnum.Accept,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentProgramVacationStatusEnum.Accept;\n      this.closeAvancedSearch();\n      this.getStudentProgramVacationRequests();\n    }\n    onRejectedChange() {\n      this.studentProgramVacationFilterRequestModel = {\n        usrName: '',\n        statusNum: StudentProgramVacationStatusEnum.Rejected,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.showTap = StudentProgramVacationStatusEnum.Rejected;\n      this.closeAvancedSearch();\n      this.getStudentProgramVacationRequests();\n    }\n    rejectStuRequestMethod(event) {\n      this.itemStuReq.emit(event);\n    }\n    AcceptStudentProgramVacation(studentProgramVacationModel) {\n      this.ids?.push(studentProgramVacationModel.id || '');\n      this.programVacationServicesService.studentProgramVacationAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentProgramVacationRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    closeAvancedSearch() {\n      this.studentProgramVacationFilterRequestModel.usrName = '';\n      this.studentProgramVacationFilterRequestModel.progId = '';\n      this.studentProgramVacationFilterRequestModel.numberRequest = undefined;\n      this.studentProgramVacationFilterRequestModel.fromDate = undefined;\n      this.studentProgramVacationFilterRequestModel.toDate = undefined;\n      this.studentProgramVacationFilterRequestModel.skip = 0;\n      this.studentProgramVacationFilterRequestModel.take = 9;\n      this.studentProgramVacationFilterRequestModel.sortField = 'requestdate';\n      this.studentProgramVacationFilterRequestModel.sortOrder = -1;\n      this.studentProgramVacationFilterRequestModel.page = 1;\n      // this.closeAdvancedSearch.emit()\n      this.closePopup.emit(); // as per issue number 3250\n    }\n    acceptAllStudentVacationRequestsChecked() {\n      this.ids = this.studentProgramVacationRequestsList?.filter(i => i.checked).map(a => a.id || '');\n      this.programVacationServicesService.studentProgramVacationAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentProgramVacationRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    StudentProgramVacationPendingChangePage(event) {\n      this.studentProgramVacationFilterRequestModel.statusNum = StudentProgramVacationStatusEnum.Pending;\n      this.studentProgramVacationFilterRequestModel.sortField = 'requestdate';\n      this.studentProgramVacationFilterRequestModel = event;\n      this.getStudentProgramVacationRequests();\n    }\n    StudentProgramVacationAcceptChangePage(event) {\n      this.studentProgramVacationFilterRequestModel.statusNum = StudentProgramVacationStatusEnum.Accept;\n      this.studentProgramVacationFilterRequestModel.sortField = 'requestdate';\n      this.studentProgramVacationFilterRequestModel = event;\n      this.getStudentProgramVacationRequests();\n    }\n    StudentProgramVacationRejectedChangePage(event) {\n      this.studentProgramVacationFilterRequestModel.statusNum = StudentProgramVacationStatusEnum.Rejected;\n      this.studentProgramVacationFilterRequestModel.sortField = 'requestdate';\n      this.studentProgramVacationFilterRequestModel = event;\n      this.getStudentProgramVacationRequests();\n    }\n    openAdvancedSearch() {\n      this.advancedSearchEvent.emit(this.studentProgramVacationFilterRequestModel);\n    }\n    advancedSearch(model) {\n      this.studentProgramVacationFilterRequestModel = model || {\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: -1,\n        page: 1\n      };\n      this.getStudentProgramVacationRequests();\n    }\n    static ɵfac = function StudentVacationRequestTabComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentVacationRequestTabComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.StudentProgramVacationServicesService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentVacationRequestTabComponent,\n      selectors: [[\"app-student-vacation-request-tab\"]],\n      outputs: {\n        advancedSearchEvent: \"advancedSearchEvent\",\n        itemStuReq: \"itemStuReq\",\n        closePopup: \"closePopup\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid reuest_container\", 4, \"ngIf\"], [3, \"resiveUserId\", \"hideUserDetails\", 4, \"ngIf\"], [1, \"container-fluid\", \"reuest_container\"], [1, \"row\"], [1, \"col-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [1, \"mr-3\", \"ml-3\", 3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", 3, \"click\"], [1, \"col-12\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [4, \"ngIf\"], [3, \"studentVacationItems\", \"numberPerRow\", \"vacationTypeEnum\", \"totalCount\", \"studentVacationFilterRequestModel\", \"userMode\", \"studentProgramVacationStudentViewModel\", \"acceptStudentProgramVacation\", \"acceptAllStudentProgramVacationChecked\", \"studentVacationFilterEvent\", \"sendStudentVacationId\", 4, \"ngIf\"], [3, \"studentProgramVacationStudentViewModel\", \"acceptStudentProgramVacation\", \"acceptAllStudentProgramVacationChecked\", \"studentVacationFilterEvent\", \"sendStudentVacationId\", \"studentVacationItems\", \"numberPerRow\", \"vacationTypeEnum\", \"totalCount\", \"studentVacationFilterRequestModel\", \"userMode\"], [1, \"No_data\"], [3, \"studentVacationItems\", \"numberPerRow\", \"vacationTypeEnum\", \"totalCount\", \"studentVacationFilterRequestModel\", \"userMode\", \"acceptStudentProgramVacation\", \"acceptAllStudentProgramVacationChecked\", \"studentVacationFilterEvent\", \"sendStudentVacationId\", 4, \"ngIf\"], [3, \"acceptStudentProgramVacation\", \"acceptAllStudentProgramVacationChecked\", \"studentVacationFilterEvent\", \"sendStudentVacationId\", \"studentVacationItems\", \"numberPerRow\", \"vacationTypeEnum\", \"totalCount\", \"studentVacationFilterRequestModel\", \"userMode\"], [3, \"hideUserDetails\", \"resiveUserId\"]],\n      template: function StudentVacationRequestTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StudentVacationRequestTabComponent_div_0_Template, 23, 25, \"div\", 0)(1, StudentVacationRequestTabComponent_app_student_details_view_1_Template, 1, 1, \"app-student-details-view\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.showUserDetailsView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserDetailsView);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.TranslatePipe],\n      styles: [\".reuest_container[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:74vh;margin-top:1rem}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.reuest_container[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.reuest_container[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:55vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}.reuest_container[_ngcontent-%COMP%]   .w-60[_ngcontent-%COMP%]{width:60%}.reuest_container[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(en){margin-left:66.666667%}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(ar){margin-right:66.666667%}\"]\n    });\n  }\n  return StudentVacationRequestTabComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}