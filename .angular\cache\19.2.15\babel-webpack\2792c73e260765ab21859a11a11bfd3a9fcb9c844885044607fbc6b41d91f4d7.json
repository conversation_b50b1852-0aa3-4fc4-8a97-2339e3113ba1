{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO clockwise\nimport IndicatorAxis from './IndicatorAxis.js';\nimport IntervalScale from '../../scale/Interval.js';\nimport * as numberUtil from '../../util/number.js';\nimport { map, each, isString, isNumber } from 'zrender/lib/core/util.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Radar = /** @class */function () {\n  function Radar(radarModel, ecModel, api) {\n    /**\r\n     *\r\n     * Radar dimensions\r\n     */\n    this.dimensions = [];\n    this._model = radarModel;\n    this._indicatorAxes = map(radarModel.getIndicatorModels(), function (indicatorModel, idx) {\n      var dim = 'indicator_' + idx;\n      var indicatorAxis = new IndicatorAxis(dim, new IntervalScale()\n      // (indicatorModel.get('axisType') === 'log') ? new LogScale() : new IntervalScale()\n      );\n      indicatorAxis.name = indicatorModel.get('name');\n      // Inject model and axis\n      indicatorAxis.model = indicatorModel;\n      indicatorModel.axis = indicatorAxis;\n      this.dimensions.push(dim);\n      return indicatorAxis;\n    }, this);\n    this.resize(radarModel, api);\n  }\n  Radar.prototype.getIndicatorAxes = function () {\n    return this._indicatorAxes;\n  };\n  Radar.prototype.dataToPoint = function (value, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    return this.coordToPoint(indicatorAxis.dataToCoord(value), indicatorIndex);\n  };\n  // TODO: API should be coordToPoint([coord, indicatorIndex])\n  Radar.prototype.coordToPoint = function (coord, indicatorIndex) {\n    var indicatorAxis = this._indicatorAxes[indicatorIndex];\n    var angle = indicatorAxis.angle;\n    var x = this.cx + coord * Math.cos(angle);\n    var y = this.cy - coord * Math.sin(angle);\n    return [x, y];\n  };\n  Radar.prototype.pointToData = function (pt) {\n    var dx = pt[0] - this.cx;\n    var dy = pt[1] - this.cy;\n    var radius = Math.sqrt(dx * dx + dy * dy);\n    dx /= radius;\n    dy /= radius;\n    var radian = Math.atan2(-dy, dx);\n    // Find the closest angle\n    // FIXME index can calculated directly\n    var minRadianDiff = Infinity;\n    var closestAxis;\n    var closestAxisIdx = -1;\n    for (var i = 0; i < this._indicatorAxes.length; i++) {\n      var indicatorAxis = this._indicatorAxes[i];\n      var diff = Math.abs(radian - indicatorAxis.angle);\n      if (diff < minRadianDiff) {\n        closestAxis = indicatorAxis;\n        closestAxisIdx = i;\n        minRadianDiff = diff;\n      }\n    }\n    return [closestAxisIdx, +(closestAxis && closestAxis.coordToData(radius))];\n  };\n  Radar.prototype.resize = function (radarModel, api) {\n    var center = radarModel.get('center');\n    var viewWidth = api.getWidth();\n    var viewHeight = api.getHeight();\n    var viewSize = Math.min(viewWidth, viewHeight) / 2;\n    this.cx = numberUtil.parsePercent(center[0], viewWidth);\n    this.cy = numberUtil.parsePercent(center[1], viewHeight);\n    this.startAngle = radarModel.get('startAngle') * Math.PI / 180;\n    // radius may be single value like `20`, `'80%'`, or array like `[10, '80%']`\n    var radius = radarModel.get('radius');\n    if (isString(radius) || isNumber(radius)) {\n      radius = [0, radius];\n    }\n    this.r0 = numberUtil.parsePercent(radius[0], viewSize);\n    this.r = numberUtil.parsePercent(radius[1], viewSize);\n    each(this._indicatorAxes, function (indicatorAxis, idx) {\n      indicatorAxis.setExtent(this.r0, this.r);\n      var angle = this.startAngle + idx * Math.PI * 2 / this._indicatorAxes.length;\n      // Normalize to [-PI, PI]\n      angle = Math.atan2(Math.sin(angle), Math.cos(angle));\n      indicatorAxis.angle = angle;\n    }, this);\n  };\n  Radar.prototype.update = function (ecModel, api) {\n    var indicatorAxes = this._indicatorAxes;\n    var radarModel = this._model;\n    each(indicatorAxes, function (indicatorAxis) {\n      indicatorAxis.scale.setExtent(Infinity, -Infinity);\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries, idx) {\n      if (radarSeries.get('coordinateSystem') !== 'radar'\n      // @ts-ignore\n      || ecModel.getComponent('radar', radarSeries.get('radarIndex')) !== radarModel) {\n        return;\n      }\n      var data = radarSeries.getData();\n      each(indicatorAxes, function (indicatorAxis) {\n        indicatorAxis.scale.unionExtentFromData(data, data.mapDimension(indicatorAxis.dim));\n      });\n    }, this);\n    var splitNumber = radarModel.get('splitNumber');\n    var dummyScale = new IntervalScale();\n    dummyScale.setExtent(0, splitNumber);\n    dummyScale.setInterval(1);\n    // Force all the axis fixing the maxSplitNumber.\n    each(indicatorAxes, function (indicatorAxis, idx) {\n      alignScaleTicks(indicatorAxis.scale, indicatorAxis.model, dummyScale);\n    });\n  };\n  Radar.prototype.convertToPixel = function (ecModel, finder, value) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    console.warn('Not implemented.');\n    return null;\n  };\n  Radar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  Radar.create = function (ecModel, api) {\n    var radarList = [];\n    ecModel.eachComponent('radar', function (radarModel) {\n      var radar = new Radar(radarModel, ecModel, api);\n      radarList.push(radar);\n      radarModel.coordinateSystem = radar;\n    });\n    ecModel.eachSeriesByType('radar', function (radarSeries) {\n      if (radarSeries.get('coordinateSystem') === 'radar') {\n        // Inject coordinate system\n        // @ts-ignore\n        radarSeries.coordinateSystem = radarList[radarSeries.get('radarIndex') || 0];\n      }\n    });\n    return radarList;\n  };\n  /**\r\n   * Radar dimensions is based on the data\r\n   */\n  Radar.dimensions = [];\n  return Radar;\n}();\nexport default Radar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}