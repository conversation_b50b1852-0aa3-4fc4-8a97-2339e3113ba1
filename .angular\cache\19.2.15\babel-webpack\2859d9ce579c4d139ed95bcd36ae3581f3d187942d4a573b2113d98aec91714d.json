{"ast": null, "code": "import { saveAs } from 'file-saver';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-batches-service/program-batches.service\";\nimport * as i2 from \"src/app/core/services/excel-exportation-service/excel-exportation.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@ng-select/ng-select\";\nfunction ExcExpStdBatchReportComponent_ng_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bat_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", bat_r1.batId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? bat_r1.progBatNameEn : bat_r1.progBatNameAr, \" \");\n  }\n}\nexport let ExcExpStdBatchReportComponent = /*#__PURE__*/(() => {\n  class ExcExpStdBatchReportComponent {\n    programBatchesService;\n    excelExpService;\n    translate;\n    alertfy;\n    allProgBatchs = [];\n    batId;\n    excelExportStudentsBatchReq;\n    langEnum = LanguageEnum;\n    constructor(programBatchesService, excelExpService, translate, alertfy) {\n      this.programBatchesService = programBatchesService;\n      this.excelExpService = excelExpService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n    }\n    ngOnInit() {\n      this.getAllProgs();\n    }\n    getAllProgs() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    setName(batId) {\n      return this.translate.currentLang === this.langEnum.en ? this.allProgBatchs.find(x => x.batId == batId)?.progBatNameEn : this.allProgBatchs.find(x => x.batId == batId)?.progBatNameAr;\n    }\n    exportStudentsBatch() {\n      this.excelExportStudentsBatchReq = {\n        batId: this.batId\n      };\n      this.excelExpService.exportStudentsBatch(this.excelExportStudentsBatchReq || {}).subscribe(res => {\n        if (res.isSuccess) {\n          let data = res.data;\n          let buff = new Buffer(data.content, 'base64');\n          const file = new Blob([buff]);\n          saveAs(file, data.fileName);\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function ExcExpStdBatchReportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ExcExpStdBatchReportComponent)(i0.ɵɵdirectiveInject(i1.ProgramBatchesService), i0.ɵɵdirectiveInject(i2.ExcelExportationService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExcExpStdBatchReportComponent,\n      selectors: [[\"app-exc-exp-std-batch-report\"]],\n      decls: 13,\n      vars: 11,\n      consts: [[1, \"row\", \"d-flex\", \"justify-content-between\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-8\", \"col-sm-8\", \"col-xs-12\", \"all-prog\"], [1, \"w-100\", \"select\"], [\"bindValue\", \"batId\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-3\", \"col-xs-12\", \"py-4\"], [1, \"cancel-btn\", 3, \"click\"], [3, \"value\"]],\n      template: function ExcExpStdBatchReportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"label\");\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"ng-select\", 3);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ExcExpStdBatchReportComponent_Template_ng_select_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(8, ExcExpStdBatchReportComponent_ng_option_8_Template, 2, 2, \"ng-option\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ExcExpStdBatchReportComponent_Template_button_click_10_listener() {\n            return ctx.exportStudentsBatch();\n          });\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"EXPORTATION_EXCEL.EXPORT_STD_PROG_BATCH\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 7, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allProgBatchs);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 9, \"EXPORTATION_EXCEL.EXPORT\"), \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i6.NgControlStatus, i6.NgModel, i7.NgSelectComponent, i7.NgOptionComponent, i3.TranslatePipe],\n      styles: [\".all-prog[_ngcontent-%COMP%]:lang(en){text-align:left}.all-prog[_ngcontent-%COMP%]:lang(ar){text-align:right}.all-prog[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:1rem;color:#333}.cancel-btn[_ngcontent-%COMP%]{font-size:1rem}.cancel-btn[_ngcontent-%COMP%]:lang(en){float:right}.cancel-btn[_ngcontent-%COMP%]:lang(ar){float:left}\"]\n    });\n  }\n  return ExcExpStdBatchReportComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}