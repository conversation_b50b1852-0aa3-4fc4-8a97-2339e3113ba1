{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Sinhalese [si]\n//! author : <PERSON><PERSON> : https://github.com/sampathsris\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n\n  /*jshint -W100*/\n  var si = moment.defineLocale('si', {\n    months: 'ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්'.split('_'),\n    monthsShort: 'ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ'.split('_'),\n    weekdays: 'ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා'.split('_'),\n    weekdaysShort: 'ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන'.split('_'),\n    weekdaysMin: 'ඉ_ස_අ_බ_බ්‍ර_සි_සෙ'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'a h:mm',\n      LTS: 'a h:mm:ss',\n      L: 'YYYY/MM/DD',\n      LL: 'YYYY MMMM D',\n      LLL: 'YYYY MMMM D, a h:mm',\n      LLLL: 'YYYY MMMM D [වැනි] dddd, a h:mm:ss'\n    },\n    calendar: {\n      sameDay: '[අද] LT[ට]',\n      nextDay: '[හෙට] LT[ට]',\n      nextWeek: 'dddd LT[ට]',\n      lastDay: '[ඊයේ] LT[ට]',\n      lastWeek: '[පසුගිය] dddd LT[ට]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%sකින්',\n      past: '%sකට පෙර',\n      s: 'තත්පර කිහිපය',\n      ss: 'තත්පර %d',\n      m: 'මිනිත්තුව',\n      mm: 'මිනිත්තු %d',\n      h: 'පැය',\n      hh: 'පැය %d',\n      d: 'දිනය',\n      dd: 'දින %d',\n      M: 'මාසය',\n      MM: 'මාස %d',\n      y: 'වසර',\n      yy: 'වසර %d'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2} වැනි/,\n    ordinal: function (number) {\n      return number + ' වැනි';\n    },\n    meridiemParse: /පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,\n    isPM: function (input) {\n      return input === 'ප.ව.' || input === 'පස් වරු';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? 'ප.ව.' : 'පස් වරු';\n      } else {\n        return isLower ? 'පෙ.ව.' : 'පෙර වරු';\n      }\n    }\n  });\n  return si;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}