{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ExamFormViewComponent } from './components/exam-form-view/exam-form-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'exam-form-view',\n    component: ExamFormViewComponent\n  }]\n}];\nexport let ExamFormRoutingModule = /*#__PURE__*/(() => {\n  class ExamFormRoutingModule {\n    static ɵfac = function ExamFormRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ExamFormRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ExamFormRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return ExamFormRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}