{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../../confirm-modal/confirm-modal.component';\nimport { ProgramConditionViewMoodEnum } from 'src/app/core/enums/programs/program-condition-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-services/program.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/program-services/program-conditions.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nfunction SettingLastProgramComponent_ng_container_0_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", item_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r3.nameEn : item_r3.nameAr, \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"p\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function SettingLastProgramComponent_ng_container_0_div_20_Template_a_click_4_listener() {\n      const p_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeItemFromSelectedLastPrograms(p_r5));\n    });\n    i0.ɵɵelement(5, \"i\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? p_r5.nameEn : p_r5.nameAr);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? p_r5.nameEn : p_r5.nameAr, \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"img\", 3);\n    i0.ɵɵlistener(\"click\", function SettingLastProgramComponent_ng_container_0_Template_img_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.confirmDialog());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"select\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingLastProgramComponent_ng_container_0_Template_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.lastProgramModel.condSelcted, $event) || (ctx_r1.lastProgramModel.condSelcted = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(11, \"option\", 9);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SettingLastProgramComponent_ng_container_0_option_14_Template, 2, 2, \"option\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 11)(16, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SettingLastProgramComponent_ng_container_0_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addDegreeItem());\n    });\n    i0.ɵɵelement(17, \"img\", 13);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15);\n    i0.ɵɵtemplate(20, SettingLastProgramComponent_ng_container_0_div_20_Template, 6, 2, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 17)(22, \"label\", 18)(23, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingLastProgramComponent_ng_container_0_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.lastProgramModel.isRequired, $event) || (ctx_r1.lastProgramModel.isRequired = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 20);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function SettingLastProgramComponent_ng_container_0_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveProgramConditions());\n    });\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.lastProgramModel.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.close, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.lastProgramModel.condSelcted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, \"UPDATE_TEACHER_PG.SELECT_PROGRAM\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.ProgramsList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedLastPrograms);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.lastProgramModel.isRequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 13, \"GENERAL.MANDATORY\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 15, \"GENERAL.SAVE\"), \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_1_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", item_r6.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r6.nameEn : item_r6.nameAr, \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 28)(8, \"select\", 29)(9, \"option\", 9);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SettingLastProgramComponent_ng_container_1_option_12_Template, 2, 2, \"option\", 10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"CONDITIONDS.PASSING_LAST_PROGRAM\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 6, \"UPDATE_TEACHER_PG.SELECT_PROGRAM\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.ProgramsList);\n  }\n}\nfunction SettingLastProgramComponent_ng_container_2_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? p_r7.nameEn : p_r7.nameAr, \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_2_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL.MANDATORY\"), \" \");\n  }\n}\nfunction SettingLastProgramComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"label\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" \\u00A0 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \" \\u00A0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"label\", 15);\n    i0.ɵɵtemplate(8, SettingLastProgramComponent_ng_container_2_span_8_Template, 2, 1, \"span\", 16)(9, SettingLastProgramComponent_ng_container_2_span_9_Template, 3, 3, \"span\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.lastProgramModel.title, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedLastPrograms);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lastProgramModel.isRequired);\n  }\n}\nexport let SettingLastProgramComponent = /*#__PURE__*/(() => {\n  class SettingLastProgramComponent {\n    ProgramService;\n    translate;\n    programConditionsService;\n    dialog;\n    alertify;\n    imagesPathesService;\n    progIdToLoadProgCond = new EventEmitter();\n    programConditionsModel = {};\n    ViewprogCondmood = ProgramConditionViewMoodEnum.conditionSettingViewMood;\n    lastProgramModel = {};\n    resultMessage = {};\n    updateProgramConditionDetailsModel = {};\n    result = '';\n    progData = [];\n    ProgramsList = [];\n    programFilterByNameFilterRequest = {};\n    langEnum = LanguageEnum;\n    selectedLastPrograms = Array();\n    programConditionViewMoodEnum = ProgramConditionViewMoodEnum;\n    constructor(ProgramService, translate, programConditionsService, dialog, alertify, imagesPathesService) {\n      this.ProgramService = ProgramService;\n      this.translate = translate;\n      this.programConditionsService = programConditionsService;\n      this.dialog = dialog;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getPrograms();\n      this.populateData();\n    }\n    populateData() {\n      this.lastProgramModel = JSON.parse(this.programConditionsModel.progCondValue || '{}') || {};\n      this.lastProgramModel.id = this.programConditionsModel.id;\n      this.lastProgramModel.condId = this.programConditionsModel.condId;\n      this.lastProgramModel.isRequired = this.programConditionsModel.condRequired;\n      this.lastProgramModel.progId = this.programConditionsModel.progId;\n      this.lastProgramModel.title = this.programConditionsModel.title;\n      this.selectedLastPrograms = this.lastProgramModel.value || [];\n    }\n    getPrograms() {\n      this.programFilterByNameFilterRequest = {\n        // name: '',\n        take: 2147483647\n      };\n      this.ProgramService.getAllPrograms(this.programFilterByNameFilterRequest).subscribe(res => {\n        let response = res;\n        this.progData = response.data;\n        this.ProgramsList = this.progData.map(item => ({\n          id: item.id,\n          nameAr: item.progName,\n          nameEn: item.progName\n        }));\n      }, error => {\n        //logging\n      });\n    }\n    addDegreeItem() {\n      if (this.lastProgramModel.condSelcted !== \"null\" && this.lastProgramModel.condSelcted !== undefined) {\n        this.lastProgramModel.value = [];\n        const exist = this.selectedLastPrograms.some(el => el.id === this.lastProgramModel.condSelcted);\n        if (!exist) {\n          this.selectedLastPrograms.push(this.ProgramsList.filter(el => el.id == this.lastProgramModel.condSelcted)[0]);\n        }\n        this.lastProgramModel.value = this.selectedLastPrograms;\n      }\n    }\n    removeItemFromSelectedLastPrograms(item) {\n      let index = this.selectedLastPrograms.indexOf(item);\n      this.selectedLastPrograms.splice(index, 1);\n    }\n    saveProgramConditions() {\n      this.resultMessage = {};\n      this.updateProgramConditionDetailsModel.id = this.lastProgramModel.id;\n      this.lastProgramModel.condSelcted = \"null\";\n      this.updateProgramConditionDetailsModel.progCondDetails = JSON.stringify(this.lastProgramModel);\n      this.updateProgramConditionDetailsModel.isRequired = this.lastProgramModel.isRequired;\n      this.programConditionsService.updateProgramConditionDetails(this.updateProgramConditionDetailsModel).subscribe(res => {\n        let response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    confirmDialog() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this condition\" : \"هل متأكد من حذف هذا الشرط\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete condition' : 'حذف الشرط', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n        if (dialogResult == true) {\n          this.programConditionsService.deleteProgramCondition(this.lastProgramModel.id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.progIdToLoadProgCond.emit(this.lastProgramModel.progId);\n              this.alertify.success(res.message || '');\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    static ɵfac = function SettingLastProgramComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingLastProgramComponent)(i0.ɵɵdirectiveInject(i1.ProgramService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ProgramConditionsService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingLastProgramComponent,\n      selectors: [[\"app-setting-last-program\"]],\n      inputs: {\n        programConditionsModel: \"programConditionsModel\",\n        ViewprogCondmood: \"ViewprogCondmood\"\n      },\n      outputs: {\n        progIdToLoadProgCond: \"progIdToLoadProgCond\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[4, \"ngIf\"], [1, \"card-setting\", \"viewMode\"], [1, \"head\"], [1, \"close\", 3, \"click\", \"src\"], [1, \"body\"], [1, \"form-group\", \"w-100\"], [1, \"input-group\"], [1, \"input-group\", \"pr-2rem\"], [\"name\", \"nameEn\", 1, \"UpdateUser__Options\", \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"plus-btn\", \"position-absolute\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-add\", 3, \"click\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [1, \"row\", \"internal_scroll_for_badge\"], [1, \"badges\"], [\"class\", \"badge badge-info p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"buttons-center\"], [1, \"switch\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"slider\", \"round\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [3, \"value\"], [1, \"badge\", \"badge-info\", \"p-2\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"ellipsis\", \"mb-0\", 3, \"title\"], [3, \"click\"], [1, \"fas\", \"fa-times-circle\"], [1, \"form-group\", \"w-90\"], [\"name\", \"nameEn\", 1, \"UpdateUser__Options\", \"form-control\"], [1, \"title\"], [\"class\", \"requiredFlag\", 4, \"ngIf\"], [1, \"requiredFlag\"]],\n      template: function SettingLastProgramComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SettingLastProgramComponent_ng_container_0_Template, 30, 17, \"ng-container\", 0)(1, SettingLastProgramComponent_ng_container_1_Template, 13, 8, \"ng-container\", 0)(2, SettingLastProgramComponent_ng_container_2_Template, 10, 3, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionProgramViewMood);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionSettingViewMood);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ViewprogCondmood === ctx.programConditionViewMoodEnum.conditionBasicInfoViewMood);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card-setting[_ngcontent-%COMP%]{padding:1rem;width:100%;height:100%;background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem;opacity:1}.card-setting[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:1rem;margin-bottom:.5rem;display:flex;justify-content:space-between}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-size:.875rem;color:#333}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto}.card-setting[_ngcontent-%COMP%]   .w-90[_ngcontent-%COMP%]{width:90%!important}.card-setting[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0}.card-setting[_ngcontent-%COMP%]   .buttons-center[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin-left:.4rem;margin-right:.4rem}.title[_ngcontent-%COMP%]{color:var(--second_color);font-size:.875rem;font-weight:700}.requiredFlag[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.875rem;font-family:normal normal normal;opacity:1}.internal_scroll_for_badge[_ngcontent-%COMP%]{height:2.5rem;overflow-y:auto}\"]\n    });\n  }\n  return SettingLastProgramComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}