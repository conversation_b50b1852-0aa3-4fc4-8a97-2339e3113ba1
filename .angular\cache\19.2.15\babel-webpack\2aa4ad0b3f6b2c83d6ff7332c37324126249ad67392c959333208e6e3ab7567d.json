{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { OurProgramOrAllProgramEmun } from 'src/app/core/enums/programs/our-program-or-all-program.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i3 from \"src/app/core/services/calls-services/recitationExplanationGroup/recitation-explanation-group.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i5 from \"@angular/common\";\nfunction StudentGroupCardsComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function StudentGroupCardsComponent_ng_container_10_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.joinStudentToGroup());\n    });\n    i0.ɵɵtext(3, \" \\u0637\\u0644\\u0628 \\u0627\\u0646\\u0636\\u0645\\u0627\\u0645 \\u0644\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport let StudentGroupCardsComponent = /*#__PURE__*/(() => {\n  class StudentGroupCardsComponent {\n    translate;\n    alertify;\n    groupExplanationServices;\n    imagesPathesService;\n    item;\n    reLoadPage = new EventEmitter();\n    typeEnum = OurProgramOrAllProgramEmun.MyProgram;\n    statusEnum = OurProgramOrAllProgramEmun;\n    currentUser;\n    langEnum = LanguageEnum;\n    constructor(translate, alertify, groupExplanationServices, imagesPathesService) {\n      this.translate = translate;\n      this.alertify = alertify;\n      this.groupExplanationServices = groupExplanationServices;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem('user') || '{}');\n    }\n    joinStudentToGroup() {\n      let model = {\n        explanationGroupId: this.item?.id,\n        studId: this.currentUser?.id\n      };\n      this.groupExplanationServices.joinStudentToGroup(model).subscribe(res => {\n        if (res.isSuccess) {\n          this.reLoadPage.emit();\n          this.alertify.success(res.message || '');\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function StudentGroupCardsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentGroupCardsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.RecitationExplanationGroupService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentGroupCardsComponent,\n      selectors: [[\"app-student-group-cards\"]],\n      inputs: {\n        item: \"item\",\n        typeEnum: \"typeEnum\"\n      },\n      outputs: {\n        reLoadPage: \"reLoadPage\"\n      },\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"chat_details\", \"studentGroupCards\", \"w-100\"], [1, \"w-100\"], [1, \"cardRecourd\"], [1, \"group_name\", \"d-flex\", \"pb-0\", \"align-items-center\", \"justify-content-start\", \"mb-3\"], [1, \"img_user\", 3, \"src\"], [1, \"ellipsis\", \"group_name\", \"mb-0\"], [1, \"teacher_name\", \"mb-0\", \"px-2\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"cancel-btn\", 3, \"click\"]],\n      template: function StudentGroupCardsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"div\")(6, \"a\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, StudentGroupCardsComponent_ng_container_10_Template, 4, 0, \"ng-container\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.group, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.item == null ? null : ctx.item.nameEn : ctx.item == null ? null : ctx.item.nameAr, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.item == null ? null : ctx.item.teachEn : ctx.item == null ? null : ctx.item.teachAr, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.typeEnum == ctx.statusEnum.AllProgram);\n        }\n      },\n      dependencies: [i5.NgIf],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.studentGroupCards[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.1875rem;opacity:1;margin-top:.5rem}.studentGroupCards[_ngcontent-%COMP%]:lang(en){text-align:left}.studentGroupCards[_ngcontent-%COMP%]:lang(ar){text-align:right}.studentGroupCards[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.85rem}.studentGroupCards[_ngcontent-%COMP%]   .teacher_name[_ngcontent-%COMP%]{color:#333;font-weight:700;font-size:.85rem}.studentGroupCards[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]{color:#333;font-size:.5625rem}.studentGroupCards[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:100%}.studentGroupCards[_ngcontent-%COMP%]   .cardRecourd[_ngcontent-%COMP%]{padding:.5rem;background-color:#fff;border:none;border:.094rem solid #d6d7d8;border-radius:1.25rem;margin:.2rem;box-shadow:none;width:80%}.studentGroupCards[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:3.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem;background:#c1c7d0;padding:.5rem}@media (max-width: 64rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:2rem!important;font-size:.7rem}}\"]\n    });\n  }\n  return StudentGroupCardsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}