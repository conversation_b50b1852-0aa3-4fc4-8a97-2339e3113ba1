{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport let UpdateTeacherProfileHasUnsavedDataGuard = /*#__PURE__*/(() => {\n  class UpdateTeacherProfileHasUnsavedDataGuard {\n    translate;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    canDeactivate(component) {\n      // if (component.userService.canDecativate) {\n      //     return confirm(this.translate.instant('GENERAL.UNSAVED_CHANGES'))\n      // }\n      return true;\n    }\n    static ɵfac = function UpdateTeacherProfileHasUnsavedDataGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UpdateTeacherProfileHasUnsavedDataGuard)(i0.ɵɵinject(i1.TranslateService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UpdateTeacherProfileHasUnsavedDataGuard,\n      factory: UpdateTeacherProfileHasUnsavedDataGuard.ɵfac\n    });\n  }\n  return UpdateTeacherProfileHasUnsavedDataGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}