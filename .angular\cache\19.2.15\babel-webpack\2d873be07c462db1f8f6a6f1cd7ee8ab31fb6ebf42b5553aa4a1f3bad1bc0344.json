{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { DoughnutChartDegreeCountComponent } from 'src/app/shared/components/admin-dash-board-widgets/doughnut-chart-degree-count/doughnut-chart-degree-count.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-batches-service/program-batches.service\";\nimport * as i2 from \"src/app/core/services/admin-dash-bord-services/admin-dash-board.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/forms\";\nfunction AdminDashBoardStudTotalTaskDegreeWidgetsComponent_ng_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bat_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", bat_r1.batId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? bat_r1.progBatNameEn : bat_r1.progBatNameAr, \" \");\n  }\n}\nfunction AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-milady-hijri-calendar\", 13);\n    i0.ɵɵlistener(\"sendDate\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_19_Template_app_milady_hijri_calendar_sendDate_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changFromDate($event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_19_Template_app_milady_hijri_calendar_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.fromDate, $event) || (ctx_r1.fromDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dateTo\", ctx_r1.fromDateInputParam)(\"hijri\", true)(\"milady\", false)(\"maxHijri\", ctx_r1.maxHijriDate)(\"maxGreg\", ctx_r1.maxGregDate);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.fromDate);\n  }\n}\nfunction AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-milady-hijri-calendar\", 13);\n    i0.ɵɵlistener(\"sendDate\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_23_Template_app_milady_hijri_calendar_sendDate_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.chanToDate($event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_23_Template_app_milady_hijri_calendar_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.toDate, $event) || (ctx_r1.toDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dateTo\", ctx_r1.toDateInputParam)(\"hijri\", true)(\"milady\", false)(\"maxHijri\", ctx_r1.maxHijriDate)(\"maxGreg\", ctx_r1.maxGregDate);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.toDate);\n  }\n}\nfunction AdminDashBoardStudTotalTaskDegreeWidgetsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"app-doughnut-chart-degree-count\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"studTotalTaskDegree\", ctx_r1.adminDashBoardTotalTaskDegree);\n  }\n}\nexport let AdminDashBoardStudTotalTaskDegreeWidgetsComponent = /*#__PURE__*/(() => {\n  class AdminDashBoardStudTotalTaskDegreeWidgetsComponent {\n    programBatchesService;\n    adminDashBoardService;\n    translate;\n    alertfy;\n    dateFormatterService;\n    studTotalTaskDegreeEchart;\n    allProgBatchs = [];\n    batId;\n    adminDashBoardStudTotalTaskDegreeReq;\n    adminDashBoardTotalTaskDegree = {\n      percentage: 0\n    };\n    maxHijriDate;\n    maxGregDate;\n    fromDateInputParam; //= {year: 0, day: 0, month: 0};\n    toDateInputParam; //= {year: 0, day: 0, month: 0};\n    hijri = false;\n    milady = false;\n    hijriBinding;\n    selectedDateType;\n    fromDate;\n    toDate;\n    langEnum = LanguageEnum;\n    constructor(programBatchesService, adminDashBoardService, translate, alertfy, dateFormatterService) {\n      this.programBatchesService = programBatchesService;\n      this.adminDashBoardService = adminDashBoardService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n      this.dateFormatterService = dateFormatterService;\n    }\n    ngOnInit() {\n      this.fromDate = new Date();\n      this.toDate = new Date();\n      this.fromDateInputParam = {\n        year: this.fromDate?.getFullYear(),\n        month: this.fromDate?.getMonth() + 1,\n        day: this.fromDate?.getDate()\n      };\n      this.toDateInputParam = {\n        year: this.fromDate?.getFullYear(),\n        month: this.fromDate?.getMonth() + 1,\n        day: this.fromDate?.getDate()\n      };\n      this.getAllProgs();\n      this.getAdminDashBoardStudTotalTaskDegree();\n    }\n    getAllProgs() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.adminDashBoardTotalTaskDegree = {\n        percentage: 0\n      };\n      this.getAdminDashBoardStudTotalTaskDegree();\n    }\n    getAdminDashBoardStudTotalTaskDegree() {\n      this.adminDashBoardStudTotalTaskDegreeReq = {\n        batId: this.batId,\n        fromDate: this.fromDate,\n        toDate: this.toDate\n      };\n      if (this.fromDate && this.toDate) {\n        if (new Date(this.toDate) < new Date(this.fromDate)) {\n          this.translate.instant('ADMIN_DASH_BORD.DATE_FROM_IS_BIGGER_THAN_DATE_TO');\n          this.alertfy.error(this.translate.instant('ADMIN_DASH_BORD.DATE_FROM_IS_BIGGER_THAN_DATE_TO') || \"\");\n          this.adminDashBoardTotalTaskDegree = {\n            percentage: 0\n          };\n          this.studTotalTaskDegreeEchart?.initiate(this.adminDashBoardTotalTaskDegree);\n          return;\n        }\n      }\n      this.adminDashBoardService.getAdminDashBoardStudTotalTaskDegree(this.adminDashBoardStudTotalTaskDegreeReq || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.adminDashBoardTotalTaskDegree = res.data;\n          this.studTotalTaskDegreeEchart?.initiate(this.adminDashBoardTotalTaskDegree);\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    setHijri() {\n      let toDayHijriDate = this.dateFormatterService.GetTodayHijri();\n      toDayHijriDate.day = toDayHijriDate.day;\n      this.maxHijriDate = toDayHijriDate;\n    }\n    setGreg() {\n      let toDayGreDate = this.dateFormatterService.GetTodayGregorian();\n      toDayGreDate.day = toDayGreDate.day;\n      this.maxGregDate = toDayGreDate;\n    }\n    changFromDate(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.fromDate = data.selectedDateValue;\n      this.getAdminDashBoardStudTotalTaskDegree();\n    }\n    chanToDate(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.toDate = data.selectedDateValue;\n      this.getAdminDashBoardStudTotalTaskDegree();\n    }\n    static ɵfac = function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardStudTotalTaskDegreeWidgetsComponent)(i0.ɵɵdirectiveInject(i1.ProgramBatchesService), i0.ɵɵdirectiveInject(i2.AdminDashBoardService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService), i0.ɵɵdirectiveInject(i5.DateFormatterService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashBoardStudTotalTaskDegreeWidgetsComponent,\n      selectors: [[\"app-admin-dash-board-stud-total-task-degree-widgets\"]],\n      viewQuery: function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(DoughnutChartDegreeCountComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.studTotalTaskDegreeEchart = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 20,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\", \"pt-3\", \"mt-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"col-6\", \"px-0\"], [1, \"head\"], [1, \"col-6\"], [\"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"px-3\"], [1, \"row\"], [\"for\", \"hijriBirthDate\", 1, \"Register__Label\"], [3, \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"ngModel\", \"sendDate\", \"ngModelChange\", 4, \"ngIf\"], [\"class\", \"col-6\", 4, \"ngIf\"], [3, \"sendDate\", \"ngModelChange\", \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"ngModel\"], [3, \"studTotalTaskDegree\"]],\n      template: function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"ng-select\", 5);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_Template_ng_select_ngModelChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminDashBoardStudTotalTaskDegreeWidgetsComponent_Template_ng_select_change_7_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementStart(9, \"ng-option\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AdminDashBoardStudTotalTaskDegreeWidgetsComponent_ng_option_12_Template, 2, 2, \"ng-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 4)(16, \"label\", 10);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_19_Template, 1, 6, \"app-milady-hijri-calendar\", 11);\n          i0.ɵɵelementStart(20, \"label\", 10);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, AdminDashBoardStudTotalTaskDegreeWidgetsComponent_app_milady_hijri_calendar_23_Template, 1, 6, \"app-milady-hijri-calendar\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, AdminDashBoardStudTotalTaskDegreeWidgetsComponent_div_24_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 10, \"ADMIN_DASH_BORD.COUNT_TASK_EXAM_PROGRAMS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(8, 12, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 14, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allProgBatchs);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 16, \"ADMIN_DASH_BORD.FROM_DATE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.fromDateInputParam);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 18, \"ADMIN_DASH_BORD.TO_DATE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.toDateInputParam);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalTaskDegree);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.NgSelectComponent, i7.NgOptionComponent, i8.NgControlStatus, i8.NgModel, i3.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .Register__Label[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}\"]\n    });\n  }\n  return AdminDashBoardStudTotalTaskDegreeWidgetsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}