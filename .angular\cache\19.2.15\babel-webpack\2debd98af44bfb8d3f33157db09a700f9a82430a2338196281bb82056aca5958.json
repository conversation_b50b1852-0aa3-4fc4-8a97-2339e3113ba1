{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/question-bank-services/question-bank-category.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"red-border-class\": a0\n});\nfunction AddQuestionBankCategoryComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.NAME_AR_MAX_LENGHT\"), \" \");\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, AddQuestionBankCategoryComponent_div_6_div_1_Template, 3, 3, \"div\", 12)(2, AddQuestionBankCategoryComponent_div_6_div_2_Template, 3, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.nameAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.nameAr.errors == null ? null : ctx_r0.f.nameAr.errors.maxlength);\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.NAME_EN_MAX_LENGHT\"), \" \");\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, AddQuestionBankCategoryComponent_div_12_div_1_Template, 3, 3, \"div\", 12)(2, AddQuestionBankCategoryComponent_div_12_div_2_Template, 3, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.nameEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.nameEn.errors == null ? null : ctx_r0.f.nameEn.errors.maxlength);\n  }\n}\nfunction AddQuestionBankCategoryComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \" \");\n  }\n}\nexport let AddQuestionBankCategoryComponent = /*#__PURE__*/(() => {\n  class AddQuestionBankCategoryComponent {\n    questionBankCategoryService;\n    activeroute;\n    router;\n    translate;\n    fb;\n    _alertify;\n    //@Input() user: any;\n    Title;\n    QuestionBankCategoryId = '';\n    QuestionBankCategory;\n    QuestionBankCategoryCreat = {};\n    QuestionBankCategoryUpdate = {};\n    isAdd = true;\n    // currentWindowWidth?: number;\n    errorMessage;\n    maxDate;\n    // msgs: Message[] = [];\n    currentForm = new FormGroup({});\n    formImport;\n    successMessage;\n    isSubmit = false;\n    resultMessage = {};\n    disableSaveButtons = false;\n    inputCategoryId;\n    closeCategoryForm = new EventEmitter();\n    addCategory = new EventEmitter();\n    submitSuccess = new EventEmitter();\n    constructor(questionBankCategoryService, activeroute, router, translate, fb, _alertify) {\n      this.questionBankCategoryService = questionBankCategoryService;\n      this.activeroute = activeroute;\n      this.router = router;\n      this.translate = translate;\n      this.fb = fb;\n      this._alertify = _alertify;\n      this.formImport = new FormGroup({\n        importFile: new FormControl('', Validators.required)\n      });\n    }\n    ngOnInit() {\n      this.QuestionBankCategoryId = this.inputCategoryId || \"\";\n      if (this.QuestionBankCategoryId !== \"\") {\n        this.isAdd = false;\n        this.currentForm.reset();\n        this.populate();\n      } else {\n        this.currentForm.reset();\n        this.isAdd = true;\n      }\n      this.buildForm();\n    }\n    ngOnChanges(changes) {\n      this.currentForm.reset();\n      this.QuestionBankCategoryId = this.inputCategoryId || \"\";\n      if (this.QuestionBankCategoryId !== \"\") {\n        this.populate();\n      }\n      if (this.QuestionBankCategoryId == \"\") {\n        this.currentForm.reset();\n      }\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n    }\n    get f() {\n      return this.currentForm?.controls;\n    }\n    buildForm() {\n      // const arabicWordPattern = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9]+$\";\n      // const englishWordPattern =\"^[a-zA-Z0-9' '-'\\s]{1,40}$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+\\\\-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}A-Za-z 0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[ A-Za-z0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      this.currentForm = this.fb.group({\n        nameAr: ['', [Validators.required, Validators.maxLength(100)]],\n        nameEn: ['', [Validators.required, Validators.maxLength(100)]]\n      });\n    }\n    populate() {\n      this.questionBankCategoryService.getQuestionBankCategoryDetails(this.QuestionBankCategoryId).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.QuestionBankCategory = response.data;\n          this.f.nameAr.setValue(this.QuestionBankCategory?.arabCatgName);\n          this.f.nameEn.setValue(this.QuestionBankCategory?.engCatgName);\n        } else {\n          // this.errorMessage = response.message;\n          this.resultMessage = {\n            message: response.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    Submit() {\n      this.isSubmit = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.resultMessage = {};\n      if (this.currentForm.valid) {\n        if (this.QuestionBankCategoryId) {\n          this.QuestionBankCategoryUpdate.id = this.QuestionBankCategoryId;\n          this.QuestionBankCategoryUpdate.no = this.QuestionBankCategory?.no;\n          this.QuestionBankCategoryUpdate.arabCatgName = this.f.nameAr.value;\n          this.QuestionBankCategoryUpdate.engCatgName = this.f.nameEn.value;\n          this.questionBankCategoryService.UpdateQuestionBankCategory(this.QuestionBankCategoryUpdate).subscribe(res => {\n            if (res.isSuccess) {\n              this.isSubmit = false;\n              this.resultMessage = {\n                message: res.message || \"\",\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.loodCategoryList();\n              this.submitSuccess?.emit(false); //close form after submit is success\n              this._alertify.success(res.message || \"\");\n            } else {\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        } else {\n          this.QuestionBankCategoryCreat.arabCatgName = this.f.nameAr.value;\n          this.QuestionBankCategoryCreat.engCatgName = this.f.nameEn.value;\n          this.questionBankCategoryService.addQuestionBankCategory(this.QuestionBankCategoryCreat).subscribe(res => {\n            this.isSubmit = false;\n            if (res.isSuccess) {\n              this.resultMessage = {\n                message: res.message || \"\",\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.loodCategoryList();\n              this.submitSuccess?.emit(false); //close form after submit is success\n              this._alertify.success(res.message || \"\");\n            } else {\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        }\n      }\n    }\n    loodCategoryList() {\n      this.addCategory.emit(true);\n    }\n    backListCatogry() {\n      this.closeCategoryForm?.emit(false);\n    }\n    static ɵfac = function AddQuestionBankCategoryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddQuestionBankCategoryComponent)(i0.ɵɵdirectiveInject(i1.QuestionBankCategoryService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddQuestionBankCategoryComponent,\n      selectors: [[\"app-add-question-bank-category\"]],\n      inputs: {\n        inputCategoryId: \"inputCategoryId\"\n      },\n      outputs: {\n        closeCategoryForm: \"closeCategoryForm\",\n        addCategory: \"addCategory\",\n        submitSuccess: \"submitSuccess\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 22,\n      vars: 23,\n      consts: [[1, \"form-group\", \"material-form\", 3, \"submit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"exampleInputEmail1\", 1, \"Catogry_Add_TextNam\"], [\"type\", \"text\", \"id\", \"InputNamAr\", \"formControlName\", \"nameAr\", 1, \"form-control\", \"Catogry_Add_Text\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"for\", \"exampleInputPassword1\", 1, \"Catogry_Add_TextNam\"], [\"type\", \"text\", \"id\", \"InputNamEN\", \"formControlName\", \"nameEn\", 1, \"form-control\", \"Catogry_Add_Text\", 3, \"ngClass\"], [1, \"row\"], [\"type\", \"submit\", 1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"disabled\"], [\"type\", \"button\", 1, \"cancel-btn\", \"btn\", \"btn-warning\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"]],\n      template: function AddQuestionBankCategoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"submit\", function AddQuestionBankCategoryComponent_Template_form_submit_0_listener() {\n            return ctx.Submit();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"input\", 3);\n          i0.ɵɵtemplate(6, AddQuestionBankCategoryComponent_div_6_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 1)(8, \"label\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 6);\n          i0.ɵɵtemplate(12, AddQuestionBankCategoryComponent_div_12_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"section\")(14, \"div\", 7)(15, \"button\", 8);\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AddQuestionBankCategoryComponent_Template_button_click_18_listener() {\n            return ctx.backListCatogry();\n          });\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(21, AddQuestionBankCategoryComponent_div_21_Template, 2, 4, \"div\", 10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.currentForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 11, \"QUESTION_BANK.DEPARTMENT_AR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.f.nameAr.errors && (ctx.f.nameAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.nameAr.errors && (ctx.f.nameAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 13, \"QUESTION_BANK.DEPARTMENT_EN\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx.f.nameEn.errors && (ctx.f.nameEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.nameEn.errors && (ctx.f.nameEn.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.disableSaveButtons);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 15, \"QUESTION_BANK.SAVE\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 17, \"QUESTION_BANK.CANCEL\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n        }\n      },\n      dependencies: [i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i6.NgClass, i6.NgIf, i3.TranslatePipe],\n      styles: [\".material-form[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border:.063rem solid #fbfbfb;opacity:1;padding:3rem 1rem 1rem}.material-form[_ngcontent-%COMP%]:lang(ar){text-align:right}.material-form[_ngcontent-%COMP%]:lang(en){text-align:left}.material-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:.875rem;color:gray;text-align:right;letter-spacing:0;opacity:1}.material-form[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.material-form[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}\"]\n    });\n  }\n  return AddQuestionBankCategoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}