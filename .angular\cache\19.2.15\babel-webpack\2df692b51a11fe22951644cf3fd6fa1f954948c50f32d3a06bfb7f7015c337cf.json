{"ast": null, "code": "import { CallTypesEnum } from '../../../../../core/enums/call-types-enum.enum';\nimport { EventEmitter } from '@angular/core';\nimport { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/common\";\nfunction AdminTeacherCallComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 5);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 6, \"GENERAL.NAME\"), \" : \", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? ctx_r0.adminTaecherCallEvent == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo.teacherNameEn : ctx_r0.adminTaecherCallEvent == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo.teacherNameEn, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(6, 8, \"GENERAL.HUFFAZ_NUM\"), \" : \", ctx_r0.adminTaecherCallEvent == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo.huffazNumber, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(9, 10, \"GENERAL.REQUESTDATE\"), \" : \", ctx_r0.adminTaecherCallEvent == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo == null ? null : ctx_r0.adminTaecherCallEvent.profileInfo.requestDate, \" \");\n  }\n}\nfunction AdminTeacherCallComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"app-jitsi-call-interview\", 7);\n    i0.ɵɵlistener(\"endCallEvent\", function AdminTeacherCallComponent_ng_container_5_Template_app_jitsi_call_interview_endCallEvent_2_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.endCall($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"callInitModel\", ctx_r0.callInitModel)(\"jitsiSettingOptions\", ctx_r0.jitsiSettingOptions);\n  }\n}\nexport let AdminTeacherCallComponent = /*#__PURE__*/(() => {\n  class AdminTeacherCallComponent {\n    translate;\n    router;\n    dialog;\n    adminTaecherCallEvent;\n    callInitModel;\n    currentUser;\n    roleEnum = CallTypesEnum;\n    jitsiSettingOptions;\n    callId;\n    langEnum = LanguageEnum;\n    constructor(translate, router, dialog) {\n      this.translate = translate;\n      this.router = router;\n      this.dialog = dialog;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\") || '{}');\n      this.initCall();\n    }\n    initCall() {\n      this.callInitModel = {\n        modrId: this.currentUser?.id,\n        toUserId: this.adminTaecherCallEvent?.profileInfo?.usrId,\n        callModuleEntityId: this.adminTaecherCallEvent?.profileInfo?.id,\n        callModuleTypehuffazId: CallTypesEnum.Interview,\n        isVideoMute: false\n      };\n      this.jitsiSettingOptions = {\n        isVideoMute: this.adminTaecherCallEvent?.isVideoMute,\n        startCallType: StartCallTypesEnum.StartMode,\n        endCallType: EndCallTypesEnum.InterviewAdminModrEndCall\n      };\n    }\n    endCallEvent = new EventEmitter();\n    endCall(event) {\n      this.endCallEvent.emit(event);\n    }\n    static ɵfac = function AdminTeacherCallComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminTeacherCallComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminTeacherCallComponent,\n      selectors: [[\"app-admin-teacher-call\"]],\n      inputs: {\n        adminTaecherCallEvent: \"adminTaecherCallEvent\"\n      },\n      outputs: {\n        endCallEvent: \"endCallEvent\"\n      },\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"row\", \"mx-0\", \"calls\"], [1, \"col-3\"], [1, \"cus_info\"], [4, \"ngIf\"], [1, \"col-9\", \"px-0\"], [1, \"info_personal\"], [1, \"call-integ\"], [3, \"endCallEvent\", \"callInitModel\", \"jitsiSettingOptions\"]],\n      template: function AdminTeacherCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AdminTeacherCallComponent_ng_container_3_Template, 10, 12, \"ng-container\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, AdminTeacherCallComponent_ng_container_5_Template, 3, 2, \"ng-container\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.adminTaecherCallEvent);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.callInitModel);\n        }\n      },\n      dependencies: [i4.NgIf, i1.TranslatePipe],\n      styles: [\".call[_ngcontent-%COMP%]   .call-integ[_ngcontent-%COMP%]{display:flex;justify-content:center;background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:74vh;margin-top:1rem}.call[_ngcontent-%COMP%]   .cus_info[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;margin-top:1rem;height:74vh}.call[_ngcontent-%COMP%]   .info_personal[_ngcontent-%COMP%]{font-size:.85rem;font-weight:700}\"]\n    });\n  }\n  return AdminTeacherCallComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}