{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/data/Projects/Mostaneer/huffadh-white-label-app - Copy/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { formatDate } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { Guid } from 'src/app/core/ng-model/generate-guid';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/list\";\nfunction AddGroupComponent_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 20);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 1, \"CHAT_GROUP.GROUP_NAME_AR\"));\n  }\n}\nfunction AddGroupComponent_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.editGroupChat.group_name);\n  }\n}\nfunction AddGroupComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddGroupComponent_div_12_div_1_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.groupNameAr.errors == null ? null : ctx_r0.f.groupNameAr.errors.required);\n  }\n}\nfunction AddGroupComponent_div_13_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.REQUIRED\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddGroupComponent_div_13_div_8_div_1_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.groupNameEn.errors == null ? null : ctx_r0.f.groupNameEn.errors.required);\n  }\n}\nfunction AddGroupComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"label\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 24);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵtemplate(8, AddGroupComponent_div_13_div_8_Template, 2, 1, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(5, 3, \"CHAT_GROUP.GROUP_NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 5, \"CHAT_GROUP.GROUP_NAME_AR\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.groupNameEn.errors && (ctx_r0.f.groupNameEn.touched || ctx_r0.isSubmit));\n  }\n}\nfunction AddGroupComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 2)(3, \"div\", 25)(4, \"div\", 26)(5, \"label\", 27);\n    i0.ɵɵelement(6, \"input\", 28);\n    i0.ɵɵelementStart(7, \"span\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 1, \"CHAT_GROUP.ALLOW_USERS\"), \" \");\n  }\n}\nfunction AddGroupComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 2)(3, \"div\", 25)(4, \"div\", 26)(5, \"label\", 27);\n    i0.ɵɵelement(6, \"input\", 28);\n    i0.ɵɵelementStart(7, \"span\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 1, \"CHAT_GROUP.ALLOW_USERS\"), \" \");\n  }\n}\nfunction AddGroupComponent_mat_list_item_23_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.user_avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddGroupComponent_mat_list_item_23_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 34);\n  }\n  if (rf & 2) {\n    const participant_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", participant_r3.avatar_url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddGroupComponent_mat_list_item_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\");\n    i0.ɵɵtemplate(1, AddGroupComponent_mat_list_item_23_img_1_Template, 1, 1, \"img\", 30)(2, AddGroupComponent_mat_list_item_23_img_2_Template, 1, 1, \"img\", 30);\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_mat_list_item_23_Template_span_click_5_listener() {\n      const participant_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.removeItemFromSelectedParticipant(participant_r3));\n    });\n    i0.ɵɵelement(6, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !participant_r3.avatar_url);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", participant_r3.avatar_url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? participant_r3.name_en : participant_r3.name_ar);\n  }\n}\nfunction AddGroupComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \"\");\n  }\n}\nfunction AddGroupComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.createNewGroup(ctx_r0.createGroupForm.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CHAT_GROUP.ADD\"), \" \");\n  }\n}\nfunction AddGroupComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeCreateGroupOverlayEvent());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CANCEL\"), \" \");\n  }\n}\nfunction AddGroupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNewGroup(ctx_r0.createGroupForm.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CHAT_GROUP.EDIT\"), \" \");\n  }\n}\nfunction AddGroupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AddGroupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeEditGroupOverlayEvent());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CANCEL\"), \" \");\n  }\n}\nexport let AddGroupComponent = /*#__PURE__*/(() => {\n  class AddGroupComponent {\n    fb;\n    alertify;\n    translate;\n    chatService;\n    imagesPathesService;\n    closeCreateGroupOverlay = new EventEmitter();\n    closeEditGroupOverlay = new EventEmitter();\n    createGroupChat = {};\n    editGroupChat;\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    createGroupForm = new FormGroup({});\n    participantsMessage = {};\n    selectedParticipantsList = Array();\n    currentUser;\n    isSubmit = false;\n    constructor(fb, alertify, translate, chatService, imagesPathesService) {\n      this.fb = fb;\n      this.alertify = alertify;\n      this.translate = translate;\n      this.chatService = chatService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.selectedParticipantsList = [];\n      this.chatService.getAllChatGroups();\n      this.chatService.getAllParticipants();\n      this.buildForm();\n      if (this.editGroupChat) {\n        if (this.editGroupChat.participants != null) {\n          this.selectedParticipantsList = this.editGroupChat.participants;\n        }\n        this.PopulateForm();\n      }\n    }\n    get f() {\n      return this.createGroupForm.controls;\n    }\n    buildForm() {\n      this.createGroupForm = this.fb.group({\n        groupNameAr: ['', [Validators.required, Validators.maxLength(256)]],\n        groupNameEn: ['', [Validators.required, Validators.maxLength(256)]],\n        participants: [],\n        allowed: [true]\n      });\n    }\n    createNewGroup(value) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isSubmit = true;\n        var GroupId = Guid.newGuid();\n        var last_date = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en');\n        const room = value;\n        if (_this.createGroupForm.valid) {\n          _this.createGroupChat = {\n            group_name: _this.translate.currentLang === LanguageEnum.ar ? _this.createGroupForm.value.groupNameAr : _this.createGroupForm.value.groupNameEn,\n            allowed: _this.createGroupForm.value.allowed == null || undefined ? true : _this.createGroupForm.value.allowed,\n            key: GroupId,\n            last_date: last_date || '',\n            last_message: \"\",\n            messages: [],\n            participants: []\n          };\n          var IsExist = _this.chatService.allChatGroupsList.some(x => x.group_name === _this.createGroupChat.group_name);\n          if (!IsExist) {\n            if (_this.selectedParticipantsList.length <= 0) {\n              _this.resultMessage = {\n                message: _this.translate.instant('CHAT_GROUP.ADD_PARTICIPANTS_VALIDATION'),\n                type: BaseConstantModel.DANGER_TYPE\n              };\n              return;\n            }\n            _this.createGroupChat.participants = [];\n            if (_this.selectedParticipantsList.length) {\n              // Add Admin Of Group\n              _this.selectedParticipantsList.push({\n                id: _this.currentUser?.id,\n                hoffazId: \"1\",\n                role: RoleEnum.SuperAdmin,\n                name_ar: _this.currentUser?.fullNameAr,\n                name_en: _this.currentUser?.fullNameEn,\n                avatar_url: _this.imagesPathesService.user_avatar,\n                groups: [GroupId],\n                gender: \"Male\"\n              });\n              // Add Participants Of Group\n              Array.from(_this.selectedParticipantsList).forEach(elm => {\n                if (_this.createGroupChat.participants) {\n                  _this.createGroupChat.participants.push({\n                    id: elm.id,\n                    hoffazId: \"1\",\n                    role: elm.role,\n                    name_ar: elm.name_ar,\n                    name_en: elm.name_en,\n                    avatar_url: elm.avatar_url == null || undefined ? _this.imagesPathesService.user_avatar : elm.avatar_url,\n                    groups: [],\n                    gender: \"Male\"\n                  });\n                }\n              });\n            }\n            yield _this.chatService.addGroup(_this.selectedParticipantsList, _this.createGroupChat, GroupId);\n            _this.closeCreateGroupOverlayEvent(_this.createGroupChat);\n            _this.isSubmit = false;\n            _this.alertify.success(_this.translate.instant('CHAT_GROUP.GROUP_ADDED_SUCCESSFULLY'));\n          } else {\n            _this.resultMessage = {\n              message: _this.translate.instant('CHAT_GROUP.GROUP_EXIST'),\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }\n      })();\n    }\n    editNewGroup(value) {\n      var last_date = formatDate(new Date(), 'dd-MM-yyyy HH:mm:ss', 'en');\n      const room = value;\n      this.editGroupChat = {\n        group_name: this.editGroupChat?.group_name,\n        allowed: this.createGroupForm.value.allowed === null || undefined ? this.editGroupChat?.allowed : this.createGroupForm.value.allowed,\n        key: this.editGroupChat?.key,\n        last_date: last_date,\n        last_message: this.editGroupChat?.last_message,\n        messages: this.editGroupChat?.messages,\n        participants: this.editGroupChat?.participants\n      };\n      this.editGroupChat.participants = [];\n      var IsExist = this.selectedParticipantsList.some(x => x.id === this.currentUser?.id);\n      if (IsExist === false) {\n        this.selectedParticipantsList.push({\n          id: this.currentUser?.id,\n          hoffazId: \"1\",\n          role: 1,\n          name_ar: this.currentUser?.fullNameAr,\n          name_en: this.currentUser?.fullNameEn,\n          avatar_url: this.imagesPathesService.user_avatar,\n          gender: \"Male\"\n        });\n      }\n      // this.editGroupChat.participants = [];\n      if (this.selectedParticipantsList.length) {\n        // Edit Participants Of Group\n        Array.from(this.selectedParticipantsList).forEach(elm => {\n          var IsExist = this.editGroupChat?.participants?.some(x => x.id === elm.id);\n          if (this.editGroupChat?.participants) {\n            this.editGroupChat?.participants.push({\n              id: elm.id,\n              hoffazId: \"1\",\n              role: elm.role,\n              name_ar: elm.name_ar,\n              name_en: elm.name_en,\n              avatar_url: elm.avatar_url?.length == 0 ? this.imagesPathesService.user_avatar : elm.avatar_url,\n              groups: [],\n              gender: \"Male\"\n            });\n          }\n        });\n      }\n      this.chatService.getAllGroupBId(this.editGroupChat?.key || '');\n      // this.editGroupChat.allowed = this.createGroupForm.value.allowed;\n      // this.chatService.editGroup(this.selectedParticipantsList, this.chatService.chatGroup || this.editGroupChat, this.editGroupChat?.key);\n      this.chatService.editGroup(this.selectedParticipantsList, this.editGroupChat, this.editGroupChat?.key);\n      this.closeEditGroupOverlayEvent();\n      this.alertify.success(this.translate.instant('CHAT_GROUP.GROUP_UPDATED_SUCCESSFULLY'));\n    }\n    addParticipant(event) {\n      if (!event) {\n        this.participantsMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_REWAYAT'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      if (this.selectedParticipantsList.some(x => x.key === event.key)) {\n        return;\n      }\n      var Data = this.chatService.allParticipantsList.filter(el => el.key == event.key)[0];\n      this.selectedParticipantsList.push({\n        id: event.id,\n        hoffazId: Data.hoffazId,\n        gender: Data.gender,\n        name_ar: Data.name_ar,\n        name_en: Data.name_en,\n        key: Data.key,\n        role: Data.role,\n        avatar_url: Data.avatar_url,\n        groups: Data.groups\n      });\n    }\n    removeItemFromSelectedParticipant(item) {\n      let index = this.selectedParticipantsList.indexOf(item);\n      this.selectedParticipantsList.splice(index, 1);\n    }\n    closeCreateGroupOverlayEvent(event) {\n      this.closeCreateGroupOverlay.emit(event);\n    }\n    closeEditGroupOverlayEvent() {\n      this.closeEditGroupOverlay.emit();\n    }\n    PopulateForm() {\n      this.f.allowed.setValue(this.editGroupChat?.allowed);\n    }\n    static ɵfac = function AddGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddGroupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ChatService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddGroupComponent,\n      selectors: [[\"app-add-group\"]],\n      inputs: {\n        createGroupChat: \"createGroupChat\",\n        editGroupChat: \"editGroupChat\"\n      },\n      outputs: {\n        closeCreateGroupOverlay: \"closeCreateGroupOverlay\",\n        closeEditGroupOverlay: \"closeEditGroupOverlay\"\n      },\n      decls: 31,\n      vars: 23,\n      consts: [[1, \"form-group\", \"DataForm\", \"mt-5\", \"pl-3\", \"pr-3\", 3, \"formGroup\"], [1, \"head\", \"bold\"], [1, \"form-group\"], [1, \"row\"], [1, \"col-12\"], [1, \"text-right\", \"header_input\"], [\"id\", \"firstNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"groupNameAr\", \"name\", \"groupNameAr\", \"class\", \"form-control UserRegister__FormControl\", 3, \"placeholder\", 4, \"ngIf\"], [\"disabled\", \"\", \"id\", \"firstNameAr\", \"type\", \"text\", \"name\", \"groupNameAr\", \"class\", \"form-control UserRegister__FormControl\", 3, \"placeholder\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-2 mb-2 pl-4 pr-4\", 4, \"ngIf\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"for\", \"exampleInputEmail1\", 1, \"label\"], [3, \"addSearchItem\", \"searchList\"], [4, \"ngFor\", \"ngForOf\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", \"class\", \"save-btn\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"reset\", \"class\", \"cancel-btn  my-0 \", 3, \"click\", 4, \"ngIf\"], [\"type\", \"reset\", \"class\", \"cancel-btn my-0 \", 3, \"click\", 4, \"ngIf\"], [\"id\", \"firstNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"groupNameAr\", \"name\", \"groupNameAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"disabled\", \"\", \"id\", \"firstNameAr\", \"type\", \"text\", \"name\", \"groupNameAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\", \"pl-4\", \"pr-4\"], [4, \"ngIf\"], [\"id\", \"firstNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"groupNameEn\", \"name\", \"groupNameEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"d-flex\"], [1, \"input-group\"], [1, \"switch\"], [\"type\", \"checkbox\", \"formControlName\", \"allowed\"], [1, \"slider\", \"round\"], [\"mat-list-icon\", \"\", \"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [\"mat-line\", \"\"], [1, \"mat-delete\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [\"mat-list-icon\", \"\", \"alt\", \"\", 3, \"src\"], [1, \"bold\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", \"my-0\", 3, \"click\"]],\n      template: function AddGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"label\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, AddGroupComponent_input_10_Template, 2, 3, \"input\", 6)(11, AddGroupComponent_input_11_Template, 1, 1, \"input\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AddGroupComponent_div_12_Template, 2, 1, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, AddGroupComponent_div_13_Template, 9, 7, \"div\", 9)(14, AddGroupComponent_div_14_Template, 10, 3, \"div\", 10)(15, AddGroupComponent_div_15_Template, 10, 3, \"div\", 10);\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 11);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\")(21, \"app-chat-input-search-list\", 12);\n          i0.ɵɵlistener(\"addSearchItem\", function AddGroupComponent_Template_app_chat_input_search_list_addSearchItem_21_listener($event) {\n            return ctx.addParticipant($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"mat-list\");\n          i0.ɵɵtemplate(23, AddGroupComponent_mat_list_item_23_Template, 8, 3, \"mat-list-item\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, AddGroupComponent_div_24_Template, 3, 4, \"div\", 14);\n          i0.ɵɵelementStart(25, \"section\", 15)(26, \"div\", 16);\n          i0.ɵɵtemplate(27, AddGroupComponent_button_27_Template, 3, 3, \"button\", 17)(28, AddGroupComponent_button_28_Template, 3, 3, \"button\", 18)(29, AddGroupComponent_button_29_Template, 3, 3, \"button\", 17)(30, AddGroupComponent_button_30_Template, 3, 3, \"button\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.createGroupForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 17, \"CHAT_GROUP.ADD_NEW_GROUP\"), \"\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 19, \"CHAT_GROUP.GROUP_NAME_AR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.groupNameAr.errors && (ctx.f.groupNameAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editGroupChat);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 21, \"Role_Management.SELECT_GROUP_MEMBERS\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"searchList\", ctx.chatService.allParticipantsList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.selectedParticipantsList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editGroupChat);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editGroupChat);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.MatList, i7.MatListItem, i7.MatDivider, i3.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.notifyForm[_ngcontent-%COMP%]{height:70vh}.notifyForm[_ngcontent-%COMP%]:lang(en){text-align:left}.notifyForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.notifyForm[_ngcontent-%COMP%]   .header_input[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.notifyForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.notifyForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-size:.875rem}.notifyForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.notifyForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .notifyForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{margin:1rem}.notifyForm[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto!important}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--main_color) 0% 0% no-repeat padding-box}.DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);height:2.31rem;margin:1rem}.DataForm[_ngcontent-%COMP%]   .mat-list-icon[_ngcontent-%COMP%]{width:2.5rem!important;height:2.5rem!important;border-radius:.625rem!important}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:.75rem!important}.DataForm[_ngcontent-%COMP%]   .mat-delete[_ngcontent-%COMP%]{color:#ea5455;cursor:pointer}.DataForm[_ngcontent-%COMP%]   .mat-list[_ngcontent-%COMP%]{height:28vh;overflow-y:auto;overflow-x:hidden}\"]\n    });\n  }\n  return AddGroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}