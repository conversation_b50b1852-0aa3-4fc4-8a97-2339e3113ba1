{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap } from 'zrender/lib/core/util.js';\n;\n;\n;\nexport var VISUAL_DIMENSIONS = createHashMap(['tooltip', 'label', 'itemName', 'itemId', 'itemGroupId', 'itemChildGroupId', 'seriesName']);\nexport var SOURCE_FORMAT_ORIGINAL = 'original';\nexport var SOURCE_FORMAT_ARRAY_ROWS = 'arrayRows';\nexport var SOURCE_FORMAT_OBJECT_ROWS = 'objectRows';\nexport var SOURCE_FORMAT_KEYED_COLUMNS = 'keyedColumns';\nexport var SOURCE_FORMAT_TYPED_ARRAY = 'typedArray';\nexport var SOURCE_FORMAT_UNKNOWN = 'unknown';\nexport var SERIES_LAYOUT_BY_COLUMN = 'column';\nexport var SERIES_LAYOUT_BY_ROW = 'row';\n;\n;\n;\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}