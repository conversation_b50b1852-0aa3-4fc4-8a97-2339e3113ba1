{"ast": null, "code": "import { DropOutRoleEnum } from 'src/app/core/enums/drop-out-request-enums/drop-out-status.enum';\nimport { StudentDropOutRequestStatusEnum } from 'src/app/core/enums/drop-out-request-enums/student-drop-out-request-status.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/student-drop-out-request-services/student-drop-out-request.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nfunction AdminStudentDropOutComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-student-drop-out-grid\", 4);\n    i0.ɵɵlistener(\"studentDropOutRequestFilterStudentEvent\", function AdminStudentDropOutComponent_ng_container_3_Template_app_student_drop_out_grid_studentDropOutRequestFilterStudentEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.studentDropOutRequestChangePage($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"studentDropOutRequestStudentItems\", ctx_r1.studentDropOutRequestList)(\"totalCount\", ctx_r1.totalCount)(\"userMode\", ctx_r1.userMode)(\"typeEnum\", ctx_r1.statusEnum.Empty)(\"studentDropOutRequestFilterRequestStudentModel\", ctx_r1.studentDropOutRequestFilterRequestModel);\n  }\n}\nfunction AdminStudentDropOutComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let AdminStudentDropOutComponent = /*#__PURE__*/(() => {\n  class AdminStudentDropOutComponent {\n    translate;\n    studentDropOutRequestService;\n    alertify;\n    studentIdOutput;\n    StudentDropIdInput;\n    studentDropOutRequestList = [];\n    studentDropOutRequestFilterRequestModel = {\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    resMessage = {};\n    resultMessage = {};\n    totalCount = 0;\n    numberItemsPerRow = 3;\n    ids = [];\n    typeEnum = StudentDropOutRequestStatusEnum.Empty;\n    statusEnum = StudentDropOutRequestStatusEnum;\n    userMode = DropOutRoleEnum.Student;\n    // userMode: ProgramSubscriptionUsersEnum = ProgramSubscriptionUsersEnum.student;\n    showTap = StudentDropOutRequestStatusEnum.Pending;\n    showUserDetailsView = false;\n    constructor(translate, studentDropOutRequestService, alertify) {\n      this.translate = translate;\n      this.studentDropOutRequestService = studentDropOutRequestService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.studentDropOutRequestFilterRequestModel.sortField = 'requestdate';\n      this.getStudentDropOutRequests();\n    }\n    getStudentDropOutRequests() {\n      this.studentDropOutRequestFilterRequestModel.usrId = this.studentIdOutput?.usrId;\n      this.studentDropOutRequestService.studentDropOutRequestAdvFilterStudentView(this.studentDropOutRequestFilterRequestModel).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.studentDropOutRequestList = res.data;\n          this.totalCount = res.count ? res.count : 0;\n          if (this.studentDropOutRequestFilterRequestModel.skip > 0 && (!this.studentDropOutRequestList || this.studentDropOutRequestList.length === 0)) {\n            this.studentDropOutRequestFilterRequestModel.page -= 1;\n            this.studentDropOutRequestFilterRequestModel.skip = (this.studentDropOutRequestFilterRequestModel.page - 1) * this.studentDropOutRequestFilterRequestModel.take;\n            this.getStudentDropOutRequests();\n          }\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    studentDropOutRequestChangePage(event) {\n      this.studentDropOutRequestFilterRequestModel = event;\n      this.getStudentDropOutRequests();\n    }\n    static ɵfac = function AdminStudentDropOutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentDropOutComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.StudentDropOutRequestService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentDropOutComponent,\n      selectors: [[\"app-admin-student-drop-out\"]],\n      inputs: {\n        studentIdOutput: \"studentIdOutput\"\n      },\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"container-fluid\", \"tab_page\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [4, \"ngIf\"], [3, \"studentDropOutRequestFilterStudentEvent\", \"studentDropOutRequestStudentItems\", \"totalCount\", \"userMode\", \"typeEnum\", \"studentDropOutRequestFilterRequestStudentModel\"], [1, \"No_data\"]],\n      template: function AdminStudentDropOutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AdminStudentDropOutComponent_ng_container_3_Template, 2, 5, \"ng-container\", 3)(4, AdminStudentDropOutComponent_ng_container_4_Template, 4, 3, \"ng-container\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.studentDropOutRequestList && ctx.studentDropOutRequestList.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.studentDropOutRequestList || ctx.studentDropOutRequestList && ctx.studentDropOutRequestList.length == 0);\n        }\n      },\n      dependencies: [i4.NgIf, i1.TranslatePipe],\n      styles: [\".tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:78vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}@media all and (min-width: 19in){.interTab[_ngcontent-%COMP%]{height:80vh}}\"]\n    });\n  }\n  return AdminStudentDropOutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}