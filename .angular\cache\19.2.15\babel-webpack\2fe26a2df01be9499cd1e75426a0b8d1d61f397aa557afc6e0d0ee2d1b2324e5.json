{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport let UpdateUserProfileHasUnsavedDataGuard = /*#__PURE__*/(() => {\n  class UpdateUserProfileHasUnsavedDataGuard {\n    translate;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    canDeactivate(component) {\n      // if (component.userService.canDecativate) {\n      //     return confirm(this.translate.instant('GENERAL.UNSAVED_CHANGES'))\n      // }\n      return true;\n    }\n    static ɵfac = function UpdateUserProfileHasUnsavedDataGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UpdateUserProfileHasUnsavedDataGuard)(i0.ɵɵinject(i1.TranslateService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UpdateUserProfileHasUnsavedDataGuard,\n      factory: UpdateUserProfileHasUnsavedDataGuard.ɵfac\n    });\n  }\n  return UpdateUserProfileHasUnsavedDataGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}