{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { AdminMessagingRoutingModule } from './admin-messaging-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { AdminMessagingViewComponent } from './components/admin-messaging-view.component';\nimport { ScientificProblemsComponent } from './components/scientific-problems-view/scientific-problems/scientific-problems.component';\nimport { AddScientifiProblemReplyComponent } from './components/scientific-problems-view/add-scientifi-problem-reply/add-scientifi-problem-reply.component';\nimport { AddScientifiProblemToQuestionBankComponent } from './components/scientific-problems-view/add-scientifi-problem-to-question-bank/add-scientifi-problem-to-question-bank.component';\nimport { ScientificProblemsViewComponent } from './components/scientific-problems-view/scientific-problems-view.component';\nimport { TeacherProgramRequestViewComponent } from './components/teacher-program-request-view/teacher-program-request-view.component';\nimport { StudentProgramRequestViewComponent } from './components/student-program-request-view/student-program-request-view.component';\nimport { StuListRequestComponent } from './components/student-program-request-view/stu-list-request/stu-list-request.component';\nimport { StuJoinRequestComponent } from './components/student-program-request-view/stu-request-details/stu-join-request/stu-join-request.component';\nimport { StuRequestDetailsComponent } from './components/student-program-request-view/stu-request-details/stu-request-details.component';\nimport { StuTabRequestComponent } from './components/student-program-request-view/stu-request-details/stu-join-request/stu-tab-request/stu-tab-request.component';\nimport { AdvancedSearchComponent } from './components/student-program-request-view/stu-request-details/stu-join-request/advanced-search/advanced-search.component';\nimport { TeacherJoinRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request/teacher-join-request.component';\nimport { TeacherListRequestComponent } from './components/teacher-program-request-view/teacher-list-request/teacher-list-request.component';\nimport { TeacherJoinRequestProgramComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request-program/teacher-join-request-program.component';\nimport { ChangTimeRequestComponent } from './components/teacher-program-request-view/teacher-request-details/chang-time-request/chang-time-request.component';\nimport { TeacherJionTabRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request/teacher-join-tab-request/teacher-join-tab-request.component';\nimport { TeacherJionProgramTabRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request-program/teacher-join-program-tab-request/teacher-join-program-tab-request.component';\nimport { StuRejectedComponent } from './components/student-program-request-view/stu-request-details/stu-join-request/stu-rejected/stu-rejected.component';\nimport { TeacheRejectedComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request-program/teache-rejected/teache-rejected.component';\nimport { StuVacationsRequestComponent } from './components/student-program-request-view/stu-request-details/stu-vacations-request/stu-vacations-request.component';\nimport { StuMovingRequestComponent } from './components/student-program-request-view/stu-request-details/stu-moving-request/stu-moving-request.component';\nimport { AdvancedSearchTeacherComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request-program/advanced-search/advanced-search.component';\nimport { TeacherSystemSubscriptionRejectedComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request/teacher-system-subscription-rejected/teacher-system-subscription-rejected.component';\nimport { StudentVacationRequestTabComponent } from './components/student-program-request-view/stu-request-details/stu-vacations-request/student-vacation-request-tab/student-vacation-request-tab.component';\nimport { StudentProgramVacationRejectComponent } from './components/student-program-request-view/stu-request-details/stu-vacations-request/student-program-vacation-reject/student-program-vacation-reject.component';\nimport { StudentProgramVacationAdvancedSearchComponent } from './components/student-program-request-view/stu-request-details/stu-vacations-request/student-program-vacation-advanced-search/student-program-vacation-advanced-search.component';\n// import { TeacherTabRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-join-request-program/teacher-tab-request/teacher-tab-request.component';\nimport { TeacherDropOutRequestRejectedComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-admin-drop-out-request/teacher-drop-out-request-rejected/teacher-drop-out-request-rejected.component';\nimport { TeacherRequestDetailsComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-request-details.component';\nimport { TeacherAdvancedSearchComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-admin-drop-out-request/teacher-advanced-search/teacher-advanced-search.component';\nimport { TeacherDropOutTabRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-admin-drop-out-request/teacher-drop-out-tab-request/teacher-drop-out-tab-request.component';\nimport { TeacherAdminDropOutRequestComponent } from './components/teacher-program-request-view/teacher-request-details/teacher-admin-drop-out-request/teacher-admin-drop-out-request.component';\nimport { StudentDropOutTabRequestComponent } from './components/student-program-request-view/stu-request-details/student-admin-drop-out-request/student-drop-out-tab-request/student-drop-out-tab-request.component';\nimport { StudentDropOutRequestRejectedComponent } from './components/student-program-request-view/stu-request-details/student-admin-drop-out-request/student-drop-out-request-rejected/student-drop-out-request-rejected.component';\nimport { StudentAdvancedSearchComponent } from './components/student-program-request-view/stu-request-details/student-admin-drop-out-request/student-advanced-search/student-advanced-search.component';\nimport { StudentAdminDropOutRequestComponent } from './components/student-program-request-view/stu-request-details/student-admin-drop-out-request/student-admin-drop-out-request.component';\nimport { ChatViewComponent } from './components/chat-view/chat-view.component';\nimport { GroupViewComponent } from './components/chat-view/group-view/group-view.component';\nimport { ChatDetailsComponent } from './components/chat-view/chat-details/chat-details.component';\nimport { GroupDetailsComponent } from './components/chat-view/group-details/group-details.component';\nimport { AddGroupComponent } from './components/chat-view/add-group/add-group.component';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatListModule } from '@angular/material/list';\nimport { TeacherAppointmentAdvancedSearchComponent } from './components/teacher-program-request-view/teacher-request-details/chang-time-request/teacher-appointment-advanced-search/teacher-appointment-advanced-search.component';\nimport { TeacherAppointmentRejectComponent } from './components/teacher-program-request-view/teacher-request-details/chang-time-request/teacher-appointment-reject/teacher-appointment-reject.component';\nimport { AppointmentRequestsTabComponent } from './components/teacher-program-request-view/teacher-request-details/chang-time-request/appointment-requests-tab/appointment-requests-tab.component';\nimport { PdfViewerModule } from 'ng2-pdf-viewer';\nimport { ScientificProblemAdvancedSearchComponent } from './components/scientific-problems-view/scientific-problem-advanced-search/scientific-problem-advanced-search.component';\nimport { CorruptedFilesRequestsViewComponent } from './components/corrupted-files-requests-view/corrupted-files-requests-view.component';\nimport { CorruptedFilesRequestsComponent } from './components/corrupted-files-requests-view/corrupted-files-requests/corrupted-files-requests.component';\nimport { CorruptedFilesRequestsAdvancedSearchComponent } from './components/corrupted-files-requests-view/corrupted-files-requests-advanced-search/corrupted-files-requests-advanced-search.component';\nimport { AdminTeacherCallComponent } from './components/teacher-program-request-view/admin-teacher-call/admin-teacher-call.component';\n// @NgModule({\n//   declarations: [AdminMessagingViewComponent, ScientificProblemsComponent, AddScientifiProblemReplyComponent,\n//     AddScientifiProblemToQuestionBankComponent, ScientificProblemsViewComponent,\n//     TeacherProgramRequestViewComponent, StudentProgramRequestViewComponent,\n//     StuListRequestComponent, StuRequestDetailsComponent, StuJoinRequestComponent, StuQuitRequestComponent,\n//     StuTabRequestComponent, AdvancedSearchComponent],\nlet AdminMessagingModule = class AdminMessagingModule {};\nAdminMessagingModule = __decorate([NgModule({\n  declarations: [AdminMessagingViewComponent, ScientificProblemsComponent, AddScientifiProblemReplyComponent, AddScientifiProblemToQuestionBankComponent, ScientificProblemsViewComponent, TeacherProgramRequestViewComponent, StudentProgramRequestViewComponent, StuListRequestComponent, StuJoinRequestComponent, StuTabRequestComponent, AdvancedSearchComponent, TeacherAdvancedSearchComponent, TeacherListRequestComponent, StuRequestDetailsComponent, TeacherRequestDetailsComponent, TeacherJoinRequestComponent, TeacherJoinRequestProgramComponent, TeacherAdminDropOutRequestComponent, ChangTimeRequestComponent, TeacherJionProgramTabRequestComponent, StuVacationsRequestComponent, StuMovingRequestComponent, TeacherJionTabRequestComponent, StuRejectedComponent, TeacheRejectedComponent, AdvancedSearchTeacherComponent, TeacherSystemSubscriptionRejectedComponent, StudentVacationRequestTabComponent, StudentProgramVacationRejectComponent, StudentProgramVacationAdvancedSearchComponent, TeacherDropOutTabRequestComponent, TeacherDropOutRequestRejectedComponent, StudentAdminDropOutRequestComponent, StudentDropOutTabRequestComponent, StudentDropOutRequestRejectedComponent, StudentAdvancedSearchComponent, ChatViewComponent, GroupViewComponent, ChatDetailsComponent, GroupDetailsComponent, AddGroupComponent, TeacherAppointmentAdvancedSearchComponent, TeacherAppointmentRejectComponent, AppointmentRequestsTabComponent, ScientificProblemAdvancedSearchComponent, CorruptedFilesRequestsViewComponent, CorruptedFilesRequestsComponent, CorruptedFilesRequestsAdvancedSearchComponent, AdminTeacherCallComponent],\n  providers: [DatePipe],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule, SharedModule, MatIconModule, AdminMessagingRoutingModule, PdfViewerModule, MatTreeModule, MatListModule]\n})], AdminMessagingModule);\nexport { AdminMessagingModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}