{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ViewProgramCategoriesComponent } from './view-program-categories/view-program-categories.component';\nimport { AddProgramCategoriesComponent } from './add-program-categories/add-program-categories.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ProgramCategoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-add-program-categories\", 6);\n    i0.ɵɵlistener(\"closeOverlay\", function ProgramCategoriesComponent_div_4_Template_app_add_program_categories_closeOverlay_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForm());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"editModel\", ctx_r1.editModel);\n  }\n}\nexport let ProgramCategoriesComponent = /*#__PURE__*/(() => {\n  class ProgramCategoriesComponent {\n    showAddForm = false;\n    viewProgramCategories;\n    editModel = {};\n    // @Output() addEditCoidition = new EventEmitter<IprogramPredefinedCustomConditionsModel>();\n    constructor() {}\n    ngOnInit() {}\n    addEditProgramCategories(event) {\n      this.showAddForm = true;\n      this.editModel = event;\n    }\n    closeForm() {\n      this.showAddForm = false;\n      this.viewProgramCategories?.getAllCategories();\n    }\n    static ɵfac = function ProgramCategoriesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramCategoriesComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramCategoriesComponent,\n      selectors: [[\"app-program-categories\"]],\n      viewQuery: function ProgramCategoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ViewProgramCategoriesComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewProgramCategories = _t.first);\n        }\n      },\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [3, \"addEditProgramCategories\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeOverlay\", \"editModel\"]],\n      template: function ProgramCategoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-view-program-categories\", 3);\n          i0.ɵɵlistener(\"addEditProgramCategories\", function ProgramCategoriesComponent_Template_app_view_program_categories_addEditProgramCategories_3_listener($event) {\n            return ctx.addEditProgramCategories($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, ProgramCategoriesComponent_div_4_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, ViewProgramCategoriesComponent, AddProgramCategoriesComponent],\n      encapsulation: 2\n    });\n  }\n  return ProgramCategoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}