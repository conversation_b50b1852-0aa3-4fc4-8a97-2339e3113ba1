{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, isFunction, isString, indexOf } from 'zrender/lib/core/util.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as throttle from '../../util/throttle.js';\nimport DataZoomView from './DataZoomView.js';\nimport { linearMap, asc, parsePercent } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { getAxisMainType, collectReferCoordSysModelInfo } from './helper.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, symbolBuildProxies } from '../../util/symbol.js';\nimport { deprecateLog } from '../../util/log.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar Rect = graphic.Rect;\n// Constants\nvar DEFAULT_LOCATION_EDGE_GAP = 7;\nvar DEFAULT_FRAME_BORDER_WIDTH = 1;\nvar DEFAULT_FILLER_SIZE = 30;\nvar DEFAULT_MOVE_HANDLE_SIZE = 7;\nvar HORIZONTAL = 'horizontal';\nvar VERTICAL = 'vertical';\nvar LABEL_GAP = 5;\nvar SHOW_DATA_SHADOW_SERIES_TYPE = ['line', 'bar', 'candlestick', 'scatter'];\nvar REALTIME_ANIMATION_CONFIG = {\n  easing: 'cubicOut',\n  duration: 100,\n  delay: 0\n};\nvar SliderZoomView = /** @class */function (_super) {\n  __extends(SliderZoomView, _super);\n  function SliderZoomView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderZoomView.type;\n    _this._displayables = {};\n    return _this;\n  }\n  SliderZoomView.prototype.init = function (ecModel, api) {\n    this.api = api;\n    // A unique handler for each dataZoom component\n    this._onBrush = bind(this._onBrush, this);\n    this._onBrushEnd = bind(this._onBrushEnd, this);\n  };\n  SliderZoomView.prototype.render = function (dataZoomModel, ecModel, api, payload) {\n    _super.prototype.render.apply(this, arguments);\n    throttle.createOrUpdate(this, '_dispatchZoomAction', dataZoomModel.get('throttle'), 'fixRate');\n    this._orient = dataZoomModel.getOrient();\n    if (dataZoomModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n    if (dataZoomModel.noTarget()) {\n      this._clear();\n      this.group.removeAll();\n      return;\n    }\n    // Notice: this._resetInterval() should not be executed when payload.type\n    // is 'dataZoom', origin this._range should be maintained, otherwise 'pan'\n    // or 'zoom' info will be missed because of 'throttle' of this.dispatchAction,\n    if (!payload || payload.type !== 'dataZoom' || payload.from !== this.uid) {\n      this._buildView();\n    }\n    this._updateView();\n  };\n  SliderZoomView.prototype.dispose = function () {\n    this._clear();\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  SliderZoomView.prototype._clear = function () {\n    throttle.clear(this, '_dispatchZoomAction');\n    var zr = this.api.getZr();\n    zr.off('mousemove', this._onBrush);\n    zr.off('mouseup', this._onBrushEnd);\n  };\n  SliderZoomView.prototype._buildView = function () {\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    this._brushing = false;\n    this._displayables.brushRect = null;\n    this._resetLocation();\n    this._resetInterval();\n    var barGroup = this._displayables.sliderGroup = new graphic.Group();\n    this._renderBackground();\n    this._renderHandle();\n    this._renderDataShadow();\n    thisGroup.add(barGroup);\n    this._positionGroup();\n  };\n  SliderZoomView.prototype._resetLocation = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var showMoveHandle = dataZoomModel.get('brushSelect');\n    var moveHandleSize = showMoveHandle ? DEFAULT_MOVE_HANDLE_SIZE : 0;\n    // If some of x/y/width/height are not specified,\n    // auto-adapt according to target grid.\n    var coordRect = this._findCoordRect();\n    var ecSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    // Default align by coordinate system rect.\n    var positionInfo = this._orient === HORIZONTAL ? {\n      // Why using 'right', because right should be used in vertical,\n      // and it is better to be consistent for dealing with position param merge.\n      right: ecSize.width - coordRect.x - coordRect.width,\n      top: ecSize.height - DEFAULT_FILLER_SIZE - DEFAULT_LOCATION_EDGE_GAP - moveHandleSize,\n      width: coordRect.width,\n      height: DEFAULT_FILLER_SIZE\n    } : {\n      right: DEFAULT_LOCATION_EDGE_GAP,\n      top: coordRect.y,\n      width: DEFAULT_FILLER_SIZE,\n      height: coordRect.height\n    };\n    // Do not write back to option and replace value 'ph', because\n    // the 'ph' value should be recalculated when resize.\n    var layoutParams = layout.getLayoutParams(dataZoomModel.option);\n    // Replace the placeholder value.\n    each(['right', 'top', 'width', 'height'], function (name) {\n      if (layoutParams[name] === 'ph') {\n        layoutParams[name] = positionInfo[name];\n      }\n    });\n    var layoutRect = layout.getLayoutRect(layoutParams, ecSize);\n    this._location = {\n      x: layoutRect.x,\n      y: layoutRect.y\n    };\n    this._size = [layoutRect.width, layoutRect.height];\n    this._orient === VERTICAL && this._size.reverse();\n  };\n  SliderZoomView.prototype._positionGroup = function () {\n    var thisGroup = this.group;\n    var location = this._location;\n    var orient = this._orient;\n    // Just use the first axis to determine mapping.\n    var targetAxisModel = this.dataZoomModel.getFirstTargetAxisModel();\n    var inverse = targetAxisModel && targetAxisModel.get('inverse');\n    var sliderGroup = this._displayables.sliderGroup;\n    var otherAxisInverse = (this._dataShadowInfo || {}).otherAxisInverse;\n    // Transform barGroup.\n    sliderGroup.attr(orient === HORIZONTAL && !inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: 1\n    } : orient === HORIZONTAL && inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: -1\n    } : orient === VERTICAL && !inverse ? {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: 1,\n      rotation: Math.PI / 2\n    }\n    // Don't use Math.PI, considering shadow direction.\n    : {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: -1,\n      rotation: Math.PI / 2\n    });\n    // Position barGroup\n    var rect = thisGroup.getBoundingRect([sliderGroup]);\n    thisGroup.x = location.x - rect.x;\n    thisGroup.y = location.y - rect.y;\n    thisGroup.markRedraw();\n  };\n  SliderZoomView.prototype._getViewExtent = function () {\n    return [0, this._size[0]];\n  };\n  SliderZoomView.prototype._renderBackground = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var size = this._size;\n    var barGroup = this._displayables.sliderGroup;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    barGroup.add(new Rect({\n      silent: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: dataZoomModel.get('backgroundColor')\n      },\n      z2: -40\n    }));\n    // Click panel, over shadow, below handles.\n    var clickPanel = new Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: 'transparent'\n      },\n      z2: 0,\n      onclick: bind(this._onClickPanel, this)\n    });\n    var zr = this.api.getZr();\n    if (brushSelect) {\n      clickPanel.on('mousedown', this._onBrushStart, this);\n      clickPanel.cursor = 'crosshair';\n      zr.on('mousemove', this._onBrush);\n      zr.on('mouseup', this._onBrushEnd);\n    } else {\n      zr.off('mousemove', this._onBrush);\n      zr.off('mouseup', this._onBrushEnd);\n    }\n    barGroup.add(clickPanel);\n  };\n  SliderZoomView.prototype._renderDataShadow = function () {\n    var info = this._dataShadowInfo = this._prepareDataShadowInfo();\n    this._displayables.dataShadowSegs = [];\n    if (!info) {\n      return;\n    }\n    var size = this._size;\n    var oldSize = this._shadowSize || [];\n    var seriesModel = info.series;\n    var data = seriesModel.getRawData();\n    var candlestickDim = seriesModel.getShadowDim && seriesModel.getShadowDim();\n    var otherDim = candlestickDim && data.getDimensionInfo(candlestickDim) ? seriesModel.getShadowDim() // @see candlestick\n    : info.otherDim;\n    if (otherDim == null) {\n      return;\n    }\n    var polygonPts = this._shadowPolygonPts;\n    var polylinePts = this._shadowPolylinePts;\n    // Not re-render if data doesn't change.\n    if (data !== this._shadowData || otherDim !== this._shadowDim || size[0] !== oldSize[0] || size[1] !== oldSize[1]) {\n      var otherDataExtent_1 = data.getDataExtent(otherDim);\n      // Nice extent.\n      var otherOffset = (otherDataExtent_1[1] - otherDataExtent_1[0]) * 0.3;\n      otherDataExtent_1 = [otherDataExtent_1[0] - otherOffset, otherDataExtent_1[1] + otherOffset];\n      var otherShadowExtent_1 = [0, size[1]];\n      var thisShadowExtent = [0, size[0]];\n      var areaPoints_1 = [[size[0], 0], [0, 0]];\n      var linePoints_1 = [];\n      var step_1 = thisShadowExtent[1] / (data.count() - 1);\n      var thisCoord_1 = 0;\n      // Optimize for large data shadow\n      var stride_1 = Math.round(data.count() / size[0]);\n      var lastIsEmpty_1;\n      data.each([otherDim], function (value, index) {\n        if (stride_1 > 0 && index % stride_1) {\n          thisCoord_1 += step_1;\n          return;\n        }\n        // FIXME\n        // Should consider axis.min/axis.max when drawing dataShadow.\n        // FIXME\n        // 应该使用统一的空判断？还是在list里进行空判断？\n        var isEmpty = value == null || isNaN(value) || value === '';\n        // See #4235.\n        var otherCoord = isEmpty ? 0 : linearMap(value, otherDataExtent_1, otherShadowExtent_1, true);\n        // Attempt to draw data shadow precisely when there are empty value.\n        if (isEmpty && !lastIsEmpty_1 && index) {\n          areaPoints_1.push([areaPoints_1[areaPoints_1.length - 1][0], 0]);\n          linePoints_1.push([linePoints_1[linePoints_1.length - 1][0], 0]);\n        } else if (!isEmpty && lastIsEmpty_1) {\n          areaPoints_1.push([thisCoord_1, 0]);\n          linePoints_1.push([thisCoord_1, 0]);\n        }\n        areaPoints_1.push([thisCoord_1, otherCoord]);\n        linePoints_1.push([thisCoord_1, otherCoord]);\n        thisCoord_1 += step_1;\n        lastIsEmpty_1 = isEmpty;\n      });\n      polygonPts = this._shadowPolygonPts = areaPoints_1;\n      polylinePts = this._shadowPolylinePts = linePoints_1;\n    }\n    this._shadowData = data;\n    this._shadowDim = otherDim;\n    this._shadowSize = [size[0], size[1]];\n    var dataZoomModel = this.dataZoomModel;\n    function createDataShadowGroup(isSelectedArea) {\n      var model = dataZoomModel.getModel(isSelectedArea ? 'selectedDataBackground' : 'dataBackground');\n      var group = new graphic.Group();\n      var polygon = new graphic.Polygon({\n        shape: {\n          points: polygonPts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('areaStyle').getAreaStyle(),\n        silent: true,\n        z2: -20\n      });\n      var polyline = new graphic.Polyline({\n        shape: {\n          points: polylinePts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('lineStyle').getLineStyle(),\n        silent: true,\n        z2: -19\n      });\n      group.add(polygon);\n      group.add(polyline);\n      return group;\n    }\n    // let dataBackgroundModel = dataZoomModel.getModel('dataBackground');\n    for (var i = 0; i < 3; i++) {\n      var group = createDataShadowGroup(i === 1);\n      this._displayables.sliderGroup.add(group);\n      this._displayables.dataShadowSegs.push(group);\n    }\n  };\n  SliderZoomView.prototype._prepareDataShadowInfo = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var showDataShadow = dataZoomModel.get('showDataShadow');\n    if (showDataShadow === false) {\n      return;\n    }\n    // Find a representative series.\n    var result;\n    var ecModel = this.ecModel;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var seriesModels = dataZoomModel.getAxisProxy(axisDim, axisIndex).getTargetSeriesModels();\n      each(seriesModels, function (seriesModel) {\n        if (result) {\n          return;\n        }\n        if (showDataShadow !== true && indexOf(SHOW_DATA_SHADOW_SERIES_TYPE, seriesModel.get('type')) < 0) {\n          return;\n        }\n        var thisAxis = ecModel.getComponent(getAxisMainType(axisDim), axisIndex).axis;\n        var otherDim = getOtherDim(axisDim);\n        var otherAxisInverse;\n        var coordSys = seriesModel.coordinateSystem;\n        if (otherDim != null && coordSys.getOtherAxis) {\n          otherAxisInverse = coordSys.getOtherAxis(thisAxis).inverse;\n        }\n        otherDim = seriesModel.getData().mapDimension(otherDim);\n        result = {\n          thisAxis: thisAxis,\n          series: seriesModel,\n          thisDim: axisDim,\n          otherDim: otherDim,\n          otherAxisInverse: otherAxisInverse\n        };\n      }, this);\n    }, this);\n    return result;\n  };\n  SliderZoomView.prototype._renderHandle = function () {\n    var thisGroup = this.group;\n    var displayables = this._displayables;\n    var handles = displayables.handles = [null, null];\n    var handleLabels = displayables.handleLabels = [null, null];\n    var sliderGroup = this._displayables.sliderGroup;\n    var size = this._size;\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var borderRadius = dataZoomModel.get('borderRadius') || 0;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    var filler = displayables.filler = new Rect({\n      silent: brushSelect,\n      style: {\n        fill: dataZoomModel.get('fillerColor')\n      },\n      textConfig: {\n        position: 'inside'\n      }\n    });\n    sliderGroup.add(filler);\n    // Frame border.\n    sliderGroup.add(new Rect({\n      silent: true,\n      subPixelOptimize: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1],\n        r: borderRadius\n      },\n      style: {\n        // deprecated option\n        stroke: dataZoomModel.get('dataBackgroundColor') || dataZoomModel.get('borderColor'),\n        lineWidth: DEFAULT_FRAME_BORDER_WIDTH,\n        fill: 'rgba(0,0,0,0)'\n      }\n    }));\n    // Left and right handle to resize\n    each([0, 1], function (handleIndex) {\n      var iconStr = dataZoomModel.get('handleIcon');\n      if (!symbolBuildProxies[iconStr] && iconStr.indexOf('path://') < 0 && iconStr.indexOf('image://') < 0) {\n        // Compatitable with the old icon parsers. Which can use a path string without path://\n        iconStr = 'path://' + iconStr;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('handleIcon now needs \\'path://\\' prefix when using a path string');\n        }\n      }\n      var path = createSymbol(iconStr, -1, 0, 2, 2, null, true);\n      path.attr({\n        cursor: getCursor(this._orient),\n        draggable: true,\n        drift: bind(this._onDragMove, this, handleIndex),\n        ondragend: bind(this._onDragEnd, this),\n        onmouseover: bind(this._showDataInfo, this, true),\n        onmouseout: bind(this._showDataInfo, this, false),\n        z2: 5\n      });\n      var bRect = path.getBoundingRect();\n      var handleSize = dataZoomModel.get('handleSize');\n      this._handleHeight = parsePercent(handleSize, this._size[1]);\n      this._handleWidth = bRect.width / bRect.height * this._handleHeight;\n      path.setStyle(dataZoomModel.getModel('handleStyle').getItemStyle());\n      path.style.strokeNoScale = true;\n      path.rectHover = true;\n      path.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n      enableHoverEmphasis(path);\n      var handleColor = dataZoomModel.get('handleColor'); // deprecated option\n      // Compatitable with previous version\n      if (handleColor != null) {\n        path.style.fill = handleColor;\n      }\n      sliderGroup.add(handles[handleIndex] = path);\n      var textStyleModel = dataZoomModel.getModel('textStyle');\n      var handleLabel = dataZoomModel.get('handleLabel') || {};\n      var handleLabelShow = handleLabel.show || false;\n      thisGroup.add(handleLabels[handleIndex] = new graphic.Text({\n        silent: true,\n        invisible: !handleLabelShow,\n        style: createTextStyle(textStyleModel, {\n          x: 0,\n          y: 0,\n          text: '',\n          verticalAlign: 'middle',\n          align: 'center',\n          fill: textStyleModel.getTextColor(),\n          font: textStyleModel.getFont()\n        }),\n        z2: 10\n      }));\n    }, this);\n    // Handle to move. Only visible when brushSelect is set true.\n    var actualMoveZone = filler;\n    if (brushSelect) {\n      var moveHandleHeight = parsePercent(dataZoomModel.get('moveHandleSize'), size[1]);\n      var moveHandle_1 = displayables.moveHandle = new graphic.Rect({\n        style: dataZoomModel.getModel('moveHandleStyle').getItemStyle(),\n        silent: true,\n        shape: {\n          r: [0, 0, 2, 2],\n          y: size[1] - 0.5,\n          height: moveHandleHeight\n        }\n      });\n      var iconSize = moveHandleHeight * 0.8;\n      var moveHandleIcon = displayables.moveHandleIcon = createSymbol(dataZoomModel.get('moveHandleIcon'), -iconSize / 2, -iconSize / 2, iconSize, iconSize, '#fff', true);\n      moveHandleIcon.silent = true;\n      moveHandleIcon.y = size[1] + moveHandleHeight / 2 - 0.5;\n      moveHandle_1.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'moveHandleStyle']).getItemStyle();\n      var moveZoneExpandSize = Math.min(size[1] / 2, Math.max(moveHandleHeight, 10));\n      actualMoveZone = displayables.moveZone = new graphic.Rect({\n        invisible: true,\n        shape: {\n          y: size[1] - moveZoneExpandSize,\n          height: moveHandleHeight + moveZoneExpandSize\n        }\n      });\n      actualMoveZone.on('mouseover', function () {\n        api.enterEmphasis(moveHandle_1);\n      }).on('mouseout', function () {\n        api.leaveEmphasis(moveHandle_1);\n      });\n      sliderGroup.add(moveHandle_1);\n      sliderGroup.add(moveHandleIcon);\n      sliderGroup.add(actualMoveZone);\n    }\n    actualMoveZone.attr({\n      draggable: true,\n      cursor: getCursor(this._orient),\n      drift: bind(this._onDragMove, this, 'all'),\n      ondragstart: bind(this._showDataInfo, this, true),\n      ondragend: bind(this._onDragEnd, this),\n      onmouseover: bind(this._showDataInfo, this, true),\n      onmouseout: bind(this._showDataInfo, this, false)\n    });\n  };\n  SliderZoomView.prototype._resetInterval = function () {\n    var range = this._range = this.dataZoomModel.getPercentRange();\n    var viewExtent = this._getViewExtent();\n    this._handleEnds = [linearMap(range[0], [0, 100], viewExtent, true), linearMap(range[1], [0, 100], viewExtent, true)];\n  };\n  SliderZoomView.prototype._updateInterval = function (handleIndex, delta) {\n    var dataZoomModel = this.dataZoomModel;\n    var handleEnds = this._handleEnds;\n    var viewExtend = this._getViewExtent();\n    var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();\n    var percentExtent = [0, 100];\n    sliderMove(delta, handleEnds, viewExtend, dataZoomModel.get('zoomLock') ? 'all' : handleIndex, minMaxSpan.minSpan != null ? linearMap(minMaxSpan.minSpan, percentExtent, viewExtend, true) : null, minMaxSpan.maxSpan != null ? linearMap(minMaxSpan.maxSpan, percentExtent, viewExtend, true) : null);\n    var lastRange = this._range;\n    var range = this._range = asc([linearMap(handleEnds[0], viewExtend, percentExtent, true), linearMap(handleEnds[1], viewExtend, percentExtent, true)]);\n    return !lastRange || lastRange[0] !== range[0] || lastRange[1] !== range[1];\n  };\n  SliderZoomView.prototype._updateView = function (nonRealtime) {\n    var displaybles = this._displayables;\n    var handleEnds = this._handleEnds;\n    var handleInterval = asc(handleEnds.slice());\n    var size = this._size;\n    each([0, 1], function (handleIndex) {\n      // Handles\n      var handle = displaybles.handles[handleIndex];\n      var handleHeight = this._handleHeight;\n      handle.attr({\n        scaleX: handleHeight / 2,\n        scaleY: handleHeight / 2,\n        // This is a trick, by adding an extra tiny offset to let the default handle's end point align to the drag window.\n        // NOTE: It may affect some custom shapes a bit. But we prefer to have better result by default.\n        x: handleEnds[handleIndex] + (handleIndex ? -1 : 1),\n        y: size[1] / 2 - handleHeight / 2\n      });\n    }, this);\n    // Filler\n    displaybles.filler.setShape({\n      x: handleInterval[0],\n      y: 0,\n      width: handleInterval[1] - handleInterval[0],\n      height: size[1]\n    });\n    var viewExtent = {\n      x: handleInterval[0],\n      width: handleInterval[1] - handleInterval[0]\n    };\n    // Move handle\n    if (displaybles.moveHandle) {\n      displaybles.moveHandle.setShape(viewExtent);\n      displaybles.moveZone.setShape(viewExtent);\n      // Force update path on the invisible object\n      displaybles.moveZone.getBoundingRect();\n      displaybles.moveHandleIcon && displaybles.moveHandleIcon.attr('x', viewExtent.x + viewExtent.width / 2);\n    }\n    // update clip path of shadow.\n    var dataShadowSegs = displaybles.dataShadowSegs;\n    var segIntervals = [0, handleInterval[0], handleInterval[1], size[0]];\n    for (var i = 0; i < dataShadowSegs.length; i++) {\n      var segGroup = dataShadowSegs[i];\n      var clipPath = segGroup.getClipPath();\n      if (!clipPath) {\n        clipPath = new graphic.Rect();\n        segGroup.setClipPath(clipPath);\n      }\n      clipPath.setShape({\n        x: segIntervals[i],\n        y: 0,\n        width: segIntervals[i + 1] - segIntervals[i],\n        height: size[1]\n      });\n    }\n    this._updateDataInfo(nonRealtime);\n  };\n  SliderZoomView.prototype._updateDataInfo = function (nonRealtime) {\n    var dataZoomModel = this.dataZoomModel;\n    var displaybles = this._displayables;\n    var handleLabels = displaybles.handleLabels;\n    var orient = this._orient;\n    var labelTexts = ['', ''];\n    // FIXME\n    // date型，支持formatter，autoformatter（ec2 date.getAutoFormatter）\n    if (dataZoomModel.get('showDetail')) {\n      var axisProxy = dataZoomModel.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        var axis = axisProxy.getAxisModel().axis;\n        var range = this._range;\n        var dataInterval = nonRealtime\n        // See #4434, data and axis are not processed and reset yet in non-realtime mode.\n        ? axisProxy.calculateDataWindow({\n          start: range[0],\n          end: range[1]\n        }).valueWindow : axisProxy.getDataValueWindow();\n        labelTexts = [this._formatLabel(dataInterval[0], axis), this._formatLabel(dataInterval[1], axis)];\n      }\n    }\n    var orderedHandleEnds = asc(this._handleEnds.slice());\n    setLabel.call(this, 0);\n    setLabel.call(this, 1);\n    function setLabel(handleIndex) {\n      // Label\n      // Text should not transform by barGroup.\n      // Ignore handlers transform\n      var barTransform = graphic.getTransform(displaybles.handles[handleIndex].parent, this.group);\n      var direction = graphic.transformDirection(handleIndex === 0 ? 'right' : 'left', barTransform);\n      var offset = this._handleWidth / 2 + LABEL_GAP;\n      var textPoint = graphic.applyTransform([orderedHandleEnds[handleIndex] + (handleIndex === 0 ? -offset : offset), this._size[1] / 2], barTransform);\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        verticalAlign: orient === HORIZONTAL ? 'middle' : direction,\n        align: orient === HORIZONTAL ? direction : 'center',\n        text: labelTexts[handleIndex]\n      });\n    }\n  };\n  SliderZoomView.prototype._formatLabel = function (value, axis) {\n    var dataZoomModel = this.dataZoomModel;\n    var labelFormatter = dataZoomModel.get('labelFormatter');\n    var labelPrecision = dataZoomModel.get('labelPrecision');\n    if (labelPrecision == null || labelPrecision === 'auto') {\n      labelPrecision = axis.getPixelPrecision();\n    }\n    var valueStr = value == null || isNaN(value) ? ''\n    // FIXME Glue code\n    : axis.type === 'category' || axis.type === 'time' ? axis.scale.getLabel({\n      value: Math.round(value)\n    })\n    // param of toFixed should less then 20.\n    : value.toFixed(Math.min(labelPrecision, 20));\n    return isFunction(labelFormatter) ? labelFormatter(value, valueStr) : isString(labelFormatter) ? labelFormatter.replace('{value}', valueStr) : valueStr;\n  };\n  /**\r\n   * @param isEmphasis true: show, false: hide\r\n   */\n  SliderZoomView.prototype._showDataInfo = function (isEmphasis) {\n    var handleLabel = this.dataZoomModel.get('handleLabel') || {};\n    var normalShow = handleLabel.show || false;\n    var emphasisHandleLabel = this.dataZoomModel.getModel(['emphasis', 'handleLabel']);\n    var emphasisShow = emphasisHandleLabel.get('show') || false;\n    // Dragging is considered as emphasis, unless emphasisShow is false\n    var toShow = isEmphasis || this._dragging ? emphasisShow : normalShow;\n    var displayables = this._displayables;\n    var handleLabels = displayables.handleLabels;\n    handleLabels[0].attr('invisible', !toShow);\n    handleLabels[1].attr('invisible', !toShow);\n    // Highlight move handle\n    displayables.moveHandle && this.api[toShow ? 'enterEmphasis' : 'leaveEmphasis'](displayables.moveHandle, 1);\n  };\n  SliderZoomView.prototype._onDragMove = function (handleIndex, dx, dy, event) {\n    this._dragging = true;\n    // For mobile device, prevent screen slider on the button.\n    eventTool.stop(event.event);\n    // Transform dx, dy to bar coordination.\n    var barTransform = this._displayables.sliderGroup.getLocalTransform();\n    var vertex = graphic.applyTransform([dx, dy], barTransform, true);\n    var changed = this._updateInterval(handleIndex, vertex[0]);\n    var realtime = this.dataZoomModel.get('realtime');\n    this._updateView(!realtime);\n    // Avoid dispatch dataZoom repeatly but range not changed,\n    // which cause bad visual effect when progressive enabled.\n    changed && realtime && this._dispatchZoomAction(true);\n  };\n  SliderZoomView.prototype._onDragEnd = function () {\n    this._dragging = false;\n    this._showDataInfo(false);\n    // While in realtime mode and stream mode, dispatch action when\n    // drag end will cause the whole view rerender, which is unnecessary.\n    var realtime = this.dataZoomModel.get('realtime');\n    !realtime && this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onClickPanel = function (e) {\n    var size = this._size;\n    var localPoint = this._displayables.sliderGroup.transformCoordToLocal(e.offsetX, e.offsetY);\n    if (localPoint[0] < 0 || localPoint[0] > size[0] || localPoint[1] < 0 || localPoint[1] > size[1]) {\n      return;\n    }\n    var handleEnds = this._handleEnds;\n    var center = (handleEnds[0] + handleEnds[1]) / 2;\n    var changed = this._updateInterval('all', localPoint[0] - center);\n    this._updateView();\n    changed && this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onBrushStart = function (e) {\n    var x = e.offsetX;\n    var y = e.offsetY;\n    this._brushStart = new graphic.Point(x, y);\n    this._brushing = true;\n    this._brushStartTime = +new Date();\n    // this._updateBrushRect(x, y);\n  };\n  SliderZoomView.prototype._onBrushEnd = function (e) {\n    if (!this._brushing) {\n      return;\n    }\n    var brushRect = this._displayables.brushRect;\n    this._brushing = false;\n    if (!brushRect) {\n      return;\n    }\n    brushRect.attr('ignore', true);\n    var brushShape = brushRect.shape;\n    var brushEndTime = +new Date();\n    // console.log(brushEndTime - this._brushStartTime);\n    if (brushEndTime - this._brushStartTime < 200 && Math.abs(brushShape.width) < 5) {\n      // Will treat it as a click\n      return;\n    }\n    var viewExtend = this._getViewExtent();\n    var percentExtent = [0, 100];\n    this._range = asc([linearMap(brushShape.x, viewExtend, percentExtent, true), linearMap(brushShape.x + brushShape.width, viewExtend, percentExtent, true)]);\n    this._handleEnds = [brushShape.x, brushShape.x + brushShape.width];\n    this._updateView();\n    this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onBrush = function (e) {\n    if (this._brushing) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n      this._updateBrushRect(e.offsetX, e.offsetY);\n    }\n  };\n  SliderZoomView.prototype._updateBrushRect = function (mouseX, mouseY) {\n    var displayables = this._displayables;\n    var dataZoomModel = this.dataZoomModel;\n    var brushRect = displayables.brushRect;\n    if (!brushRect) {\n      brushRect = displayables.brushRect = new Rect({\n        silent: true,\n        style: dataZoomModel.getModel('brushStyle').getItemStyle()\n      });\n      displayables.sliderGroup.add(brushRect);\n    }\n    brushRect.attr('ignore', false);\n    var brushStart = this._brushStart;\n    var sliderGroup = this._displayables.sliderGroup;\n    var endPoint = sliderGroup.transformCoordToLocal(mouseX, mouseY);\n    var startPoint = sliderGroup.transformCoordToLocal(brushStart.x, brushStart.y);\n    var size = this._size;\n    endPoint[0] = Math.max(Math.min(size[0], endPoint[0]), 0);\n    brushRect.setShape({\n      x: startPoint[0],\n      y: 0,\n      width: endPoint[0] - startPoint[0],\n      height: size[1]\n    });\n  };\n  /**\r\n   * This action will be throttled.\r\n   */\n  SliderZoomView.prototype._dispatchZoomAction = function (realtime) {\n    var range = this._range;\n    this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      dataZoomId: this.dataZoomModel.id,\n      animation: realtime ? REALTIME_ANIMATION_CONFIG : null,\n      start: range[0],\n      end: range[1]\n    });\n  };\n  SliderZoomView.prototype._findCoordRect = function () {\n    // Find the grid corresponding to the first axis referred by dataZoom.\n    var rect;\n    var coordSysInfoList = collectReferCoordSysModelInfo(this.dataZoomModel).infoList;\n    if (!rect && coordSysInfoList.length) {\n      var coordSys = coordSysInfoList[0].model.coordinateSystem;\n      rect = coordSys.getRect && coordSys.getRect();\n    }\n    if (!rect) {\n      var width = this.api.getWidth();\n      var height = this.api.getHeight();\n      rect = {\n        x: width * 0.2,\n        y: height * 0.2,\n        width: width * 0.6,\n        height: height * 0.6\n      };\n    }\n    return rect;\n  };\n  SliderZoomView.type = 'dataZoom.slider';\n  return SliderZoomView;\n}(DataZoomView);\nfunction getOtherDim(thisDim) {\n  // FIXME\n  // 这个逻辑和getOtherAxis里一致，但是写在这里是否不好\n  var map = {\n    x: 'y',\n    y: 'x',\n    radius: 'angle',\n    angle: 'radius'\n  };\n  return map[thisDim];\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default SliderZoomView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}