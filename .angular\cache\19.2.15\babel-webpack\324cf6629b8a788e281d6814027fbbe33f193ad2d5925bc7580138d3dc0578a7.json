{"ast": null, "code": "import { LanguageEnum } from '../../../core/enums/language-enum.enum';\nimport { ProgramSubscriptionUsersEnum } from '../../../core/enums/program-subscription-users-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"../../../core/services/alertify-services/alertify.service\";\nimport * as i3 from \"src/app/core/services/teacher-program-subscription-services/teacher-program-subscription-services.service\";\nimport * as i4 from \"src/app/core/services/language-services/language.service\";\nimport * as i5 from \"@angular/common\";\nfunction TeacherProgramsComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"app-teacher-stu-program-for-subscription-grid\", 8);\n    i0.ɵɵlistener(\"teacherFilterEvent\", function TeacherProgramsComponent_ng_container_7_Template_app_teacher_stu_program_for_subscription_grid_teacherFilterEvent_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterRequestTeacher($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"teacherItems\", ctx_r1.programsForTeacherSubscriptionsLst)(\"teacherFilterRequestModel\", ctx_r1.filterRequest)(\"totalCount\", ctx_r1.totalCount)(\"userMode\", ctx_r1.teacherCard);\n  }\n}\nfunction TeacherProgramsComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let TeacherProgramsComponent = /*#__PURE__*/(() => {\n  class TeacherProgramsComponent {\n    translate;\n    alertify;\n    teacherProgramSubscriptionServicesService;\n    languageService;\n    programsForTeacherSubscriptionsLst;\n    filterRequest = {\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    totalCount = 0;\n    // errorMessage?: string;\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    teacherCard = ProgramSubscriptionUsersEnum.teacher;\n    currentUser;\n    constructor(translate, alertify, teacherProgramSubscriptionServicesService, languageService) {\n      this.translate = translate;\n      this.alertify = alertify;\n      this.teacherProgramSubscriptionServicesService = teacherProgramSubscriptionServicesService;\n      this.languageService = languageService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.filterRequest.sortField = 'progcreation';\n      this.filterRequest.usrId = this.currentUser?.id;\n      this.getProgramsForTeachersSubscriptions();\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.PROG_FOR_SUBSCRIPTION'));\n    }\n    getProgramsForTeachersSubscriptions() {\n      this.teacherProgramSubscriptionServicesService.getProgramsForTeachersSubscriptions(this.filterRequest || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.programsForTeacherSubscriptionsLst = res.data;\n          this.totalCount = res.count ? res.count : 0;\n          if (this.filterRequest.skip > 0 && (!this.programsForTeacherSubscriptionsLst || this.programsForTeacherSubscriptionsLst.length === 0)) {\n            this.filterRequest.page -= 1;\n            this.filterRequest.skip = (this.filterRequest.page - 1) * this.filterRequest.take;\n            this.getProgramsForTeachersSubscriptions();\n          }\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    filterRequestTeacher(event) {\n      this.filterRequest = event;\n      this.getProgramsForTeachersSubscriptions();\n    }\n    filterByText(searchKey) {\n      this.filterRequest.progName = searchKey;\n      this.getProgramsForTeachersSubscriptions();\n    }\n    static ɵfac = function TeacherProgramsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherProgramsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.TeacherProgramSubscriptionServicesService), i0.ɵɵdirectiveInject(i4.LanguageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherProgramsComponent,\n      selectors: [[\"app-teacher-programs-for-subscription\"]],\n      decls: 9,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"teacher\"], [1, \"row\", \"stu-program-subscriptions\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"mb-0\", \"head\"], [3, \"searchTerm\", \"searchKey\"], [1, \"row\", \"fix_margin\", \"w-100\"], [4, \"ngIf\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [3, \"teacherFilterEvent\", \"teacherItems\", \"teacherFilterRequestModel\", \"totalCount\", \"userMode\"], [1, \"col-12\", \"justify-content-center\"], [1, \"No_data\", \"mt-5\"]],\n      template: function TeacherProgramsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n          i0.ɵɵtext(4, \" \\u0628\\u0631\\u0627\\u0645\\u062C\\u0646\\u0627\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"app-search-input\", 4);\n          i0.ɵɵlistener(\"searchTerm\", function TeacherProgramsComponent_Template_app_search_input_searchTerm_5_listener($event) {\n            return ctx.filterByText($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtemplate(7, TeacherProgramsComponent_ng_container_7_Template, 3, 4, \"ng-container\", 6)(8, TeacherProgramsComponent_ng_container_8_Template, 5, 3, \"ng-container\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"searchKey\", ctx.filterRequest.progName || \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.programsForTeacherSubscriptionsLst && ctx.programsForTeacherSubscriptionsLst.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.programsForTeacherSubscriptionsLst || ctx.programsForTeacherSubscriptionsLst && ctx.programsForTeacherSubscriptionsLst.length == 0);\n        }\n      },\n      dependencies: [i5.NgIf, i1.TranslatePipe],\n      styles: [\".teacher[_ngcontent-%COMP%]{height:86vh;background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:1rem;margin-top:1rem;margin-bottom:0;width:98%}.teacher[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.teacher[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.teacher[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:72vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}\"]\n    });\n  }\n  return TeacherProgramsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}