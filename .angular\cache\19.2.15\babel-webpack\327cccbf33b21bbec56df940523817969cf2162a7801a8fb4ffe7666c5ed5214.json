{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { StudentProgDutiesComponent } from './student-prog-duties/student-prog-duties.component';\nimport { StudentProgramWrapperComponent } from './student-program-wrapper/student-program-wrapper.component';\nimport { StudentProgramDetailsComponent } from './student-program-details/student-program-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StudentProgramWrapperComponent\n}, {\n  path: 'Student-duty/:id/:batch/:progId',\n  component: StudentProgDutiesComponent\n}, {\n  path: 'Student-program-details/:id/:batch/:progId',\n  component: StudentProgramDetailsComponent\n}];\nexport let StudentProgramsRoutingModule = /*#__PURE__*/(() => {\n  class StudentProgramsRoutingModule {\n    static ɵfac = function StudentProgramsRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentProgramsRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentProgramsRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return StudentProgramsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}