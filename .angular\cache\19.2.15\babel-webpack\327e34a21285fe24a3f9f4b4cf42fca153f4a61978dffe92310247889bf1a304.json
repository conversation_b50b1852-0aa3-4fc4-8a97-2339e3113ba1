{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { TeacherJionProgramTabRequestComponent } from './teacher-join-program-tab-request/teacher-join-program-tab-request.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"disableBlock\": a0\n});\nfunction TeacherJoinRequestProgramComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"app-teache-rejected\", 4);\n    i0.ɵɵlistener(\"closeRejectedRequest\", function TeacherJoinRequestProgramComponent_div_2_Template_app_teache_rejected_closeRejectedRequest_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeRejectedRequest());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"itemTeacherReq\", ctx_r1.itemTeacherReq);\n  }\n}\nfunction TeacherJoinRequestProgramComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"app-advanced-search-teacher\", 5);\n    i0.ɵɵlistener(\"advancedSearchEvent\", function TeacherJoinRequestProgramComponent_div_3_Template_app_advanced_search_teacher_advancedSearchEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.teacherAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"teacherFilterAdvancedSearch\", ctx_r1.teacherFilterAdvancedSearch);\n  }\n}\nexport let TeacherJoinRequestProgramComponent = /*#__PURE__*/(() => {\n  class TeacherJoinRequestProgramComponent {\n    loadTeatcherProg;\n    rejectTeacherProgramSubscription = new EventEmitter();\n    showTap = 'Pending';\n    itemTeacherReq = {\n      totalRows: 0\n    };\n    openStuRejectOverlay = false;\n    openTeacherAdvancedSearch = false;\n    teacherFilterAdvancedSearch = {\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    filter = {\n      isSearch: false,\n      teacherFilter: this.teacherFilterAdvancedSearch\n    };\n    constructor() {}\n    ngOnInit() {}\n    openRejectRequest(event) {\n      this.itemTeacherReq = event;\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n    }\n    closeRejectedRequest() {\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n      this.loadTeatcherProg?.getTeachersProgramsSubscriptions();\n    }\n    closeOverlay() {\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n      this.loadTeatcherProg?.getTeachersProgramsSubscriptions();\n    }\n    openTeacherAdvancedSearchPopup(event) {\n      this.openTeacherAdvancedSearch = !this.openTeacherAdvancedSearch;\n      this.teacherFilterAdvancedSearch = event;\n    }\n    teacherAdvancedSearch(event) {\n      // if (event.isSearch == false) { }\n      this.loadTeatcherProg?.advancedSearch(event.teacherFilter || undefined);\n      this.openTeacherAdvancedSearch = !this.openTeacherAdvancedSearch;\n    }\n    closeAdvancedSearch(event) {\n      this.openTeacherAdvancedSearch = false;\n      this.filter = event;\n      this.loadTeatcherProg?.getTeachersProgramsSubscriptions();\n    }\n    closeForms() {\n      this.openTeacherAdvancedSearch = false;\n      this.openStuRejectOverlay = false;\n    }\n    static ɵfac = function TeacherJoinRequestProgramComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherJoinRequestProgramComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherJoinRequestProgramComponent,\n      selectors: [[\"app-teacher-join-request-program\"]],\n      viewQuery: function TeacherJoinRequestProgramComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TeacherJionProgramTabRequestComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadTeatcherProg = _t.first);\n        }\n      },\n      outputs: {\n        rejectTeacherProgramSubscription: \"rejectTeacherProgramSubscription\"\n      },\n      decls: 5,\n      vars: 5,\n      consts: [[3, \"rejectTeacherProgramSubscription\", \"advancedSearchEvent\", \"closePopup\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"showoverlay\", 3, \"click\", \"ngClass\"], [1, \"overlay\"], [3, \"closeRejectedRequest\", \"itemTeacherReq\"], [3, \"advancedSearchEvent\", \"teacherFilterAdvancedSearch\"]],\n      template: function TeacherJoinRequestProgramComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"app-teacher-join-program-tab-request\", 0);\n          i0.ɵɵlistener(\"rejectTeacherProgramSubscription\", function TeacherJoinRequestProgramComponent_Template_app_teacher_join_program_tab_request_rejectTeacherProgramSubscription_1_listener($event) {\n            return ctx.openRejectRequest($event);\n          })(\"advancedSearchEvent\", function TeacherJoinRequestProgramComponent_Template_app_teacher_join_program_tab_request_advancedSearchEvent_1_listener($event) {\n            return ctx.openTeacherAdvancedSearchPopup($event);\n          })(\"closePopup\", function TeacherJoinRequestProgramComponent_Template_app_teacher_join_program_tab_request_closePopup_1_listener() {\n            return ctx.closeForms();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, TeacherJoinRequestProgramComponent_div_2_Template, 2, 1, \"div\", 1)(3, TeacherJoinRequestProgramComponent_div_3_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function TeacherJoinRequestProgramComponent_Template_div_click_4_listener() {\n            return ctx.closeOverlay();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.openStuRejectOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openTeacherAdvancedSearch);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx.openStuRejectOverlay));\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return TeacherJoinRequestProgramComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}