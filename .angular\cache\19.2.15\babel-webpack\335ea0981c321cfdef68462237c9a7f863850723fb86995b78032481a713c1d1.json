{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/feelings/feelings.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"dimmed\": a0\n});\nfunction StudentFeelingsComponent_ng_container_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"p\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵelement(5, \"img\", 17);\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"p\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const feelingsDetailsModel_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feelingsDetailsModel_r1 == null ? null : feelingsDetailsModel_r1.crdOn);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", feelingsDetailsModel_r1 == null ? null : feelingsDetailsModel_r1.proPic, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? feelingsDetailsModel_r1.fullNameEn && ctx_r1.checkNameSpace(feelingsDetailsModel_r1.fullNameEn) ? feelingsDetailsModel_r1.fullNameEn : feelingsDetailsModel_r1.fullNameAr : feelingsDetailsModel_r1.fullNameAr && ctx_r1.checkNameSpace(feelingsDetailsModel_r1.fullNameAr) ? feelingsDetailsModel_r1.fullNameAr : feelingsDetailsModel_r1.fullNameEn, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feelingsDetailsModel_r1 == null ? null : feelingsDetailsModel_r1.des);\n  }\n}\nfunction StudentFeelingsComponent_ng_container_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StudentFeelingsComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StudentFeelingsComponent_ng_container_14_div_1_Template, 11, 4, \"div\", 11)(2, StudentFeelingsComponent_ng_container_14_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.feelingsPublishedList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.feelingsPublishedList || ctx_r1.feelingsPublishedList.length === 0);\n  }\n}\nexport let StudentFeelingsComponent = /*#__PURE__*/(() => {\n  class StudentFeelingsComponent {\n    roleService;\n    translate;\n    feelingsServices;\n    imagesPathesService;\n    FeelingsForm = new EventEmitter();\n    lang = LanguageEnum;\n    typeUser = RoleEnum.Student;\n    feelingsPublishedList = [];\n    feelingFilter = {\n      take: 2147483647,\n      skip: 0\n    };\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    constructor(roleService, translate, feelingsServices, imagesPathesService) {\n      this.roleService = roleService;\n      this.translate = translate;\n      this.feelingsServices = feelingsServices;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.feelingFilter.usrRe = this.typeUser;\n      this.getPublishedFeelings();\n    }\n    openFeelingsForm() {\n      this.FeelingsForm.emit();\n    }\n    getPublishedFeelings() {\n      this.feelingsServices.getPublishedFeelingsByFilter(this.feelingFilter).subscribe(res => {\n        if (res.isSuccess) {\n          this.feelingsPublishedList = res.data;\n          this.feelingsPublishedList.forEach(element => {\n            // element.crdOn = element.crdOn ? new Date(element.crdOn).toDateString() : '';\n            element.isNew = false;\n            if (!element?.proPic) {\n              element.proPic = this.imagesPathesService.profile;\n            }\n          });\n          this.resultMessage = {\n            message: res.message || \"\",\n            type: BaseConstantModel.SUCCESS_TYPE\n          };\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    checkNameSpace(str) {\n      let reg = new RegExp(/^ *$/);\n      return str.match(reg) === null;\n    }\n    static ɵfac = function StudentFeelingsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentFeelingsComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.FeelingsService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentFeelingsComponent,\n      selectors: [[\"app-student-feelings\"]],\n      outputs: {\n        FeelingsForm: \"FeelingsForm\"\n      },\n      decls: 15,\n      vars: 12,\n      consts: [[1, \"available_appointments\"], [1, \"d-flex\", \"mb-1\", \"flex-column\", \"justify-content-between\", \"align-items-center\"], [1, \"feeling_img\", 3, \"src\"], [1, \"mb-0\", \"text\"], [1, \"row\", \"px-3\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"add_feelings\", \"mb-2\", \"px-3\"], [1, \"link_Add\", 3, \"click\", \"ngClass\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [1, \"row\", \"w-100\", \"m-auto\"], [1, \"cardTime_container\", \"w-100\"], [4, \"ngIf\"], [\"class\", \"col-12 mb-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"col-12\", \"mb-2\"], [1, \"card_container\"], [1, \"data_feeling\", \"mx-1\"], [1, \"card_feeling\"], [1, \"img_user\", 3, \"src\"], [1, \"info_user\", \"mx-3\"], [1, \"name_user\"], [1, \"des_user\"], [1, \"col-12\"], [1, \"No_data\"]],\n      template: function StudentFeelingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"p\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"a\", 6);\n          i0.ɵɵlistener(\"click\", function StudentFeelingsComponent_Template_a_click_8_listener() {\n            return ctx.openFeelingsForm();\n          });\n          i0.ɵɵelement(9, \"img\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9);\n          i0.ɵɵtemplate(14, StudentFeelingsComponent_ng_container_14_Template, 3, 2, \"ng-container\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.feeling_icon, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 6, \"FEELINGS.STUDENT_FEELINGS\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, !ctx.roleService.isAccAcc()));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 8, \"FEELINGS.ADD_FEELINGS\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.roleService.isAccAcc());\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i2.TranslatePipe],\n      styles: [\".feeling_img[_ngcontent-%COMP%]{width:96%}.text[_ngcontent-%COMP%]{font-size:1rem;font-weight:700;color:#333}.add_feelings[_ngcontent-%COMP%]:lang(ar){text-align:left}.add_feelings[_ngcontent-%COMP%]:lang(en){text-align:right}.add_feelings[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{color:var(--second_color);cursor:pointer;text-decoration:underline;font-size:.75rem;font-weight:700}.add_feelings[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:.3rem;margin-left:.3rem}.cardTime_container[_ngcontent-%COMP%]{height:60vh;overflow-y:auto}.card_container[_ngcontent-%COMP%]{border-radius:.625rem;padding:.5rem;background:#fff}.card_container[_ngcontent-%COMP%]   .data_feeling[_ngcontent-%COMP%]{font-size:.65rem;font-weight:700;color:#333}.card_container[_ngcontent-%COMP%]   .data_feeling[_ngcontent-%COMP%]:lang(ar){text-align:left!important}.card_container[_ngcontent-%COMP%]   .data_feeling[_ngcontent-%COMP%]:lang(en){text-align:right!important}.card_container[_ngcontent-%COMP%]   .card_feeling[_ngcontent-%COMP%]{display:flex!important;justify-content:start!important;align-items:flex-start!important}.card_container[_ngcontent-%COMP%]   .card_feeling[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:2.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem}.card_container[_ngcontent-%COMP%]   .card_feeling[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{color:var(--main_color);font-size:1.1rem;font-weight:700}.card_container[_ngcontent-%COMP%]   .card_feeling[_ngcontent-%COMP%]   .des_user[_ngcontent-%COMP%]{font-size:.75rem;color:#333;overflow-y:auto;max-height:10vh}.card_container.active[_ngcontent-%COMP%], .card_container[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none;cursor:pointer}.card_container.active[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%], .card_container.active[_ngcontent-%COMP%]   .info_user[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%], .card_container.active[_ngcontent-%COMP%]   .des_user[_ngcontent-%COMP%], .card_container.active[_ngcontent-%COMP%]   .data_feeling[_ngcontent-%COMP%], .card_container[_ngcontent-%COMP%]:hover   .img_user[_ngcontent-%COMP%], .card_container[_ngcontent-%COMP%]:hover   .info_user[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%], .card_container[_ngcontent-%COMP%]:hover   .des_user[_ngcontent-%COMP%], .card_container[_ngcontent-%COMP%]:hover   .data_feeling[_ngcontent-%COMP%]{color:#fff}.card_container[_ngcontent-%COMP%]:lang(ar){text-align:right}.card_container[_ngcontent-%COMP%]:lang(en){text-align:left}@media screen and (max-width: 64rem){.card_feeling[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5}}.available_appointments[_ngcontent-%COMP%]{background-color:#fbfbfb;border-radius:1.25rem;height:85vh}@media screen and (max-width: 64rem){.card_feeling[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5}.des_user[_ngcontent-%COMP%]{max-height:10.5vh;overflow-y:auto}.program_result[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{padding:.25rem .625rem!important}}@media screen and (min-width: 100rem){.available_appointments[_ngcontent-%COMP%]{height:90vh}.available_appointments[_ngcontent-%COMP%]   .cardTime_container[_ngcontent-%COMP%]{height:67vh}}\"]\n    });\n  }\n  return StudentFeelingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}