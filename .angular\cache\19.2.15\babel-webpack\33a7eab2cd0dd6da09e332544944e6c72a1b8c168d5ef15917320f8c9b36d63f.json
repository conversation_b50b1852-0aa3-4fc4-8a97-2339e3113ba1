{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-categories-services/program-categories.service\";\nimport * as i2 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction AddProgramCategoriesComponent_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_CATEGORY.ADD_CATEGORY\"), \" \");\n  }\n}\nfunction AddProgramCategoriesComponent_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_CATEGORY.EDIT_CATEGORY\"), \" \");\n  }\n}\nfunction AddProgramCategoriesComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resMessage.message, \" \");\n  }\n}\nfunction AddProgramCategoriesComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function AddProgramCategoriesComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveProgramCategories());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.SAVE\"), \" \");\n  }\n}\nfunction AddProgramCategoriesComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function AddProgramCategoriesComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.savingInEdit());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EDIT\"), \" \");\n  }\n}\nexport let AddProgramCategoriesComponent = /*#__PURE__*/(() => {\n  class AddProgramCategoriesComponent {\n    programCategoriesService;\n    alertify;\n    addEditProgramCategories = new EventEmitter();\n    closeOverlay = new EventEmitter();\n    editModel;\n    programCategoryModel = {};\n    model;\n    listProgramCategporyList = [];\n    resMessage = {};\n    constructor(programCategoriesService, alertify) {\n      this.programCategoriesService = programCategoriesService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      // in case edit form \n      if (this.editModel) {\n        this.populatData();\n      }\n    }\n    saveProgramCategories() {\n      // fill model\n      this.model = {\n        arabCatgName: this.programCategoryModel.arCatName,\n        engCatgName: this.programCategoryModel.enCatName\n      };\n      // 2-send to api in add\n      this.programCategoriesService.addProgramCatiegories(this.model).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.closeForm();\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    // case edit\n    // 1-populate data in form \n    populatData() {\n      this.programCategoryModel = {\n        id: this.editModel?.id,\n        arCatName: this.editModel?.arCatName,\n        enCatName: this.editModel?.enCatName\n      };\n    }\n    // 2-send update input in form \n    savingInEdit() {\n      // 1-fill model\n      this.model = {\n        id: this.programCategoryModel.id,\n        arabCatgName: this.programCategoryModel.arCatName,\n        engCatgName: this.programCategoryModel.enCatName\n      };\n      // 2-send to api in edit\n      this.programCategoriesService.updateProgramCatiegories(this.model).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.closeForm();\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    closeForm() {\n      this.closeOverlay.emit(false);\n    }\n    static ɵfac = function AddProgramCategoriesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddProgramCategoriesComponent)(i0.ɵɵdirectiveInject(i1.ProgramCategoriesService), i0.ɵɵdirectiveInject(i2.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProgramCategoriesComponent,\n      selectors: [[\"app-add-program-categories\"]],\n      inputs: {\n        editModel: \"editModel\"\n      },\n      outputs: {\n        addEditProgramCategories: \"addEditProgramCategories\",\n        closeOverlay: \"closeOverlay\"\n      },\n      decls: 24,\n      vars: 16,\n      consts: [[1, \"form-group\", \"conditionForm\", \"mt-3\", \"pl-3\", \"pr-3\"], [\"class\", \" head  bold\", 4, \"ngIf\"], [1, \"form-group\"], [1, \"row\"], [1, \"col-12\"], [1, \"header_input\"], [\"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"mt-5\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", \"class\", \"save-btn\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"head\", \"bold\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"]],\n      template: function AddProgramCategoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AddProgramCategoriesComponent_h3_1_Template, 3, 3, \"h3\", 1)(2, AddProgramCategoriesComponent_h3_2_Template, 3, 3, \"h3\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"label\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProgramCategoriesComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.programCategoryModel.arCatName, $event) || (ctx.programCategoryModel.arCatName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 5);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProgramCategoriesComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.programCategoryModel.enCatName, $event) || (ctx.programCategoryModel.enCatName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 4);\n          i0.ɵɵtemplate(16, AddProgramCategoriesComponent_div_16_Template, 2, 4, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"section\", 9)(18, \"div\", 10);\n          i0.ɵɵtemplate(19, AddProgramCategoriesComponent_button_19_Template, 3, 3, \"button\", 11)(20, AddProgramCategoriesComponent_button_20_Template, 3, 3, \"button\", 11);\n          i0.ɵɵelementStart(21, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AddProgramCategoriesComponent_Template_button_click_21_listener() {\n            return ctx.closeForm();\n          });\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.editModel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editModel);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 10, \"PROGRAM_CATEGORY.AR_NAME_CATEGORY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.programCategoryModel.arCatName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 12, \"PROGRAM_CATEGORY.EN_NAME_CATEGORY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.programCategoryModel.enCatName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resMessage.message);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.editModel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 14, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, TranslateModule, i5.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.conditionForm[_ngcontent-%COMP%]{height:70vh}.conditionForm[_ngcontent-%COMP%]:lang(en){text-align:left}.conditionForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.conditionForm[_ngcontent-%COMP%]   .header_input[_ngcontent-%COMP%]{font-size:.937rem;color:#333;display:block}.conditionForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.conditionForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-size:.875rem}.conditionForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin-right:1rem;margin-left:1rem}@media (max-width: 48rem){.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:3rem}}\"]\n    });\n  }\n  return AddProgramCategoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}