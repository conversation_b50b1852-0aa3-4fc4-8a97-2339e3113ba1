{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/language-services/language.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction RoleManagementViewComponent_app_group_roles_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-group-roles\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedRoleId\", ctx_r0.selectedRoleId)(\"selectedRoles\", ctx_r0.selectedRoles)(\"listRoles\", ctx_r0.listRolesPermissions);\n  }\n}\nfunction RoleManagementViewComponent_app_group_users_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-group-users\", 12);\n    i0.ɵɵlistener(\"addUserToRole\", function RoleManagementViewComponent_app_group_users_14_Template_app_group_users_addUserToRole_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addUserToRole($event));\n    })(\"deleteUser\", function RoleManagementViewComponent_app_group_users_14_Template_app_group_users_deleteUser_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.deleteUser($event));\n    })(\"saveUsersRole\", function RoleManagementViewComponent_app_group_users_14_Template_app_group_users_saveUsersRole_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveUsersRole());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"UsersNotBelongToRole\", ctx_r0.UsersNotBelongToRole)(\"selectedRoleId\", ctx_r0.selectedRoleId)(\"listUsers\", ctx_r0.listRoleUesrs);\n  }\n}\nfunction RoleManagementViewComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"app-add-group\", 14);\n    i0.ɵɵlistener(\"hideform\", function RoleManagementViewComponent_div_15_Template_app_add_group_hideform_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.showAddGroup($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataEdit\", ctx_r0.editRoleData);\n  }\n}\nexport let RoleManagementViewComponent = /*#__PURE__*/(() => {\n  class RoleManagementViewComponent {\n    RoleManagement;\n    translate;\n    _alertify;\n    dialog;\n    languageService;\n    showTap = 'USERS';\n    listRoleUesrs = [];\n    editRoleData = {\n      arRoleName: '',\n      enRoleName: '',\n      id: ''\n    };\n    listRolesPermissions;\n    UsersNotBelongToRole;\n    showAddGroupForm = false;\n    selectedRoleId = '';\n    selectedRoles;\n    resultMessage = {};\n    assignUser = {\n      roleId: '',\n      usrs: []\n    };\n    constructor(RoleManagement, translate, _alertify, dialog, languageService) {\n      this.RoleManagement = RoleManagement;\n      this.translate = translate;\n      this._alertify = _alertify;\n      this.dialog = dialog;\n      this.languageService = languageService;\n    }\n    roleList = [];\n    RoleManagementFilter = {};\n    ngOnInit() {\n      this.getRolesList();\n      this.getPermissionsTreeView();\n      this.setCurrentLang();\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.currentLang == LanguageEnum.ar ? 'الأدوار' : 'Roles');\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    showTapFn(event) {\n      this.showTap = event;\n      this.getRoleDetails(this.selectedRoleId);\n    }\n    getRolesList() {\n      this.RoleManagement.getRolesList(this.RoleManagementFilter).subscribe(res => {\n        this.roleList = res.data;\n        this.getRoleDetails(this.roleList[0].id);\n      });\n    }\n    deleteRole(roleId) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete Role\" : \"هل متأكد من حذف هذا الدور\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Role' : 'حذف دور', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.RoleManagement.DeleteRole(roleId).subscribe(res => {\n            this._alertify.success(res.message || '');\n            this.getRolesList();\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    showAddGroup(event) {\n      this.showAddGroupForm = event;\n      this.editRoleData = {\n        id: '',\n        enRoleName: '',\n        arRoleName: ''\n      };\n      if (event == false) {\n        this.getRolesList();\n      }\n    }\n    filterRole(event) {\n      this.RoleManagementFilter.roleTextFilter = event;\n      this.getRolesList();\n    }\n    getRoleDetails(roleId) {\n      this.selectedRoleId = roleId;\n      this.RoleManagement.getRoleDetails(roleId).subscribe(res => {\n        this.listRoleUesrs = res.data.roleUsrs;\n        this.selectedRoles = res.data.rolePerms;\n      });\n      if (this.showTap == 'USERS') {\n        this.getUserNotBelongToRole(this.selectedRoleId);\n      }\n    }\n    getPermissionsTreeView() {\n      this.RoleManagement.getPermissionsTreeView().subscribe(res => {\n        this.listRolesPermissions = res.data;\n      });\n    }\n    getUserNotBelongToRole(RoleId) {\n      this.RoleManagement.getUsersNotBelongToRole(RoleId).subscribe(res => {\n        this.UsersNotBelongToRole = res.data;\n      });\n    }\n    addUserToRole(event) {\n      event.usrNameAr = event.arUsrName;\n      event.usrNameEn = event.enUsrName;\n      this.listRoleUesrs.push(event);\n    }\n    deleteUser(userId) {\n      let idx = 0;\n      this.listRoleUesrs.forEach((element, index) => {\n        if (element.usrId == userId) {\n          idx = index;\n          this.UsersNotBelongToRole.push(element);\n        }\n      });\n      this.listRoleUesrs.splice(idx, 1);\n    }\n    saveUsersRole() {\n      // map to git new Object\n      this.assignUser.roleId = this.selectedRoleId;\n      this.assignUser.usrs = [];\n      this.listRoleUesrs.forEach((element, index) => {\n        this.assignUser.usrs.push({\n          usrId: element.usrId\n        });\n      });\n      this.RoleManagement.assignUserRole(this.assignUser).subscribe(res => {\n        this._alertify.success(res.message || '');\n      });\n    }\n    editRole(event) {\n      this.showAddGroupForm = true;\n      this.editRoleData = event;\n    }\n    static ɵfac = function RoleManagementViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoleManagementViewComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.LanguageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleManagementViewComponent,\n      selectors: [[\"app-role-management-view\"]],\n      decls: 16,\n      vars: 17,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-3\"], [3, \"showAddGroup\", \"getRoleDetails\", \"filterRole\", \"deleteRole\", \"editRole\", \"selectedRoleId\", \"listRole\"], [1, \"col-md-9\"], [1, \"row\", \"group_header\", \"fix_margin\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\"], [\"style\", \"width: 100%\", 3, \"selectedRoleId\", \"selectedRoles\", \"listRoles\", 4, \"ngIf\"], [\"style\", \"width: 100%\", 3, \"UsersNotBelongToRole\", \"selectedRoleId\", \"listUsers\", \"addUserToRole\", \"deleteUser\", \"saveUsersRole\", 4, \"ngIf\"], [\"class\", \"overlay\", 4, \"ngIf\"], [2, \"width\", \"100%\", 3, \"selectedRoleId\", \"selectedRoles\", \"listRoles\"], [2, \"width\", \"100%\", 3, \"addUserToRole\", \"deleteUser\", \"saveUsersRole\", \"UsersNotBelongToRole\", \"selectedRoleId\", \"listUsers\"], [1, \"overlay\"], [3, \"hideform\", \"dataEdit\"]],\n      template: function RoleManagementViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-group-list\", 3);\n          i0.ɵɵlistener(\"showAddGroup\", function RoleManagementViewComponent_Template_app_group_list_showAddGroup_3_listener($event) {\n            return ctx.showAddGroup($event);\n          })(\"getRoleDetails\", function RoleManagementViewComponent_Template_app_group_list_getRoleDetails_3_listener($event) {\n            return ctx.getRoleDetails($event);\n          })(\"filterRole\", function RoleManagementViewComponent_Template_app_group_list_filterRole_3_listener($event) {\n            return ctx.filterRole($event);\n          })(\"deleteRole\", function RoleManagementViewComponent_Template_app_group_list_deleteRole_3_listener($event) {\n            return ctx.deleteRole($event);\n          })(\"editRole\", function RoleManagementViewComponent_Template_app_group_list_editRole_3_listener($event) {\n            return ctx.editRole($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function RoleManagementViewComponent_Template_div_click_6_listener() {\n            return ctx.showTapFn(\"USERS\");\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function RoleManagementViewComponent_Template_div_click_9_listener() {\n            return ctx.showTapFn(\"ROLES\");\n          });\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 7);\n          i0.ɵɵtemplate(13, RoleManagementViewComponent_app_group_roles_13_Template, 1, 3, \"app-group-roles\", 8)(14, RoleManagementViewComponent_app_group_users_14_Template, 1, 3, \"app-group-users\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, RoleManagementViewComponent_div_15_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"selectedRoleId\", ctx.selectedRoleId)(\"listRole\", ctx.roleList);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx.showTap == \"USERS\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 9, \"Role_Management.USERS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c0, ctx.showTap == \"ROLES\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 11, \"Role_Management.ROLES\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showTap == \"ROLES\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showTap == \"USERS\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddGroupForm);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i2.TranslatePipe],\n      styles: [\".group_header[_ngcontent-%COMP%]{margin-top:1.5%;border-bottom:.063rem solid var(--second_color)}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}*[_ngcontent-%COMP%]{font-weight:700}.fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}\"]\n    });\n  }\n  return RoleManagementViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}