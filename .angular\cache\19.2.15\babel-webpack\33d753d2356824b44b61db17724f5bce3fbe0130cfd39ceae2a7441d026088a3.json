{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nexport default function themeRiverLayout(ecModel, api) {\n  ecModel.eachSeriesByType('themeRiver', function (seriesModel) {\n    var data = seriesModel.getData();\n    var single = seriesModel.coordinateSystem;\n    var layoutInfo = {};\n    // use the axis boundingRect for view\n    var rect = single.getRect();\n    layoutInfo.rect = rect;\n    var boundaryGap = seriesModel.get('boundaryGap');\n    var axis = single.getAxis();\n    layoutInfo.boundaryGap = boundaryGap;\n    if (axis.orient === 'horizontal') {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.height);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.height);\n      var height = rect.height - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, height);\n    } else {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.width);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.width);\n      var width = rect.width - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, width);\n    }\n    data.setLayout('layoutInfo', layoutInfo);\n  });\n}\n/**\r\n * The layout information about themeriver\r\n *\r\n * @param data  data in the series\r\n * @param seriesModel  the model object of themeRiver series\r\n * @param height  value used to compute every series height\r\n */\nfunction doThemeRiverLayout(data, seriesModel, height) {\n  if (!data.count()) {\n    return;\n  }\n  var coordSys = seriesModel.coordinateSystem;\n  // the data in each layer are organized into a series.\n  var layerSeries = seriesModel.getLayerSeries();\n  // the points in each layer.\n  var timeDim = data.mapDimension('single');\n  var valueDim = data.mapDimension('value');\n  var layerPoints = zrUtil.map(layerSeries, function (singleLayer) {\n    return zrUtil.map(singleLayer.indices, function (idx) {\n      var pt = coordSys.dataToPoint(data.get(timeDim, idx));\n      pt[1] = data.get(valueDim, idx);\n      return pt;\n    });\n  });\n  var base = computeBaseline(layerPoints);\n  var baseLine = base.y0;\n  var ky = height / base.max;\n  // set layout information for each item.\n  var n = layerSeries.length;\n  var m = layerSeries[0].indices.length;\n  var baseY0;\n  for (var j = 0; j < m; ++j) {\n    baseY0 = baseLine[j] * ky;\n    data.setItemLayout(layerSeries[0].indices[j], {\n      layerIndex: 0,\n      x: layerPoints[0][j][0],\n      y0: baseY0,\n      y: layerPoints[0][j][1] * ky\n    });\n    for (var i = 1; i < n; ++i) {\n      baseY0 += layerPoints[i - 1][j][1] * ky;\n      data.setItemLayout(layerSeries[i].indices[j], {\n        layerIndex: i,\n        x: layerPoints[i][j][0],\n        y0: baseY0,\n        y: layerPoints[i][j][1] * ky\n      });\n    }\n  }\n}\n/**\r\n * Compute the baseLine of the rawdata\r\n * Inspired by Lee Byron's paper Stacked Graphs - Geometry & Aesthetics\r\n *\r\n * @param  data  the points in each layer\r\n */\nfunction computeBaseline(data) {\n  var layerNum = data.length;\n  var pointNum = data[0].length;\n  var sums = [];\n  var y0 = [];\n  var max = 0;\n  for (var i = 0; i < pointNum; ++i) {\n    var temp = 0;\n    for (var j = 0; j < layerNum; ++j) {\n      temp += data[j][i][1];\n    }\n    if (temp > max) {\n      max = temp;\n    }\n    sums.push(temp);\n  }\n  for (var k = 0; k < pointNum; ++k) {\n    y0[k] = (max - sums[k]) / 2;\n  }\n  max = 0;\n  for (var l = 0; l < pointNum; ++l) {\n    var sum = sums[l] + y0[l];\n    if (sum > max) {\n      max = sum;\n    }\n  }\n  return {\n    y0: y0,\n    max: max\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}