{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { StudentProgramsForSubscriptionComponent } from './student-programs-for-subscription/student-programs-for-subscription.component';\nimport { StudentProgramSubViewComponent } from './student-program-sub-view/student-program-sub-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StudentProgramsForSubscriptionComponent\n}, {\n  path: 'student_pro_sub_deatils/:id/:batch',\n  component: StudentProgramSubViewComponent\n}];\nexport let StudentProgramSubscriptionRoutingModule = /*#__PURE__*/(() => {\n  class StudentProgramSubscriptionRoutingModule {\n    static ɵfac = function StudentProgramSubscriptionRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentProgramSubscriptionRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentProgramSubscriptionRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return StudentProgramSubscriptionRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}