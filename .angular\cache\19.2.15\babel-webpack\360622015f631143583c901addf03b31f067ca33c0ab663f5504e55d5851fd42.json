{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/exam-form-services/exam-form.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ExamViewComponent_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function ExamViewComponent_a_13_Template_a_click_0_listener() {\n      const ctx_r1 = i0.ɵɵrestoreView(_r1);\n      const examForm_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      ctx_r4.loadExams(examForm_r3.id, examForm_r3.arabExamFormNam, examForm_r3.engExamFormNam);\n      return i0.ɵɵresetView(ctx_r4.selectedIndex = i_r4);\n    });\n    i0.ɵɵelementStart(1, \"p\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function ExamViewComponent_a_13_Template_a_click_4_listener() {\n      const examForm_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.confirmDialog(examForm_r3.id));\n    });\n    i0.ɵɵelement(5, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const examForm_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r4.selectedIndex === i_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r4.translate.currentLang === ctx_r4.langEnum.en ? examForm_r3.engExamFormNam : examForm_r3.arabExamFormNam);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.translate.currentLang === ctx_r4.langEnum.en ? examForm_r3.engExamFormNam : examForm_r3.arabExamFormNam);\n  }\n}\nfunction ExamViewComponent_label_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let ExamViewComponent = /*#__PURE__*/(() => {\n  class ExamViewComponent {\n    examFormService;\n    translate;\n    fb;\n    dialog;\n    _alertify;\n    imagesPathesService;\n    filterErrorMessage;\n    examFormsList = [];\n    examFormFilter = {};\n    isView = true;\n    currentForm = new FormGroup({});\n    selectedExamFormId = new EventEmitter();\n    inputExamId = new EventEmitter();\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    addExamForm = false;\n    constructor(examFormService, translate, fb, dialog, _alertify, imagesPathesService) {\n      this.examFormService = examFormService;\n      this.translate = translate;\n      this.fb = fb;\n      this.dialog = dialog;\n      this._alertify = _alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getExamForms();\n      this.buildForm();\n      if (this.addExamForm === true) {\n        this.getExamForms();\n      }\n    }\n    ngOnChanges(changes) {\n      if (this.addExamForm == true) {\n        this.getExamForms();\n      }\n    }\n    get f() {\n      return this.currentForm?.controls;\n    }\n    buildForm() {\n      this.currentForm = this.fb.group({\n        nameAr: ['', Validators.required],\n        nameEn: ['', Validators.required]\n      });\n    }\n    getExamForms(name) {\n      this.isView = true;\n      this.examFormFilter.examFormNam = name || '';\n      this.examFormFilter.skip = 0;\n      this.examFormFilter.take = 2147483647;\n      this.resultMessage = {};\n      this.examFormService.getExamFormFilter(this.examFormFilter).subscribe(res => {\n        let response = res;\n        this.examFormsList = response.data;\n        if (this.addExamForm === false) {\n          this.loadExams(this.examFormsList[0]?.id, this.examFormsList[0]?.arabExamFormNam, this.examFormsList[0]?.engExamFormNam);\n          this.selectedIndex = 0;\n        }\n      }, error => {\n        //logging\n      });\n    }\n    clearFilter() {\n      this.examFormFilter = {};\n      this.examFormFilter.skip = 0;\n      this.examFormFilter.take = 100;\n      this.getExamForms();\n    }\n    selectedIndex;\n    loadExams(id, arabExamName, engExamName) {\n      this.selectedExamFormId.emit({\n        id: id,\n        arabExamName: arabExamName,\n        engExamName: engExamName\n      });\n    }\n    result = '';\n    confirmDialog(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this exam\" : \"هل متأكد من حذف هذا الإمتحان\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Exam' : 'حذف الإمتحان', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n        if (dialogResult == true) {\n          this.examFormService.deleteExamForm(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this._alertify.error(res.message || \"\");\n              this.getExamForms();\n            } else {\n              this._alertify.error(res.message || \"\");\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    // selectedIndex?:Number;\n    loadExam(id) {\n      this.inputExamId?.emit(id);\n    }\n    newExam() {\n      this.inputExamId?.emit('');\n    }\n    static ɵfac = function ExamViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ExamViewComponent)(i0.ɵɵdirectiveInject(i1.ExamFormService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExamViewComponent,\n      selectors: [[\"app-exam-view\"]],\n      inputs: {\n        addExamForm: \"addExamForm\"\n      },\n      outputs: {\n        selectedExamFormId: \"selectedExamFormId\",\n        inputExamId: \"inputExamId\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 16,\n      vars: 9,\n      consts: [[1, \"list-group\", \"list-exam_form\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"mb-4\", \"p_header\"], [3, \"click\", \"routerLink\"], [1, \"link_Add\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [3, \"searchTerm\"], [1, \"internal_scroll_list_group\"], [\"class\", \"list-group-item d-flex\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"list-group-item\", \"d-flex\", 3, \"click\"], [1, \"ellipsis\", \"mb-0\", \"Exam-Forms\", 3, \"matTooltip\"], [3, \"click\"], [1, \"far\", \"fa-trash-alt\"], [1, \"no-data\"]],\n      template: function ExamViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵelementStart(6, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function ExamViewComponent_Template_a_click_6_listener() {\n            return ctx.newExam();\n          });\n          i0.ɵɵelementStart(7, \"span\", 4);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelement(10, \"img\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"app-search-input\", 6);\n          i0.ɵɵlistener(\"searchTerm\", function ExamViewComponent_Template_app_search_input_searchTerm_11_listener($event) {\n            return ctx.getExamForms($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 7);\n          i0.ɵɵtemplate(13, ExamViewComponent_a_13_Template, 6, 4, \"a\", 8);\n          i0.ɵɵelementStart(14, \"div\", 9);\n          i0.ɵɵtemplate(15, ExamViewComponent_label_15_Template, 3, 3, \"label\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, \"EXAM_FORM.EXAM_FORMS\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 7, \"EXAM_FORM.ADD_FORM\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.examFormsList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.examFormsList.length === 0);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.RouterLink, i9.MatTooltip, i2.TranslatePipe],\n      styles: [\".list-exam_form[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:85vh;margin-top:1rem}.list-exam_form[_ngcontent-%COMP%]:lang(en){margin-left:1rem}.list-exam_form[_ngcontent-%COMP%]:lang(ar){margin-right:0rem}.list-exam_form[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:29.375rem;overflow-y:auto}.list-exam_form[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:29.375rem;overflow-y:auto}.list-exam_form[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:.3rem;margin-left:.3rem}.list-exam_form[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-exam_form[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]:lang(ar){text-align:right}.list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-weight:700;font-size:1rem;justify-content:space-between}.list-exam_form[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .fa-pencil-alt[_ngcontent-%COMP%]{color:var(--main_color);margin-right:.2rem;margin-left:.2rem}.list-exam_form[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-exam_form[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-pencil-alt[_ngcontent-%COMP%], .list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-exam_form[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-pencil-alt[_ngcontent-%COMP%]{color:#fff}.list-exam_form[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{color:var(--second_color)}.list-exam_form[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]{font-size:large;font-weight:700}.list-exam_form[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{font: 1.25rem Almarai;color:#000;padding-top:2rem}.list-exam_form[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(en){text-align:left;padding-left:2rem}.list-exam_form[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(ar){text-align:right;padding-right:2rem}.list-exam_form[_ngcontent-%COMP%]   .Exam-Forms[_ngcontent-%COMP%]:lang(ar){text-align:right}@media (max-width: 64rem){.list-exam_form[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]{font-size:.9rem}.list-exam_form[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{font-size:.875rem}.list-exam_form[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:unset;margin-left:unset}}\"]\n    });\n  }\n  return ExamViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}