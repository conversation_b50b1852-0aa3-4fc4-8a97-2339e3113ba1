{"ast": null, "code": "import * as util from './core/util.js';\nimport timsort from './core/timsort.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nvar invalidZErrorLogged = false;\nfunction logInvalidZError() {\n  if (invalidZ<PERSON>rrorLogged) {\n    return;\n  }\n  invalidZErrorLogged = true;\n  console.warn('z / z2 / zlevel of displayable is invalid, which may cause unexpected errors');\n}\nfunction shapeCompareFunc(a, b) {\n  if (a.zlevel === b.zlevel) {\n    if (a.z === b.z) {\n      return a.z2 - b.z2;\n    }\n    return a.z - b.z;\n  }\n  return a.zlevel - b.zlevel;\n}\nvar Storage = function () {\n  function Storage() {\n    this._roots = [];\n    this._displayList = [];\n    this._displayListLen = 0;\n    this.displayableSortFunc = shapeCompareFunc;\n  }\n  Storage.prototype.traverse = function (cb, context) {\n    for (var i = 0; i < this._roots.length; i++) {\n      this._roots[i].traverse(cb, context);\n    }\n  };\n  Storage.prototype.getDisplayList = function (update, includeIgnore) {\n    includeIgnore = includeIgnore || false;\n    var displayList = this._displayList;\n    if (update || !displayList.length) {\n      this.updateDisplayList(includeIgnore);\n    }\n    return displayList;\n  };\n  Storage.prototype.updateDisplayList = function (includeIgnore) {\n    this._displayListLen = 0;\n    var roots = this._roots;\n    var displayList = this._displayList;\n    for (var i = 0, len = roots.length; i < len; i++) {\n      this._updateAndAddDisplayable(roots[i], null, includeIgnore);\n    }\n    displayList.length = this._displayListLen;\n    timsort(displayList, shapeCompareFunc);\n  };\n  Storage.prototype._updateAndAddDisplayable = function (el, clipPaths, includeIgnore) {\n    if (el.ignore && !includeIgnore) {\n      return;\n    }\n    el.beforeUpdate();\n    el.update();\n    el.afterUpdate();\n    var userSetClipPath = el.getClipPath();\n    if (el.ignoreClip) {\n      clipPaths = null;\n    } else if (userSetClipPath) {\n      if (clipPaths) {\n        clipPaths = clipPaths.slice();\n      } else {\n        clipPaths = [];\n      }\n      var currentClipPath = userSetClipPath;\n      var parentClipPath = el;\n      while (currentClipPath) {\n        currentClipPath.parent = parentClipPath;\n        currentClipPath.updateTransform();\n        clipPaths.push(currentClipPath);\n        parentClipPath = currentClipPath;\n        currentClipPath = currentClipPath.getClipPath();\n      }\n    }\n    if (el.childrenRef) {\n      var children = el.childrenRef();\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        if (el.__dirty) {\n          child.__dirty |= REDRAW_BIT;\n        }\n        this._updateAndAddDisplayable(child, clipPaths, includeIgnore);\n      }\n      el.__dirty = 0;\n    } else {\n      var disp = el;\n      if (clipPaths && clipPaths.length) {\n        disp.__clipPaths = clipPaths;\n      } else if (disp.__clipPaths && disp.__clipPaths.length > 0) {\n        disp.__clipPaths = [];\n      }\n      if (isNaN(disp.z)) {\n        logInvalidZError();\n        disp.z = 0;\n      }\n      if (isNaN(disp.z2)) {\n        logInvalidZError();\n        disp.z2 = 0;\n      }\n      if (isNaN(disp.zlevel)) {\n        logInvalidZError();\n        disp.zlevel = 0;\n      }\n      this._displayList[this._displayListLen++] = disp;\n    }\n    var decalEl = el.getDecalElement && el.getDecalElement();\n    if (decalEl) {\n      this._updateAndAddDisplayable(decalEl, clipPaths, includeIgnore);\n    }\n    var textGuide = el.getTextGuideLine();\n    if (textGuide) {\n      this._updateAndAddDisplayable(textGuide, clipPaths, includeIgnore);\n    }\n    var textEl = el.getTextContent();\n    if (textEl) {\n      this._updateAndAddDisplayable(textEl, clipPaths, includeIgnore);\n    }\n  };\n  Storage.prototype.addRoot = function (el) {\n    if (el.__zr && el.__zr.storage === this) {\n      return;\n    }\n    this._roots.push(el);\n  };\n  Storage.prototype.delRoot = function (el) {\n    if (el instanceof Array) {\n      for (var i = 0, l = el.length; i < l; i++) {\n        this.delRoot(el[i]);\n      }\n      return;\n    }\n    var idx = util.indexOf(this._roots, el);\n    if (idx >= 0) {\n      this._roots.splice(idx, 1);\n    }\n  };\n  Storage.prototype.delAllRoots = function () {\n    this._roots = [];\n    this._displayList = [];\n    this._displayListLen = 0;\n    return;\n  };\n  Storage.prototype.getRoots = function () {\n    return this._roots;\n  };\n  Storage.prototype.dispose = function () {\n    this._displayList = null;\n    this._roots = null;\n  };\n  return Storage;\n}();\nexport default Storage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}