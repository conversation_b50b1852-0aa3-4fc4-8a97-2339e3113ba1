{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/content-management-services/content-management.service\";\nexport let ViewAllContentManagementComponent = /*#__PURE__*/(() => {\n  class ViewAllContentManagementComponent {\n    router;\n    route;\n    contentmanagementService;\n    routeParams;\n    cmsId;\n    allCMSData;\n    isSuccess;\n    successMessage;\n    constructor(router, route, contentmanagementService) {\n      this.router = router;\n      this.route = route;\n      this.contentmanagementService = contentmanagementService;\n    }\n    ngOnInit() {\n      this.routeParams = this.router.url;\n      this.cmsId = this.route.snapshot.params.id;\n      this.contentmanagementService.getAllContentManagementSystem().subscribe(res => {\n        this.allCMSData = res.data;\n      });\n    }\n    deleteCMS(Id) {\n      this.contentmanagementService.deleteContentManagementSystem(Id).subscribe(res => {\n        this.isSuccess = res.isSuccess;\n        this.successMessage = res.message;\n      });\n    }\n    static ɵfac = function ViewAllContentManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewAllContentManagementComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentManagementService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAllContentManagementComponent,\n      selectors: [[\"app-view-all-content-management\"]],\n      decls: 0,\n      vars: 0,\n      template: function ViewAllContentManagementComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n  return ViewAllContentManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}