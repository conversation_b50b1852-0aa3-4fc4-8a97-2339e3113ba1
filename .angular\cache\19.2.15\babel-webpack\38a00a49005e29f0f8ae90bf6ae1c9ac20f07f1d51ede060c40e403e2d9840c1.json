{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport Model from '../../model/Model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nvar TreeSeriesModel = /** @class */function (_super) {\n  __extends(TreeSeriesModel, _super);\n  function TreeSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.hasSymbolVisual = true;\n    // Do it self.\n    _this.ignoreStyleOnData = true;\n    return _this;\n  }\n  /**\r\n   * Init a tree data structure from data in option series\r\n   */\n  TreeSeriesModel.prototype.getInitialData = function (option) {\n    // create a virtual root\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    var leaves = option.leaves || {};\n    var leavesModel = new Model(leaves, this, this.ecModel);\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        if (!(node && node.children.length && node.isExpand)) {\n          model.parentModel = leavesModel;\n        }\n        return model;\n      });\n    }\n    var treeDepth = 0;\n    tree.eachNode('preorder', function (node) {\n      if (node.depth > treeDepth) {\n        treeDepth = node.depth;\n      }\n    });\n    var expandAndCollapse = option.expandAndCollapse;\n    var expandTreeDepth = expandAndCollapse && option.initialTreeDepth >= 0 ? option.initialTreeDepth : treeDepth;\n    tree.root.eachNode('preorder', function (node) {\n      var item = node.hostTree.data.getRawDataItem(node.dataIndex);\n      // Add item.collapsed != null, because users can collapse node original in the series.data.\n      node.isExpand = item && item.collapsed != null ? !item.collapsed : node.depth <= expandTreeDepth;\n    });\n    return tree.data;\n  };\n  /**\r\n   * Make the configuration 'orient' backward compatibly, with 'horizontal = LR', 'vertical = TB'.\r\n   * @returns {string} orient\r\n   */\n  TreeSeriesModel.prototype.getOrient = function () {\n    var orient = this.get('orient');\n    if (orient === 'horizontal') {\n      orient = 'LR';\n    } else if (orient === 'vertical') {\n      orient = 'TB';\n    }\n    return orient;\n  };\n  TreeSeriesModel.prototype.setZoom = function (zoom) {\n    this.option.zoom = zoom;\n  };\n  TreeSeriesModel.prototype.setCenter = function (center) {\n    this.option.center = center;\n  };\n  TreeSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var tree = this.getData().tree;\n    var realRoot = tree.root.children[0];\n    var node = tree.getNodeByDataIndex(dataIndex);\n    var value = node.getValue();\n    var name = node.name;\n    while (node && node !== realRoot) {\n      name = node.parentNode.name + '.' + name;\n      node = node.parentNode;\n    }\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value,\n      noValue: isNaN(value) || value == null\n    });\n  };\n  // Add tree path to tooltip param\n  TreeSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treeAncestors = wrapTreePathInfo(node, this);\n    params.collapsed = !node.isExpand;\n    return params;\n  };\n  TreeSeriesModel.type = 'series.tree';\n  // can support the position parameters 'left', 'top','right','bottom', 'width',\n  // 'height' in the setOption() with 'merge' mode normal.\n  TreeSeriesModel.layoutMode = 'box';\n  TreeSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    // the position of the whole view\n    left: '12%',\n    top: '12%',\n    right: '12%',\n    bottom: '12%',\n    // the layout of the tree, two value can be selected, 'orthogonal' or 'radial'\n    layout: 'orthogonal',\n    // value can be 'polyline'\n    edgeShape: 'curve',\n    edgeForkPosition: '50%',\n    // true | false | 'move' | 'scale', see module:component/helper/RoamController.\n    roam: false,\n    // Symbol size scale ratio in roam\n    nodeScaleRatio: 0.4,\n    // Default on center of graph\n    center: null,\n    zoom: 1,\n    orient: 'LR',\n    symbol: 'emptyCircle',\n    symbolSize: 7,\n    expandAndCollapse: true,\n    initialTreeDepth: 2,\n    lineStyle: {\n      color: '#ccc',\n      width: 1.5,\n      curveness: 0.5\n    },\n    itemStyle: {\n      color: 'lightsteelblue',\n      // borderColor: '#c23531',\n      borderWidth: 1.5\n    },\n    label: {\n      show: true\n    },\n    animationEasing: 'linear',\n    animationDuration: 700,\n    animationDurationUpdate: 500\n  };\n  return TreeSeriesModel;\n}(SeriesModel);\nexport default TreeSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}