{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i3 from \"@angular/common\";\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 14);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"src\", item_r2 == null ? null : item_r2.teatchImg, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_ng_container_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"span\", 17);\n    i0.ɵɵtext(2, \"\\u2605\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"\\u2605 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fill_r4 = ctx.fill;\n    i0.ɵɵclassProp(\"filled\", fill_r4 === 100);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", fill_r4, \"%\");\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ngb-rating\", 15);\n    i0.ɵɵtwoWayListener(\"rateChange\", function AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_ng_container_10_Template_ngb_rating_rateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const item_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r2.rate, $event) || (item_r2.rate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_ng_container_10_ng_template_2_Template, 4, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"rate\", item_r2.rate);\n    i0.ɵɵproperty(\"max\", 5)(\"readonly\", true);\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_img_5_Template, 1, 1, \"img\", 7)(6, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_img_6_Template, 1, 1, \"img\", 8);\n    i0.ɵɵelementStart(7, \"div\", 9)(8, \"p\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_ng_container_10_Template, 3, 3, \"ng-container\", 1);\n    i0.ɵɵelementStart(11, \"p\", 11);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i_r5 + 1, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !(item_r2 == null ? null : item_r2.teatchImg));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2 == null ? null : item_r2.teatchImg);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? item_r2.teatchNamEn && ctx_r0.checkNameSpace(item_r2.teatchNamEn) ? item_r2.teatchNamEn : item_r2.teatchNamAr : item_r2.teatchNamAr && ctx_r0.checkNameSpace(item_r2.teatchNamAr) ? item_r2.teatchNamAr : item_r2.teatchNamEn, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2 && item_r2.rate != null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(13, 10, \"ADMIN_DASH_BORD.AVERAGE_RATINGS\"), \" :\", item_r2.teatchRate, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(16, 12, \"ADMIN_DASH_BORD.TOTAL_HOURS_TASMEA\"), \" :\", item_r2.rectHours, \" \", i0.ɵɵpipeBind1(17, 14, \"ADMIN_DASH_BORD.HOUR\"), \" \");\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵtemplate(2, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_div_2_Template, 19, 16, \"div\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teatchRatingHonorBoard);\n  }\n}\nfunction AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let AdminDashBoardHonorBoardTeatchRatingWidgetsComponent = /*#__PURE__*/(() => {\n  class AdminDashBoardHonorBoardTeatchRatingWidgetsComponent {\n    translate;\n    imagesPathesService;\n    teatchRatingHonorBoard = [];\n    langEnum = LanguageEnum;\n    constructor(translate, imagesPathesService) {\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {}\n    checkNameSpace(str) {\n      let reg = new RegExp(/^ *$/);\n      return str.match(reg) === null;\n    }\n    static ɵfac = function AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardHonorBoardTeatchRatingWidgetsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashBoardHonorBoardTeatchRatingWidgetsComponent,\n      selectors: [[\"app-admin-dash-board-honor-board-teatch-rating-widgets\"]],\n      inputs: {\n        teatchRatingHonorBoard: \"teatchRatingHonorBoard\"\n      },\n      decls: 5,\n      vars: 5,\n      consts: [[1, \"px-2\", \"head\"], [4, \"ngIf\"], [1, \"d-flex\", \"flex-column\"], [\"class\", \" d-flex mb-3 \", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"mb-3\"], [1, \"pacman\"], [1, \"board_card\", \"w-100\", \"general-box\", \"d-inline-flex\"], [\"class\", \"user__image   img-container rounded mx-auto d-block\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"user__image rounded  img-container\", 3, \"src\", 4, \"ngIf\"], [1, \"board-details\"], [1, \"student-name\", \"d-block\", \"mb-1\"], [1, \"date\", \"d-block\", \"mb-0\"], [1, \"arrow\"], [1, \"user__image\", \"img-container\", \"rounded\", \"mx-auto\", \"d-block\", 3, \"src\"], [1, \"user__image\", \"rounded\", \"img-container\", 3, \"src\"], [3, \"rateChange\", \"rate\", \"max\", \"readonly\"], [1, \"star\"], [1, \"half\"], [1, \"program_result\"], [1, \"No_data\", \"pt-5\"]],\n      template: function AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h3\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_3_Template, 3, 1, \"ng-container\", 1)(4, AdminDashBoardHonorBoardTeatchRatingWidgetsComponent_ng_container_4_Template, 5, 3, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"ADMIN_DASH_BORD.TOP_TEACHER_RATING\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.teatchRatingHonorBoard);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.teatchRatingHonorBoard || ctx.teatchRatingHonorBoard && ctx.teatchRatingHonorBoard.length == 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.TranslatePipe],\n      styles: [\".head[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}\"]\n    });\n  }\n  return AdminDashBoardHonorBoardTeatchRatingWidgetsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}