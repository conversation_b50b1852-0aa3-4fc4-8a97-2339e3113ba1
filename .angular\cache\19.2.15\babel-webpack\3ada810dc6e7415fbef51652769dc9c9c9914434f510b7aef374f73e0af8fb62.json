{"ast": null, "code": "import env from './env.js';\nimport { buildTransformer } from './fourPointsTransform.js';\nvar EVENT_SAVED_PROP = '___zrEVENTSAVED';\nvar _calcOut = [];\nexport function transformLocalCoord(out, elFrom, elTarget, inX, inY) {\n  return transformCoordWithViewport(_calcOut, elFrom, inX, inY, true) && transformCoordWithViewport(out, elTarget, _calcOut[0], _calcOut[1]);\n}\nexport function transformCoordWithViewport(out, el, inX, inY, inverse) {\n  if (el.getBoundingClientRect && env.domSupported && !isCanvasEl(el)) {\n    var saved = el[EVENT_SAVED_PROP] || (el[EVENT_SAVED_PROP] = {});\n    var markers = prepareCoordMarkers(el, saved);\n    var transformer = preparePointerTransformer(markers, saved, inverse);\n    if (transformer) {\n      transformer(out, inX, inY);\n      return true;\n    }\n  }\n  return false;\n}\nfunction prepareCoordMarkers(el, saved) {\n  var markers = saved.markers;\n  if (markers) {\n    return markers;\n  }\n  markers = saved.markers = [];\n  var propLR = ['left', 'right'];\n  var propTB = ['top', 'bottom'];\n  for (var i = 0; i < 4; i++) {\n    var marker = document.createElement('div');\n    var stl = marker.style;\n    var idxLR = i % 2;\n    var idxTB = (i >> 1) % 2;\n    stl.cssText = ['position: absolute', 'visibility: hidden', 'padding: 0', 'margin: 0', 'border-width: 0', 'user-select: none', 'width:0', 'height:0', propLR[idxLR] + ':0', propTB[idxTB] + ':0', propLR[1 - idxLR] + ':auto', propTB[1 - idxTB] + ':auto', ''].join('!important;');\n    el.appendChild(marker);\n    markers.push(marker);\n  }\n  return markers;\n}\nfunction preparePointerTransformer(markers, saved, inverse) {\n  var transformerName = inverse ? 'invTrans' : 'trans';\n  var transformer = saved[transformerName];\n  var oldSrcCoords = saved.srcCoords;\n  var srcCoords = [];\n  var destCoords = [];\n  var oldCoordTheSame = true;\n  for (var i = 0; i < 4; i++) {\n    var rect = markers[i].getBoundingClientRect();\n    var ii = 2 * i;\n    var x = rect.left;\n    var y = rect.top;\n    srcCoords.push(x, y);\n    oldCoordTheSame = oldCoordTheSame && oldSrcCoords && x === oldSrcCoords[ii] && y === oldSrcCoords[ii + 1];\n    destCoords.push(markers[i].offsetLeft, markers[i].offsetTop);\n  }\n  return oldCoordTheSame && transformer ? transformer : (saved.srcCoords = srcCoords, saved[transformerName] = inverse ? buildTransformer(destCoords, srcCoords) : buildTransformer(srcCoords, destCoords));\n}\nexport function isCanvasEl(el) {\n  return el.nodeName.toUpperCase() === 'CANVAS';\n}\nvar replaceReg = /([&<>\"'])/g;\nvar replaceMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\nexport function encodeHTML(source) {\n  return source == null ? '' : (source + '').replace(replaceReg, function (str, c) {\n    return replaceMap[c];\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}