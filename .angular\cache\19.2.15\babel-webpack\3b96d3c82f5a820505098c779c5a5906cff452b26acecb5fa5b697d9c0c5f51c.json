{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// FIXME step not support polar\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport lineAnimationDiff from './lineAnimationDiff.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as modelUtil from '../../util/model.js';\nimport { ECPolyline, ECPolygon } from './poly.js';\nimport ChartView from '../../view/Chart.js';\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createGridClipPath, createPolarClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setStatesStylesFromModel, setStatesFlag, toggleHoverEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, labelInner } from '../../label/labelStyle.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nimport { convertToColorString } from '../../util/format.js';\nimport { lerp } from 'zrender/lib/tool/color.js';\nfunction isPointsSame(points1, points2) {\n  if (points1.length !== points2.length) {\n    return;\n  }\n  for (var i = 0; i < points1.length; i++) {\n    if (points1[i] !== points2[i]) {\n      return;\n    }\n  }\n  return true;\n}\nfunction bboxFromPoints(points) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n  for (var i = 0; i < points.length;) {\n    var x = points[i++];\n    var y = points[i++];\n    if (!isNaN(x)) {\n      minX = Math.min(x, minX);\n      maxX = Math.max(x, maxX);\n    }\n    if (!isNaN(y)) {\n      minY = Math.min(y, minY);\n      maxY = Math.max(y, maxY);\n    }\n  }\n  return [[minX, minY], [maxX, maxY]];\n}\nfunction getBoundingDiff(points1, points2) {\n  var _a = bboxFromPoints(points1),\n    min1 = _a[0],\n    max1 = _a[1];\n  var _b = bboxFromPoints(points2),\n    min2 = _b[0],\n    max2 = _b[1];\n  // Get a max value from each corner of two boundings.\n  return Math.max(Math.abs(min1[0] - min2[0]), Math.abs(min1[1] - min2[1]), Math.abs(max1[0] - max2[0]), Math.abs(max1[1] - max2[1]));\n}\nfunction getSmooth(smooth) {\n  return zrUtil.isNumber(smooth) ? smooth : smooth ? 0.5 : 0;\n}\nfunction getStackedOnPoints(coordSys, data, dataCoordInfo) {\n  if (!dataCoordInfo.valueDim) {\n    return [];\n  }\n  var len = data.count();\n  var points = createFloat32Array(len * 2);\n  for (var idx = 0; idx < len; idx++) {\n    var pt = getStackedOnPoint(dataCoordInfo, coordSys, data, idx);\n    points[idx * 2] = pt[0];\n    points[idx * 2 + 1] = pt[1];\n  }\n  return points;\n}\n/**\r\n * Filter the null data and extend data for step considering `stepTurnAt`\r\n *\r\n * @param points data to convert, that may containing null\r\n * @param basePoints base data to reference, used only for areaStyle points\r\n * @param coordSys coordinate system\r\n * @param stepTurnAt 'start' | 'end' | 'middle' | true\r\n * @param connectNulls whether to connect nulls\r\n * @returns converted point positions\r\n */\nfunction turnPointsIntoStep(points, basePoints, coordSys, stepTurnAt, connectNulls) {\n  var baseAxis = coordSys.getBaseAxis();\n  var baseIndex = baseAxis.dim === 'x' || baseAxis.dim === 'radius' ? 0 : 1;\n  var stepPoints = [];\n  var i = 0;\n  var stepPt = [];\n  var pt = [];\n  var nextPt = [];\n  var filteredPoints = [];\n  if (connectNulls) {\n    for (i = 0; i < points.length; i += 2) {\n      /**\r\n       * For areaStyle of stepped lines, `stackedOnPoints` should be\r\n       * filtered the same as `points` so that the base axis values\r\n       * should stay the same as the lines above. See #20021\r\n       */\n      var reference = basePoints || points;\n      if (!isNaN(reference[i]) && !isNaN(reference[i + 1])) {\n        filteredPoints.push(points[i], points[i + 1]);\n      }\n    }\n    points = filteredPoints;\n  }\n  for (i = 0; i < points.length - 2; i += 2) {\n    nextPt[0] = points[i + 2];\n    nextPt[1] = points[i + 3];\n    pt[0] = points[i];\n    pt[1] = points[i + 1];\n    stepPoints.push(pt[0], pt[1]);\n    switch (stepTurnAt) {\n      case 'end':\n        stepPt[baseIndex] = nextPt[baseIndex];\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        break;\n      case 'middle':\n        var middle = (pt[baseIndex] + nextPt[baseIndex]) / 2;\n        var stepPt2 = [];\n        stepPt[baseIndex] = stepPt2[baseIndex] = middle;\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPt2[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        stepPoints.push(stepPt2[0], stepPt2[1]);\n        break;\n      default:\n        // default is start\n        stepPt[baseIndex] = pt[baseIndex];\n        stepPt[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n    }\n  }\n  // Last points\n  stepPoints.push(points[i++], points[i++]);\n  return stepPoints;\n}\n/**\r\n * Clip color stops to edge. Avoid creating too large gradients.\r\n * Which may lead to blurry when GPU acceleration is enabled. See #15680\r\n *\r\n * The stops has been sorted from small to large.\r\n */\nfunction clipColorStops(colorStops, maxSize) {\n  var newColorStops = [];\n  var len = colorStops.length;\n  // coord will always < 0 in prevOutOfRangeColorStop.\n  var prevOutOfRangeColorStop;\n  var prevInRangeColorStop;\n  function lerpStop(stop0, stop1, clippedCoord) {\n    var coord0 = stop0.coord;\n    var p = (clippedCoord - coord0) / (stop1.coord - coord0);\n    var color = lerp(p, [stop0.color, stop1.color]);\n    return {\n      coord: clippedCoord,\n      color: color\n    };\n  }\n  for (var i = 0; i < len; i++) {\n    var stop_1 = colorStops[i];\n    var coord = stop_1.coord;\n    if (coord < 0) {\n      prevOutOfRangeColorStop = stop_1;\n    } else if (coord > maxSize) {\n      if (prevInRangeColorStop) {\n        newColorStops.push(lerpStop(prevInRangeColorStop, stop_1, maxSize));\n      } else if (prevOutOfRangeColorStop) {\n        // If there are two stops and coord range is between these two stops\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0), lerpStop(prevOutOfRangeColorStop, stop_1, maxSize));\n      }\n      // All following stop will be out of range. So just ignore them.\n      break;\n    } else {\n      if (prevOutOfRangeColorStop) {\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0));\n        // Reset\n        prevOutOfRangeColorStop = null;\n      }\n      newColorStops.push(stop_1);\n      prevInRangeColorStop = stop_1;\n    }\n  }\n  return newColorStops;\n}\nfunction getVisualGradient(data, coordSys, api) {\n  var visualMetaList = data.getVisual('visualMeta');\n  if (!visualMetaList || !visualMetaList.length || !data.count()) {\n    // When data.count() is 0, gradient range can not be calculated.\n    return;\n  }\n  if (coordSys.type !== 'cartesian2d') {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style is only supported on cartesian2d.');\n    }\n    return;\n  }\n  var coordDim;\n  var visualMeta;\n  for (var i = visualMetaList.length - 1; i >= 0; i--) {\n    var dimInfo = data.getDimensionInfo(visualMetaList[i].dimension);\n    coordDim = dimInfo && dimInfo.coordDim;\n    // Can only be x or y\n    if (coordDim === 'x' || coordDim === 'y') {\n      visualMeta = visualMetaList[i];\n      break;\n    }\n  }\n  if (!visualMeta) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style only support x or y dimension.');\n    }\n    return;\n  }\n  // If the area to be rendered is bigger than area defined by LinearGradient,\n  // the canvas spec prescribes that the color of the first stop and the last\n  // stop should be used. But if two stops are added at offset 0, in effect\n  // browsers use the color of the second stop to render area outside\n  // LinearGradient. So we can only infinitesimally extend area defined in\n  // LinearGradient to render `outerColors`.\n  var axis = coordSys.getAxis(coordDim);\n  // dataToCoord mapping may not be linear, but must be monotonic.\n  var colorStops = zrUtil.map(visualMeta.stops, function (stop) {\n    // offset will be calculated later.\n    return {\n      coord: axis.toGlobalCoord(axis.dataToCoord(stop.value)),\n      color: stop.color\n    };\n  });\n  var stopLen = colorStops.length;\n  var outerColors = visualMeta.outerColors.slice();\n  if (stopLen && colorStops[0].coord > colorStops[stopLen - 1].coord) {\n    colorStops.reverse();\n    outerColors.reverse();\n  }\n  var colorStopsInRange = clipColorStops(colorStops, coordDim === 'x' ? api.getWidth() : api.getHeight());\n  var inRangeStopLen = colorStopsInRange.length;\n  if (!inRangeStopLen && stopLen) {\n    // All stops are out of range. All will be the same color.\n    return colorStops[0].coord < 0 ? outerColors[1] ? outerColors[1] : colorStops[stopLen - 1].color : outerColors[0] ? outerColors[0] : colorStops[0].color;\n  }\n  var tinyExtent = 10; // Arbitrary value: 10px\n  var minCoord = colorStopsInRange[0].coord - tinyExtent;\n  var maxCoord = colorStopsInRange[inRangeStopLen - 1].coord + tinyExtent;\n  var coordSpan = maxCoord - minCoord;\n  if (coordSpan < 1e-3) {\n    return 'transparent';\n  }\n  zrUtil.each(colorStopsInRange, function (stop) {\n    stop.offset = (stop.coord - minCoord) / coordSpan;\n  });\n  colorStopsInRange.push({\n    // NOTE: inRangeStopLen may still be 0 if stoplen is zero.\n    offset: inRangeStopLen ? colorStopsInRange[inRangeStopLen - 1].offset : 0.5,\n    color: outerColors[1] || 'transparent'\n  });\n  colorStopsInRange.unshift({\n    offset: inRangeStopLen ? colorStopsInRange[0].offset : 0.5,\n    color: outerColors[0] || 'transparent'\n  });\n  var gradient = new graphic.LinearGradient(0, 0, 0, 0, colorStopsInRange, true);\n  gradient[coordDim] = minCoord;\n  gradient[coordDim + '2'] = maxCoord;\n  return gradient;\n}\nfunction getIsIgnoreFunc(seriesModel, data, coordSys) {\n  var showAllSymbol = seriesModel.get('showAllSymbol');\n  var isAuto = showAllSymbol === 'auto';\n  if (showAllSymbol && !isAuto) {\n    return;\n  }\n  var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n  if (!categoryAxis) {\n    return;\n  }\n  // Note that category label interval strategy might bring some weird effect\n  // in some scenario: users may wonder why some of the symbols are not\n  // displayed. So we show all symbols as possible as we can.\n  if (isAuto\n  // Simplify the logic, do not determine label overlap here.\n  && canShowAllSymbolForCategory(categoryAxis, data)) {\n    return;\n  }\n  // Otherwise follow the label interval strategy on category axis.\n  var categoryDataDim = data.mapDimension(categoryAxis.dim);\n  var labelMap = {};\n  zrUtil.each(categoryAxis.getViewLabels(), function (labelItem) {\n    var ordinalNumber = categoryAxis.scale.getRawOrdinalNumber(labelItem.tickValue);\n    labelMap[ordinalNumber] = 1;\n  });\n  return function (dataIndex) {\n    return !labelMap.hasOwnProperty(data.get(categoryDataDim, dataIndex));\n  };\n}\nfunction canShowAllSymbolForCategory(categoryAxis, data) {\n  // In most cases, line is monotonous on category axis, and the label size\n  // is close with each other. So we check the symbol size and some of the\n  // label size alone with the category axis to estimate whether all symbol\n  // can be shown without overlap.\n  var axisExtent = categoryAxis.getExtent();\n  var availSize = Math.abs(axisExtent[1] - axisExtent[0]) / categoryAxis.scale.count();\n  isNaN(availSize) && (availSize = 0); // 0/0 is NaN.\n  // Sampling some points, max 5.\n  var dataLen = data.count();\n  var step = Math.max(1, Math.round(dataLen / 5));\n  for (var dataIndex = 0; dataIndex < dataLen; dataIndex += step) {\n    if (SymbolClz.getSymbolSize(data, dataIndex\n    // Only for cartesian, where `isHorizontal` exists.\n    )[categoryAxis.isHorizontal() ? 1 : 0]\n    // Empirical number\n    * 1.5 > availSize) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\nfunction getLastIndexNotNull(points) {\n  var len = points.length / 2;\n  for (; len > 0; len--) {\n    if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n      break;\n    }\n  }\n  return len - 1;\n}\nfunction getPointAtIndex(points, idx) {\n  return [points[idx * 2], points[idx * 2 + 1]];\n}\nfunction getIndexRange(points, xOrY, dim) {\n  var len = points.length / 2;\n  var dimIdx = dim === 'x' ? 0 : 1;\n  var a;\n  var b;\n  var prevIndex = 0;\n  var nextIndex = -1;\n  for (var i = 0; i < len; i++) {\n    b = points[i * 2 + dimIdx];\n    if (isNaN(b) || isNaN(points[i * 2 + 1 - dimIdx])) {\n      continue;\n    }\n    if (i === 0) {\n      a = b;\n      continue;\n    }\n    if (a <= xOrY && b >= xOrY || a >= xOrY && b <= xOrY) {\n      nextIndex = i;\n      break;\n    }\n    prevIndex = i;\n    a = b;\n  }\n  return {\n    range: [prevIndex, nextIndex],\n    t: (xOrY - a) / (b - a)\n  };\n}\nfunction anyStateShowEndLabel(seriesModel) {\n  if (seriesModel.get(['endLabel', 'show'])) {\n    return true;\n  }\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    if (seriesModel.get([SPECIAL_STATES[i], 'endLabel', 'show'])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction createLineClipPath(lineView, coordSys, hasAnimation, seriesModel) {\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    var endLabelModel_1 = seriesModel.getModel('endLabel');\n    var valueAnimation_1 = endLabelModel_1.get('valueAnimation');\n    var data_1 = seriesModel.getData();\n    var labelAnimationRecord_1 = {\n      lastFrameIndex: 0\n    };\n    var during = anyStateShowEndLabel(seriesModel) ? function (percent, clipRect) {\n      lineView._endLabelOnDuring(percent, clipRect, data_1, labelAnimationRecord_1, valueAnimation_1, endLabelModel_1, coordSys);\n    } : null;\n    var isHorizontal = coordSys.getBaseAxis().isHorizontal();\n    var clipPath = createGridClipPath(coordSys, hasAnimation, seriesModel, function () {\n      var endLabel = lineView._endLabel;\n      if (endLabel && hasAnimation) {\n        if (labelAnimationRecord_1.originalX != null) {\n          endLabel.attr({\n            x: labelAnimationRecord_1.originalX,\n            y: labelAnimationRecord_1.originalY\n          });\n        }\n      }\n    }, during);\n    // Expand clip shape to avoid clipping when line value exceeds axis\n    if (!seriesModel.get('clip', true)) {\n      var rectShape = clipPath.shape;\n      var expandSize = Math.max(rectShape.width, rectShape.height);\n      if (isHorizontal) {\n        rectShape.y -= expandSize;\n        rectShape.height += expandSize * 2;\n      } else {\n        rectShape.x -= expandSize;\n        rectShape.width += expandSize * 2;\n      }\n    }\n    // Set to the final frame. To make sure label layout is right.\n    if (during) {\n      during(1, clipPath);\n    }\n    return clipPath;\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (seriesModel.get(['endLabel', 'show'])) {\n        console.warn('endLabel is not supported for lines in polar systems.');\n      }\n    }\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  }\n}\nfunction getEndLabelStateSpecified(endLabelModel, coordSys) {\n  var baseAxis = coordSys.getBaseAxis();\n  var isHorizontal = baseAxis.isHorizontal();\n  var isBaseInversed = baseAxis.inverse;\n  var align = isHorizontal ? isBaseInversed ? 'right' : 'left' : 'center';\n  var verticalAlign = isHorizontal ? 'middle' : isBaseInversed ? 'top' : 'bottom';\n  return {\n    normal: {\n      align: endLabelModel.get('align') || align,\n      verticalAlign: endLabelModel.get('verticalAlign') || verticalAlign\n    }\n  };\n}\nvar LineView = /** @class */function (_super) {\n  __extends(LineView, _super);\n  function LineView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  LineView.prototype.init = function () {\n    var lineGroup = new graphic.Group();\n    var symbolDraw = new SymbolDraw();\n    this.group.add(symbolDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineGroup = lineGroup;\n    this._changePolyState = zrUtil.bind(this._changePolyState, this);\n  };\n  LineView.prototype.render = function (seriesModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var lineStyleModel = seriesModel.getModel('lineStyle');\n    var areaStyleModel = seriesModel.getModel('areaStyle');\n    var points = data.getLayout('points') || [];\n    var isCoordSysPolar = coordSys.type === 'polar';\n    var prevCoordSys = this._coordSys;\n    var symbolDraw = this._symbolDraw;\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var lineGroup = this._lineGroup;\n    var hasAnimation = !ecModel.ssr && seriesModel.get('animation');\n    var isAreaChart = !areaStyleModel.isEmpty();\n    var valueOrigin = areaStyleModel.get('origin');\n    var dataCoordInfo = prepareDataCoordInfo(coordSys, data, valueOrigin);\n    var stackedOnPoints = isAreaChart && getStackedOnPoints(coordSys, data, dataCoordInfo);\n    var showSymbol = seriesModel.get('showSymbol');\n    var connectNulls = seriesModel.get('connectNulls');\n    var isIgnoreFunc = showSymbol && !isCoordSysPolar && getIsIgnoreFunc(seriesModel, data, coordSys);\n    // Remove temporary symbols\n    var oldData = this._data;\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    // Remove previous created symbols if showSymbol changed to false\n    if (!showSymbol) {\n      symbolDraw.remove();\n    }\n    group.add(lineGroup);\n    // FIXME step not support polar\n    var step = !isCoordSysPolar ? seriesModel.get('step') : false;\n    var clipShapeForSymbol;\n    if (coordSys && coordSys.getArea && seriesModel.get('clip', true)) {\n      clipShapeForSymbol = coordSys.getArea();\n      // Avoid float number rounding error for symbol on the edge of axis extent.\n      // See #7913 and `test/dataZoom-clip.html`.\n      if (clipShapeForSymbol.width != null) {\n        clipShapeForSymbol.x -= 0.1;\n        clipShapeForSymbol.y -= 0.1;\n        clipShapeForSymbol.width += 0.2;\n        clipShapeForSymbol.height += 0.2;\n      } else if (clipShapeForSymbol.r0) {\n        clipShapeForSymbol.r0 -= 0.5;\n        clipShapeForSymbol.r += 0.5;\n      }\n    }\n    this._clipShapeForSymbol = clipShapeForSymbol;\n    var visualColor = getVisualGradient(data, coordSys, api) || data.getVisual('style')[data.getVisual('drawType')];\n    // Initialization animation or coordinate system changed\n    if (!(polyline && prevCoordSys.type === coordSys.type && step === this._step)) {\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      hasAnimation && this._initSymbolLabelAnimation(data, coordSys, clipShapeForSymbol);\n      if (step) {\n        if (stackedOnPoints) {\n          stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n        }\n        // TODO If stacked series is not step\n        points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n      }\n      polyline = this._newPolyline(points);\n      if (isAreaChart) {\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } // If areaStyle is removed\n      else if (polygon) {\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n    } else {\n      if (isAreaChart && !polygon) {\n        // If areaStyle is added\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } else if (polygon && !isAreaChart) {\n        // If areaStyle is removed\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      // Update clipPath\n      var oldClipPath = lineGroup.getClipPath();\n      if (oldClipPath) {\n        var newClipPath = createLineClipPath(this, coordSys, false, seriesModel);\n        graphic.initProps(oldClipPath, {\n          shape: newClipPath.shape\n        }, seriesModel);\n      } else {\n        lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n      }\n      // Always update, or it is wrong in the case turning on legend\n      // because points are not changed.\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      // In the case data zoom triggered refreshing frequently\n      // Data may not change if line has a category axis. So it should animate nothing.\n      if (!isPointsSame(this._stackedOnPoints, stackedOnPoints) || !isPointsSame(this._points, points)) {\n        if (hasAnimation) {\n          this._doUpdateAnimation(data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls);\n        } else {\n          // Not do it in update with animation\n          if (step) {\n            if (stackedOnPoints) {\n              stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n            }\n            // TODO If stacked series is not step\n            points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n          }\n          polyline.setShape({\n            points: points\n          });\n          polygon && polygon.setShape({\n            points: points,\n            stackedOnPoints: stackedOnPoints\n          });\n        }\n      }\n    }\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    polyline.useStyle(zrUtil.defaults(\n    // Use color in lineStyle first\n    lineStyleModel.getLineStyle(), {\n      fill: 'none',\n      stroke: visualColor,\n      lineJoin: 'bevel'\n    }));\n    setStatesStylesFromModel(polyline, seriesModel, 'lineStyle');\n    if (polyline.style.lineWidth > 0 && seriesModel.get(['emphasis', 'lineStyle', 'width']) === 'bolder') {\n      var emphasisLineStyle = polyline.getState('emphasis').style;\n      emphasisLineStyle.lineWidth = +polyline.style.lineWidth + 1;\n    }\n    // Needs seriesIndex for focus\n    getECData(polyline).seriesIndex = seriesModel.seriesIndex;\n    toggleHoverEmphasis(polyline, focus, blurScope, emphasisDisabled);\n    var smooth = getSmooth(seriesModel.get('smooth'));\n    var smoothMonotone = seriesModel.get('smoothMonotone');\n    polyline.setShape({\n      smooth: smooth,\n      smoothMonotone: smoothMonotone,\n      connectNulls: connectNulls\n    });\n    if (polygon) {\n      var stackedOnSeries = data.getCalculationInfo('stackedOnSeries');\n      var stackedOnSmooth = 0;\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: visualColor,\n        opacity: 0.7,\n        lineJoin: 'bevel',\n        decal: data.getVisual('style').decal\n      }));\n      if (stackedOnSeries) {\n        stackedOnSmooth = getSmooth(stackedOnSeries.get('smooth'));\n      }\n      polygon.setShape({\n        smooth: smooth,\n        stackedOnSmooth: stackedOnSmooth,\n        smoothMonotone: smoothMonotone,\n        connectNulls: connectNulls\n      });\n      setStatesStylesFromModel(polygon, seriesModel, 'areaStyle');\n      // Needs seriesIndex for focus\n      getECData(polygon).seriesIndex = seriesModel.seriesIndex;\n      toggleHoverEmphasis(polygon, focus, blurScope, emphasisDisabled);\n    }\n    var changePolyState = this._changePolyState;\n    data.eachItemGraphicEl(function (el) {\n      // Switch polyline / polygon state if element changed its state.\n      el && (el.onHoverStateChange = changePolyState);\n    });\n    this._polyline.onHoverStateChange = changePolyState;\n    this._data = data;\n    // Save the coordinate system for transition animation when data changed\n    this._coordSys = coordSys;\n    this._stackedOnPoints = stackedOnPoints;\n    this._points = points;\n    this._step = step;\n    this._valueOrigin = valueOrigin;\n    if (seriesModel.get('triggerLineEvent')) {\n      this.packEventData(seriesModel, polyline);\n      polygon && this.packEventData(seriesModel, polygon);\n    }\n  };\n  LineView.prototype.packEventData = function (seriesModel, el) {\n    getECData(el).eventData = {\n      componentType: 'series',\n      componentSubType: 'line',\n      componentIndex: seriesModel.componentIndex,\n      seriesIndex: seriesModel.seriesIndex,\n      seriesName: seriesModel.name,\n      seriesType: 'line'\n    };\n  };\n  LineView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('emphasis');\n    if (!(dataIndex instanceof Array) && dataIndex != null && dataIndex >= 0) {\n      var points = data.getLayout('points');\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (!symbol) {\n        // Create a temporary symbol if it is not exists\n        var x = points[dataIndex * 2];\n        var y = points[dataIndex * 2 + 1];\n        if (isNaN(x) || isNaN(y)) {\n          // Null data\n          return;\n        }\n        // fix #11360: shouldn't draw symbol outside clipShapeForSymbol\n        if (this._clipShapeForSymbol && !this._clipShapeForSymbol.contain(x, y)) {\n          return;\n        }\n        var zlevel = seriesModel.get('zlevel') || 0;\n        var z = seriesModel.get('z') || 0;\n        symbol = new SymbolClz(data, dataIndex);\n        symbol.x = x;\n        symbol.y = y;\n        symbol.setZ(zlevel, z);\n        // ensure label text of the temporary symbol is in front of line and area polygon\n        var symbolLabel = symbol.getSymbolPath().getTextContent();\n        if (symbolLabel) {\n          symbolLabel.zlevel = zlevel;\n          symbolLabel.z = z;\n          symbolLabel.z2 = this._polyline.z2 + 1;\n        }\n        symbol.__temp = true;\n        data.setItemGraphicEl(dataIndex, symbol);\n        // Stop scale animation\n        symbol.stopSymbolAnimation(true);\n        this.group.add(symbol);\n      }\n      symbol.highlight();\n    } else {\n      // Highlight whole series\n      ChartView.prototype.highlight.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('normal');\n    if (dataIndex != null && dataIndex >= 0) {\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (symbol) {\n        if (symbol.__temp) {\n          data.setItemGraphicEl(dataIndex, null);\n          this.group.remove(symbol);\n        } else {\n          symbol.downplay();\n        }\n      }\n    } else {\n      // FIXME\n      // can not downplay completely.\n      // Downplay whole series\n      ChartView.prototype.downplay.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype._changePolyState = function (toState) {\n    var polygon = this._polygon;\n    setStatesFlag(this._polyline, toState);\n    polygon && setStatesFlag(polygon, toState);\n  };\n  LineView.prototype._newPolyline = function (points) {\n    var polyline = this._polyline;\n    // Remove previous created polyline\n    if (polyline) {\n      this._lineGroup.remove(polyline);\n    }\n    polyline = new ECPolyline({\n      shape: {\n        points: points\n      },\n      segmentIgnoreThreshold: 2,\n      z2: 10\n    });\n    this._lineGroup.add(polyline);\n    this._polyline = polyline;\n    return polyline;\n  };\n  LineView.prototype._newPolygon = function (points, stackedOnPoints) {\n    var polygon = this._polygon;\n    // Remove previous created polygon\n    if (polygon) {\n      this._lineGroup.remove(polygon);\n    }\n    polygon = new ECPolygon({\n      shape: {\n        points: points,\n        stackedOnPoints: stackedOnPoints\n      },\n      segmentIgnoreThreshold: 2\n    });\n    this._lineGroup.add(polygon);\n    this._polygon = polygon;\n    return polygon;\n  };\n  LineView.prototype._initSymbolLabelAnimation = function (data, coordSys, clipShape) {\n    var isHorizontalOrRadial;\n    var isCoordSysPolar;\n    var baseAxis = coordSys.getBaseAxis();\n    var isAxisInverse = baseAxis.inverse;\n    if (coordSys.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n      isCoordSysPolar = false;\n    } else if (coordSys.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n      isCoordSysPolar = true;\n    }\n    var seriesModel = data.hostModel;\n    var seriesDuration = seriesModel.get('animationDuration');\n    if (zrUtil.isFunction(seriesDuration)) {\n      seriesDuration = seriesDuration(null);\n    }\n    var seriesDelay = seriesModel.get('animationDelay') || 0;\n    var seriesDelayValue = zrUtil.isFunction(seriesDelay) ? seriesDelay(null) : seriesDelay;\n    data.eachItemGraphicEl(function (symbol, idx) {\n      var el = symbol;\n      if (el) {\n        var point = [symbol.x, symbol.y];\n        var start = void 0;\n        var end = void 0;\n        var current = void 0;\n        if (clipShape) {\n          if (isCoordSysPolar) {\n            var polarClip = clipShape;\n            var coord = coordSys.pointToCoord(point);\n            if (isHorizontalOrRadial) {\n              start = polarClip.startAngle;\n              end = polarClip.endAngle;\n              current = -coord[1] / 180 * Math.PI;\n            } else {\n              start = polarClip.r0;\n              end = polarClip.r;\n              current = coord[0];\n            }\n          } else {\n            var gridClip = clipShape;\n            if (isHorizontalOrRadial) {\n              start = gridClip.x;\n              end = gridClip.x + gridClip.width;\n              current = symbol.x;\n            } else {\n              start = gridClip.y + gridClip.height;\n              end = gridClip.y;\n              current = symbol.y;\n            }\n          }\n        }\n        var ratio = end === start ? 0 : (current - start) / (end - start);\n        if (isAxisInverse) {\n          ratio = 1 - ratio;\n        }\n        var delay = zrUtil.isFunction(seriesDelay) ? seriesDelay(idx) : seriesDuration * ratio + seriesDelayValue;\n        var symbolPath = el.getSymbolPath();\n        var text = symbolPath.getTextContent();\n        el.attr({\n          scaleX: 0,\n          scaleY: 0\n        });\n        el.animateTo({\n          scaleX: 1,\n          scaleY: 1\n        }, {\n          duration: 200,\n          setToFinal: true,\n          delay: delay\n        });\n        if (text) {\n          text.animateFrom({\n            style: {\n              opacity: 0\n            }\n          }, {\n            duration: 300,\n            delay: delay\n          });\n        }\n        symbolPath.disableLabelAnimation = true;\n      }\n    });\n  };\n  LineView.prototype._initOrUpdateEndLabel = function (seriesModel, coordSys, inheritColor) {\n    var endLabelModel = seriesModel.getModel('endLabel');\n    if (anyStateShowEndLabel(seriesModel)) {\n      var data_2 = seriesModel.getData();\n      var polyline = this._polyline;\n      // series may be filtered.\n      var points = data_2.getLayout('points');\n      if (!points) {\n        polyline.removeTextContent();\n        this._endLabel = null;\n        return;\n      }\n      var endLabel = this._endLabel;\n      if (!endLabel) {\n        endLabel = this._endLabel = new graphic.Text({\n          z2: 200 // should be higher than item symbol\n        });\n        endLabel.ignoreClip = true;\n        polyline.setTextContent(this._endLabel);\n        polyline.disableLabelAnimation = true;\n      }\n      // Find last non-NaN data to display data\n      var dataIndex = getLastIndexNotNull(points);\n      if (dataIndex >= 0) {\n        setLabelStyle(polyline, getLabelStatesModels(seriesModel, 'endLabel'), {\n          inheritColor: inheritColor,\n          labelFetcher: seriesModel,\n          labelDataIndex: dataIndex,\n          defaultText: function (dataIndex, opt, interpolatedValue) {\n            return interpolatedValue != null ? getDefaultInterpolatedLabel(data_2, interpolatedValue) : getDefaultLabel(data_2, dataIndex);\n          },\n          enableTextSetter: true\n        }, getEndLabelStateSpecified(endLabelModel, coordSys));\n        polyline.textConfig.position = null;\n      }\n    } else if (this._endLabel) {\n      this._polyline.removeTextContent();\n      this._endLabel = null;\n    }\n  };\n  LineView.prototype._endLabelOnDuring = function (percent, clipRect, data, animationRecord, valueAnimation, endLabelModel, coordSys) {\n    var endLabel = this._endLabel;\n    var polyline = this._polyline;\n    if (endLabel) {\n      // NOTE: Don't remove percent < 1. percent === 1 means the first frame during render.\n      // The label is not prepared at this time.\n      if (percent < 1 && animationRecord.originalX == null) {\n        animationRecord.originalX = endLabel.x;\n        animationRecord.originalY = endLabel.y;\n      }\n      var points = data.getLayout('points');\n      var seriesModel = data.hostModel;\n      var connectNulls = seriesModel.get('connectNulls');\n      var precision = endLabelModel.get('precision');\n      var distance = endLabelModel.get('distance') || 0;\n      var baseAxis = coordSys.getBaseAxis();\n      var isHorizontal = baseAxis.isHorizontal();\n      var isBaseInversed = baseAxis.inverse;\n      var clipShape = clipRect.shape;\n      var xOrY = isBaseInversed ? isHorizontal ? clipShape.x : clipShape.y + clipShape.height : isHorizontal ? clipShape.x + clipShape.width : clipShape.y;\n      var distanceX = (isHorizontal ? distance : 0) * (isBaseInversed ? -1 : 1);\n      var distanceY = (isHorizontal ? 0 : -distance) * (isBaseInversed ? -1 : 1);\n      var dim = isHorizontal ? 'x' : 'y';\n      var dataIndexRange = getIndexRange(points, xOrY, dim);\n      var indices = dataIndexRange.range;\n      var diff = indices[1] - indices[0];\n      var value = void 0;\n      if (diff >= 1) {\n        // diff > 1 && connectNulls, which is on the null data.\n        if (diff > 1 && !connectNulls) {\n          var pt = getPointAtIndex(points, indices[0]);\n          endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          valueAnimation && (value = seriesModel.getRawValue(indices[0]));\n        } else {\n          var pt = polyline.getPointOn(xOrY, dim);\n          pt && endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          var startValue = seriesModel.getRawValue(indices[0]);\n          var endValue = seriesModel.getRawValue(indices[1]);\n          valueAnimation && (value = modelUtil.interpolateRawValues(data, precision, startValue, endValue, dataIndexRange.t));\n        }\n        animationRecord.lastFrameIndex = indices[0];\n      } else {\n        // If diff <= 0, which is the range is not found(Include NaN)\n        // Choose the first point or last point.\n        var idx = percent === 1 || animationRecord.lastFrameIndex > 0 ? indices[0] : 0;\n        var pt = getPointAtIndex(points, idx);\n        valueAnimation && (value = seriesModel.getRawValue(idx));\n        endLabel.attr({\n          x: pt[0] + distanceX,\n          y: pt[1] + distanceY\n        });\n      }\n      if (valueAnimation) {\n        var inner = labelInner(endLabel);\n        if (typeof inner.setLabelText === 'function') {\n          inner.setLabelText(value);\n        }\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  // FIXME Two value axis\n  LineView.prototype._doUpdateAnimation = function (data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls) {\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var seriesModel = data.hostModel;\n    var diff = lineAnimationDiff(this._data, data, this._stackedOnPoints, stackedOnPoints, this._coordSys, coordSys, this._valueOrigin, valueOrigin);\n    var current = diff.current;\n    var stackedOnCurrent = diff.stackedOnCurrent;\n    var next = diff.next;\n    var stackedOnNext = diff.stackedOnNext;\n    if (step) {\n      // TODO If stacked series is not step\n      stackedOnCurrent = turnPointsIntoStep(diff.stackedOnCurrent, diff.current, coordSys, step, connectNulls);\n      current = turnPointsIntoStep(diff.current, null, coordSys, step, connectNulls);\n      stackedOnNext = turnPointsIntoStep(diff.stackedOnNext, diff.next, coordSys, step, connectNulls);\n      next = turnPointsIntoStep(diff.next, null, coordSys, step, connectNulls);\n    }\n    // Don't apply animation if diff is large.\n    // For better result and avoid memory explosion problems like\n    // https://github.com/apache/incubator-echarts/issues/12229\n    if (getBoundingDiff(current, next) > 3000 || polygon && getBoundingDiff(stackedOnCurrent, stackedOnNext) > 3000) {\n      polyline.stopAnimation();\n      polyline.setShape({\n        points: next\n      });\n      if (polygon) {\n        polygon.stopAnimation();\n        polygon.setShape({\n          points: next,\n          stackedOnPoints: stackedOnNext\n        });\n      }\n      return;\n    }\n    polyline.shape.__points = diff.current;\n    polyline.shape.points = current;\n    var target = {\n      shape: {\n        points: next\n      }\n    };\n    // Also animate the original points.\n    // If points reference is changed when turning into step line.\n    if (diff.current !== current) {\n      target.shape.__points = diff.next;\n    }\n    // Stop previous animation.\n    polyline.stopAnimation();\n    graphic.updateProps(polyline, target, seriesModel);\n    if (polygon) {\n      polygon.setShape({\n        // Reuse the points with polyline.\n        points: current,\n        stackedOnPoints: stackedOnCurrent\n      });\n      polygon.stopAnimation();\n      graphic.updateProps(polygon, {\n        shape: {\n          stackedOnPoints: stackedOnNext\n        }\n      }, seriesModel);\n      // If use attr directly in updateProps.\n      if (polyline.shape.points !== polygon.shape.points) {\n        polygon.shape.points = polyline.shape.points;\n      }\n    }\n    var updatedDataInfo = [];\n    var diffStatus = diff.status;\n    for (var i = 0; i < diffStatus.length; i++) {\n      var cmd = diffStatus[i].cmd;\n      if (cmd === '=') {\n        var el = data.getItemGraphicEl(diffStatus[i].idx1);\n        if (el) {\n          updatedDataInfo.push({\n            el: el,\n            ptIdx: i // Index of points\n          });\n        }\n      }\n    }\n    if (polyline.animators && polyline.animators.length) {\n      polyline.animators[0].during(function () {\n        polygon && polygon.dirtyShape();\n        var points = polyline.shape.__points;\n        for (var i = 0; i < updatedDataInfo.length; i++) {\n          var el = updatedDataInfo[i].el;\n          var offset = updatedDataInfo[i].ptIdx * 2;\n          el.x = points[offset];\n          el.y = points[offset + 1];\n          el.markRedraw();\n        }\n      });\n    }\n  };\n  LineView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var oldData = this._data;\n    this._lineGroup.removeAll();\n    this._symbolDraw.remove(true);\n    // Remove temporary created elements when highlighting\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    this._polyline = this._polygon = this._coordSys = this._points = this._stackedOnPoints = this._endLabel = this._data = null;\n  };\n  LineView.type = 'line';\n  return LineView;\n}(ChartView);\nexport default LineView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}