{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProgramNotificationService = /*#__PURE__*/(() => {\n  class ProgramNotificationService {\n    http;\n    AddNotificationURL = environment.baseUrl + 'Programs/add-program-notification';\n    UpdateNotificationURL = environment.baseUrl + 'Programs/update-program-notification';\n    DeleteNotificationURL = environment.baseUrl + 'Programs/delete-program-notification/';\n    GetAllNotificationURL = environment.baseUrl + 'Programs/get-program-notifications-by-program-id/';\n    GetNotificationDetailsURL = environment.baseUrl + 'Programs/get-program-notification-types-to-program';\n    constructor(http) {\n      this.http = http;\n    }\n    addNotification(model) {\n      return this.http.post(this.AddNotificationURL, model);\n    }\n    updateNotification(model) {\n      return this.http.put(this.UpdateNotificationURL, model);\n    }\n    getAllNotifications(id) {\n      return this.http.get(this.GetAllNotificationURL + id);\n    }\n    getNotificationDetails(id) {\n      return this.http.get(this.GetNotificationDetailsURL + id);\n    }\n    deleteNotification(id) {\n      return this.http.delete(this.DeleteNotificationURL + id);\n    }\n    static ɵfac = function ProgramNotificationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramNotificationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProgramNotificationService,\n      factory: ProgramNotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ProgramNotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}