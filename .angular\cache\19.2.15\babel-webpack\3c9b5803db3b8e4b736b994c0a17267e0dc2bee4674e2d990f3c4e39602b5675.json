{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { encodeHTML } from 'zrender/lib/core/dom.js';\nimport { parseDate, isNumeric, numericToNumber } from './number.js';\nimport { format as timeFormat, pad } from './time.js';\nimport { deprecateReplaceLog } from './log.js';\n/**\r\n * Add a comma each three digit.\r\n */\nexport function addCommas(x) {\n  if (!isNumeric(x)) {\n    return zrUtil.isString(x) ? x : '-';\n  }\n  var parts = (x + '').split('.');\n  return parts[0].replace(/(\\d{1,3})(?=(?:\\d{3})+(?!\\d))/g, '$1,') + (parts.length > 1 ? '.' + parts[1] : '');\n}\nexport function toCamelCase(str, upperCaseFirst) {\n  str = (str || '').toLowerCase().replace(/-(.)/g, function (match, group1) {\n    return group1.toUpperCase();\n  });\n  if (upperCaseFirst && str) {\n    str = str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  return str;\n}\nexport var normalizeCssArray = zrUtil.normalizeCssArray;\nexport { encodeHTML };\n/**\r\n * Make value user readable for tooltip and label.\r\n * \"User readable\":\r\n *     Try to not print programmer-specific text like NaN, Infinity, null, undefined.\r\n *     Avoid to display an empty string, which users can not recognize there is\r\n *     a value and it might look like a bug.\r\n */\nexport function makeValueReadable(value, valueType, useUTC) {\n  var USER_READABLE_DEFUALT_TIME_PATTERN = '{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}';\n  function stringToUserReadable(str) {\n    return str && zrUtil.trim(str) ? str : '-';\n  }\n  function isNumberUserReadable(num) {\n    return !!(num != null && !isNaN(num) && isFinite(num));\n  }\n  var isTypeTime = valueType === 'time';\n  var isValueDate = value instanceof Date;\n  if (isTypeTime || isValueDate) {\n    var date = isTypeTime ? parseDate(value) : value;\n    if (!isNaN(+date)) {\n      return timeFormat(date, USER_READABLE_DEFUALT_TIME_PATTERN, useUTC);\n    } else if (isValueDate) {\n      return '-';\n    }\n    // In other cases, continue to try to display the value in the following code.\n  }\n  if (valueType === 'ordinal') {\n    return zrUtil.isStringSafe(value) ? stringToUserReadable(value) : zrUtil.isNumber(value) ? isNumberUserReadable(value) ? value + '' : '-' : '-';\n  }\n  // By default.\n  var numericResult = numericToNumber(value);\n  return isNumberUserReadable(numericResult) ? addCommas(numericResult) : zrUtil.isStringSafe(value) ? stringToUserReadable(value) : typeof value === 'boolean' ? value + '' : '-';\n}\nvar TPL_VAR_ALIAS = ['a', 'b', 'c', 'd', 'e', 'f', 'g'];\nvar wrapVar = function (varName, seriesIdx) {\n  return '{' + varName + (seriesIdx == null ? '' : seriesIdx) + '}';\n};\n/**\r\n * Template formatter\r\n * @param {Array.<Object>|Object} paramsList\r\n */\nexport function formatTpl(tpl, paramsList, encode) {\n  if (!zrUtil.isArray(paramsList)) {\n    paramsList = [paramsList];\n  }\n  var seriesLen = paramsList.length;\n  if (!seriesLen) {\n    return '';\n  }\n  var $vars = paramsList[0].$vars || [];\n  for (var i = 0; i < $vars.length; i++) {\n    var alias = TPL_VAR_ALIAS[i];\n    tpl = tpl.replace(wrapVar(alias), wrapVar(alias, 0));\n  }\n  for (var seriesIdx = 0; seriesIdx < seriesLen; seriesIdx++) {\n    for (var k = 0; k < $vars.length; k++) {\n      var val = paramsList[seriesIdx][$vars[k]];\n      tpl = tpl.replace(wrapVar(TPL_VAR_ALIAS[k], seriesIdx), encode ? encodeHTML(val) : val);\n    }\n  }\n  return tpl;\n}\n/**\r\n * simple Template formatter\r\n */\nexport function formatTplSimple(tpl, param, encode) {\n  zrUtil.each(param, function (value, key) {\n    tpl = tpl.replace('{' + key + '}', encode ? encodeHTML(value) : value);\n  });\n  return tpl;\n}\nexport function getTooltipMarker(inOpt, extraCssText) {\n  var opt = zrUtil.isString(inOpt) ? {\n    color: inOpt,\n    extraCssText: extraCssText\n  } : inOpt || {};\n  var color = opt.color;\n  var type = opt.type;\n  extraCssText = opt.extraCssText;\n  var renderMode = opt.renderMode || 'html';\n  if (!color) {\n    return '';\n  }\n  if (renderMode === 'html') {\n    return type === 'subItem' ? '<span style=\"display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;' + 'border-radius:4px;width:4px;height:4px;background-color:'\n    // Only support string\n    + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>' : '<span style=\"display:inline-block;margin-right:4px;' + 'border-radius:10px;width:10px;height:10px;background-color:' + encodeHTML(color) + ';' + (extraCssText || '') + '\"></span>';\n  } else {\n    // Should better not to auto generate style name by auto-increment number here.\n    // Because this util is usually called in tooltip formatter, which is probably\n    // called repeatedly when mouse move and the auto-increment number increases fast.\n    // Users can make their own style name by theirselves, make it unique and readable.\n    var markerId = opt.markerId || 'markerX';\n    return {\n      renderMode: renderMode,\n      content: '{' + markerId + '|}  ',\n      style: type === 'subItem' ? {\n        width: 4,\n        height: 4,\n        borderRadius: 2,\n        backgroundColor: color\n      } : {\n        width: 10,\n        height: 10,\n        borderRadius: 5,\n        backgroundColor: color\n      }\n    };\n  }\n}\n/**\r\n * @deprecated Use `time/format` instead.\r\n * ISO Date format\r\n * @param {string} tpl\r\n * @param {number} value\r\n * @param {boolean} [isUTC=false] Default in local time.\r\n *           see `module:echarts/scale/Time`\r\n *           and `module:echarts/util/number#parseDate`.\r\n * @inner\r\n */\nexport function formatTime(tpl, value, isUTC) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateReplaceLog('echarts.format.formatTime', 'echarts.time.format');\n  }\n  if (tpl === 'week' || tpl === 'month' || tpl === 'quarter' || tpl === 'half-year' || tpl === 'year') {\n    tpl = 'MM-dd\\nyyyy';\n  }\n  var date = parseDate(value);\n  var getUTC = isUTC ? 'getUTC' : 'get';\n  var y = date[getUTC + 'FullYear']();\n  var M = date[getUTC + 'Month']() + 1;\n  var d = date[getUTC + 'Date']();\n  var h = date[getUTC + 'Hours']();\n  var m = date[getUTC + 'Minutes']();\n  var s = date[getUTC + 'Seconds']();\n  var S = date[getUTC + 'Milliseconds']();\n  tpl = tpl.replace('MM', pad(M, 2)).replace('M', M).replace('yyyy', y).replace('yy', pad(y % 100 + '', 2)).replace('dd', pad(d, 2)).replace('d', d).replace('hh', pad(h, 2)).replace('h', h).replace('mm', pad(m, 2)).replace('m', m).replace('ss', pad(s, 2)).replace('s', s).replace('SSS', pad(S, 3));\n  return tpl;\n}\n/**\r\n * Capital first\r\n * @param {string} str\r\n * @return {string}\r\n */\nexport function capitalFirst(str) {\n  return str ? str.charAt(0).toUpperCase() + str.substr(1) : str;\n}\n/**\r\n * @return Never be null/undefined.\r\n */\nexport function convertToColorString(color, defaultColor) {\n  defaultColor = defaultColor || 'transparent';\n  return zrUtil.isString(color) ? color : zrUtil.isObject(color) ? color.colorStops && (color.colorStops[0] || {}).color || defaultColor : defaultColor;\n}\nexport { truncateText } from 'zrender/lib/graphic/helper/parseText.js';\n/**\r\n * open new tab\r\n * @param link url\r\n * @param target blank or self\r\n */\nexport function windowOpen(link, target) {\n  /* global window */\n  if (target === '_blank' || target === 'blank') {\n    var blank = window.open();\n    blank.opener = null;\n    blank.location.href = link;\n  } else {\n    window.open(link, target);\n  }\n}\nexport { getTextRect } from '../legacy/getTextRect.js';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}