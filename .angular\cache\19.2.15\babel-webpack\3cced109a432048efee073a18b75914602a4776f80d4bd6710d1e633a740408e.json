{"ast": null, "code": "import { formatDate } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { BaseSelectedDateModel } from 'src/app/core/ng-model/base-selected-date-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/program-batches-service/program-batches.service\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"red-border-class\": a0\n});\nconst _c1 = a0 => ({\n  \"dimmed\": a0\n});\nfunction AddProgBatchComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BATCHES.REQUIRED\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddProgBatchComponent_div_9_div_1_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.batchNameAr.errors.required);\n  }\n}\nfunction AddProgBatchComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"PROGRAM_BATCHES.REQUIRED\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddProgBatchComponent_div_14_div_1_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.batchNameEn.errors.required);\n  }\n}\nfunction AddProgBatchComponent_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.HIJRIBIRTHDATE_REQUIRED\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddProgBatchComponent_div_20_div_1_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.batchSubscriptionStartDate.errors == null ? null : ctx_r0.f.batchSubscriptionStartDate.errors.required);\n  }\n}\nfunction AddProgBatchComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.HIJRIBIRTHDATE_REQUIRED\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddProgBatchComponent_div_26_div_1_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.batchSubscriptionEndDate.errors == null ? null : ctx_r0.f.batchSubscriptionEndDate.errors.required);\n  }\n}\nfunction AddProgBatchComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function AddProgBatchComponent_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.close());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AddProgBatchComponent_div_27_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.submit());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"PROGRAM_BATCHES.CANCEL\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"PROGRAM_BATCHES.SEND\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function AddProgBatchComponent_div_28_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.close());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function AddProgBatchComponent_div_28_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.update());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"PROGRAM_BATCHES.CANCEL\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"PROGRAM_BATCHES.SEND\"), \" \");\n  }\n}\nfunction AddProgBatchComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resMessage.message, \" \");\n  }\n}\nexport let AddProgBatchComponent = /*#__PURE__*/(() => {\n  class AddProgBatchComponent {\n    fb;\n    dateFormatterService;\n    translate;\n    programBatchesService;\n    alertfyService;\n    hideAddBatchOverlayEvent = new EventEmitter();\n    programDetails;\n    programBatchDetails;\n    currentForm = new FormGroup({});\n    isSubmit = false;\n    resMessage = {};\n    calenderType = new BaseSelectedDateModel();\n    createProgBatchModel;\n    updateProgBatchModel;\n    GetTodayDate;\n    minGregorianBatchDate;\n    batchSubscriptionStartDateInputParam = {\n      year: 0,\n      day: 0,\n      month: 0\n    };\n    batchSubscriptionEndDateInputParam = {\n      year: 0,\n      day: 0,\n      month: 0\n    };\n    isMoreToday = false;\n    constructor(fb, dateFormatterService, translate, programBatchesService, alertfyService) {\n      this.fb = fb;\n      this.dateFormatterService = dateFormatterService;\n      this.translate = translate;\n      this.programBatchesService = programBatchesService;\n      this.alertfyService = alertfyService;\n    }\n    ngOnInit() {\n      this.buildForm();\n      this.setMilady();\n      if (this.programBatchDetails != null) {\n        this.PopulateForm();\n      }\n    }\n    get f() {\n      return this.currentForm?.controls;\n    }\n    buildForm() {\n      this.currentForm = this.fb.group({\n        batchNameAr: ['', [Validators.required]],\n        batchNameEn: ['', [Validators.required]],\n        batchSubscriptionStartDate: ['', [Validators.required]],\n        batchSubscriptionEndDate: ['', [Validators.required]]\n      });\n    }\n    submit() {\n      this.GetTodayDate = formatDate(new Date(), 'yyyy/MM/dd', 'en');\n      this.isSubmit = true;\n      this.resMessage = {};\n      if (this.currentForm.valid) {\n        this.mapCreateModel();\n        var startDate = formatDate(this.createProgBatchModel?.startDateBatSub ? this.createProgBatchModel?.startDateBatSub : '', 'yyyy/MM/dd', 'en');\n        var endDate = formatDate(this.createProgBatchModel?.endDateBatSub ? this.createProgBatchModel?.endDateBatSub : '', 'yyyy/MM/dd', 'en');\n        if (startDate < this.GetTodayDate) {\n          this.resMessage = {\n            message: this.translate.instant('PROGRAM_BATCH.FROM_VALIDATION'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        if (startDate > endDate) {\n          this.resMessage = {\n            message: this.translate.instant('PROGRAM_BATCH.TO_VALIDATION'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        this.createProgBatchModel ? this.programBatchesService.addProgBatch(this.createProgBatchModel).subscribe(res => {\n          if (res.isSuccess) {\n            this.alertfyService.success(res.message || '');\n            this.close();\n          } else {\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        }) : '';\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    update() {\n      this.GetTodayDate = formatDate(new Date(), 'yyyy/MM/dd', 'en');\n      this.isSubmit = true;\n      this.resMessage = {};\n      if (this.currentForm.valid) {\n        this.mapUpdateModel();\n        var startDate = formatDate(this.updateProgBatchModel?.startDateBatSub ? this.updateProgBatchModel?.startDateBatSub : '', 'yyyy/MM/dd', 'en');\n        var endDate = formatDate(this.updateProgBatchModel?.endDateBatSub ? this.updateProgBatchModel?.endDateBatSub : '', 'yyyy/MM/dd', 'en');\n        if (startDate < this.GetTodayDate) {\n          this.resMessage = {\n            message: this.translate.instant('PROGRAM_BATCH.FROM_VALIDATION'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        if (startDate > endDate) {\n          this.resMessage = {\n            message: this.translate.instant('PROGRAM_BATCH.TO_VALIDATION'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        this.updateProgBatchModel ? this.programBatchesService.updateProgBatch(this.updateProgBatchModel).subscribe(res => {\n          if (res.isSuccess) {\n            this.alertfyService.success(res.message || '');\n            this.programBatchDetails = {};\n            this.close();\n          } else {\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        }) : '';\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    mapCreateModel() {\n      this.createProgBatchModel = {\n        arabBatName: this.f.batchNameAr.value,\n        engBatName: this.f.batchNameEn.value,\n        endDateBatSub: this.f.batchSubscriptionEndDate.value,\n        startDateBatSub: this.f.batchSubscriptionStartDate.value,\n        progId: this.programDetails?.progBaseInfo?.id\n      };\n    }\n    mapUpdateModel() {\n      this.updateProgBatchModel = {\n        arabBatName: this.f.batchNameAr.value,\n        engBatName: this.f.batchNameEn.value,\n        endDateBatSub: this.f.batchSubscriptionEndDate.value,\n        startDateBatSub: this.f.batchSubscriptionStartDate.value,\n        id: this.programBatchDetails?.id\n      };\n    }\n    close() {\n      this.hideAddBatchOverlayEvent.emit(false);\n    }\n    setMilady() {\n      let toDayTodayGregorian = this.dateFormatterService.GetTodayGregorian();\n      this.minGregorianBatchDate = toDayTodayGregorian;\n    }\n    updateStartDate(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.f.batchSubscriptionStartDate.setValue(data.selectedDateValue);\n    }\n    updateEndDate(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.f.batchSubscriptionEndDate.setValue(data.selectedDateValue);\n    }\n    PopulateForm() {\n      this.f.batchNameAr.setValue(this.programBatchDetails?.arBatName);\n      this.f.batchNameEn.setValue(this.programBatchDetails?.enBatName);\n      this.f.batchSubscriptionStartDate.setValue(this.programBatchDetails?.batStaSubsDat);\n      let dateOfBatchSubscriptionStart = new Date(this.programBatchDetails?.batStaSubsDat || '');\n      this.batchSubscriptionStartDateInputParam = {\n        year: dateOfBatchSubscriptionStart?.getFullYear(),\n        month: dateOfBatchSubscriptionStart?.getMonth() + 1,\n        day: dateOfBatchSubscriptionStart?.getDate()\n      };\n      this.f.batchSubscriptionEndDate.setValue(this.programBatchDetails?.batEnSubsDat);\n      let dateOfBatchSubscriptionEndDateInputParam = new Date(this.programBatchDetails?.batEnSubsDat || '');\n      this.batchSubscriptionEndDateInputParam = {\n        year: dateOfBatchSubscriptionEndDateInputParam?.getFullYear(),\n        month: dateOfBatchSubscriptionEndDateInputParam?.getMonth() + 1,\n        day: dateOfBatchSubscriptionEndDateInputParam?.getDate()\n      };\n      let GetToDay = formatDate(new Date(), 'yyyy/MM/dd', 'en');\n      var endDate = formatDate(this.programBatchDetails?.batEnSubsDat || '', 'yyyy/MM/dd', 'en');\n      if (GetToDay > endDate) {\n        this.isMoreToday = true;\n      }\n    }\n    static ɵfac = function AddProgBatchComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddProgBatchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DateFormatterService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ProgramBatchesService), i0.ɵɵdirectiveInject(i5.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProgBatchComponent,\n      selectors: [[\"app-add-prog-batch\"]],\n      inputs: {\n        programDetails: \"programDetails\",\n        programBatchDetails: \"programBatchDetails\"\n      },\n      outputs: {\n        hideAddBatchOverlayEvent: \"hideAddBatchOverlayEvent\"\n      },\n      decls: 30,\n      vars: 44,\n      consts: [[1, \"form-group\", \"material-form\", 3, \"formGroup\"], [1, \"bold\", \"mb-2\"], [1, \"form-group\"], [\"for\", \"comment\"], [\"formControlName\", \"batchNameAr\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"formControlName\", \"batchNameEn\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-12\", \"p-0\"], [1, \"label\"], [3, \"sendDate\", \"keypress\", \"editcalenderType\", \"minGreg\", \"dateTo\", \"hijri\", \"milady\", \"ngClass\"], [3, \"sendDate\", \"keypress\", \"editcalenderType\", \"dateTo\", \"hijri\", \"milady\", \"ngClass\"], [\"class\", \"d-flex\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"cancel-btn\", \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"submit\", \"type\", \"submit\", 1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"click\"], [1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"click\"]],\n      template: function AddProgBatchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"p\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"input\", 4);\n          i0.ɵɵtemplate(9, AddProgBatchComponent_div_9_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementStart(10, \"label\", 3);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 6);\n          i0.ɵɵtemplate(14, AddProgBatchComponent_div_14_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"label\", 8);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"app-milady-hijri-calendar\", 9);\n          i0.ɵɵlistener(\"sendDate\", function AddProgBatchComponent_Template_app_milady_hijri_calendar_sendDate_19_listener($event) {\n            return ctx.updateStartDate($event);\n          })(\"keypress\", function AddProgBatchComponent_Template_app_milady_hijri_calendar_keypress_19_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, AddProgBatchComponent_div_20_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 7)(22, \"label\", 8);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"app-milady-hijri-calendar\", 10);\n          i0.ɵɵlistener(\"sendDate\", function AddProgBatchComponent_Template_app_milady_hijri_calendar_sendDate_25_listener($event) {\n            return ctx.updateEndDate($event);\n          })(\"keypress\", function AddProgBatchComponent_Template_app_milady_hijri_calendar_keypress_25_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, AddProgBatchComponent_div_26_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, AddProgBatchComponent_div_27_Template, 7, 6, \"div\", 11)(28, AddProgBatchComponent_div_28_Template, 7, 6, \"div\", 11)(29, AddProgBatchComponent_div_29_Template, 2, 4, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.currentForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 26, \"PROGRAM_BATCHES.ADD_PROGRAM_BATCH\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(7, 28, \"PROGRAM_BATCHES.BAT_NAME_AR\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c0, ctx.f.batchNameAr.errors && (ctx.f.batchNameAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.batchNameAr.errors && (ctx.f.batchNameAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(12, 30, \"PROGRAM_BATCHES.BAT_NAME_EN\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c0, ctx.f.batchNameEn.errors && (ctx.f.batchNameEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.batchNameEn.errors && (ctx.f.batchNameEn.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 32, \"PROGRAM_BATCHES.FROM_DATE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"minGreg\", ctx.minGregorianBatchDate)(\"dateTo\", ctx.batchSubscriptionStartDateInputParam)(\"hijri\", true)(\"milady\", false)(\"ngClass\", i0.ɵɵpureFunction1(40, _c1, ctx.isMoreToday === true));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !(ctx.f.batchSubscriptionStartDate == null ? null : ctx.f.batchSubscriptionStartDate.errors) && (ctx.f.batchSubscriptionStartDate.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(24, 34, \"PROGRAM_BATCHES.TO_DATE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"dateTo\", ctx.batchSubscriptionEndDateInputParam)(\"hijri\", true)(\"milady\", false)(\"ngClass\", i0.ɵɵpureFunction1(42, _c1, ctx.isMoreToday === true));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !(ctx.f.batchSubscriptionEndDate == null ? null : ctx.f.batchSubscriptionEndDate.errors) && (ctx.f.batchSubscriptionEndDate.touched || ctx.isSubmit));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.programBatchDetails);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.programBatchDetails);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resMessage);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.TranslatePipe],\n      styles: [\".material-form[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border:.063rem solid #fbfbfb;opacity:1;padding:3rem 1rem 1rem}.material-form[_ngcontent-%COMP%]:lang(ar){text-align:right}.material-form[_ngcontent-%COMP%]:lang(en){text-align:left}.material-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{text-align:right;font-size:.875rem;color:gray;letter-spacing:0;opacity:1}.material-form[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.material-form[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.material-form[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}\"]\n    });\n  }\n  return AddProgBatchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}