{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nlet RouterLink = /*#__PURE__*/(() => {\n  class RouterLink {\n    router;\n    route;\n    tabIndexAttribute;\n    renderer;\n    el;\n    locationStrategy;\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is `<a>`. For other tags, the value is `null`.\n     */\n    href = null;\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is an `<a>` tag.\n     */\n    target;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParams;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    fragment;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParamsHandling;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    state;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    info;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    relativeTo;\n    /** Whether a host element is an `<a>` tag. */\n    isAnchorElement;\n    subscription;\n    /** @internal */\n    onChanges = new Subject();\n    constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n      this.router = router;\n      this.route = route;\n      this.tabIndexAttribute = tabIndexAttribute;\n      this.renderer = renderer;\n      this.el = el;\n      this.locationStrategy = locationStrategy;\n      const tagName = el.nativeElement.tagName?.toLowerCase();\n      this.isAnchorElement = tagName === 'a' || tagName === 'area';\n      if (this.isAnchorElement) {\n        this.subscription = router.events.subscribe(s => {\n          if (s instanceof NavigationEnd) {\n            this.updateHref();\n          }\n        });\n      } else {\n        this.setTabIndexIfNotOnNativeEl('0');\n      }\n    }\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    preserveFragment = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    skipLocationChange = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    replaceUrl = false;\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n      if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n        return;\n      }\n      this.applyAttributeValue('tabindex', newTabIndex);\n    }\n    /** @docs-private */\n    // TODO(atscott): Remove changes parameter in major version as a breaking change.\n    ngOnChanges(changes) {\n      if (ngDevMode && isUrlTree(this.routerLinkInput) && (this.fragment !== undefined || this.queryParams || this.queryParamsHandling || this.preserveFragment || this.relativeTo)) {\n        throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n      }\n      if (this.isAnchorElement) {\n        this.updateHref();\n      }\n      // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n      // to the RouterLinks it's tracking.\n      this.onChanges.next(this);\n    }\n    routerLinkInput = null;\n    /**\n     * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n     *   - **array**: commands to pass to {@link Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n     *     and other inputs that correspond to properties of `UrlCreationOptions`.\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree}\n     */\n    set routerLink(commandsOrUrlTree) {\n      if (commandsOrUrlTree == null) {\n        this.routerLinkInput = null;\n        this.setTabIndexIfNotOnNativeEl(null);\n      } else {\n        if (isUrlTree(commandsOrUrlTree)) {\n          this.routerLinkInput = commandsOrUrlTree;\n        } else {\n          this.routerLinkInput = Array.isArray(commandsOrUrlTree) ? commandsOrUrlTree : [commandsOrUrlTree];\n        }\n        this.setTabIndexIfNotOnNativeEl('0');\n      }\n    }\n    /** @docs-private */\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n      const urlTree = this.urlTree;\n      if (urlTree === null) {\n        return true;\n      }\n      if (this.isAnchorElement) {\n        if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n          return true;\n        }\n        if (typeof this.target === 'string' && this.target != '_self') {\n          return true;\n        }\n      }\n      const extras = {\n        skipLocationChange: this.skipLocationChange,\n        replaceUrl: this.replaceUrl,\n        state: this.state,\n        info: this.info\n      };\n      this.router.navigateByUrl(urlTree, extras);\n      // Return `false` for `<a>` elements to prevent default action\n      // and cancel the native behavior, since the navigation is handled\n      // by the Router.\n      return !this.isAnchorElement;\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.subscription?.unsubscribe();\n    }\n    updateHref() {\n      const urlTree = this.urlTree;\n      this.href = urlTree !== null && this.locationStrategy ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree)) : null;\n      const sanitizedValue = this.href === null ? null :\n      // This class represents a directive that can be added to both `<a>` elements,\n      // as well as other elements. As a result, we can't define security context at\n      // compile time. So the security context is deferred to runtime.\n      // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n      // based on the tag and property names. The logic mimics the one from\n      // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n      //\n      // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n      // instead of applying a value via a renderer, after a final merge of the\n      // `RouterLinkWithHref` directive.\n      __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n      this.applyAttributeValue('href', sanitizedValue);\n    }\n    applyAttributeValue(attrName, attrValue) {\n      const renderer = this.renderer;\n      const nativeElement = this.el.nativeElement;\n      if (attrValue !== null) {\n        renderer.setAttribute(nativeElement, attrName, attrValue);\n      } else {\n        renderer.removeAttribute(nativeElement, attrName);\n      }\n    }\n    get urlTree() {\n      if (this.routerLinkInput === null) {\n        return null;\n      } else if (isUrlTree(this.routerLinkInput)) {\n        return this.routerLinkInput;\n      }\n      return this.router.createUrlTree(this.routerLinkInput, {\n        // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n        // Otherwise, we should use the value provided by the user in the input.\n        relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: this.preserveFragment\n      });\n    }\n    static ɵfac = function RouterLink_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RouterLink,\n      selectors: [[\"\", \"routerLink\", \"\"]],\n      hostVars: 1,\n      hostBindings: function RouterLink_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function RouterLink_click_HostBindingHandler($event) {\n            return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"target\", ctx.target);\n        }\n      },\n      inputs: {\n        target: \"target\",\n        queryParams: \"queryParams\",\n        fragment: \"fragment\",\n        queryParamsHandling: \"queryParamsHandling\",\n        state: \"state\",\n        info: \"info\",\n        relativeTo: \"relativeTo\",\n        preserveFragment: [2, \"preserveFragment\", \"preserveFragment\", booleanAttribute],\n        skipLocationChange: [2, \"skipLocationChange\", \"skipLocationChange\", booleanAttribute],\n        replaceUrl: [2, \"replaceUrl\", \"replaceUrl\", booleanAttribute],\n        routerLink: \"routerLink\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return RouterLink;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nlet RouterLinkActive = /*#__PURE__*/(() => {\n  class RouterLinkActive {\n    router;\n    element;\n    renderer;\n    cdr;\n    link;\n    links;\n    classes = [];\n    routerEventsSubscription;\n    linkInputChangesSubscription;\n    _isActive = false;\n    get isActive() {\n      return this._isActive;\n    }\n    /**\n     * Options to configure how to determine if the router link is active.\n     *\n     * These options are passed to the `Router.isActive()` function.\n     *\n     * @see {@link Router#isActive}\n     */\n    routerLinkActiveOptions = {\n      exact: false\n    };\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    ariaCurrentWhenActive;\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    isActiveChange = new EventEmitter();\n    constructor(router, element, renderer, cdr, link) {\n      this.router = router;\n      this.element = element;\n      this.renderer = renderer;\n      this.cdr = cdr;\n      this.link = link;\n      this.routerEventsSubscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.update();\n        }\n      });\n    }\n    /** @docs-private */\n    ngAfterContentInit() {\n      // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n      of(this.links.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n        this.update();\n        this.subscribeToEachLinkOnChanges();\n      });\n    }\n    subscribeToEachLinkOnChanges() {\n      this.linkInputChangesSubscription?.unsubscribe();\n      const allLinkChanges = [...this.links.toArray(), this.link].filter(link => !!link).map(link => link.onChanges);\n      this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n        if (this._isActive !== this.isLinkActive(this.router)(link)) {\n          this.update();\n        }\n      });\n    }\n    set routerLinkActive(data) {\n      const classes = Array.isArray(data) ? data : data.split(' ');\n      this.classes = classes.filter(c => !!c);\n    }\n    /** @docs-private */\n    ngOnChanges(changes) {\n      this.update();\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.routerEventsSubscription.unsubscribe();\n      this.linkInputChangesSubscription?.unsubscribe();\n    }\n    update() {\n      if (!this.links || !this.router.navigated) return;\n      queueMicrotask(() => {\n        const hasActiveLinks = this.hasActiveLinks();\n        this.classes.forEach(c => {\n          if (hasActiveLinks) {\n            this.renderer.addClass(this.element.nativeElement, c);\n          } else {\n            this.renderer.removeClass(this.element.nativeElement, c);\n          }\n        });\n        if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n          this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n        } else {\n          this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n        }\n        // Only emit change if the active state changed.\n        if (this._isActive !== hasActiveLinks) {\n          this._isActive = hasActiveLinks;\n          this.cdr.markForCheck();\n          // Emit on isActiveChange after classes are updated\n          this.isActiveChange.emit(hasActiveLinks);\n        }\n      });\n    }\n    isLinkActive(router) {\n      const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions :\n      // While the types should disallow `undefined` here, it's possible without strict inputs\n      this.routerLinkActiveOptions.exact || false;\n      return link => {\n        const urlTree = link.urlTree;\n        return urlTree ? router.isActive(urlTree, options) : false;\n      };\n    }\n    hasActiveLinks() {\n      const isActiveCheckFn = this.isLinkActive(this.router);\n      return this.link && isActiveCheckFn(this.link) || this.links.some(isActiveCheckFn);\n    }\n    static ɵfac = function RouterLinkActive_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RouterLinkActive,\n      selectors: [[\"\", \"routerLinkActive\", \"\"]],\n      contentQueries: function RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n        }\n      },\n      inputs: {\n        routerLinkActiveOptions: \"routerLinkActiveOptions\",\n        ariaCurrentWhenActive: \"ariaCurrentWhenActive\",\n        routerLinkActive: \"routerLinkActive\"\n      },\n      outputs: {\n        isActiveChange: \"isActiveChange\"\n      },\n      exportAs: [\"routerLinkActive\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return RouterLinkActive;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nlet PreloadAllModules = /*#__PURE__*/(() => {\n  class PreloadAllModules {\n    preload(route, fn) {\n      return fn().pipe(catchError(() => of(null)));\n    }\n    static ɵfac = function PreloadAllModules_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreloadAllModules)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PreloadAllModules,\n      factory: PreloadAllModules.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PreloadAllModules;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nlet NoPreloading = /*#__PURE__*/(() => {\n  class NoPreloading {\n    preload(route, fn) {\n      return of(null);\n    }\n    static ɵfac = function NoPreloading_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoPreloading)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NoPreloading,\n      factory: NoPreloading.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return NoPreloading;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nlet RouterPreloader = /*#__PURE__*/(() => {\n  class RouterPreloader {\n    router;\n    injector;\n    preloadingStrategy;\n    loader;\n    subscription;\n    constructor(router, injector, preloadingStrategy, loader) {\n      this.router = router;\n      this.injector = injector;\n      this.preloadingStrategy = preloadingStrategy;\n      this.loader = loader;\n    }\n    setUpPreloading() {\n      this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n    }\n    preload() {\n      return this.processRoutes(this.injector, this.router.config);\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    processRoutes(injector, routes) {\n      const res = [];\n      for (const route of routes) {\n        if (route.providers && !route._injector) {\n          route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n        }\n        const injectorForCurrentRoute = route._injector ?? injector;\n        const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n        // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n        // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n        // happens as a consequence of needing to descend into children for route matching immediately\n        // while component loading is deferred until route activation. Because `canLoad` guards can\n        // have side effects, we cannot execute them here so we instead skip preloading altogether\n        // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n        // at all. Code splitting and lazy loading is separate from client-side authorization checks\n        // and should not be used as a security measure to prevent loading of code.\n        if (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined || route.loadComponent && !route._loadedComponent) {\n          res.push(this.preloadConfig(injectorForCurrentRoute, route));\n        }\n        if (route.children || route._loadedRoutes) {\n          res.push(this.processRoutes(injectorForChildren, route.children ?? route._loadedRoutes));\n        }\n      }\n      return from(res).pipe(mergeAll());\n    }\n    preloadConfig(injector, route) {\n      return this.preloadingStrategy.preload(route, () => {\n        let loadedChildren$;\n        if (route.loadChildren && route.canLoad === undefined) {\n          loadedChildren$ = this.loader.loadChildren(injector, route);\n        } else {\n          loadedChildren$ = of(null);\n        }\n        const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap(config => {\n          if (config === null) {\n            return of(void 0);\n          }\n          route._loadedRoutes = config.routes;\n          route._loadedInjector = config.injector;\n          // If the loaded config was a module, use that as the module/module injector going\n          // forward. Otherwise, continue using the current module/module injector.\n          return this.processRoutes(config.injector ?? injector, config.routes);\n        }));\n        if (route.loadComponent && !route._loadedComponent) {\n          const loadComponent$ = this.loader.loadComponent(route);\n          return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n        } else {\n          return recursiveLoadChildren$;\n        }\n      });\n    }\n    static ɵfac = function RouterPreloader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.EnvironmentInjector), i0.ɵɵinject(PreloadingStrategy), i0.ɵɵinject(RouterConfigLoader));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterPreloader,\n      factory: RouterPreloader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RouterPreloader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst ROUTER_SCROLLER = /*#__PURE__*/new InjectionToken('');\nlet RouterScroller = /*#__PURE__*/(() => {\n  class RouterScroller {\n    urlSerializer;\n    transitions;\n    viewportScroller;\n    zone;\n    options;\n    routerEventsSubscription;\n    scrollEventsSubscription;\n    lastId = 0;\n    lastSource = 'imperative';\n    restoredId = 0;\n    store = {};\n    /** @docs-private */\n    constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n      this.urlSerializer = urlSerializer;\n      this.transitions = transitions;\n      this.viewportScroller = viewportScroller;\n      this.zone = zone;\n      this.options = options;\n      // Default both options to 'disabled'\n      options.scrollPositionRestoration ||= 'disabled';\n      options.anchorScrolling ||= 'disabled';\n    }\n    init() {\n      // we want to disable the automatic scrolling because having two places\n      // responsible for scrolling results race conditions, especially given\n      // that browser don't implement this behavior consistently\n      if (this.options.scrollPositionRestoration !== 'disabled') {\n        this.viewportScroller.setHistoryScrollRestoration('manual');\n      }\n      this.routerEventsSubscription = this.createScrollEvents();\n      this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n    createScrollEvents() {\n      return this.transitions.events.subscribe(e => {\n        if (e instanceof NavigationStart) {\n          // store the scroll position of the current stable navigations.\n          this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n          this.lastSource = e.navigationTrigger;\n          this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n        } else if (e instanceof NavigationEnd) {\n          this.lastId = e.id;\n          this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n        } else if (e instanceof NavigationSkipped && e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n          this.lastSource = undefined;\n          this.restoredId = 0;\n          this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n        }\n      });\n    }\n    consumeScrollEvents() {\n      return this.transitions.events.subscribe(e => {\n        if (!(e instanceof Scroll)) return;\n        // a popstate event. The pop state event will always ignore anchor scrolling.\n        if (e.position) {\n          if (this.options.scrollPositionRestoration === 'top') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          } else if (this.options.scrollPositionRestoration === 'enabled') {\n            this.viewportScroller.scrollToPosition(e.position);\n          }\n          // imperative navigation \"forward\"\n        } else {\n          if (e.anchor && this.options.anchorScrolling === 'enabled') {\n            this.viewportScroller.scrollToAnchor(e.anchor);\n          } else if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          }\n        }\n      });\n    }\n    scheduleScrollEvent(routerEvent, anchor) {\n      this.zone.runOutsideAngular(() => {\n        // The scroll event needs to be delayed until after change detection. Otherwise, we may\n        // attempt to restore the scroll position before the router outlet has fully rendered the\n        // component by executing its update block of the template function.\n        setTimeout(() => {\n          this.zone.run(() => {\n            this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n          });\n        }, 0);\n      });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.routerEventsSubscription?.unsubscribe();\n      this.scrollEventsSubscription?.unsubscribe();\n    }\n    static ɵfac = function RouterScroller_Factory(__ngFactoryType__) {\n      i0.ɵɵinvalidFactory();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterScroller,\n      factory: RouterScroller.ɵfac\n    });\n  }\n  return RouterScroller;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n  return makeEnvironmentProviders([{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n    provide: ROUTER_IS_PROVIDED,\n    useValue: true\n  } : [], {\n    provide: ActivatedRoute,\n    useFactory: rootRoute,\n    deps: [Router]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: getBootstrapListener\n  }, features.map(feature => feature.ɵproviders)]);\n}\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = /*#__PURE__*/new InjectionToken('', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' + 'This is likely a mistake.');\n      }\n    };\n  }\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n  return [{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : []];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n  const providers = [{\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n    }\n  }];\n  return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n  const injector = inject(Injector);\n  return bootstrappedComponentRef => {\n    const ref = injector.get(ApplicationRef);\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n    if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n      router.initialNavigation();\n    }\n    injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)?.setUpPreloading();\n    injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)?.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n  factory: () => {\n    return new Subject();\n  }\n});\nconst INITIAL_NAVIGATION = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', {\n  providedIn: 'root',\n  factory: () => 1 /* InitialNavigation.EnabledNonBlocking */\n});\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n  const providers = [{\n    provide: INITIAL_NAVIGATION,\n    useValue: 0 /* InitialNavigation.EnabledBlocking */\n  }, provideAppInitializer(() => {\n    const injector = inject(Injector);\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n    return locationInitialized.then(() => {\n      return new Promise(resolve => {\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        afterNextNavigation(router, () => {\n          // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n          // without a redirect.\n          resolve(true);\n        });\n        injector.get(NavigationTransitions).afterPreactivation = () => {\n          // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n          // assume activation will complete successfully (even though this is not\n          // guaranteed).\n          resolve(true);\n          return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n        };\n        router.initialNavigation();\n      });\n    });\n  })];\n  return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n  const providers = [provideAppInitializer(() => {\n    inject(Router).setUpLocationChangeListener();\n  }), {\n    provide: INITIAL_NAVIGATION,\n    useValue: 2 /* InitialNavigation.Disabled */\n  }];\n  return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n  let providers = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [{\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        const router = inject(Router);\n        return () => router.events.subscribe(e => {\n          // tslint:disable:no-console\n          console.group?.(`Router Event: ${e.constructor.name}`);\n          console.log(stringifyEvent(e));\n          console.log(e);\n          console.groupEnd?.();\n          // tslint:enable:no-console\n        });\n      }\n    }];\n  } else {\n    providers = [];\n  }\n  return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n  const providers = [{\n    provide: ROUTER_PRELOADER,\n    useExisting: RouterPreloader\n  }, {\n    provide: PreloadingStrategy,\n    useExisting: preloadingStrategy\n  }];\n  return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n  const providers = [{\n    provide: ROUTER_CONFIGURATION,\n    useValue: options\n  }];\n  return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n  const providers = [{\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }];\n  return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n  const providers = [{\n    provide: NAVIGATION_ERROR_HANDLER,\n    useValue: handler\n  }];\n  return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n  const providers = [RoutedComponentInputBinder, {\n    provide: INPUT_BINDER,\n    useExisting: RoutedComponentInputBinder\n  }];\n  return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n  _performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [{\n    provide: CREATE_VIEW_TRANSITION,\n    useValue: createViewTransition\n  }, {\n    provide: VIEW_TRANSITION_OPTIONS,\n    useValue: {\n      skipNextTransition: !!options?.skipInitialTransition,\n      ...options\n    }\n  }];\n  return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, Router, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterConfigLoader,\n// Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n// be removed when `provideRoutes` is removed.\ntypeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: ROUTER_IS_PROVIDED,\n  useValue: true\n} : []];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nlet RouterModule = /*#__PURE__*/(() => {\n  class RouterModule {\n    constructor() {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        inject(ROUTER_FORROOT_GUARD, {\n          optional: true\n        });\n      }\n    }\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n    static forRoot(routes, config) {\n      return {\n        ngModule: RouterModule,\n        providers: [ROUTER_PROVIDERS, typeof ngDevMode === 'undefined' || ngDevMode ? config?.enableTracing ? withDebugTracing().ɵproviders : [] : [], {\n          provide: ROUTES,\n          multi: true,\n          useValue: routes\n        }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n          provide: ROUTER_FORROOT_GUARD,\n          useFactory: provideForRootGuard,\n          deps: [[Router, new Optional(), new SkipSelf()]]\n        } : [], config?.errorHandler ? {\n          provide: NAVIGATION_ERROR_HANDLER,\n          useValue: config.errorHandler\n        } : [], {\n          provide: ROUTER_CONFIGURATION,\n          useValue: config ? config : {}\n        }, config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(), provideRouterScroller(), config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [], config?.initialNavigation ? provideInitialNavigation(config) : [], config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [], config?.enableViewTransitions ? withViewTransitions().ɵproviders : [], provideRouterInitializer()]\n      };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n    static forChild(routes) {\n      return {\n        ngModule: RouterModule,\n        providers: [{\n          provide: ROUTES,\n          multi: true,\n          useValue: routes\n        }]\n      };\n    }\n    static ɵfac = function RouterModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RouterModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return RouterModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    }\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  };\n}\nfunction provideForRootGuard(router) {\n  if (router) {\n    throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` + ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n  return [config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [], config.initialNavigation === 'enabledBlocking' ? withEnabledBlockingInitialNavigation().ɵproviders : []];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n  return [\n  // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n  // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n  {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n//# sourceMappingURL=router_module-DTJgGWLd.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}