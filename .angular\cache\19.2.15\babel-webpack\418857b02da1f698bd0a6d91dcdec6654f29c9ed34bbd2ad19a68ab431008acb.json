{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from '../../../core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nfunction CardStudentScientificProblemComponent_p_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"SCIENTIFIC_PROBLEM.PROGRAM_NAME\"), \"\");\n  }\n}\nfunction CardStudentScientificProblemComponent_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.scientificProblem.enProgBatchName : ctx_r0.scientificProblem.arProgBatchName, \"\");\n  }\n}\nfunction CardStudentScientificProblemComponent_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.scientificProblem.enProgramTaskName : ctx_r0.scientificProblem.arProgramTaskName, \" - \", i0.ɵɵpipeBind1(2, 3, \"SCIENTIFIC_PROBLEM.DAY\"), \" \", ctx_r0.scientificProblem.dayNum, \"\");\n  }\n}\nexport let CardStudentScientificProblemComponent = /*#__PURE__*/(() => {\n  class CardStudentScientificProblemComponent {\n    translate;\n    langEnum = LanguageEnum;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    scientificProblem = {};\n    deleteScientificProblem = new EventEmitter();\n    ngOnInit() {}\n    deleteScientificProblemEve() {\n      this.deleteScientificProblem.emit(this.scientificProblem.id);\n    }\n    static ɵfac = function CardStudentScientificProblemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CardStudentScientificProblemComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CardStudentScientificProblemComponent,\n      selectors: [[\"app-card-student-scientific-problem\"]],\n      inputs: {\n        scientificProblem: \"scientificProblem\"\n      },\n      outputs: {\n        deleteScientificProblem: \"deleteScientificProblem\"\n      },\n      decls: 29,\n      vars: 18,\n      consts: [[1, \"card-student-scientific-problem\"], [1, \"card_header\"], [1, \"far\", \"fa-trash-alt\", 3, \"click\"], [1, \"card_body\"], [1, \"body_header\"], [1, \"mt-4\"], [\"class\", \"program_user bold mb-0\", 4, \"ngIf\"], [1, \"bold\"], [1, \"body_container\"], [1, \"program_user\", \"bold\", \"mb-0\"]],\n      template: function CardStudentScientificProblemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\");\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\")(8, \"i\", 2);\n          i0.ɵɵlistener(\"click\", function CardStudentScientificProblemComponent_Template_i_click_8_listener() {\n            return ctx.deleteScientificProblemEve();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 3)(10, \"div\", 4)(11, \"div\", 5);\n          i0.ɵɵtemplate(12, CardStudentScientificProblemComponent_p_12_Template, 3, 3, \"p\", 6)(13, CardStudentScientificProblemComponent_p_13_Template, 2, 1, \"p\", 6)(14, CardStudentScientificProblemComponent_p_14_Template, 3, 5, \"p\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 4)(16, \"span\", 7);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 7);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"p\", 7);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 8)(24, \"span\");\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"p\", 7);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 12, \"SCIENTIFIC_PROBLEM.TIME\"), \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.scientificProblem.scCreationDate, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"disabled\", ctx.scientificProblem.reply);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.scientificProblem.batId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.scientificProblem.batId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.scientificProblem.batId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 14, \"SCIENTIFIC_PROBLEM.QUESTION\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.scientificProblem.huffazNo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.scientificProblem.question);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 16, \"SCIENTIFIC_PROBLEM.REPLY\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.scientificProblem.reply, \" \");\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card-student-scientific-problem[_ngcontent-%COMP%]{background:#fff;box-shadow:0 .188rem 1rem #f2f1f1de;border-radius:.75rem;padding:0;overflow:hidden;font-size:.875rem}.card-student-scientific-problem[_ngcontent-%COMP%]   .bold[_ngcontent-%COMP%]{font-weight:700;font-size:1rem}.card-student-scientific-problem[_ngcontent-%COMP%]   .card_header[_ngcontent-%COMP%]{background-color:var(--main_color);color:#fff;display:flex;justify-content:space-between;align-items:center;padding:.313rem 1.25rem;font-size:.75rem}.card-student-scientific-problem[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;height:15.625rem;padding:0 1.25rem 1.25rem;color:#4d4d4d;overflow-y:auto;margin-top:1rem}.card-student-scientific-problem[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(en){text-align:left}.card-student-scientific-problem[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(ar){text-align:right}.card-student-scientific-problem[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .body_header[_ngcontent-%COMP%]{background-color:#fff;display:flex;justify-content:space-between;padding-bottom:.5rem;color:#d6d7d8}a[_ngcontent-%COMP%]{cursor:pointer}a.disabled[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}.program_user[_ngcontent-%COMP%]{color:#333;font-size:.875rem}@media (max-width: 48rem){.card-student-scientific-problem[_ngcontent-%COMP%]{height:12.5rem!important}}\"]\n    });\n  }\n  return CardStudentScientificProblemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}