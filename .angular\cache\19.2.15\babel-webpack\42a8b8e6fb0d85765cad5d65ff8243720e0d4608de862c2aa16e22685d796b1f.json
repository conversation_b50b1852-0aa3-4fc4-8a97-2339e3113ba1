{"ast": null, "code": "import { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { TeacherRecitationGroupSelectedComponent } from './teacher-recitation-group-selected/teacher-recitation-group-selected.component';\nimport { TeacherRecitationGroupsComponent } from './teacher-recitation-groups/teacher-recitation-groups.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nfunction TeacherRecitationWrapperComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-add-new-group-teacher-recitation\", 10);\n    i0.ɵɵlistener(\"hideform\", function TeacherRecitationWrapperComponent_div_0_div_6_Template_app_add_new_group_teacher_recitation_hideform_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showAddGroup($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherRecitationWrapperComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-rejected-group-request-form\", 11);\n    i0.ɵɵlistener(\"hideform\", function TeacherRecitationWrapperComponent_div_0_div_7_Template_app_rejected_group_request_form_hideform_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openRejectForm($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rejectedRequestId\", ctx_r1.rejectedRequestId);\n  }\n}\nfunction TeacherRecitationWrapperComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-add-new-student-in-group\", 12);\n    i0.ɵɵlistener(\"hideform\", function TeacherRecitationWrapperComponent_div_0_div_8_Template_app_add_new_student_in_group_hideform_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openAddStudentForm($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"sendBatchDetails\", ctx_r1.sendBatchDetails);\n  }\n}\nfunction TeacherRecitationWrapperComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"app-teacher-recitation-groups\", 5);\n    i0.ɵɵlistener(\"showAddGroup\", function TeacherRecitationWrapperComponent_div_0_Template_app_teacher_recitation_groups_showAddGroup_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddGroup($event));\n    })(\"sendGroupId\", function TeacherRecitationWrapperComponent_div_0_Template_app_teacher_recitation_groups_sendGroupId_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.reciveGroupId($event));\n    })(\"stuCallPhonEvent\", function TeacherRecitationWrapperComponent_div_0_Template_app_teacher_recitation_groups_stuCallPhonEvent_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.stuCallPhon($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"app-teacher-recitation-group-selected\", 7);\n    i0.ɵɵlistener(\"addStudentRequest\", function TeacherRecitationWrapperComponent_div_0_Template_app_teacher_recitation_group_selected_addStudentRequest_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addStudentRequestMethod($event));\n    })(\"rejectedRequestId\", function TeacherRecitationWrapperComponent_div_0_Template_app_teacher_recitation_group_selected_rejectedRequestId_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.reciveRejectedRequestId($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, TeacherRecitationWrapperComponent_div_0_div_6_Template, 2, 0, \"div\", 8)(7, TeacherRecitationWrapperComponent_div_0_div_7_Template, 2, 1, \"div\", 8)(8, TeacherRecitationWrapperComponent_div_0_div_8_Template, 2, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"GroupId\", ctx_r1.GroupId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAddGroupForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showRejectForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAddStudent);\n  }\n}\nfunction TeacherRecitationWrapperComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-group-call\", 13);\n    i0.ɵɵlistener(\"endCallEvent\", function TeacherRecitationWrapperComponent_ng_container_1_Template_app_group_call_endCallEvent_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.endCallLisener());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"groupCallConnector\", ctx_r1.groupCallConnector);\n  }\n}\nexport let TeacherRecitationWrapperComponent = /*#__PURE__*/(() => {\n  class TeacherRecitationWrapperComponent {\n    languageService;\n    translate;\n    lestTeacherComponent;\n    detailsComponent;\n    sendBatchDetails;\n    rejectedRequestId = '';\n    showAddGroupForm = false;\n    showRejectForm = false;\n    showAddStudent = false;\n    GroupId = '';\n    groupCallConnector;\n    isShowCall = false;\n    role = RoleEnum.Teacher;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.APPO_GROUP'));\n    }\n    showAddGroup(event) {\n      this.showAddGroupForm = event;\n      if (this.showAddGroupForm === false) {\n        if (this.lestTeacherComponent) {\n          this.lestTeacherComponent.getTeacherViewtGroupExplanation();\n        }\n      }\n    }\n    reciveGroupId(event) {\n      this.GroupId = event;\n      if (this.detailsComponent) {\n        // this.detailsComponent.GroupId = this.GroupId\n        this.detailsComponent.getDetailsGroupExplanation();\n      }\n    }\n    reciveRejectedRequestId(event) {\n      this.rejectedRequestId = event;\n      this.showRejectForm = true;\n    }\n    openRejectForm(event) {\n      this.showRejectForm = event;\n      if (this.detailsComponent) {\n        this.detailsComponent.GroupId = this.GroupId;\n        this.detailsComponent.getDetailsGroupExplanation();\n      }\n    }\n    openAddStudentForm(event) {\n      this.showAddStudent = event;\n      if (this.detailsComponent) {\n        this.detailsComponent.GroupId = this.GroupId;\n        this.detailsComponent.getDetailsGroupExplanation();\n      }\n    }\n    addStudentRequestMethod(event) {\n      this.showAddStudent = true;\n      this.sendBatchDetails = event;\n    }\n    stuCallPhon(event) {\n      this.groupCallConnector = event;\n      this.isShowCall = true;\n    }\n    endCallLisener() {\n      this.isShowCall = false;\n      this.lestTeacherComponent?.getTeacherViewtGroupExplanation();\n    }\n    static ɵfac = function TeacherRecitationWrapperComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherRecitationWrapperComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherRecitationWrapperComponent,\n      selectors: [[\"app-teacher-recitation-wrapper\"]],\n      viewQuery: function TeacherRecitationWrapperComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TeacherRecitationGroupsComponent, 5);\n          i0.ɵɵviewQuery(TeacherRecitationGroupSelectedComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lestTeacherComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.detailsComponent = _t.first);\n        }\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid px-0 \", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"container-fluid\", \"px-0\"], [1, \"row\"], [1, \"col-lg-4\", \"col-md-4\", \"col-sm-12\"], [3, \"showAddGroup\", \"sendGroupId\", \"stuCallPhonEvent\"], [1, \"col-lg-8\", \"col-md-8\", \"col-sm-12\"], [3, \"addStudentRequest\", \"rejectedRequestId\", \"GroupId\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"hideform\"], [3, \"hideform\", \"rejectedRequestId\"], [3, \"hideform\", \"sendBatchDetails\"], [3, \"endCallEvent\", \"groupCallConnector\"]],\n      template: function TeacherRecitationWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TeacherRecitationWrapperComponent_div_0_Template, 9, 4, \"div\", 0)(1, TeacherRecitationWrapperComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isShowCall);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowCall);\n        }\n      },\n      dependencies: [i3.NgIf],\n      encapsulation: 2\n    });\n  }\n  return TeacherRecitationWrapperComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}