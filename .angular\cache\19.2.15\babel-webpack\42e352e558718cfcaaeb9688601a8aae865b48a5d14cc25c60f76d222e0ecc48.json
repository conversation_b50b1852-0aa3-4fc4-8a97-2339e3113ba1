{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/data/Projects/Mostaneer/huffadh-white-label-app - Copy/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { ExamFormsListComponent } from './exam-forms-list/exam-forms-list.component';\nimport { ProgramAttacheExamTemplatsComponent } from './program-attache-exam-templats/program-attache-exam-templats.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  \"dimmed\": a0\n});\nexport let ProgramExamesComponent = /*#__PURE__*/(() => {\n  class ProgramExamesComponent {\n    examFormsListComponent;\n    examDetailsComponent;\n    progDetailsEvent = new EventEmitter();\n    progDetails;\n    selectedExamFormId = {\n      id: '',\n      arabExamName: '',\n      engExamName: ''\n    };\n    constructor() {}\n    ngOnInit() {}\n    setSelectedExam(event) {\n      // this.selectedExamFormId = {id:event.id,arabExamName:event.arabExamName,engExamName:event.engExamName}; \n      this.examDetailsComponent?.getAttacheExamTemplate(event);\n    }\n    assignExamFormToProgram() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.examFormsListComponent?.assignExamFormToProgram();\n      })();\n    }\n    getProgDetails() {\n      this.progDetailsEvent.emit();\n    }\n    static ɵfac = function ProgramExamesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramExamesComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramExamesComponent,\n      selectors: [[\"app-program-exames\"]],\n      viewQuery: function ProgramExamesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ExamFormsListComponent, 5);\n          i0.ɵɵviewQuery(ProgramAttacheExamTemplatsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.examFormsListComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.examDetailsComponent = _t.first);\n        }\n      },\n      inputs: {\n        progDetails: \"progDetails\"\n      },\n      outputs: {\n        progDetailsEvent: \"progDetailsEvent\"\n      },\n      decls: 10,\n      vars: 7,\n      consts: [[1, \"container-fluid\", 3, \"ngClass\"], [1, \"row\"], [1, \"col-lg-3\", \"col-md-3\", \"col-sm-12\", \"px-0\"], [3, \"selectedExamFormId\", \"progDetailsEvent\", \"progDetails\"], [1, \"col-lg-9\", \"col-md-9\", \"col-sm-12\", \"px-0\"], [1, \"row\", \"justify-content-end\", \"ml-3\", \"mt-1\"], [1, \"save-btn\", 3, \"click\"]],\n      template: function ProgramExamesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-exam-forms-list\", 3);\n          i0.ɵɵlistener(\"selectedExamFormId\", function ProgramExamesComponent_Template_app_exam_forms_list_selectedExamFormId_3_listener($event) {\n            return ctx.setSelectedExam($event);\n          })(\"progDetailsEvent\", function ProgramExamesComponent_Template_app_exam_forms_list_progDetailsEvent_3_listener() {\n            return ctx.getProgDetails();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-program-attache-exam-templats\");\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ProgramExamesComponent_Template_button_click_7_listener() {\n            return ctx.assignExamFormToProgram();\n          });\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, (ctx.progDetails == null ? null : ctx.progDetails.progBaseInfo == null ? null : ctx.progDetails.progBaseInfo.prgPasuDate) != null));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"progDetails\", ctx.progDetails);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 3, \"GENERAL.SAVE\"), \" \");\n        }\n      },\n      dependencies: [i1.NgClass, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}\"]\n    });\n  }\n  return ProgramExamesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}