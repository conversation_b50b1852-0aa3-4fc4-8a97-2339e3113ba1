{"ast": null, "code": "import { ProgramsListComponent } from './programs-list/programs-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nfunction ProgramDetailsComponent_app_programs_tabs_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-programs-tabs\", 7);\n    i0.ɵɵlistener(\"refreshProgListEvent\", function ProgramDetailsComponent_app_programs_tabs_5_Template_app_programs_tabs_refreshProgListEvent_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshProgList());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"programModel\", ctx_r1.programModel);\n  }\n}\nfunction ProgramDetailsComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"span\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"VIEW_PROGRAMS_BASIC_INFO.Please_Choose_Program\"), \" \");\n  }\n}\nexport let ProgramDetailsComponent = /*#__PURE__*/(() => {\n  class ProgramDetailsComponent {\n    languageService;\n    translate;\n    progListChild;\n    showTap = 'USERS';\n    programModel;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('GENERAL.MY_PROGRAM'));\n    }\n    selectedProgramCallBack(event) {\n      this.programModel = event;\n    }\n    refreshProgList() {\n      this.progListChild?.loadProgramsbyAdvancedFilter();\n      this.programModel = undefined;\n    }\n    static ɵfac = function ProgramDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramDetailsComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramDetailsComponent,\n      selectors: [[\"app-program-details\"]],\n      viewQuery: function ProgramDetailsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ProgramsListComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progListChild = _t.first);\n        }\n      },\n      decls: 7,\n      vars: 2,\n      consts: [[1, \"container-fluid\", \"px-0\"], [1, \"row\"], [1, \"col-lg-3\", \"col-md-3\", \"col-sm-12\", \"pl-0\"], [3, \"selectedProgram\"], [1, \"col-lg-9\", \"col-md-9\", \"col-sm-12\"], [3, \"programModel\", \"refreshProgListEvent\", 4, \"ngIf\"], [\"class\", \"row d-flex align-items-center justify-content-center h-100 width-100\", 4, \"ngIf\"], [3, \"refreshProgListEvent\", \"programModel\"], [1, \"row\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"h-100\", \"width-100\"], [1, \"col-12\", \"text-center\", \"mt-30\"], [1, \"No_data\"]],\n      template: function ProgramDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-programs-list\", 3);\n          i0.ɵɵlistener(\"selectedProgram\", function ProgramDetailsComponent_Template_app_programs_list_selectedProgram_3_listener($event) {\n            return ctx.selectedProgramCallBack($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, ProgramDetailsComponent_app_programs_tabs_5_Template, 1, 1, \"app-programs-tabs\", 5)(6, ProgramDetailsComponent_div_6_Template, 5, 3, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.programModel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.programModel);\n        }\n      },\n      dependencies: [i3.NgIf, i2.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n  return ProgramDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}