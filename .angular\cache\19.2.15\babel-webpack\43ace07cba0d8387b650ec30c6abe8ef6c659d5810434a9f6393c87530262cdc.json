{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { RoleManagementViewComponent } from './role-management-view/role-management-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'role-management-view',\n    component: RoleManagementViewComponent\n  }]\n}];\nexport let RoleManagementRoutingModule = /*#__PURE__*/(() => {\n  class RoleManagementRoutingModule {\n    static ɵfac = function RoleManagementRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoleManagementRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RoleManagementRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return RoleManagementRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}