{"ast": null, "code": "import { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AdminStudentTabService = /*#__PURE__*/(() => {\n  class AdminStudentTabService {\n    http;\n    getStudentManagementUrl = environment.baseUrl + 'StudentManagement/get-all-students';\n    getStudentProgramsURL = environment.baseUrl + 'StudentManagement/get-student-programs/';\n    getStudentProgramsStartedURL = environment.baseUrl + 'StudentProgramSubscription/get-student-programs-started/';\n    getStudentProgramDaysURL = environment.baseUrl + 'StudentManagement/get-student-program-batch-days/';\n    getStudentProgramDayTasksURL = environment.baseUrl + 'StudentManagement/get-student-program-batch-tasks/';\n    addStudentToSharedProgramURL = environment.baseUrl + 'StudentManagement/add-student-to-shared-program/';\n    deleteStudentByIdUrl = environment.baseUrl + 'StudentManagement/delete-student/';\n    constructor(http) {\n      this.http = http;\n    }\n    getStudentManagement(model) {\n      return this.http.post(this.getStudentManagementUrl, model);\n    }\n    getStudentPrograms(model) {\n      return this.http.post(this.getStudentProgramsURL, model);\n    }\n    getStudentProgramsStarted(model) {\n      return this.http.post(this.getStudentProgramsStartedURL, model);\n    }\n    getStudentProgramDays(model) {\n      return this.http.post(this.getStudentProgramDaysURL, model);\n    }\n    getStudentProgramDayTasks(model) {\n      return this.http.post(this.getStudentProgramDayTasksURL, model);\n    }\n    addStudentToSharedProgram(model) {\n      return this.http.post(this.addStudentToSharedProgramURL, model);\n    }\n    // get-student-programs-with-batches-filter => without programs dropedout Done  (URL => \n    //   /api/StudentProgramSubscription/get-student-programs\n    deleteStudentById(id) {\n      return this.http.delete(this.deleteStudentByIdUrl + id);\n    }\n    static ɵfac = function AdminStudentTabService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentTabService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminStudentTabService,\n      factory: AdminStudentTabService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AdminStudentTabService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}