{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\n/**\r\n * Language: English.\r\n */\nexport default {\n  time: {\n    month: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n    monthAbbr: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n    dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    dayOfWeekAbbr: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\n  },\n  legend: {\n    selector: {\n      all: 'All',\n      inverse: 'Inv'\n    }\n  },\n  toolbox: {\n    brush: {\n      title: {\n        rect: 'Box Select',\n        polygon: 'Lasso Select',\n        lineX: 'Horizontally Select',\n        lineY: 'Vertically Select',\n        keep: 'Keep Selections',\n        clear: 'Clear Selections'\n      }\n    },\n    dataView: {\n      title: 'Data View',\n      lang: ['Data View', 'Close', 'Refresh']\n    },\n    dataZoom: {\n      title: {\n        zoom: 'Zoom',\n        back: 'Zoom Reset'\n      }\n    },\n    magicType: {\n      title: {\n        line: 'Switch to Line Chart',\n        bar: 'Switch to Bar Chart',\n        stack: 'Stack',\n        tiled: 'Tile'\n      }\n    },\n    restore: {\n      title: 'Restore'\n    },\n    saveAsImage: {\n      title: 'Save as Image',\n      lang: ['Right Click to Save Image']\n    }\n  },\n  series: {\n    typeNames: {\n      pie: 'Pie chart',\n      bar: 'Bar chart',\n      line: 'Line chart',\n      scatter: 'Scatter plot',\n      effectScatter: 'Ripple scatter plot',\n      radar: 'Radar chart',\n      tree: 'Tree',\n      treemap: 'Treemap',\n      boxplot: 'Boxplot',\n      candlestick: 'Candlestick',\n      k: 'K line chart',\n      heatmap: 'Heat map',\n      map: 'Map',\n      parallel: 'Parallel coordinate map',\n      lines: 'Line graph',\n      graph: 'Relationship graph',\n      sankey: 'Sankey diagram',\n      funnel: 'Funnel chart',\n      gauge: 'Gauge',\n      pictorialBar: 'Pictorial bar',\n      themeRiver: 'Theme River Map',\n      sunburst: 'Sunburst',\n      custom: 'Custom chart',\n      chart: 'Chart'\n    }\n  },\n  aria: {\n    general: {\n      withTitle: 'This is a chart about \"{title}\"',\n      withoutTitle: 'This is a chart'\n    },\n    series: {\n      single: {\n        prefix: '',\n        withName: ' with type {seriesType} named {seriesName}.',\n        withoutName: ' with type {seriesType}.'\n      },\n      multiple: {\n        prefix: '. It consists of {seriesCount} series count.',\n        withName: ' The {seriesId} series is a {seriesType} representing {seriesName}.',\n        withoutName: ' The {seriesId} series is a {seriesType}.',\n        separator: {\n          middle: '',\n          end: ''\n        }\n      }\n    },\n    data: {\n      allData: 'The data is as follows: ',\n      partialData: 'The first {displayCnt} items are: ',\n      withName: 'the data for {name} is {value}',\n      withoutName: '{value}',\n      separator: {\n        middle: ', ',\n        end: '. '\n      }\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}