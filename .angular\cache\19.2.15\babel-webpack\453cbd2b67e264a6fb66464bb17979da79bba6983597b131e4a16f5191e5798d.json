{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ConfirmModalComponent } from './components/confirm-modal/confirm-modal.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CustomeCardComponent } from './components/custome-card/custome-card.component';\nimport { MatCardModule } from '@angular/material/card';\nimport { CustomAccordionComponent } from './components/custom-accordion/custom-accordion.component';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { ViewUserProfileCustomComponent } from './components/view-user-profile-custom/view-user-profile-custom.component';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SearchInputComponent } from './components/search-input/search-input.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { TelInputComponent } from './components/tel-input/tel-input.component';\nimport { MiladyHijriCalendarComponent } from './components/milady-hijri-calendar/milady-hijri-calendar.component';\nimport { UsersCounterComponent } from './components/users-counter/users-counter.component';\nimport { KhatmeenStudentsComponent } from './components/khatmeen-students/khatmeen-students.component';\nimport { StudentNumbersComponent } from './components/student-numbers/student-numbers.component';\nimport { StudentsRatingComponent } from './components/students-rating/students-rating.component';\nimport { CardStudentScientificProblemComponent } from './components/card-student-scientific-problem/card-student-scientific-problem.component';\nimport { CardAdminScientificProblemComponent } from './components/card-admin-scientific-problem/card-admin-scientific-problem.component';\nimport { QuestionTemplateComponent } from './components/question-template/question-template.component';\nimport { FormsModule } from '@angular/forms';\nimport { VoiceRecordingComponent } from './components/voice-recording/voice-recording.component';\nimport { ScientificProblemsGridComponent } from './components/scientific-problems-grid/scientific-problems-grid.component';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { NotAuthComponent } from './components/not-auth/not-auth.component';\nimport { NgbModule, NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';\nimport { GroupUsersCardComponent } from './components/group-users-card/group-users-card.component';\nimport { InputSearchListComponent } from './components/input-search-list/input-search-list.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { CardNotifacationsComponent } from './components/card-notifacations/card-notifacations.component';\nimport { ProgramDayTaskHearingComponent } from './components/program-day-tasks/program-day-task-hearing/program-day-task-hearing.component';\nimport { ProgramDayTaskReadExplanationComponent } from './components/program-day-tasks/program-day-task-read-explanation/program-day-task-read-explanation.component';\nimport { ProgramDayTaskMemorizeComponent } from './components/program-day-tasks/program-day-task-memorize/program-day-task-memorize.component';\nimport { ProgramDayTaskRepetitionComponent } from './components/program-day-tasks/program-day-task-repetition/program-day-task-repetition.component';\nimport { ProgramDayTaskLinkingComponent } from './components/program-day-tasks/program-day-task-linking/program-day-task-linking.component';\nimport { ProgramDayTaskDailyTestComponent } from './components/program-day-tasks/program-day-task-daily-test/program-day-task-daily-test.component';\nimport { ProgramDayTaskEncouragementLetterComponent } from './components/program-day-tasks/program-day-task-encouragement-letter/program-day-task-encouragement-letter.component';\nimport { ProgramDayTaskVideoComponent } from './components/program-day-tasks/program-day-task-video/program-day-task-video.component';\nimport { ProgramDayTaskReviewComponent } from './components/program-day-tasks/program-day-task-review/program-day-task-review.component';\nimport { ProgramDayTaskRecitationComponent } from './components/program-day-tasks/program-day-task-recitation/program-day-task-recitation.component';\nimport { ProgramDayTaskRecitationStudentsComponent } from './components/program-day-tasks/program-day-task-recitation-students/program-day-task-recitation-students.component';\nimport { ProgramDayTaskTestPhasedComponent } from './components/program-day-tasks/program-day-task-test-phased/program-day-task-test-phased.component';\nimport { ProgramDayTaskTasmeaComponent } from './components/program-day-tasks/program-day-task-tasmea/program-day-task-tasmea.component';\nimport { CardFeelingsComponent } from './components/card-feelings/card-feelings.component';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { SettingAgeComponent } from './components/setting-conditions/setting-age/setting-age.component';\nimport { SettingLastProgramComponent } from './components/setting-conditions/setting-last-program/setting-last-program.component';\nimport { SettingDegreeLastProgramComponent } from './components/setting-conditions/setting-degree-last-program/setting-degree-last-program.component';\nimport { SettingMaxmumSubscribeComponent } from './components/setting-conditions/setting-maxmum-subscribe/setting-maxmum-subscribe.component';\nimport { SettingPartQraanComponent } from './components/setting-conditions/setting-part-qraan/setting-part-qraan.component';\nimport { CustomConditionsComponent } from './components/setting-conditions/custom-conditions/custom-conditions.component';\nimport { SettingQualificationsComponent } from './components/setting-conditions/setting-qualifications/setting-qualifications.component';\nimport { SettingAcceptComponent } from './components/setting-conditions/setting-accept/setting-accept.component';\nimport { PdfViewerModule } from 'ng2-pdf-viewer';\nimport { StuCardRequestComponent } from './components/stu-card-request/stu-card-request.component';\nimport { TeacherCardRequestComponent } from './components/teacher-card-request/teacher-card-request.component';\nimport { ProgramSubscriptionGridComponent } from './components/program-subscription-grid/program-subscription-grid.component';\nimport { TeacherSystemCardRequestComponent } from './components/teacher-system-card-request/teacher-system-card-request.component';\nimport { TeacherSystemSubscriptionGridComponent } from './components/teacher-system-subscription-grid/teacher-system-subscription-grid.component';\nimport { StudentProgramVacationGridComponent } from './components/student-program-vacation-grid/student-program-vacation-grid.component';\nimport { StudentProgramVacationCardAdminComponent } from './components/student-program-vacation-card-admin/student-program-vacation-card-admin.component';\nimport { TeacherDropOutRequestAdminCardComponent } from './components/teacher-drop-out-request-admin-card/teacher-drop-out-request-admin-card.component';\nimport { TeacherDropOutRequestAdminGridComponent } from './components/teacher-drop-out-request-admin-grid/teacher-drop-out-request-admin-grid.component';\nimport { TeacherDropOutRequestTeacherCardComponent } from './components/teacher-drop-out-request-teacher-card/teacher-drop-out-request-teacher-card.component';\nimport { StudentDropOutRequestAdminCardComponent } from './components/student-drop-out-request-admin-card/student-drop-out-request-admin-card.component';\nimport { StudentDropOutRequestStudentCardComponent } from './components/student-drop-out-request-student-card/student-drop-out-request-student-card.component';\nimport { StudentDropOutGridComponent } from './components/student-drop-out-grid/student-drop-out-grid.component';\nimport { TeacherDetailsViewComponent } from './components/teacher-details-view/teacher-details-view.component';\nimport { StudentDetailsViewComponent } from './components/student-details-view/student-details-view.component';\nimport { FormatTimePipe } from '../core/pipe/format-time.pipe';\nimport { JoinExamComponent } from './components/program-day-tasks/join-exam/join-exam.component';\nimport { TeacherAppointmentRequestsGridComponent } from './components/teacher-appointment-requests-grid/teacher-appointment-requests-grid.component';\nimport { TeacherAppointmentRequestCardComponent } from './components/teacher-appointment-request-card/teacher-appointment-request-card.component';\nimport { TeacherStudentProgramForSubscriptionComponent } from './components/teacher-student-program-for-subscription-card/teacher-student-program-for-subscription.component';\nimport { TeacherStuProgramForSubscriptionGridComponent } from './components/teacher-stu-program-for-subscription-grid/teacher-stu-program-for-subscription-grid.component';\nimport { StudentProgramVacationCardStudentViewComponent } from './components/student-program-vacation-card-student-view/student-program-vacation-card-student-view.component';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { GetAllAvailableTeachersGridComponent } from './components/get-all-available-teachers-grid/get-all-available-teachers-grid.component';\nimport { GetAllAvailableTeachersCardComponent } from './components/get-all-available-teachers-card/get-all-available-teachers-card.component';\nimport { GetAllAvailableStudentGridComponent } from './components/get-all-available-student-grid/get-all-available-student-grid.component';\nimport { GetAllAvailableStudentCardComponent } from './components/get-all-available-student-card/get-all-available-student-card.component';\nimport { ViewExamAnswersComponent } from './components/view-exam-answers/view-exam-answers.component';\nimport { ChatInputSearchListComponent } from './components/chat-input-search-list/chat-input-search-list.component';\nimport { ProgramDayTaskExplanationComponent } from './components/program-day-tasks/program-day-task-explanation/program-day-task-explanation.component';\nimport { JitsiCallIntegComponent } from './components/jitsi-call-integ/jitsi-call-integ.component';\nimport { StudentFreeRecitationGridComponent } from './components/student-free-recitation-grid/student-free-recitation-grid.component';\nimport { StudentScheduleFreeRecitationCardComponent } from './components/student-schedule-free-recitation-card/student-schedule-free-recitation-card.component';\nimport { StudentScheduleFreeRecitationGridComponent } from './components/student-schedule-free-recitation-grid/student-schedule-free-recitation-grid.component';\nimport { StudentFreeRecitationCardsComponent } from './components/student-free-recitation-cards/student-free-recitation-cards.component';\nimport { TeacherScheduleFreeRecitationCardComponent } from './components/teacher-schedule-free-recitation-card/teacher-schedule-free-recitation-card.component';\nimport { TeacherScheduleFreeRecitationGridComponent } from './components/teacher-schedule-free-recitation-grid/teacher-schedule-free-recitation-grid.component';\nimport { JitsiGroupCallIntegComponent } from './components/jitsi-group-call-integ/jitsi-group-call-integ.component';\nimport { UserGroupManagementCardComponent } from './components/user-group-management-card/user-group-management-card.component';\nimport { SharedOwnerBoardCardComponent } from './components/shared-owner-board-card/shared-owner-board-card.component';\nimport { CorruptedFilesRequestsGridComponent } from './components/corrupted-files-requests-grid/corrupted-files-requests-grid.component';\nimport { StudTotalExamTaskDegreeEchartComponent } from './components/admin-dash-board-widgets/stud-total-exam-task-degree-echart/stud-total-exam-task-degree-echart.component';\nimport { DoughnutChartDegreeCountComponent } from './components/admin-dash-board-widgets/doughnut-chart-degree-count/doughnut-chart-degree-count.component';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport { JitsiCallInterviewComponent } from './components/jitsi-call-interview/jitsi-call-interview.component';\nimport { BankAccountCardComponent } from './components/bank-account-card/bank-account-card.component';\nimport { AdminStudentStatisticsCountsChartComponent } from './components/admin-student-statistics-counts-chart/admin-student-statistics-counts-chart.component';\nimport { PrintCertificateGridComponent } from './components/print-certificate/print-certificate-grid/print-certificate-grid.component';\nimport { AdminFreeRecitationRequestsGridComponent } from './components/admin-free-recitation-requests-grid/admin-free-recitation-requests-grid.component';\nimport { ShortNumberFormatPipe } from '../core/pipe/short-number-format.pipe';\nlet SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [ConfirmModalComponent, CustomeCardComponent, CustomAccordionComponent, ViewUserProfileCustomComponent, SearchInputComponent, TelInputComponent, MiladyHijriCalendarComponent, UsersCounterComponent, KhatmeenStudentsComponent, StudentNumbersComponent, StudentsRatingComponent, CardStudentScientificProblemComponent, CardAdminScientificProblemComponent, QuestionTemplateComponent, VoiceRecordingComponent, ScientificProblemsGridComponent, NotAuthComponent, GroupUsersCardComponent, InputSearchListComponent, CardNotifacationsComponent, ProgramDayTaskHearingComponent, ProgramDayTaskReadExplanationComponent, ProgramDayTaskMemorizeComponent, ProgramDayTaskRepetitionComponent, ProgramDayTaskLinkingComponent, ProgramDayTaskDailyTestComponent, ProgramDayTaskEncouragementLetterComponent, ProgramDayTaskVideoComponent, ProgramDayTaskReviewComponent, ProgramDayTaskRecitationComponent, ProgramDayTaskRecitationStudentsComponent, ProgramDayTaskTestPhasedComponent, ProgramDayTaskTasmeaComponent, CardFeelingsComponent, SettingAgeComponent, SettingLastProgramComponent, SettingDegreeLastProgramComponent, SettingMaxmumSubscribeComponent, SettingPartQraanComponent, CustomConditionsComponent, SettingQualificationsComponent, SettingAcceptComponent, StuCardRequestComponent, TeacherCardRequestComponent, ProgramSubscriptionGridComponent, TeacherStudentProgramForSubscriptionComponent, TeacherStuProgramForSubscriptionGridComponent, TeacherSystemCardRequestComponent, TeacherSystemSubscriptionGridComponent, StudentProgramVacationGridComponent, StudentProgramVacationCardAdminComponent, TeacherDropOutRequestAdminCardComponent, TeacherDropOutRequestAdminGridComponent, TeacherDropOutRequestTeacherCardComponent, StudentDropOutRequestAdminCardComponent, StudentDropOutRequestStudentCardComponent, StudentDropOutGridComponent, StudentProgramVacationCardStudentViewComponent, TeacherDetailsViewComponent, StudentDetailsViewComponent, FormatTimePipe,\n  //AdminJoinRequestForTeacherTabAndStudentTabGridComponent,\n  //AdminJoinRequestForTeacherTabAndStudentTabCardComponent,\n  //AdminDropOutRequestForTeacherTabAndStudentTabGridComponent,\n  //AdminVacationRequestForStudentTabGridComponent,\n  //AdminVacationRequestForStudentTabCardComponent,\n  JoinExamComponent, TeacherAppointmentRequestsGridComponent, TeacherAppointmentRequestCardComponent, GetAllAvailableTeachersGridComponent, GetAllAvailableTeachersCardComponent, GetAllAvailableStudentGridComponent, GetAllAvailableStudentCardComponent\n  // Standalone components removed from declarations\n  ],\n  imports: [CommonModule, RouterModule, TranslateModule, MatButtonModule, MatDialogModule, MatCardModule, MatExpansionModule, MatSelectModule, DragDropModule, MatIconModule, FormsModule, MatCheckboxModule, MatRadioModule, MatGridListModule, NgbModule, MatAutocompleteModule, NgbRatingModule, MatTooltipModule, PdfViewerModule, MatDatepickerModule, MatNativeDateModule, NgxEchartsModule.forRoot({\n    echarts: () => import('echarts')\n  })],\n  exports: [MatRadioModule, MatCheckboxModule, MatButtonModule, MatDialogModule, MatCardModule, MatExpansionModule, MatSelectModule, DragDropModule, CustomeCardComponent, CustomAccordionComponent, ViewUserProfileCustomComponent, SearchInputComponent, TelInputComponent, MiladyHijriCalendarComponent, UsersCounterComponent, KhatmeenStudentsComponent, StudentNumbersComponent, StudentsRatingComponent, CardStudentScientificProblemComponent, QuestionTemplateComponent, VoiceRecordingComponent, MatGridListModule, ScientificProblemsGridComponent, CardAdminScientificProblemComponent, NotAuthComponent, GroupUsersCardComponent, InputSearchListComponent, ChatInputSearchListComponent, NgbRatingModule, CardNotifacationsComponent, ProgramDayTaskHearingComponent, NgbModule, ProgramDayTaskReadExplanationComponent, ProgramDayTaskRepetitionComponent, ProgramDayTaskMemorizeComponent, ProgramDayTaskLinkingComponent, ProgramDayTaskVideoComponent, ProgramDayTaskDailyTestComponent, ProgramDayTaskEncouragementLetterComponent, ProgramDayTaskReviewComponent, ProgramDayTaskRecitationComponent, ProgramDayTaskRecitationStudentsComponent, ProgramDayTaskTestPhasedComponent, ProgramDayTaskTasmeaComponent, CardFeelingsComponent, MatTooltipModule, SettingAgeComponent, SettingDegreeLastProgramComponent, SettingLastProgramComponent, SettingQualificationsComponent, SettingMaxmumSubscribeComponent, SettingPartQraanComponent, CustomConditionsComponent, SettingAcceptComponent, StuCardRequestComponent, TeacherCardRequestComponent, TeacherSystemCardRequestComponent, TeacherStuProgramForSubscriptionGridComponent, ProgramSubscriptionGridComponent, TeacherSystemSubscriptionGridComponent, StudentProgramVacationGridComponent, TeacherStudentProgramForSubscriptionComponent, StudentDropOutGridComponent, TeacherDropOutRequestAdminGridComponent, TeacherDetailsViewComponent, StudentDetailsViewComponent, FormatTimePipe, JitsiCallInterviewComponent, JoinExamComponent, TeacherAppointmentRequestsGridComponent, MatDatepickerModule, MatNativeDateModule, GetAllAvailableTeachersGridComponent, GetAllAvailableTeachersCardComponent, GetAllAvailableStudentGridComponent, GetAllAvailableStudentCardComponent, ViewExamAnswersComponent, ProgramDayTaskExplanationComponent, JitsiCallIntegComponent, StudentFreeRecitationGridComponent, StudentScheduleFreeRecitationCardComponent, StudentScheduleFreeRecitationGridComponent, StudentFreeRecitationCardsComponent, TeacherScheduleFreeRecitationCardComponent, TeacherScheduleFreeRecitationGridComponent, JitsiGroupCallIntegComponent, UserGroupManagementCardComponent, CorruptedFilesRequestsGridComponent, SharedOwnerBoardCardComponent, StudTotalExamTaskDegreeEchartComponent, DoughnutChartDegreeCountComponent, NgxEchartsModule, BankAccountCardComponent, AdminStudentStatisticsCountsChartComponent, PrintCertificateGridComponent, AdminFreeRecitationRequestsGridComponent, ShortNumberFormatPipe]\n})], SharedModule);\nexport { SharedModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}