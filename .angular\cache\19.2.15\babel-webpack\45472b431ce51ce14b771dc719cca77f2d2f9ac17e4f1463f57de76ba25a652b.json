{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/role-management/role-management.service\";\nexport let VacationsGuard = /*#__PURE__*/(() => {\n  class VacationsGuard {\n    router;\n    roleService;\n    constructor(router, roleService) {\n      this.router = router;\n      this.roleService = roleService;\n    }\n    canActivate(route, state) {\n      var user = JSON.parse(localStorage.getItem(\"user\") || '{}');\n      if (user.token && this.roleService.isStudent()) {\n        return true;\n      }\n      this.router.navigateByUrl('not-authorized');\n      return false;\n    }\n    static ɵfac = function VacationsGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VacationsGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.RoleManagementService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VacationsGuard,\n      factory: VacationsGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return VacationsGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}