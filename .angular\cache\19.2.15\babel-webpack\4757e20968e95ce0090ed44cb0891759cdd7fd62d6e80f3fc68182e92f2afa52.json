{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { WalkthroughHasUnsavedDataGuard } from 'src/app/core/guards/walkthrough-has-unsaved-data-guard';\nimport { ViewAllWalkThroughComponent } from './components/view-all-walk-through/view-all-walk-through';\nimport { WalkThroughComponent } from './components/view-all-walk-through/walk-through/walk-through';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'create-walk-through',\n    component: WalkThroughComponent\n  }, {\n    path: 'update-walk-through/:id',\n    component: WalkThroughComponent\n  }, {\n    path: 'view-all-walk-through',\n    component: ViewAllWalkThroughComponent,\n    canDeactivate: [WalkthroughHasUnsavedDataGuard]\n  }]\n}];\nexport let WalkthroughRoutingModule = /*#__PURE__*/(() => {\n  class WalkthroughRoutingModule {\n    static ɵfac = function WalkthroughRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WalkthroughRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WalkthroughRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return WalkthroughRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}