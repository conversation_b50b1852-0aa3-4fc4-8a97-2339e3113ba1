{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UpdateTeacherProfileHasUnsavedDataGuard } from 'src/app/core/guards/update-teacher-profile-has-unsaved-data-guard';\nimport { UpdateTeacherProfileComponent } from './components/update-teacher-profile/update-teacher-profile.component';\nimport { ViewTeacherProfileComponent } from './components/view-teacher-profile/view-teacher-profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'update-teacher-profile',\n    component: UpdateTeacherProfileComponent,\n    canDeactivate: [UpdateTeacherProfileHasUnsavedDataGuard]\n  },\n  // { path: 'update-teacher-profile-details/:id', component: UpdateTeacherProfileComponent },\n  {\n    path: 'view-teacher-profile-details',\n    component: ViewTeacherProfileComponent\n  }]\n}];\nexport let TeacherRoutingModule = /*#__PURE__*/(() => {\n  class TeacherRoutingModule {\n    static ɵfac = function TeacherRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TeacherRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return TeacherRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}