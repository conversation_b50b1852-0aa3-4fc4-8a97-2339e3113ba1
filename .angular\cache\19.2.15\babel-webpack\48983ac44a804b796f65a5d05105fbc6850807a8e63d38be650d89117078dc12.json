{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { ProgramDayTasksDetailsComponent } from './program-day-tasks-details/program-day-tasks-details.component';\nimport { ProgramDayTasksComponent } from './program-day-tasks/program-day-tasks.component';\nimport { ProgramDutyDaysComponent } from './program-duty-days/program-duty-days.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../core/services/program-services/program.service\";\nimport * as i2 from \"@angular/common\";\nfunction ProgramDaysComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"app-program-day-tasks-details\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"daysTaskCount\", ctx_r0.daysTaskCount)(\"programDutyDay\", ctx_r0.programDutyDay)(\"taskDetails\", ctx_r0.taskDetails)(\"progamDetails\", ctx_r0.progamDetails);\n  }\n}\nfunction ProgramDaysComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-add-program-day-tasks\", 11);\n    i0.ɵɵlistener(\"closeDayTasks\", function ProgramDaysComponent_div_7_Template_app_add_program_day_tasks_closeDayTasks_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeDayTasks($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"programDutyDay\", ctx_r0.programDutyDay)(\"selectedProgDutyDays\", ctx_r0.selectedProgDutyDays)(\"haveMemorize\", ctx_r0.haveMemorize)(\"isContainFirstDay\", ctx_r0.isContainFirstDay)(\"isSard\", ctx_r0.isSard);\n  }\n}\nexport let ProgramDaysComponent = /*#__PURE__*/(() => {\n  class ProgramDaysComponent {\n    programService;\n    progDayTaskChild;\n    progDayTaskDetailsChild;\n    // @ViewChild(AddProgramDayTasksComponent) addProgDayTaskCompChild:AddProgramDayTasksComponent | undefined;\n    progDayChild;\n    refreshProgDetails = new EventEmitter();\n    progDetails = {};\n    programDutyDay;\n    taskDetails;\n    progamDetails = {};\n    showAddDayTasksForm = false;\n    dataOfProgDutyDay;\n    progDutyDayModel;\n    selectedProgDutyDays = [];\n    haveMemorize = false;\n    daysTaskCount;\n    isSard = false;\n    isContainFirstDay = false;\n    constructor(programService) {\n      this.programService = programService;\n    }\n    ngOnInit() {\n      this.daysTaskCount = this.progDetails.dayCount?.daysTaskCount;\n      // this.DetailsOfProgram = this.getProgramDetails || this.DetailsOfProgram;\n      this.refreshProgDetails.emit();\n    }\n    progDutyDayEventCallBk(event) {\n      if (this.progDayTaskChild && event) {\n        this.progDayTaskChild.programDutyDay = event;\n        this.progDayTaskChild?.getProgramDutyDays();\n      }\n      this.programDutyDay = event;\n      //this.selectedProgDutyDays = [];\n    }\n    progDutyDaysCheckBoxCallBK(event) {\n      if (this.selectedProgDutyDays.includes(event)) {\n        let it = this.selectedProgDutyDays.filter(i => i.id === event.id)[0];\n        const ind = this.selectedProgDutyDays.indexOf(it);\n        if (ind > -1) {\n          this.selectedProgDutyDays.splice(ind, 1);\n        }\n      } else {\n        this.selectedProgDutyDays.push(event);\n      }\n      //update prog duty day object to undefined as we dealt with group\n      this.programDutyDay = undefined;\n      this.isContainFirstDay = this.selectedProgDutyDays.some(x => x.order == 1);\n    }\n    openAddDayTasks(event) {\n      this.showAddDayTasksForm = event;\n    }\n    closeDayTasks(event) {\n      this.showAddDayTasksForm = event;\n      this.showAddDayTasksForm = false;\n      this.progDayChild?.getProgramDays();\n      //this.refreshProgDetails.emit();\n      this.selectedProgDutyDays = [];\n      // this.progDutyDayEventCallBk(this.programDutyDay);\n      // this.userScientificProbChild?.getScientificProblemByUserId();\n    }\n    refreshProgramDays(event) {\n      this.progDayChild?.getProgramDays();\n    }\n    openAddDayTasksForm($event) {\n      this.haveMemorize = $event;\n      this.showAddDayTasksForm = true;\n      this.isSard = this.progamDetails.progBaseInfo?.prgIsSard;\n    }\n    getProgramDutyDay() {\n      this.dataOfProgDutyDay = this.programDutyDay;\n    }\n    sendTaskIdToProgDayTaskDetails(item) {\n      this.taskDetails = item;\n      this.progamDetails = this.progDetails;\n    }\n    static ɵfac = function ProgramDaysComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramDaysComponent)(i0.ɵɵdirectiveInject(i1.ProgramService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramDaysComponent,\n      selectors: [[\"app-program-days\"]],\n      viewQuery: function ProgramDaysComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ProgramDayTasksComponent, 5);\n          i0.ɵɵviewQuery(ProgramDayTasksDetailsComponent, 5);\n          i0.ɵɵviewQuery(ProgramDutyDaysComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progDayTaskChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progDayTaskDetailsChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progDayChild = _t.first);\n        }\n      },\n      inputs: {\n        progDetails: \"progDetails\"\n      },\n      outputs: {\n        refreshProgDetails: \"refreshProgDetails\"\n      },\n      decls: 8,\n      vars: 7,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-3\", \"px-0\"], [3, \"progDutyDayEvent\", \"progDutyDaysCheckBoxEvent\", \"progDaysList\", \"programDetails\"], [1, \"col-3\"], [3, \"openAddDayTasks\", \"taskDetailsEvent\", \"refreshProgramDays\", \"daysTaskCount\", \"programDetails\", \"programDutyDay\"], [\"class\", \"col-6 px-0\", 4, \"ngIf\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"col-6\", \"px-0\"], [3, \"daysTaskCount\", \"programDutyDay\", \"taskDetails\", \"progamDetails\"], [1, \"overlay\"], [3, \"closeDayTasks\", \"programDutyDay\", \"selectedProgDutyDays\", \"haveMemorize\", \"isContainFirstDay\", \"isSard\"]],\n      template: function ProgramDaysComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-program-duty-days\", 3);\n          i0.ɵɵlistener(\"progDutyDayEvent\", function ProgramDaysComponent_Template_app_program_duty_days_progDutyDayEvent_3_listener($event) {\n            return ctx.progDutyDayEventCallBk($event);\n          })(\"progDutyDaysCheckBoxEvent\", function ProgramDaysComponent_Template_app_program_duty_days_progDutyDaysCheckBoxEvent_3_listener($event) {\n            return ctx.progDutyDaysCheckBoxCallBK($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"app-program-day-tasks\", 5);\n          i0.ɵɵlistener(\"openAddDayTasks\", function ProgramDaysComponent_Template_app_program_day_tasks_openAddDayTasks_5_listener($event) {\n            return ctx.openAddDayTasksForm($event);\n          })(\"taskDetailsEvent\", function ProgramDaysComponent_Template_app_program_day_tasks_taskDetailsEvent_5_listener($event) {\n            return ctx.sendTaskIdToProgDayTaskDetails($event);\n          })(\"refreshProgramDays\", function ProgramDaysComponent_Template_app_program_day_tasks_refreshProgramDays_5_listener($event) {\n            return ctx.refreshProgramDays($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ProgramDaysComponent_div_6_Template, 2, 4, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, ProgramDaysComponent_div_7_Template, 2, 5, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"progDaysList\", ctx.progDetails == null ? null : ctx.progDetails.progDays)(\"programDetails\", ctx.progamDetails);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"daysTaskCount\", ctx.daysTaskCount)(\"programDetails\", ctx.progDetails)(\"programDutyDay\", ctx.programDutyDay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.taskDetails);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddDayTasksForm);\n        }\n      },\n      dependencies: [i2.NgIf],\n      encapsulation: 2\n    });\n  }\n  return ProgramDaysComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}