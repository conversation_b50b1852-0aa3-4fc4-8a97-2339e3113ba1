{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-services/program-notification.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"src/app/core/services/lookup-services/lookup.service\";\nimport * as i6 from \"@angular/common\";\nfunction ProgramNotificationViewComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"GENERAL.NO_DATA\"), \"\");\n  }\n}\nfunction ProgramNotificationViewComponent_ng_container_14_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10)(2, \"app-card-notifacations\", 11);\n    i0.ɵɵlistener(\"deleteCardNotify\", function ProgramNotificationViewComponent_ng_container_14_ng_container_1_Template_app_card_notifacations_deleteCardNotify_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteCardNotify($event));\n    })(\"editCardNotify\", function ProgramNotificationViewComponent_ng_container_14_ng_container_1_Template_app_card_notifacations_editCardNotify_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editNotificationCard($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"notificationsCardDetails\", item_r3);\n  }\n}\nfunction ProgramNotificationViewComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProgramNotificationViewComponent_ng_container_14_ng_container_1_Template, 3, 1, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.notificationsCardList);\n  }\n}\nexport let ProgramNotificationViewComponent = /*#__PURE__*/(() => {\n  class ProgramNotificationViewComponent {\n    notificationService;\n    translate;\n    dialog;\n    alertify;\n    lookupService;\n    collectionOfLookup = {};\n    listOfLookup = ['PROG_NOTIF_TYPES'];\n    notificationDetails = {};\n    resultMessage = {};\n    openNotifyfrom = new EventEmitter();\n    progId = '';\n    // @Output() editCardNotify = new EventEmitter<IProgramNotificationDetails>();\n    constructor(notificationService, translate, dialog, alertify, lookupService) {\n      this.notificationService = notificationService;\n      this.translate = translate;\n      this.dialog = dialog;\n      this.alertify = alertify;\n      this.lookupService = lookupService;\n    }\n    ngOnInit() {\n      this.getLookupByKey();\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookup).subscribe(res => {\n        if (res.isSuccess) {\n          this.collectionOfLookup = res.data;\n          this.getAllNotifications();\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    notificationsCardList = [];\n    // {\n    //   notifyName: 'تنبيه واحد ',\n    //   no: 5,\n    //   notifyType: 'تنبيه رقم 1',\n    //   msgAr: 'ىىىى حخهلث لث ع ث0ع0عث  ثع سعبحح حع بحصبح  ',\n    //   msgEn: ' Lorem Ipsum is simply dummy text of the printing and typesetting industry Lorem Ipsum is simply dummy text of the printing and typesetting industry '\n    // },\n    // {\n    //   notifyName: 'تنبيه واحد ',\n    //   no: 5,\n    //   notifyType: 'تنبيه رقم 1',\n    //   msgAr: 'ىىىى حخهلث لث ع ث0ع0عث  ثع سعبحح حع بحصبح  ',\n    //   msgEn: ' Lorem Ipsum is simply dummy text of the printing and typesetting industry Lorem Ipsum is simply dummy text of the printing and typesetting industry '\n    // }\n    // ,\n    // {\n    //   notifyName: 'تنبيه واحد ',\n    //   no: 5,\n    //   notifyType: 'تنبيه رقم 1',\n    //   msgAr: 'ىىىى حخهلث لث ع ث0ع0عث  ثع سعبحح حع بحصبح  ',\n    //   msgEn: ' Lorem Ipsum is simply dummy text of the printing and typesetting industry Lorem Ipsum is simply dummy text of the printing and typesetting industry '\n    // }\n    // notificationsCardList: IProgramNotificationModel[] =[]\n    getAllNotifications() {\n      this.notificationService.getAllNotifications(this.progId || '').subscribe(res => {\n        this.notificationsCardList = res.data;\n        this.notificationsCardList.forEach(item => {\n          item.notifyTypeLookup = this.collectionOfLookup.PROG_NOTIF_TYPES?.filter(i => i.id === item.notifyType)[0];\n        });\n        if (res.isSuccess) {\n          this.resultMessage = {\n            message: res.message || \"\",\n            type: BaseConstantModel.SUCCESS_TYPE\n          };\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    AddProgram() {\n      this.openNotifyfrom.emit(undefined);\n    }\n    editNotificationCard(event) {\n      this.openNotifyfrom.emit(event);\n    }\n    deleteCardNotify(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this notification\" : \"هل متأكد من حذف هذا التنبيه \";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete notification' : 'حذف  التنبيه', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.notificationService.deleteNotification(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getAllNotifications();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    static ɵfac = function ProgramNotificationViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramNotificationViewComponent)(i0.ɵɵdirectiveInject(i1.ProgramNotificationService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.AlertifyService), i0.ɵɵdirectiveInject(i5.LookupService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramNotificationViewComponent,\n      selectors: [[\"app-program-notification-view\"]],\n      inputs: {\n        progId: \"progId\"\n      },\n      outputs: {\n        openNotifyfrom: \"openNotifyfrom\"\n      },\n      decls: 15,\n      vars: 8,\n      consts: [[1, \"tab_page\", \"notifacation_page\"], [1, \"pt-3\"], [1, \"row\"], [1, \"col-md-12\", \"container_haeder\", \"mb-2\"], [1, \"haeder\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"col-md-12\", \"max_h\"], [4, \"ngIf\"], [1, \"col-12\", \"text-center\", \"bold\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-4\", \"mb-3\"], [3, \"deleteCardNotify\", \"editCardNotify\", \"notificationsCardDetails\"]],\n      template: function ProgramNotificationViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ProgramNotificationViewComponent_Template_button_click_8_listener() {\n            return ctx.AddProgram();\n          });\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 2);\n          i0.ɵɵtemplate(13, ProgramNotificationViewComponent_ng_container_13_Template, 5, 3, \"ng-container\", 7)(14, ProgramNotificationViewComponent_ng_container_14_Template, 2, 1, \"ng-container\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"NOTIFICATIONS.NOTIFICATIONS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 6, \"NOTIFICATIONS.ADD_PROGRAM\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.notificationsCardList || ctx.notificationsCardList.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notificationsCardList);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i2.TranslatePipe],\n      styles: [\".notifacation_page.tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;margin-top:1rem;border-radius:.313rem;padding:1rem}.notifacation_page[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:65vh;max-height:65vh;overflow-y:auto}.notifacation_page[_ngcontent-%COMP%]   .container_haeder[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.notifacation_page[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;padding:.5rem;border:none;color:#fff;display:block}.notifacation_page[_ngcontent-%COMP%]   .haeder[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}\"]\n    });\n  }\n  return ProgramNotificationViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}