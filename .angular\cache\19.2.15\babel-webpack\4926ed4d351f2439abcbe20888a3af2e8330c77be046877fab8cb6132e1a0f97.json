{"ast": null, "code": "import { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ProgramDaysComponent } from './program-days/program-days.component';\nimport { ProgramExamesComponent } from './program-exames/program-exames.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/program-services/program.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ngx-translate/core\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction AddProgramComponent_div_0_app_program_basic_info_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-program-basic-info\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progBasicInfoDetails\", ctx_r1.programDetails.progBaseInfo);\n  }\n}\nfunction AddProgramComponent_div_0_app_program_conditions_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-program-conditions\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progId\", ctx_r1.programDetails == null ? null : ctx_r1.programDetails.progBaseInfo == null ? null : ctx_r1.programDetails.progBaseInfo.id)(\"progDetails\", ctx_r1.programDetails);\n  }\n}\nfunction AddProgramComponent_div_0_app_program_days_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-program-days\", 13);\n    i0.ɵɵlistener(\"refreshProgDetails\", function AddProgramComponent_div_0_app_program_days_21_Template_app_program_days_refreshProgDetails_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getProgramDetails());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progDetails\", ctx_r1.programDetails);\n  }\n}\nfunction AddProgramComponent_div_0_app_program_exames_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-program-exames\", 14);\n    i0.ɵɵlistener(\"progDetailsEvent\", function AddProgramComponent_div_0_app_program_exames_22_Template_app_program_exames_progDetailsEvent_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getProgramDetails());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progDetails\", ctx_r1.programDetails);\n  }\n}\nfunction AddProgramComponent_div_0_app_program_notifacations_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-program-notifacations\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"progId\", ctx_r1.programDetails == null ? null : ctx_r1.programDetails.progBaseInfo == null ? null : ctx_r1.programDetails.progBaseInfo.id);\n  }\n}\nfunction AddProgramComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_0_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"BASEINFO\");\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_0_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"CANDITION\");\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_0_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"JOINEXAME\");\n    });\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_0_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"DAYS\");\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_0_Template_div_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"NOTIFY\");\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 5);\n    i0.ɵɵtemplate(19, AddProgramComponent_div_0_app_program_basic_info_19_Template, 1, 1, \"app-program-basic-info\", 6)(20, AddProgramComponent_div_0_app_program_conditions_20_Template, 1, 2, \"app-program-conditions\", 7)(21, AddProgramComponent_div_0_app_program_days_21_Template, 1, 1, \"app-program-days\", 8)(22, AddProgramComponent_div_0_app_program_exames_22_Template, 1, 1, \"app-program-exames\", 9)(23, AddProgramComponent_div_0_app_program_notifacations_23_Template, 1, 1, \"app-program-notifacations\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ctx_r1.showTap == \"BASEINFO\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 15, \"NOTIFICATIONS.BASICiNFO\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx_r1.showTap == \"CANDITION\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 17, \"NOTIFICATIONS.PROGRAM_CONDITIONS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx_r1.showTap == \"JOINEXAME\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 19, \"NOTIFICATIONS.PROGRAM_JOINEXAME\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(31, _c0, ctx_r1.showTap == \"DAYS\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 21, \"NOTIFICATIONS.PROGRAM_DAYS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx_r1.showTap == \"NOTIFY\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 23, \"NOTIFICATIONS.PROGRAM_NOTIFICATIONS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"BASEINFO\" && ctx_r1.programDetails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"CANDITION\" && ctx_r1.programDetails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"DAYS\" && ctx_r1.programDetails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"JOINEXAME\" && ctx_r1.programDetails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"NOTIFY\" && ctx_r1.programDetails);\n  }\n}\nfunction AddProgramComponent_div_1_app_program_basic_info_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-program-basic-info\");\n  }\n}\nfunction AddProgramComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function AddProgramComponent_div_1_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTap = \"BASEINFO\");\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n    i0.ɵɵtemplate(7, AddProgramComponent_div_1_app_program_basic_info_7_Template, 1, 0, \"app-program-basic-info\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r1.showTap == \"BASEINFO\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, \"NOTIFICATIONS.BASICiNFO\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == \"BASEINFO\");\n  }\n}\nexport let AddProgramComponent = /*#__PURE__*/(() => {\n  class AddProgramComponent {\n    route;\n    router;\n    programService;\n    progDaysCompChild;\n    progExamesCompChild;\n    showTap = 'BASEINFO';\n    programDetails;\n    resMessage = {};\n    programId;\n    // @Input() getProgramDetails: IProgramDetailsModel | undefined\n    constructor(route, router, programService) {\n      this.route = route;\n      this.router = router;\n      this.programService = programService;\n    }\n    ngOnInit() {\n      this.programId = this.route.snapshot.params.id;\n      if (this.programId) this.getProgramDetails();\n    }\n    getProgramDetails() {\n      this.programService.getProgramDetails(this.programId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.programDetails = res.data;\n          if (this.programDetails?.progBaseInfo?.prgPubliDate && this.programDetails?.progBaseInfo?.prgIsPubli) {\n            this.router.navigate([\"/program\"]);\n          }\n          if (this.progDaysCompChild && this.progDaysCompChild.progDetails) {\n            this.progDaysCompChild.progDetails = this.programDetails;\n            if (this.progDaysCompChild?.programDutyDay) {\n              this.progDaysCompChild.progDutyDayEventCallBk(this.progDaysCompChild?.programDutyDay);\n            } else if (this.progDaysCompChild.selectedProgDutyDays.length > 0) {\n              this.progDaysCompChild.programDutyDay = this.progDaysCompChild?.selectedProgDutyDays[this.progDaysCompChild?.selectedProgDutyDays.length - 1];\n              this.progDaysCompChild.progDutyDayEventCallBk(this.progDaysCompChild.programDutyDay);\n            }\n          }\n          if (this.progExamesCompChild && this.progExamesCompChild.progDetails) {\n            this.progExamesCompChild.progDetails = this.programDetails;\n            if (this.progExamesCompChild?.examFormsListComponent && this.progExamesCompChild?.examFormsListComponent.progDetails) {\n              this.progExamesCompChild.examFormsListComponent.progDetails = this.programDetails;\n              this.progExamesCompChild.examFormsListComponent.getExamForms();\n              this.progExamesCompChild.examFormsListComponent.mapProgExams();\n            }\n          }\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function AddProgramComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddProgramComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProgramService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProgramComponent,\n      selectors: [[\"app-add-program\"]],\n      viewQuery: function AddProgramComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ProgramDaysComponent, 5);\n          i0.ɵɵviewQuery(ProgramExamesComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progDaysCompChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progExamesCompChild = _t.first);\n        }\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid\", 4, \"ngIf\"], [1, \"container-fluid\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-0\"], [3, \"progBasicInfoDetails\", 4, \"ngIf\"], [3, \"progId\", \"progDetails\", 4, \"ngIf\"], [3, \"progDetails\", \"refreshProgDetails\", 4, \"ngIf\"], [3, \"progDetails\", \"progDetailsEvent\", 4, \"ngIf\"], [3, \"progId\", 4, \"ngIf\"], [3, \"progBasicInfoDetails\"], [3, \"progId\", \"progDetails\"], [3, \"refreshProgDetails\", \"progDetails\"], [3, \"progDetailsEvent\", \"progDetails\"], [3, \"progId\"], [4, \"ngIf\"]],\n      template: function AddProgramComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AddProgramComponent_div_0_Template, 24, 35, \"div\", 0)(1, AddProgramComponent_div_1_Template, 8, 7, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.programId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.programId);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i4.TranslatePipe],\n      styles: [\".group_header[_ngcontent-%COMP%]{margin-top:1.5%;border-bottom:.063rem solid var(--second_color)}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#fff;border:.063rem solid #e2e2e2;font-size:.9rem;color:var(--main_color);text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0;font-weight:700;box-shadow:0 .188rem 1rem #fbfbfb}.group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}\"]\n    });\n  }\n  return AddProgramComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}