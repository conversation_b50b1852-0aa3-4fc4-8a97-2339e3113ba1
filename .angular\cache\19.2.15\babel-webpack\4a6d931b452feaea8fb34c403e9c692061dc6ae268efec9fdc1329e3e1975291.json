{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { StudentProgramSubscriptionStatusEnum } from 'src/app/core/enums/subscriptionStatusEnum/student-program-subscription-status-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/student-program-subscription-services/student-program-subscription-services.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-program-subscription-grid\", 11);\n    i0.ɵɵlistener(\"itemStuReq\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_itemStuReq_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejecteStuRequestMethod($event));\n    })(\"acceptStuReq\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_acceptStuReq_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptStuReq($event));\n    })(\"acceptAllStudentProgramSubscriptionCheched\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_acceptAllStudentProgramSubscriptionCheched_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllStudentProgramSubscriptionCheched());\n    })(\"studentFilterEvent\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_studentFilterEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.stuPendingChangePage($event));\n    })(\"studentIdFormGrid\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_studentIdFormGrid_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    })(\"viewExamEvent\", function StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template_app_program_subscription_grid_viewExamEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewExam($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"studentItems\", ctx_r1.studProgsSubsItems)(\"typeEnum\", ctx_r1.statusEnum.Pending)(\"totalCount\", ctx_r1.totalCount)(\"studentFilterRequestModel\", ctx_r1.filter);\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_18_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StuTabRequestComponent_div_0_ng_container_18_ng_container_1_Template, 2, 4, \"ng-container\", 2)(2, StuTabRequestComponent_div_0_ng_container_18_ng_container_2_Template, 4, 3, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studProgsSubsItems || ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length == 0);\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_19_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-program-subscription-grid\", 13);\n    i0.ɵɵlistener(\"studentFilterEvent\", function StuTabRequestComponent_div_0_ng_container_19_ng_container_1_Template_app_program_subscription_grid_studentFilterEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.stuAcceptChangePage($event));\n    })(\"studentIdFormGrid\", function StuTabRequestComponent_div_0_ng_container_19_ng_container_1_Template_app_program_subscription_grid_studentIdFormGrid_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    })(\"viewExamEvent\", function StuTabRequestComponent_div_0_ng_container_19_ng_container_1_Template_app_program_subscription_grid_viewExamEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewExam($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"typeEnum\", ctx_r1.statusEnum.Accept)(\"studentItems\", ctx_r1.studProgsSubsItems)(\"totalCount\", ctx_r1.totalCount)(\"studentFilterRequestModel\", ctx_r1.filter);\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_19_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StuTabRequestComponent_div_0_ng_container_19_ng_container_1_Template, 2, 4, \"ng-container\", 2)(2, StuTabRequestComponent_div_0_ng_container_19_ng_container_2_Template, 4, 3, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studProgsSubsItems || ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length == 0);\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-program-subscription-grid\", 13);\n    i0.ɵɵlistener(\"studentFilterEvent\", function StuTabRequestComponent_div_0_ng_container_20_ng_container_1_Template_app_program_subscription_grid_studentFilterEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.stuRejectedChangePage($event));\n    })(\"studentIdFormGrid\", function StuTabRequestComponent_div_0_ng_container_20_ng_container_1_Template_app_program_subscription_grid_studentIdFormGrid_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    })(\"viewExamEvent\", function StuTabRequestComponent_div_0_ng_container_20_ng_container_1_Template_app_program_subscription_grid_viewExamEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewExam($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"typeEnum\", ctx_r1.statusEnum.Rejected)(\"studentItems\", ctx_r1.studProgsSubsItems)(\"totalCount\", ctx_r1.totalCount)(\"studentFilterRequestModel\", ctx_r1.filter);\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_20_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction StuTabRequestComponent_div_0_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StuTabRequestComponent_div_0_ng_container_20_ng_container_1_Template, 2, 4, \"ng-container\", 2)(2, StuTabRequestComponent_div_0_ng_container_20_ng_container_2_Template, 4, 3, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studProgsSubsItems || ctx_r1.studProgsSubsItems && ctx_r1.studProgsSubsItems.length == 0);\n  }\n}\nfunction StuTabRequestComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"app-search-input\", 5);\n    i0.ɵɵlistener(\"searchTerm\", function StuTabRequestComponent_div_0_Template_app_search_input_searchTerm_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_0_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAvancedSearch());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_0_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPendingChange());\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_0_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptChange());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_0_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRejectedChange());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 9)(17, \"div\", 10);\n    i0.ɵɵtemplate(18, StuTabRequestComponent_div_0_ng_container_18_Template, 3, 2, \"ng-container\", 2)(19, StuTabRequestComponent_div_0_ng_container_19_Template, 3, 2, \"ng-container\", 2)(20, StuTabRequestComponent_div_0_ng_container_20_Template, 3, 2, \"ng-container\", 2);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"searchKey\", ctx_r1.filter.usrName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 11, \"STUDENT_SUBSCRIBERS.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Pending));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 13, \"STUDENT_SUBSCRIBERS.NEW_JOIN_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Accept));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 15, \"STUDENT_SUBSCRIBERS.ACCEPTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Rejected));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 17, \"STUDENT_SUBSCRIBERS.REJECTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Accept);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Rejected);\n  }\n}\nfunction StuTabRequestComponent_app_student_details_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-student-details-view\", 14);\n    i0.ɵɵlistener(\"hideUserDetails\", function StuTabRequestComponent_app_student_details_view_1_Template_app_student_details_view_hideUserDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideUserDetailsView($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resiveUserId\", ctx_r1.sendUserID);\n  }\n}\nfunction StuTabRequestComponent_div_2_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_2_div_2_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptStuReq(ctx_r1.studentExamAnswerResponse.stuProgSub));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function StuTabRequestComponent_div_2_div_2_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejecteStuRequestMethod(ctx_r1.studentExamAnswerResponse.stuProgSub));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"STUDENT_SUBSCRIBERS.ACCEPT\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"STUDENT_SUBSCRIBERS.REJECT\"), \" \");\n  }\n}\nfunction StuTabRequestComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtemplate(2, StuTabRequestComponent_div_2_div_2_div_2_Template, 7, 6, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentExamAnswerResponse && ctx_r1.studentExamAnswerResponse.stuProgSub);\n  }\n}\nfunction StuTabRequestComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-view-exam-answers\", 15);\n    i0.ɵɵlistener(\"hideViewExamEvent\", function StuTabRequestComponent_div_2_Template_app_view_exam_answers_hideViewExamEvent_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideViewExam());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, StuTabRequestComponent_div_2_div_2_Template, 3, 1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"studentExamAnswer\", ctx_r1.studentExamAnswerResponse);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n  }\n}\nexport let StuTabRequestComponent = /*#__PURE__*/(() => {\n  class StuTabRequestComponent {\n    progSubsService;\n    translate;\n    alertify;\n    itemStuReq = new EventEmitter();\n    openAdvancedSearch = new EventEmitter();\n    closePopup = new EventEmitter();\n    // @Output() closeAdvancedSearch = new EventEmitter<IStudentSubscriptionFilterRequestModel>();\n    advancedSearchObject = new EventEmitter();\n    filter = {\n      statusNum: StudentProgramSubscriptionStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    typeEnum = StudentProgramSubscriptionStatusEnum.Pending;\n    resultMessage = {};\n    showTap = StudentProgramSubscriptionStatusEnum.Pending;\n    statusEnum = StudentProgramSubscriptionStatusEnum;\n    studProgsSubsItems = [];\n    totalCount = 0;\n    sendUserID;\n    showUserDetailsView = false;\n    showViewExam = false;\n    studentExamAnswerResponse;\n    constructor(progSubsService, translate, alertify) {\n      this.progSubsService = progSubsService;\n      this.translate = translate;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.onPendingChange();\n    }\n    sendUserIDEvent(event) {\n      this.sendUserID = event;\n      this.showUserDetailsView = true;\n    }\n    hideUserDetailsView(event) {\n      this.showUserDetailsView = event;\n    }\n    getStudentProgramSubscriptionsFilter() {\n      this.progSubsService.getStudentsSubscriptionsFilterAdminView(this.filter).subscribe(res => {\n        if (res.isSuccess) {\n          this.studProgsSubsItems = res.data;\n          this.showViewExam = false;\n          // this.studProgsSubsItems?.forEach(function (item) {\n          //   item.requestDate = item.requestDate ? new Date(item.requestDate).toDateString(): '';\n          // });\n          this.totalCount = res.count ? res.count : 0;\n          if (this.filter.skip > 0 && (!this.studProgsSubsItems || this.studProgsSubsItems.length === 0)) {\n            this.filter.page -= 1;\n            this.filter.skip = (this.filter.page - 1) * this.filter.take;\n            this.getStudentProgramSubscriptionsFilter();\n          }\n        } else {}\n      }, error => {\n        //logging\n      });\n    }\n    // advancedSearchRequest() {\n    //   this.advancedSearchObject.emit(this.filter)\n    // }\n    onPendingChange() {\n      this.showTap = StudentProgramSubscriptionStatusEnum.Pending;\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Pending;\n      //this.clearfilterByText();\n      // this.advancedSearchRequest()\n      this.closeAvancedSearch();\n      this.filter.sortField = 'requestdate';\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    onAcceptChange() {\n      this.showTap = StudentProgramSubscriptionStatusEnum.Accept;\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Accept;\n      //this.clearfilterByText();\n      // this.advancedSearchRequest()\n      this.closeAvancedSearch();\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    onRejectedChange() {\n      this.showTap = StudentProgramSubscriptionStatusEnum.Rejected;\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Rejected;\n      //this.clearfilterByText();\n      // this.advancedSearchRequest()\n      this.closeAvancedSearch();\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    rejecteStuRequestMethod(event) {\n      this.itemStuReq.emit(event);\n    }\n    ids = [];\n    acceptStuReq(stuModel) {\n      this.ids = [];\n      this.ids?.push(stuModel.id || '');\n      this.progSubsService.studentProgramSubscriptionsAcceptance(this.ids || []).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentProgramSubscriptionsFilter();\n          this.showViewExam = false;\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    acceptAllStudentProgramSubscriptionCheched() {\n      this.ids = [];\n      this.ids = this.studProgsSubsItems?.filter(i => i.checked).map(a => a.id || '');\n      this.progSubsService.studentProgramSubscriptionsAcceptance(this.ids).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getStudentProgramSubscriptionsFilter();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    stuPendingChangePage(event) {\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Pending;\n      this.filter = event;\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    stuAcceptChangePage(event) {\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Accept;\n      this.filter = event;\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    stuRejectedChangePage(event) {\n      this.filter.statusNum = StudentProgramSubscriptionStatusEnum.Rejected;\n      this.filter = event;\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    filterByText(searchKey) {\n      this.filter.usrName = searchKey;\n      this.getStudentProgramSubscriptionsFilter();\n    }\n    //clearfilterByText() {\n    //  this.filter.progId = '';\n    //}\n    openAvancedSearch() {\n      this.openAdvancedSearch.emit(this.filter);\n    }\n    closeAvancedSearch() {\n      this.filter.usrName = '';\n      this.filter.progId = '';\n      this.filter.numberRequest = undefined;\n      this.filter.fromDate = undefined;\n      this.filter.toDate = undefined;\n      this.filter.skip = 0;\n      this.filter.take = 9;\n      this.filter.page = 1;\n      this.filter.sortField = '';\n      this.closePopup.emit(); // as per issue number 3250\n      // this.filter = { skip: 0, take: 9, sortField: '', sortOrder: 1, page: 1 }\n      // this.closeAdvancedSearch.emit(this.filter)\n    }\n    viewExam(event) {\n      this.showViewExam = true;\n      this.studentExamAnswerResponse = {\n        stuProgSub: {},\n        studentExamAnswer: {}\n      };\n      this.studentExamAnswerResponse.stuProgSub = event;\n      this.getStudentExamAnswer(event.id || \"\");\n    }\n    getStudentExamAnswer(subsId) {\n      this.progSubsService.getStudentExamAnswer(subsId).subscribe(res => {\n        if (res.isSuccess) {\n          if (this.studentExamAnswerResponse) {\n            this.studentExamAnswerResponse.studentExamAnswer = res.data;\n            if (this.studentExamAnswerResponse.studentExamAnswer) this.studentExamAnswerResponse.studentExamAnswer.answList = res.data.answ ? JSON.parse(res.data.answ) : [];\n          }\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {});\n    }\n    hideViewExam() {\n      this.showViewExam = false;\n    }\n    static ɵfac = function StuTabRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StuTabRequestComponent)(i0.ɵɵdirectiveInject(i1.StudentProgramSubscriptionServicesService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StuTabRequestComponent,\n      selectors: [[\"app-stu-tab-request\"]],\n      inputs: {\n        filter: \"filter\"\n      },\n      outputs: {\n        itemStuReq: \"itemStuReq\",\n        openAdvancedSearch: \"openAdvancedSearch\",\n        closePopup: \"closePopup\",\n        advancedSearchObject: \"advancedSearchObject\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"container-fluid reuest_container\", 4, \"ngIf\"], [3, \"resiveUserId\", \"hideUserDetails\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"container-fluid\", \"reuest_container\"], [1, \"col-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", \"mx-3\", 3, \"click\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [3, \"itemStuReq\", \"acceptStuReq\", \"acceptAllStudentProgramSubscriptionCheched\", \"studentFilterEvent\", \"studentIdFormGrid\", \"viewExamEvent\", \"studentItems\", \"typeEnum\", \"totalCount\", \"studentFilterRequestModel\"], [1, \"No_data\"], [3, \"studentFilterEvent\", \"studentIdFormGrid\", \"viewExamEvent\", \"typeEnum\", \"studentItems\", \"totalCount\", \"studentFilterRequestModel\"], [3, \"hideUserDetails\", \"resiveUserId\"], [3, \"hideViewExamEvent\", \"studentExamAnswer\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [\"class\", \"body_footer\", 4, \"ngIf\"], [1, \"body_footer\"], [1, \"btn\", \"cancel-btn\", 3, \"click\"], [1, \"btn\", \"save-btn\", 3, \"click\"]],\n      template: function StuTabRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StuTabRequestComponent_div_0_Template, 21, 25, \"div\", 0)(1, StuTabRequestComponent_app_student_details_view_1_Template, 1, 1, \"app-student-details-view\", 1)(2, StuTabRequestComponent_div_2_Template, 3, 2, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.showUserDetailsView && !ctx.showViewExam);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserDetailsView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showViewExam);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right!important}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left!important}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(ar), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(en), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{margin:1rem}.reuest_container[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:74vh}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.reuest_container[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.reuest_container[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:55vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}.reuest_container[_ngcontent-%COMP%]   .w-60[_ngcontent-%COMP%]{width:60%}.reuest_container[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(en){margin-left:66.666667%}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(ar){margin-right:66.666667%}.body_footer[_ngcontent-%COMP%]{position:unset;display:flex;justify-content:flex-end}\"]\n    });\n  }\n  return StuTabRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}