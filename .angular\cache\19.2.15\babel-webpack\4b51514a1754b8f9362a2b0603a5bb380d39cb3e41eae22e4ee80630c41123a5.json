{"ast": null, "code": "import LRU from '../core/LRU.js';\nimport { extend, isGradientObject, isString, map } from '../core/util.js';\nvar kCSSColorTable = {\n  'transparent': [0, 0, 0, 0],\n  'aliceblue': [240, 248, 255, 1],\n  'antiquewhite': [250, 235, 215, 1],\n  'aqua': [0, 255, 255, 1],\n  'aquamarine': [127, 255, 212, 1],\n  'azure': [240, 255, 255, 1],\n  'beige': [245, 245, 220, 1],\n  'bisque': [255, 228, 196, 1],\n  'black': [0, 0, 0, 1],\n  'blanchedalmond': [255, 235, 205, 1],\n  'blue': [0, 0, 255, 1],\n  'blueviolet': [138, 43, 226, 1],\n  'brown': [165, 42, 42, 1],\n  'burlywood': [222, 184, 135, 1],\n  'cadetblue': [95, 158, 160, 1],\n  'chartreuse': [127, 255, 0, 1],\n  'chocolate': [210, 105, 30, 1],\n  'coral': [255, 127, 80, 1],\n  'cornflowerblue': [100, 149, 237, 1],\n  'cornsilk': [255, 248, 220, 1],\n  'crimson': [220, 20, 60, 1],\n  'cyan': [0, 255, 255, 1],\n  'darkblue': [0, 0, 139, 1],\n  'darkcyan': [0, 139, 139, 1],\n  'darkgoldenrod': [184, 134, 11, 1],\n  'darkgray': [169, 169, 169, 1],\n  'darkgreen': [0, 100, 0, 1],\n  'darkgrey': [169, 169, 169, 1],\n  'darkkhaki': [189, 183, 107, 1],\n  'darkmagenta': [139, 0, 139, 1],\n  'darkolivegreen': [85, 107, 47, 1],\n  'darkorange': [255, 140, 0, 1],\n  'darkorchid': [153, 50, 204, 1],\n  'darkred': [139, 0, 0, 1],\n  'darksalmon': [233, 150, 122, 1],\n  'darkseagreen': [143, 188, 143, 1],\n  'darkslateblue': [72, 61, 139, 1],\n  'darkslategray': [47, 79, 79, 1],\n  'darkslategrey': [47, 79, 79, 1],\n  'darkturquoise': [0, 206, 209, 1],\n  'darkviolet': [148, 0, 211, 1],\n  'deeppink': [255, 20, 147, 1],\n  'deepskyblue': [0, 191, 255, 1],\n  'dimgray': [105, 105, 105, 1],\n  'dimgrey': [105, 105, 105, 1],\n  'dodgerblue': [30, 144, 255, 1],\n  'firebrick': [178, 34, 34, 1],\n  'floralwhite': [255, 250, 240, 1],\n  'forestgreen': [34, 139, 34, 1],\n  'fuchsia': [255, 0, 255, 1],\n  'gainsboro': [220, 220, 220, 1],\n  'ghostwhite': [248, 248, 255, 1],\n  'gold': [255, 215, 0, 1],\n  'goldenrod': [218, 165, 32, 1],\n  'gray': [128, 128, 128, 1],\n  'green': [0, 128, 0, 1],\n  'greenyellow': [173, 255, 47, 1],\n  'grey': [128, 128, 128, 1],\n  'honeydew': [240, 255, 240, 1],\n  'hotpink': [255, 105, 180, 1],\n  'indianred': [205, 92, 92, 1],\n  'indigo': [75, 0, 130, 1],\n  'ivory': [255, 255, 240, 1],\n  'khaki': [240, 230, 140, 1],\n  'lavender': [230, 230, 250, 1],\n  'lavenderblush': [255, 240, 245, 1],\n  'lawngreen': [124, 252, 0, 1],\n  'lemonchiffon': [255, 250, 205, 1],\n  'lightblue': [173, 216, 230, 1],\n  'lightcoral': [240, 128, 128, 1],\n  'lightcyan': [224, 255, 255, 1],\n  'lightgoldenrodyellow': [250, 250, 210, 1],\n  'lightgray': [211, 211, 211, 1],\n  'lightgreen': [144, 238, 144, 1],\n  'lightgrey': [211, 211, 211, 1],\n  'lightpink': [255, 182, 193, 1],\n  'lightsalmon': [255, 160, 122, 1],\n  'lightseagreen': [32, 178, 170, 1],\n  'lightskyblue': [135, 206, 250, 1],\n  'lightslategray': [119, 136, 153, 1],\n  'lightslategrey': [119, 136, 153, 1],\n  'lightsteelblue': [176, 196, 222, 1],\n  'lightyellow': [255, 255, 224, 1],\n  'lime': [0, 255, 0, 1],\n  'limegreen': [50, 205, 50, 1],\n  'linen': [250, 240, 230, 1],\n  'magenta': [255, 0, 255, 1],\n  'maroon': [128, 0, 0, 1],\n  'mediumaquamarine': [102, 205, 170, 1],\n  'mediumblue': [0, 0, 205, 1],\n  'mediumorchid': [186, 85, 211, 1],\n  'mediumpurple': [147, 112, 219, 1],\n  'mediumseagreen': [60, 179, 113, 1],\n  'mediumslateblue': [123, 104, 238, 1],\n  'mediumspringgreen': [0, 250, 154, 1],\n  'mediumturquoise': [72, 209, 204, 1],\n  'mediumvioletred': [199, 21, 133, 1],\n  'midnightblue': [25, 25, 112, 1],\n  'mintcream': [245, 255, 250, 1],\n  'mistyrose': [255, 228, 225, 1],\n  'moccasin': [255, 228, 181, 1],\n  'navajowhite': [255, 222, 173, 1],\n  'navy': [0, 0, 128, 1],\n  'oldlace': [253, 245, 230, 1],\n  'olive': [128, 128, 0, 1],\n  'olivedrab': [107, 142, 35, 1],\n  'orange': [255, 165, 0, 1],\n  'orangered': [255, 69, 0, 1],\n  'orchid': [218, 112, 214, 1],\n  'palegoldenrod': [238, 232, 170, 1],\n  'palegreen': [152, 251, 152, 1],\n  'paleturquoise': [175, 238, 238, 1],\n  'palevioletred': [219, 112, 147, 1],\n  'papayawhip': [255, 239, 213, 1],\n  'peachpuff': [255, 218, 185, 1],\n  'peru': [205, 133, 63, 1],\n  'pink': [255, 192, 203, 1],\n  'plum': [221, 160, 221, 1],\n  'powderblue': [176, 224, 230, 1],\n  'purple': [128, 0, 128, 1],\n  'red': [255, 0, 0, 1],\n  'rosybrown': [188, 143, 143, 1],\n  'royalblue': [65, 105, 225, 1],\n  'saddlebrown': [139, 69, 19, 1],\n  'salmon': [250, 128, 114, 1],\n  'sandybrown': [244, 164, 96, 1],\n  'seagreen': [46, 139, 87, 1],\n  'seashell': [255, 245, 238, 1],\n  'sienna': [160, 82, 45, 1],\n  'silver': [192, 192, 192, 1],\n  'skyblue': [135, 206, 235, 1],\n  'slateblue': [106, 90, 205, 1],\n  'slategray': [112, 128, 144, 1],\n  'slategrey': [112, 128, 144, 1],\n  'snow': [255, 250, 250, 1],\n  'springgreen': [0, 255, 127, 1],\n  'steelblue': [70, 130, 180, 1],\n  'tan': [210, 180, 140, 1],\n  'teal': [0, 128, 128, 1],\n  'thistle': [216, 191, 216, 1],\n  'tomato': [255, 99, 71, 1],\n  'turquoise': [64, 224, 208, 1],\n  'violet': [238, 130, 238, 1],\n  'wheat': [245, 222, 179, 1],\n  'white': [255, 255, 255, 1],\n  'whitesmoke': [245, 245, 245, 1],\n  'yellow': [255, 255, 0, 1],\n  'yellowgreen': [154, 205, 50, 1]\n};\nfunction clampCssByte(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 255 ? 255 : i;\n}\nfunction clampCssAngle(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 360 ? 360 : i;\n}\nfunction clampCssFloat(f) {\n  return f < 0 ? 0 : f > 1 ? 1 : f;\n}\nfunction parseCssInt(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssByte(parseFloat(str) / 100 * 255);\n  }\n  return clampCssByte(parseInt(str, 10));\n}\nfunction parseCssFloat(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssFloat(parseFloat(str) / 100);\n  }\n  return clampCssFloat(parseFloat(str));\n}\nfunction cssHueToRgb(m1, m2, h) {\n  if (h < 0) {\n    h += 1;\n  } else if (h > 1) {\n    h -= 1;\n  }\n  if (h * 6 < 1) {\n    return m1 + (m2 - m1) * h * 6;\n  }\n  if (h * 2 < 1) {\n    return m2;\n  }\n  if (h * 3 < 2) {\n    return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n  }\n  return m1;\n}\nfunction lerpNumber(a, b, p) {\n  return a + (b - a) * p;\n}\nfunction setRgba(out, r, g, b, a) {\n  out[0] = r;\n  out[1] = g;\n  out[2] = b;\n  out[3] = a;\n  return out;\n}\nfunction copyRgba(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\nvar colorCache = new LRU(20);\nvar lastRemovedArr = null;\nfunction putToCache(colorStr, rgbaArr) {\n  if (lastRemovedArr) {\n    copyRgba(lastRemovedArr, rgbaArr);\n  }\n  lastRemovedArr = colorCache.put(colorStr, lastRemovedArr || rgbaArr.slice());\n}\nexport function parse(colorStr, rgbaArr) {\n  if (!colorStr) {\n    return;\n  }\n  rgbaArr = rgbaArr || [];\n  var cached = colorCache.get(colorStr);\n  if (cached) {\n    return copyRgba(rgbaArr, cached);\n  }\n  colorStr = colorStr + '';\n  var str = colorStr.replace(/ /g, '').toLowerCase();\n  if (str in kCSSColorTable) {\n    copyRgba(rgbaArr, kCSSColorTable[str]);\n    putToCache(colorStr, rgbaArr);\n    return rgbaArr;\n  }\n  var strLen = str.length;\n  if (str.charAt(0) === '#') {\n    if (strLen === 4 || strLen === 5) {\n      var iv = parseInt(str.slice(1, 4), 16);\n      if (!(iv >= 0 && iv <= 0xfff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xf00) >> 4 | (iv & 0xf00) >> 8, iv & 0xf0 | (iv & 0xf0) >> 4, iv & 0xf | (iv & 0xf) << 4, strLen === 5 ? parseInt(str.slice(4), 16) / 0xf : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    } else if (strLen === 7 || strLen === 9) {\n      var iv = parseInt(str.slice(1, 7), 16);\n      if (!(iv >= 0 && iv <= 0xffffff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, strLen === 9 ? parseInt(str.slice(7), 16) / 0xff : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    }\n    return;\n  }\n  var op = str.indexOf('(');\n  var ep = str.indexOf(')');\n  if (op !== -1 && ep + 1 === strLen) {\n    var fname = str.substr(0, op);\n    var params = str.substr(op + 1, ep - (op + 1)).split(',');\n    var alpha = 1;\n    switch (fname) {\n      case 'rgba':\n        if (params.length !== 4) {\n          return params.length === 3 ? setRgba(rgbaArr, +params[0], +params[1], +params[2], 1) : setRgba(rgbaArr, 0, 0, 0, 1);\n        }\n        alpha = parseCssFloat(params.pop());\n      case 'rgb':\n        if (params.length >= 3) {\n          setRgba(rgbaArr, parseCssInt(params[0]), parseCssInt(params[1]), parseCssInt(params[2]), params.length === 3 ? alpha : parseCssFloat(params[3]));\n          putToCache(colorStr, rgbaArr);\n          return rgbaArr;\n        } else {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n      case 'hsla':\n        if (params.length !== 4) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        params[3] = parseCssFloat(params[3]);\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      case 'hsl':\n        if (params.length !== 3) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      default:\n        return;\n    }\n  }\n  setRgba(rgbaArr, 0, 0, 0, 1);\n  return;\n}\nfunction hsla2rgba(hsla, rgba) {\n  var h = (parseFloat(hsla[0]) % 360 + 360) % 360 / 360;\n  var s = parseCssFloat(hsla[1]);\n  var l = parseCssFloat(hsla[2]);\n  var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n  var m1 = l * 2 - m2;\n  rgba = rgba || [];\n  setRgba(rgba, clampCssByte(cssHueToRgb(m1, m2, h + 1 / 3) * 255), clampCssByte(cssHueToRgb(m1, m2, h) * 255), clampCssByte(cssHueToRgb(m1, m2, h - 1 / 3) * 255), 1);\n  if (hsla.length === 4) {\n    rgba[3] = hsla[3];\n  }\n  return rgba;\n}\nfunction rgba2hsla(rgba) {\n  if (!rgba) {\n    return;\n  }\n  var R = rgba[0] / 255;\n  var G = rgba[1] / 255;\n  var B = rgba[2] / 255;\n  var vMin = Math.min(R, G, B);\n  var vMax = Math.max(R, G, B);\n  var delta = vMax - vMin;\n  var L = (vMax + vMin) / 2;\n  var H;\n  var S;\n  if (delta === 0) {\n    H = 0;\n    S = 0;\n  } else {\n    if (L < 0.5) {\n      S = delta / (vMax + vMin);\n    } else {\n      S = delta / (2 - vMax - vMin);\n    }\n    var deltaR = ((vMax - R) / 6 + delta / 2) / delta;\n    var deltaG = ((vMax - G) / 6 + delta / 2) / delta;\n    var deltaB = ((vMax - B) / 6 + delta / 2) / delta;\n    if (R === vMax) {\n      H = deltaB - deltaG;\n    } else if (G === vMax) {\n      H = 1 / 3 + deltaR - deltaB;\n    } else if (B === vMax) {\n      H = 2 / 3 + deltaG - deltaR;\n    }\n    if (H < 0) {\n      H += 1;\n    }\n    if (H > 1) {\n      H -= 1;\n    }\n  }\n  var hsla = [H * 360, S, L];\n  if (rgba[3] != null) {\n    hsla.push(rgba[3]);\n  }\n  return hsla;\n}\nexport function lift(color, level) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    for (var i = 0; i < 3; i++) {\n      if (level < 0) {\n        colorArr[i] = colorArr[i] * (1 - level) | 0;\n      } else {\n        colorArr[i] = (255 - colorArr[i]) * level + colorArr[i] | 0;\n      }\n      if (colorArr[i] > 255) {\n        colorArr[i] = 255;\n      } else if (colorArr[i] < 0) {\n        colorArr[i] = 0;\n      }\n    }\n    return stringify(colorArr, colorArr.length === 4 ? 'rgba' : 'rgb');\n  }\n}\nexport function toHex(color) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    return ((1 << 24) + (colorArr[0] << 16) + (colorArr[1] << 8) + +colorArr[2]).toString(16).slice(1);\n  }\n}\nexport function fastLerp(normalizedValue, colors, out) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  out = out || [];\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = colors[leftIndex];\n  var rightColor = colors[rightIndex];\n  var dv = value - leftIndex;\n  out[0] = clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv));\n  out[1] = clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv));\n  out[2] = clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv));\n  out[3] = clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv));\n  return out;\n}\nexport var fastMapToColor = fastLerp;\nexport function lerp(normalizedValue, colors, fullOutput) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = parse(colors[leftIndex]);\n  var rightColor = parse(colors[rightIndex]);\n  var dv = value - leftIndex;\n  var color = stringify([clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv)), clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv)), clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv)), clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv))], 'rgba');\n  return fullOutput ? {\n    color: color,\n    leftIndex: leftIndex,\n    rightIndex: rightIndex,\n    value: value\n  } : color;\n}\nexport var mapToColor = lerp;\nexport function modifyHSL(color, h, s, l) {\n  var colorArr = parse(color);\n  if (color) {\n    colorArr = rgba2hsla(colorArr);\n    h != null && (colorArr[0] = clampCssAngle(h));\n    s != null && (colorArr[1] = parseCssFloat(s));\n    l != null && (colorArr[2] = parseCssFloat(l));\n    return stringify(hsla2rgba(colorArr), 'rgba');\n  }\n}\nexport function modifyAlpha(color, alpha) {\n  var colorArr = parse(color);\n  if (colorArr && alpha != null) {\n    colorArr[3] = clampCssFloat(alpha);\n    return stringify(colorArr, 'rgba');\n  }\n}\nexport function stringify(arrColor, type) {\n  if (!arrColor || !arrColor.length) {\n    return;\n  }\n  var colorStr = arrColor[0] + ',' + arrColor[1] + ',' + arrColor[2];\n  if (type === 'rgba' || type === 'hsva' || type === 'hsla') {\n    colorStr += ',' + arrColor[3];\n  }\n  return type + '(' + colorStr + ')';\n}\nexport function lum(color, backgroundLum) {\n  var arr = parse(color);\n  return arr ? (0.299 * arr[0] + 0.587 * arr[1] + 0.114 * arr[2]) * arr[3] / 255 + (1 - arr[3]) * backgroundLum : 0;\n}\nexport function random() {\n  return stringify([Math.round(Math.random() * 255), Math.round(Math.random() * 255), Math.round(Math.random() * 255)], 'rgb');\n}\nvar liftedColorCache = new LRU(100);\nexport function liftColor(color) {\n  if (isString(color)) {\n    var liftedColor = liftedColorCache.get(color);\n    if (!liftedColor) {\n      liftedColor = lift(color, -0.1);\n      liftedColorCache.put(color, liftedColor);\n    }\n    return liftedColor;\n  } else if (isGradientObject(color)) {\n    var ret = extend({}, color);\n    ret.colorStops = map(color.colorStops, function (stop) {\n      return {\n        offset: stop.offset,\n        color: lift(stop.color, -0.1)\n      };\n    });\n    return ret;\n  }\n  return color;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}