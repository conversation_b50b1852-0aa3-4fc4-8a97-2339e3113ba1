{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Parse and decode geo json\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { GeoJSONLineStringGeometry, GeoJSONPolygonGeometry, GeoJSONRegion } from './Region.js';\nfunction decode(json) {\n  if (!json.UTF8Encoding) {\n    return json;\n  }\n  var jsonCompressed = json;\n  var encodeScale = jsonCompressed.UTF8Scale;\n  if (encodeScale == null) {\n    encodeScale = 1024;\n  }\n  var features = jsonCompressed.features;\n  zrUtil.each(features, function (feature) {\n    var geometry = feature.geometry;\n    var encodeOffsets = geometry.encodeOffsets;\n    var coordinates = geometry.coordinates;\n    // Geometry may be appeded manually in the script after json loaded.\n    // In this case this geometry is usually not encoded.\n    if (!encodeOffsets) {\n      return;\n    }\n    switch (geometry.type) {\n      case 'LineString':\n        geometry.coordinates = decodeRing(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'Polygon':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiLineString':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(coordinates, function (rings, idx) {\n          return decodeRings(rings, encodeOffsets[idx], encodeScale);\n        });\n    }\n  });\n  // Has been decoded\n  jsonCompressed.UTF8Encoding = false;\n  return jsonCompressed;\n}\nfunction decodeRings(rings, encodeOffsets, encodeScale) {\n  for (var c = 0; c < rings.length; c++) {\n    rings[c] = decodeRing(rings[c], encodeOffsets[c], encodeScale);\n  }\n}\nfunction decodeRing(coordinate, encodeOffsets, encodeScale) {\n  var result = [];\n  var prevX = encodeOffsets[0];\n  var prevY = encodeOffsets[1];\n  for (var i = 0; i < coordinate.length; i += 2) {\n    var x = coordinate.charCodeAt(i) - 64;\n    var y = coordinate.charCodeAt(i + 1) - 64;\n    // ZigZag decoding\n    x = x >> 1 ^ -(x & 1);\n    y = y >> 1 ^ -(y & 1);\n    // Delta deocding\n    x += prevX;\n    y += prevY;\n    prevX = x;\n    prevY = y;\n    // Dequantize\n    result.push([x / encodeScale, y / encodeScale]);\n  }\n  return result;\n}\nexport default function parseGeoJSON(geoJson, nameProperty) {\n  geoJson = decode(geoJson);\n  return zrUtil.map(zrUtil.filter(geoJson.features, function (featureObj) {\n    // Output of mapshaper may have geometry null\n    return featureObj.geometry && featureObj.properties && featureObj.geometry.coordinates.length > 0;\n  }), function (featureObj) {\n    var properties = featureObj.properties;\n    var geo = featureObj.geometry;\n    var geometries = [];\n    switch (geo.type) {\n      case 'Polygon':\n        var coordinates = geo.coordinates;\n        // According to the GeoJSON specification.\n        // First must be exterior, and the rest are all interior(holes).\n        geometries.push(new GeoJSONPolygonGeometry(coordinates[0], coordinates.slice(1)));\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(geo.coordinates, function (item) {\n          if (item[0]) {\n            geometries.push(new GeoJSONPolygonGeometry(item[0], item.slice(1)));\n          }\n        });\n        break;\n      case 'LineString':\n        geometries.push(new GeoJSONLineStringGeometry([geo.coordinates]));\n        break;\n      case 'MultiLineString':\n        geometries.push(new GeoJSONLineStringGeometry(geo.coordinates));\n    }\n    var region = new GeoJSONRegion(properties[nameProperty || 'name'], geometries, properties.cp);\n    region.properties = properties;\n    return region;\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}