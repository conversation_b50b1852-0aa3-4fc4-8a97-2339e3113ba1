{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Linear continuous scale\r\n * http://en.wikipedia.org/wiki/Level_of_measurement\r\n */\n// FIXME only one data\nimport Scale from './Scale.js';\nimport OrdinalMeta from '../data/OrdinalMeta.js';\nimport * as scaleHelper from './helper.js';\nimport { isArray, map, isObject, isString } from 'zrender/lib/core/util.js';\nvar OrdinalScale = /** @class */function (_super) {\n  __extends(OrdinalScale, _super);\n  function OrdinalScale(setting) {\n    var _this = _super.call(this, setting) || this;\n    _this.type = 'ordinal';\n    var ordinalMeta = _this.getSetting('ordinalMeta');\n    // Caution: Should not use instanceof, consider ec-extensions using\n    // import approach to get OrdinalMeta class.\n    if (!ordinalMeta) {\n      ordinalMeta = new OrdinalMeta({});\n    }\n    if (isArray(ordinalMeta)) {\n      ordinalMeta = new OrdinalMeta({\n        categories: map(ordinalMeta, function (item) {\n          return isObject(item) ? item.value : item;\n        })\n      });\n    }\n    _this._ordinalMeta = ordinalMeta;\n    _this._extent = _this.getSetting('extent') || [0, ordinalMeta.categories.length - 1];\n    return _this;\n  }\n  OrdinalScale.prototype.parse = function (val) {\n    // Caution: Math.round(null) will return `0` rather than `NaN`\n    if (val == null) {\n      return NaN;\n    }\n    return isString(val) ? this._ordinalMeta.getOrdinal(val)\n    // val might be float.\n    : Math.round(val);\n  };\n  OrdinalScale.prototype.contain = function (rank) {\n    rank = this.parse(rank);\n    return scaleHelper.contain(rank, this._extent) && this._ordinalMeta.categories[rank] != null;\n  };\n  /**\r\n   * Normalize given rank or name to linear [0, 1]\r\n   * @param val raw ordinal number.\r\n   * @return normalized value in [0, 1].\r\n   */\n  OrdinalScale.prototype.normalize = function (val) {\n    val = this._getTickNumber(this.parse(val));\n    return scaleHelper.normalize(val, this._extent);\n  };\n  /**\r\n   * @param val normalized value in [0, 1].\r\n   * @return raw ordinal number.\r\n   */\n  OrdinalScale.prototype.scale = function (val) {\n    val = Math.round(scaleHelper.scale(val, this._extent));\n    return this.getRawOrdinalNumber(val);\n  };\n  OrdinalScale.prototype.getTicks = function () {\n    var ticks = [];\n    var extent = this._extent;\n    var rank = extent[0];\n    while (rank <= extent[1]) {\n      ticks.push({\n        value: rank\n      });\n      rank++;\n    }\n    return ticks;\n  };\n  OrdinalScale.prototype.getMinorTicks = function (splitNumber) {\n    // Not support.\n    return;\n  };\n  /**\r\n   * @see `Ordinal['_ordinalNumbersByTick']`\r\n   */\n  OrdinalScale.prototype.setSortInfo = function (info) {\n    if (info == null) {\n      this._ordinalNumbersByTick = this._ticksByOrdinalNumber = null;\n      return;\n    }\n    var infoOrdinalNumbers = info.ordinalNumbers;\n    var ordinalsByTick = this._ordinalNumbersByTick = [];\n    var ticksByOrdinal = this._ticksByOrdinalNumber = [];\n    // Unnecessary support negative tick in `realtimeSort`.\n    var tickNum = 0;\n    var allCategoryLen = this._ordinalMeta.categories.length;\n    for (var len = Math.min(allCategoryLen, infoOrdinalNumbers.length); tickNum < len; ++tickNum) {\n      var ordinalNumber = infoOrdinalNumbers[tickNum];\n      ordinalsByTick[tickNum] = ordinalNumber;\n      ticksByOrdinal[ordinalNumber] = tickNum;\n    }\n    // Handle that `series.data` only covers part of the `axis.category.data`.\n    var unusedOrdinal = 0;\n    for (; tickNum < allCategoryLen; ++tickNum) {\n      while (ticksByOrdinal[unusedOrdinal] != null) {\n        unusedOrdinal++;\n      }\n      ;\n      ordinalsByTick.push(unusedOrdinal);\n      ticksByOrdinal[unusedOrdinal] = tickNum;\n    }\n  };\n  OrdinalScale.prototype._getTickNumber = function (ordinal) {\n    var ticksByOrdinalNumber = this._ticksByOrdinalNumber;\n    // also support ordinal out of range of `ordinalMeta.categories.length`,\n    // where ordinal numbers are used as tick value directly.\n    return ticksByOrdinalNumber && ordinal >= 0 && ordinal < ticksByOrdinalNumber.length ? ticksByOrdinalNumber[ordinal] : ordinal;\n  };\n  /**\r\n   * @usage\r\n   * ```js\r\n   * const ordinalNumber = ordinalScale.getRawOrdinalNumber(tickVal);\r\n   *\r\n   * // case0\r\n   * const rawOrdinalValue = axisModel.getCategories()[ordinalNumber];\r\n   * // case1\r\n   * const rawOrdinalValue = this._ordinalMeta.categories[ordinalNumber];\r\n   * // case2\r\n   * const coord = axis.dataToCoord(ordinalNumber);\r\n   * ```\r\n   *\r\n   * @param {OrdinalNumber} tickNumber index of display\r\n   */\n  OrdinalScale.prototype.getRawOrdinalNumber = function (tickNumber) {\n    var ordinalNumbersByTick = this._ordinalNumbersByTick;\n    // tickNumber may be out of range, e.g., when axis max is larger than `ordinalMeta.categories.length`.,\n    // where ordinal numbers are used as tick value directly.\n    return ordinalNumbersByTick && tickNumber >= 0 && tickNumber < ordinalNumbersByTick.length ? ordinalNumbersByTick[tickNumber] : tickNumber;\n  };\n  /**\r\n   * Get item on tick\r\n   */\n  OrdinalScale.prototype.getLabel = function (tick) {\n    if (!this.isBlank()) {\n      var ordinalNumber = this.getRawOrdinalNumber(tick.value);\n      var cateogry = this._ordinalMeta.categories[ordinalNumber];\n      // Note that if no data, ordinalMeta.categories is an empty array.\n      // Return empty if it's not exist.\n      return cateogry == null ? '' : cateogry + '';\n    }\n  };\n  OrdinalScale.prototype.count = function () {\n    return this._extent[1] - this._extent[0] + 1;\n  };\n  OrdinalScale.prototype.unionExtentFromData = function (data, dim) {\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * @override\r\n   * If value is in extent range\r\n   */\n  OrdinalScale.prototype.isInExtentRange = function (value) {\n    value = this._getTickNumber(value);\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  OrdinalScale.prototype.getOrdinalMeta = function () {\n    return this._ordinalMeta;\n  };\n  OrdinalScale.prototype.calcNiceTicks = function () {};\n  OrdinalScale.prototype.calcNiceExtent = function () {};\n  OrdinalScale.type = 'ordinal';\n  return OrdinalScale;\n}(Scale);\nScale.registerClass(OrdinalScale);\nexport default OrdinalScale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}