{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nfunction TeacherDashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-add-feelings-form\", 11);\n    i0.ɵɵlistener(\"closeForm\", function TeacherDashboardComponent_div_12_Template_app_add_feelings_form_closeForm_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeFeelingsForm());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TeacherDashboardComponent = /*#__PURE__*/(() => {\n  class TeacherDashboardComponent {\n    languageService;\n    translate;\n    openfeelingsOverlay = false;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.DASHBORD'));\n    }\n    openFeelingsForm() {\n      this.openfeelingsOverlay = true;\n    }\n    closeFeelingsForm() {\n      this.openfeelingsOverlay = false;\n    }\n    static ɵfac = function TeacherDashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherDashboardComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherDashboardComponent,\n      selectors: [[\"app-teacher-dashboard\"]],\n      decls: 13,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"mt-2\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\", \"teacher_feelings\"], [3, \"FeelingsForm\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\", \"mt-1\"], [1, \"row\"], [1, \"col-12\", \"col-sm-12\", \"mb-2\", \"height_50\", \"available_appointments\"], [1, \"col-12\", \"height_50\", \"teacher_rate\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\", \"daily_tasks\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeForm\"]],\n      template: function TeacherDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-teacher-feelings\", 3);\n          i0.ɵɵlistener(\"FeelingsForm\", function TeacherDashboardComponent_Template_app_teacher_feelings_FeelingsForm_3_listener() {\n            return ctx.openFeelingsForm();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"app-teacher-available-appointments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵelement(9, \"app-teacher-rate-for-student\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵelement(11, \"app-teacher-daily-free-recitation-appointments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, TeacherDashboardComponent_div_12_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.openfeelingsOverlay);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".commantbackgrond[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;margin-top:1rem}.daily_tasks[_ngcontent-%COMP%]{padding-inline-start:0px!important}.overlay.add_teacher_appointment_request[_ngcontent-%COMP%]{width:50%}.teacher_feelings[_ngcontent-%COMP%], .available_appointments[_ngcontent-%COMP%], .teacher_rate[_ngcontent-%COMP%], .daily_upcoming_tasks[_ngcontent-%COMP%]{background-color:#fbfbfb;border-radius:1.25rem;max-width:93%;margin:auto}.height_50[_ngcontent-%COMP%]{height:42vh}.daily_upcoming_tasks[_ngcontent-%COMP%], .teacher_feelings[_ngcontent-%COMP%]{height:85vh}\"]\n    });\n  }\n  return TeacherDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}