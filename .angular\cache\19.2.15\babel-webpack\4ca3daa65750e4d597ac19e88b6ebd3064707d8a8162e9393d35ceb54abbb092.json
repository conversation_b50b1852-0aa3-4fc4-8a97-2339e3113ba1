{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ProgramDayTasksDetails } from 'src/app/core/enums/programs/program-day-tasks-details.enum';\nimport { StudTotalExamTaskDegreeEchartComponent } from 'src/app/shared/components/admin-dash-board-widgets/stud-total-exam-task-degree-echart/stud-total-exam-task-degree-echart.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-batches-service/program-batches.service\";\nimport * as i2 from \"src/app/core/services/admin-dash-bord-services/admin-dash-board.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"@angular/forms\";\nfunction AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-radio-group\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_7_Template_mat_radio_group_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp, $event) || (ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_7_Template_mat_radio_group_change_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeBatch());\n    });\n    i0.ɵɵelementStart(2, \"mat-radio-button\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-radio-button\", 11);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.adminDashBoardTotalExamTaskDegreeReq.taskTyp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.typeExamEnum.TaskTestPhased);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"ADMIN_DASH_BORD.PHASE_EXAM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.typeExamEnum.TaskDailyTest);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 7, \"ADMIN_DASH_BORD.DAILY_EXAM\"), \" \");\n  }\n}\nfunction AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_ng_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bat_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", bat_r3.batId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? bat_r3.progBatNameEn : bat_r3.progBatNameAr, \" \");\n  }\n}\nfunction AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"input\", 12);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_15_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay, $event) || (ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_15_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeBatch());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \" \", i0.ɵɵpipeBind1(2, 3, \"ADMIN_DASH_BORD.DAY\"), \" \");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.adminDashBoardTotalExamTaskDegreeReq.fromDay);\n  }\n}\nfunction AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"app-stud-total-exam-task-degree-echart\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"adminDashBoardStudTotalExamTaskDegree\", ctx_r1.adminDashBoardTotalExamTaskDegreeList);\n  }\n}\nexport let AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent = /*#__PURE__*/(() => {\n  class AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent {\n    programBatchesService;\n    adminDashBoardService;\n    translate;\n    alertfy;\n    examCountEchart;\n    allProgBatchs = [];\n    batId;\n    typeExamEnum = ProgramDayTasksDetails;\n    adminDashBoardTotalExamTaskDegreeReq = {\n      taskTyp: ProgramDayTasksDetails.TaskTestPhased,\n      fromDay: 1,\n      toDay: 10\n    };\n    adminDashBoardTotalExamTaskDegreeList;\n    langEnum = LanguageEnum;\n    // listOfOptions= [\n    //   {\"name\": this.translate.instant('ADMIN_DASH_BORD.PHASE_EXAM'), ID:ProgramDayTasksDetails.TaskTestPhased, \"checked\": true},\n    //   {\"name\": this.translate.instant('ADMIN_DASH_BORD.DAILY_EXAM'), ID: ProgramDayTasksDetails.TaskDailyTest, \"checked\": false}\n    // ]\n    constructor(programBatchesService, adminDashBoardService, translate, alertfy) {\n      this.programBatchesService = programBatchesService;\n      this.adminDashBoardService = adminDashBoardService;\n      this.translate = translate;\n      this.alertfy = alertfy;\n    }\n    ngOnInit() {\n      this.getAllProgs();\n      this.getAdminDashBoardExamTaskDegree();\n    }\n    getAllProgs() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.adminDashBoardTotalExamTaskDegreeList = [];\n      this.adminDashBoardTotalExamTaskDegreeReq.batId = this.batId;\n      if (this.adminDashBoardTotalExamTaskDegreeReq && !this.adminDashBoardTotalExamTaskDegreeReq.fromDay) this.adminDashBoardTotalExamTaskDegreeReq.fromDay = 1;\n      if (this.adminDashBoardTotalExamTaskDegreeReq && this.adminDashBoardTotalExamTaskDegreeReq.fromDay) this.adminDashBoardTotalExamTaskDegreeReq.toDay = this.adminDashBoardTotalExamTaskDegreeReq.fromDay + 9;\n      this.getAdminDashBoardExamTaskDegree();\n    }\n    getAdminDashBoardExamTaskDegree() {\n      this.adminDashBoardService.getAdminDashBoardStudTotalExamTaskDegree(this.adminDashBoardTotalExamTaskDegreeReq || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.adminDashBoardTotalExamTaskDegreeList = res.data;\n          this.examCountEchart?.initiate(this.adminDashBoardTotalExamTaskDegreeList || []);\n        } else {\n          this.alertfy.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent)(i0.ɵɵdirectiveInject(i1.ProgramBatchesService), i0.ɵɵdirectiveInject(i2.AdminDashBoardService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent,\n      selectors: [[\"app-admin-dash-board-stud-total-exam-task-degree-widgets\"]],\n      viewQuery: function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StudTotalExamTaskDegreeEchartComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.examCountEchart = _t.first);\n        }\n      },\n      decls: 17,\n      vars: 14,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\", \"mt-3\"], [1, \"col-12\"], [1, \"row\", \"my-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"col-3\"], [1, \"head\"], [\"class\", \"col-3\", 4, \"ngIf\"], [\"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12  mb-2 px-1\", 4, \"ngIf\"], [\"aria-label\", \"Select an option\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"px-1\", 3, \"value\"], [\"type\", \"number\", \"min\", \"1\", 1, \"form-control\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [1, \"col-12\", \"mb-2\", \"px-1\"], [3, \"adminDashBoardStudTotalExamTaskDegree\"]],\n      template: function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_7_Template, 8, 9, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"ng-select\", 6);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_Template_ng_select_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_Template_ng_select_change_9_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementStart(11, \"ng-option\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_ng_option_14_Template, 2, 2, \"ng-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(15, AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_15_Template, 3, 5, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(16, AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent_div_16_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 8, \"ADMIN_DASH_BORD.COUNT_TASK_EXAM_STUD\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeReq);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(10, 10, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 12, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allProgBatchs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeReq);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.adminDashBoardTotalExamTaskDegreeList);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectComponent, i6.NgOptionComponent, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.MinValidator, i7.NgModel, i3.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .Register__Label[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}\"]\n    });\n  }\n  return AdminDashBoardStudTotalExamTaskDegreeWidgetsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}