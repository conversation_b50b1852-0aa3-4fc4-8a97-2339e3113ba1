{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nexport let LandingAboutHofaazComponent = /*#__PURE__*/(() => {\n  class LandingAboutHofaazComponent {\n    imagesPathesService;\n    constructor(imagesPathesService) {\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.setScssImages();\n    }\n    setScssImages() {\n      this.imagesPathesService.setUnionInStyle();\n    }\n    static ɵfac = function LandingAboutHofaazComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LandingAboutHofaazComponent)(i0.ɵɵdirectiveInject(i1.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LandingAboutHofaazComponent,\n      selectors: [[\"app-landing-about-hofaaz\"]],\n      decls: 77,\n      vars: 5,\n      consts: [[\"id\", \"about\", 1, \"container-fluid\", \"about_us_container\", \"mb-5\"], [1, \"row\", \"mb-3\", \"mt-5\"], [1, \"col-6\", \"align-self-center\"], [1, \"mb-4\"], [1, \"header_partion\"], [1, \"border_bottom\"], [1, \"about_us\", \"my-4\", \"px-4\"], [1, \"info\"], [1, \"col-6\", \"bag_about_us\"], [1, \"img_aboutHofaz\"], [1, \"img_logo\", 3, \"src\"], [1, \"row\", \"my-5\", \"mx-5\", \"d-flex\", \"justify-content-center\", \"margin-card\"], [1, \"col-xl-4\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"margin-responsive\", \"padding-card\"], [1, \"row\", \"vation_card\"], [1, \"col-12\", \"p-0\", \"img_vation\"], [3, \"src\"], [1, \"col-12\"], [1, \"text\"], [1, \"info_vation\"], [1, \"col-xl-7\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mx-4\", \"padding-card\"], [1, \"row\", \"w-100\", \"goal_card\", \"px-0\"], [1, \"col-6\"], [1, \"text_goal\", \"text\"], [1, \"list_ourgoal\"], [1, \"col-6\", \"p-0\"], [1, \"icon6\", 3, \"src\"], [1, \"col-xl-7\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"pr-0\", \"pl-0\", \"mr-0\"], [1, \"row\", \"w-100\", \"goal_card\", \"px-0\", \"margin-responsive\"], [1, \"col-xl-4\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"padding-card\"]],\n      template: function LandingAboutHofaazComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \" \\u0639\\u0646 \");\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u0645\\u0633\\u062A\\u0646\\u064A\\u0631\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \" \\u0645\\u0646 \\u0646\\u062D\\u0646 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 7);\n          i0.ɵɵtext(11, \" \\u062C\\u0647\\u0629 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629 \\u063A\\u064A\\u0631 \\u0631\\u0628\\u062D\\u064A\\u0629\\u060C \\u0645\\u062A\\u062E\\u0635\\u0635\\u0629 \\u0628\\u062A\\u0639\\u0644\\u064A\\u0645 \\u0627\\u0644\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A\\u060C \\u062D\\u0641\\u0638\\u0627\\u064B \\u0648\\u062A\\u0641\\u0647\\u0645\\u0627\\u064B\\u060C \\u0648\\u062A\\u062D\\u0645\\u0644 \\u0639\\u0644\\u0649 \\u0639\\u0627\\u062A\\u0642\\u0647\\u0627 \\u0646\\u0634\\u0631\\u0647\\u060C \\u0645\\u0648\\u0627\\u0643\\u0628\\u0629 \\u0641\\u064A \\u0630\\u0644\\u0643 \\u0627\\u0644\\u062A\\u0642\\u0646\\u064A\\u0629 \\u0627\\u0644\\u062D\\u062F\\u064A\\u062B\\u0629. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9);\n          i0.ɵɵelement(14, \"img\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"div\", 13)(18, \"div\", 14);\n          i0.ɵɵelement(19, \"img\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"p\", 17);\n          i0.ɵɵtext(22, \"\\u0631\\u0624\\u064A\\u062A\\u0646\\u0627\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 18);\n          i0.ɵɵtext(24, \" \\u0645\\u0631\\u062C\\u0639\\u064A\\u0629 \\u0631\\u0627\\u0626\\u062F\\u0629 \\u0641\\u064A \\u062A\\u062D\\u0641\\u064A\\u0638 \\u0648\\u062A\\u0639\\u0644\\u064A\\u0645 \\u0627\\u0644\\u0633\\u0646\\u0629 \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 20)(27, \"div\", 21)(28, \"p\", 22);\n          i0.ɵɵtext(29, \"\\u0623\\u0647\\u062F\\u0627\\u0641\\u0646\\u0627\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"ul\")(31, \"li\", 23);\n          i0.ɵɵtext(32, \" \\u062A\\u0633\\u0647\\u064A\\u0644 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0644\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0633\\u0646\\u0629 \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A\\u0629. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 23);\n          i0.ɵɵtext(34, \" \\u062A\\u0623\\u0633\\u064A\\u0633 \\u0637\\u0644\\u0627\\u0628 \\u0639\\u0644\\u0645 \\u062D\\u0627\\u0641\\u0638\\u064A\\u0646 \\u0644\\u0644\\u0633\\u0646\\u0629 \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A\\u0629 \\u0644\\u064A\\u0643\\u0648\\u0646\\u0648\\u0627 \\u062F\\u0639\\u0627\\u0629 \\u0644\\u0646\\u0634\\u0631\\u0647\\u0627. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 23);\n          i0.ɵɵtext(36, \" \\u0627\\u0644\\u062A\\u0639\\u0631\\u064A\\u0641 \\u0628\\u0623\\u0635\\u0648\\u0644 \\u062F\\u0648\\u0648\\u0627\\u064A\\u0646 \\u0627\\u0644\\u0627\\u0633\\u0644\\u0627\\u0645 \\u0627\\u0644\\u062A\\u064A \\u062D\\u0648\\u062A \\u0623\\u062D\\u0627\\u062F\\u064A\\u062B \\u0627\\u0644\\u0646\\u0628\\u064A \\uFDFA. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 23);\n          i0.ɵɵtext(38, \" \\u0646\\u0634\\u0631 \\u0633\\u064A\\u0631\\u0629 \\u0627\\u0644\\u0646\\u0628\\u064A \\uFDFA \\u0648\\u0627\\u0644\\u062A\\u0639\\u0631\\u064A\\u0641 \\u0628\\u0634\\u0645\\u0627\\u0626\\u0644\\u0647 \\u0648\\u0623\\u062E\\u0644\\u0627\\u0642\\u0647 \\u0648\\u0622\\u062F\\u0627\\u0628\\u0647 \\u0648\\u062A\\u0639\\u0627\\u0645\\u0644\\u0647 \\u0644\\u0643\\u064A \\u064A\\u0643\\u0648\\u0646 \\u0645\\u0646\\u0647\\u062C\\u0627 \\u0644\\u0644\\u0645\\u0633\\u0644\\u0645\\u064A\\u0646 \\u0641\\u064A \\u062D\\u064A\\u0627\\u062A\\u0647\\u0645. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 24);\n          i0.ɵɵelement(40, \"img\", 25);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 11)(42, \"div\", 26)(43, \"div\", 27)(44, \"div\", 21)(45, \"p\", 22);\n          i0.ɵɵtext(46, \"\\u0645\\u0627 \\u064A\\u0645\\u064A\\u0632\\u0646\\u0627\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"ul\")(48, \"li\", 23);\n          i0.ɵɵtext(49, \" \\u0623\\u0648\\u0644 \\u0645\\u0646\\u0635\\u0629 \\u062A\\u0642\\u0646\\u064A\\u0629 \\u0644\\u062A\\u062D\\u0641\\u064A\\u0638 \\u0645\\u062D\\u0635\\u0644 \\u0627\\u0644\\u0635\\u062D\\u064A\\u062D\\u064A\\u0646 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"li\", 23);\n          i0.ɵɵtext(51, \" \\u0628\\u0631\\u0627\\u0645\\u062C \\u0646\\u0648\\u0639\\u064A\\u0629 \\u0641\\u064A \\u062A\\u062D\\u0641\\u064A\\u0638 \\u0648\\u062A\\u0639\\u0644\\u064A\\u0645 \\u0627\\u0644\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A \\u0628\\u064A\\u0633\\u0631 \\u0648\\u0633\\u0647\\u0648\\u0644\\u0629 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"li\", 23);\n          i0.ɵɵtext(53, \" \\u0633\\u0647\\u0648\\u0644\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0648\\u0627\\u0644\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643 \\u0628\\u0627\\u0644\\u0645\\u0646\\u0635\\u0629 \\u0628\\u0636\\u063A\\u0637\\u0629 \\u0648\\u0627\\u062D\\u062F\\u0629 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"li\", 23);\n          i0.ɵɵtext(55, \" \\u0633\\u0647\\u0648\\u0644\\u0629 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0642\\u0631\\u0631\\u0627\\u062A \\u0648\\u0627\\u0644\\u0648\\u0627\\u062C\\u0628\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\\u064A\\u0629. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"li\", 23);\n          i0.ɵɵtext(57, \" \\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0644\\u0644\\u0637\\u0627\\u0644\\u0628. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"li\", 23);\n          i0.ɵɵtext(59, \" \\u0646\\u062E\\u0628\\u0629 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646 \\u0648\\u0627\\u0644\\u0645\\u0639\\u0644\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u062C\\u0627\\u0632\\u064A\\u0646. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"li\", 23);\n          i0.ɵɵtext(61, \" \\u0628\\u064A\\u0626\\u0629 \\u062A\\u0646\\u0627\\u0641\\u0633\\u064A\\u0629 . \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"li\", 23);\n          i0.ɵɵtext(63, \" \\u062E\\u0637\\u0629 \\u0645\\u0639\\u062A\\u0645\\u062F\\u0629 \\u0645\\u062D\\u0643\\u0645\\u0629 . \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"li\", 23);\n          i0.ɵɵtext(65, \" \\u0634\\u0647\\u0627\\u062F\\u0629 \\u0627\\u062C\\u062A\\u064A\\u0627\\u0632 \\u0641\\u064A \\u0646\\u0647\\u0627\\u064A\\u0629 \\u0627\\u0644\\u0628\\u0631\\u0646\\u0627\\u0645\\u062C. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 24);\n          i0.ɵɵelement(67, \"img\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 28)(69, \"div\", 13)(70, \"div\", 14);\n          i0.ɵɵelement(71, \"img\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 16)(73, \"p\", 17);\n          i0.ɵɵtext(74, \"\\u0631\\u0633\\u0627\\u0644\\u062A\\u0646\\u0627\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\", 18);\n          i0.ɵɵtext(76, \" \\u062A\\u062D\\u0641\\u064A\\u0638 \\u0648\\u062A\\u0639\\u0644\\u064A\\u0645 \\u0627\\u0644\\u0633\\u0646\\u0629 \\u0627\\u0644\\u0646\\u0628\\u0648\\u064A\\u0629\\u060C \\u0639\\u0628\\u0631 \\u0627\\u0644\\u062A\\u0642\\u0646\\u064A\\u0629 \\u0627\\u0644\\u062D\\u062F\\u064A\\u062B\\u0629 \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.redlogo, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.maskGroup_5, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.maskGroup_6, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.maskGroup_8, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.maskGroup_7, i0.ɵɵsanitizeUrl);\n        }\n      },\n      styles: [\"[_ngcontent-%COMP%]:root{--union: \\\"\\\"}.about_us_container[_ngcontent-%COMP%]:lang(ar){text-align:right}.about_us_container[_ngcontent-%COMP%]:lang(en){text-align:left}.about_us_container[_ngcontent-%COMP%]   .header_partion[_ngcontent-%COMP%]{color:var(--main_color);font-size:3rem;font-weight:700}.about_us_container[_ngcontent-%COMP%]   .border_bottom[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--main_color)}.about_us_container[_ngcontent-%COMP%]   .about_us[_ngcontent-%COMP%]{color:var(--main_color);font-size:1rem;font-weight:700;margin:2rem}.about_us_container[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]{color:#000;font-size:1.1rem;font-weight:700;padding:0 4rem;margin:1rem 0rem;width:80%;line-height:1.7}.about_us_container[_ngcontent-%COMP%]   .bag_about_us[_ngcontent-%COMP%]{background-image:var(--union);background-size:cover;background-position:center;background-repeat:no-repeat;width:100%;border:.125rem solid transparent;border-radius:0 1.5rem 1.5rem 0;height:26.9rem;align-self:center}.about_us_container[_ngcontent-%COMP%]   .img_aboutHofaz[_ngcontent-%COMP%]{padding:2rem;background-color:#fbfbfb;border-radius:1.25rem;border:.063rem solid transparent;text-align:center;width:16.25rem;height:14.75rem;position:absolute;top:22%}.about_us_container[_ngcontent-%COMP%]   .img_aboutHofaz[_ngcontent-%COMP%]:lang(ar){right:-26%}.about_us_container[_ngcontent-%COMP%]   .img_aboutHofaz[_ngcontent-%COMP%]:lang(en){left:-26%}.about_us_container[_ngcontent-%COMP%]   .img_logo[_ngcontent-%COMP%]{width:11.25rem;height:8.625rem}.about_us_container[_ngcontent-%COMP%]   .vation_card[_ngcontent-%COMP%]{background:#3a3a3a;border-radius:1.1875rem;box-shadow:0 .188rem 1rem #fbfbfb;opacity:1;padding:0rem;text-decoration:none;box-sizing:border-box;height:100%}@media (max-width: 78.125rem){.about_us_container[_ngcontent-%COMP%]   .vation_card[_ngcontent-%COMP%]{margin-bottom:3rem}}.about_us_container[_ngcontent-%COMP%]   .goal_card[_ngcontent-%COMP%]{background:#b3b3b3;border-radius:1.1875rem;box-shadow:0 .188rem 1rem #fbfbfb;opacity:1;padding:1rem;text-decoration:none;box-sizing:border-box}@media (max-width: 78.125rem){.about_us_container[_ngcontent-%COMP%]   .goal_card[_ngcontent-%COMP%]{margin-bottom:3rem}}.about_us_container[_ngcontent-%COMP%]   .img_vation[_ngcontent-%COMP%]{height:11.1875rem;width:12.9375rem;text-align:left}.about_us_container[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#fff}.about_us_container[_ngcontent-%COMP%]   .text_goal[_ngcontent-%COMP%]{color:var(--main_color)}.about_us_container[_ngcontent-%COMP%]   .info_vation[_ngcontent-%COMP%]{font-size:1.1rem;color:#fff;width:50%;line-height:1.7}.about_us_container[_ngcontent-%COMP%]   .list_ourgoal[_ngcontent-%COMP%]{color:#000;font-size:1.1rem;line-height:1.4}.about_us_container[_ngcontent-%COMP%]   .icon6[_ngcontent-%COMP%]{width:100%;height:100%}.about_us_container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin-left:0;margin-right:0}@media (max-width: 78.125rem){.about_us_container[_ngcontent-%COMP%]   .margin-card[_ngcontent-%COMP%]{margin:0!important}}@media (max-width: 78.125rem){.about_us_container[_ngcontent-%COMP%]   .padding-card[_ngcontent-%COMP%]{padding:0!important}}.about_us_container[_ngcontent-%COMP%]   .w-98[_ngcontent-%COMP%]{width:98%}@media all and (min-width: 19in){.img_aboutHofaz[_ngcontent-%COMP%]{right:-8.25rem!important;top:23%!important}}@media (max-width: 78.125rem){.margin-responsive[_ngcontent-%COMP%]{margin-bottom:3rem}.list_ourgoal[_ngcontent-%COMP%]{line-height:2.4!important}}\"]\n    });\n  }\n  return LandingAboutHofaazComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}