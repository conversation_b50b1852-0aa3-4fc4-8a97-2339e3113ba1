{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { AdminStudentStatisticsCountsChartComponent } from 'src/app/shared/components/admin-student-statistics-counts-chart/admin-student-statistics-counts-chart.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/studentStatistics/student-statistics.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"src/app/core/services/program-batches-service/program-batches.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"@angular/forms\";\nfunction AdminDashBoardStusDescCountWidgetsComponent_ng_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bat_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", bat_r1.batId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? bat_r1.progBatNameEn : bat_r1.progBatNameAr, \" \");\n  }\n}\nfunction AdminDashBoardStusDescCountWidgetsComponent_app_admin_student_statistics_counts_chart_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-admin-student-statistics-counts-chart\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"adminStudentTabStatistics\", ctx_r1.adminStudentTabStatistics);\n  }\n}\nexport let AdminDashBoardStusDescCountWidgetsComponent = /*#__PURE__*/(() => {\n  class AdminDashBoardStusDescCountWidgetsComponent {\n    studentStatisticsService;\n    translate;\n    alertify;\n    programBatchesService;\n    adminStudentNotificationsChartComponent;\n    adminStudentTabStatistics;\n    allProgBatchs = [];\n    batId;\n    langEnum = LanguageEnum;\n    constructor(studentStatisticsService, translate, alertify, programBatchesService) {\n      this.studentStatisticsService = studentStatisticsService;\n      this.translate = translate;\n      this.alertify = alertify;\n      this.programBatchesService = programBatchesService;\n    }\n    ngOnInit() {\n      this.getAllProgramBatches();\n    }\n    getAdminStudentTabStatistics() {\n      this.studentStatisticsService.getAdminStudentTabStatistics(this.batId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.adminStudentTabStatistics = res.data;\n          this.adminStudentNotificationsChartComponent?.initiate(this.adminStudentTabStatistics || {});\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    getAllProgramBatches() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n          this.batId = this.allProgBatchs[0].batId;\n          this.getAdminStudentTabStatistics();\n        } else {\n          this.alertify.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.getAdminStudentTabStatistics();\n    }\n    static ɵfac = function AdminDashBoardStusDescCountWidgetsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminDashBoardStusDescCountWidgetsComponent)(i0.ɵɵdirectiveInject(i1.StudentStatisticsService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.ProgramBatchesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashBoardStusDescCountWidgetsComponent,\n      selectors: [[\"app-admin-dash-board-stus-desc-count-widgets\"]],\n      viewQuery: function AdminDashBoardStusDescCountWidgetsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AdminStudentStatisticsCountsChartComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.adminStudentNotificationsChartComponent = _t.first);\n        }\n      },\n      decls: 15,\n      vars: 13,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"align-items-center\", \"mx-0\", \"pt-3\", \"mt-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"col-6\", \"px-0\"], [1, \"head\"], [1, \"col-6\"], [\"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"placeholder\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\"], [3, \"adminStudentTabStatistics\", 4, \"ngIf\"], [3, \"adminStudentTabStatistics\"]],\n      template: function AdminDashBoardStusDescCountWidgetsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"ng-select\", 5);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminDashBoardStusDescCountWidgetsComponent_Template_ng_select_ngModelChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminDashBoardStusDescCountWidgetsComponent_Template_ng_select_change_7_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementStart(9, \"ng-option\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AdminDashBoardStusDescCountWidgetsComponent_ng_option_12_Template, 2, 2, \"ng-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8);\n          i0.ɵɵtemplate(14, AdminDashBoardStusDescCountWidgetsComponent_app_admin_student_statistics_counts_chart_14_Template, 1, 1, \"app-admin-student-statistics-counts-chart\", 9);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(5, 7, \"ADMIN_DASH_BORD.STUDENT_NUMBER\"), \" \", ctx.adminStudentTabStatistics == null ? null : ctx.adminStudentTabStatistics.registeredStdModel == null ? null : ctx.adminStudentTabStatistics.registeredStdModel.registeredStdCount, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(8, 9, \"PROGRAMS_LIST.PROGRAM_NAME\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 11, \"ADMIN_DASH_BORD.ALL_PROGRAMS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allProgBatchs);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.adminStudentTabStatistics);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectComponent, i6.NgOptionComponent, i7.NgControlStatus, i7.NgModel, i2.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}\"]\n    });\n  }\n  return AdminDashBoardStusDescCountWidgetsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}