{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/data/Projects/Mostaneer/huffadh-white-label-app - Copy/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i2 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"src/app/core/services/loader-services/loader.service\";\nfunction GroupViewComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function GroupViewComponent_ng_container_10_Template_div_click_1_listener() {\n      const ctx_r1 = i0.ɵɵrestoreView(_r1);\n      const groupOfItem_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      ctx_r4.getGroupDetails(groupOfItem_r3);\n      return i0.ɵɵresetView(ctx_r4.selectedIndex = i_r4);\n    });\n    i0.ɵɵelementStart(2, \"div\", 8)(3, \"p\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 8)(8, \"p\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 12);\n    i0.ɵɵlistener(\"click\", function GroupViewComponent_ng_container_10_Template_span_click_10_listener() {\n      const groupOfItem_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.deleteGroup(groupOfItem_r3.participants, groupOfItem_r3.key));\n    });\n    i0.ɵɵelement(11, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 12);\n    i0.ɵɵlistener(\"click\", function GroupViewComponent_ng_container_10_Template_span_click_12_listener() {\n      const groupOfItem_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editGroup(groupOfItem_r3));\n    });\n    i0.ɵɵelement(13, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const groupOfItem_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r4.selectedIndex === i_r4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \" \", groupOfItem_r3.group_name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r3.group_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r3.last_date, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"\", groupOfItem_r3.last_message, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r3.last_message, \" \");\n  }\n}\nexport let GroupViewComponent = /*#__PURE__*/(() => {\n  class GroupViewComponent {\n    alertify;\n    chatService;\n    datepipe;\n    translate;\n    dialog;\n    loaderService;\n    createGroupOverlayEvent = new EventEmitter();\n    groupDetailsEvent = new EventEmitter();\n    groupDetailsForEditEvent = new EventEmitter();\n    groupDetailsForDeleteEvent = new EventEmitter();\n    listOfGroups = [];\n    selectedIndex = 0;\n    langEnum = LanguageEnum;\n    groupFilter = {};\n    constructor(alertify, chatService, datepipe, translate, dialog, loaderService) {\n      this.alertify = alertify;\n      this.chatService = chatService;\n      this.datepipe = datepipe;\n      this.translate = translate;\n      this.dialog = dialog;\n      this.loaderService = loaderService;\n    }\n    ngOnInit() {\n      /*\n       stopping loading started in parent component 'chat-view'.\n       please reviste parent component for more details\n      */\n      this.loaderService.isLoading.next(false);\n      this.getGroupDetails(this.chatService.allChatGroupsList[this.selectedIndex]);\n    }\n    deleteGroup(listOfUsers, id) {\n      var _this = this;\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this Group\" : \"هل متأكد من حذف هذه المجموعة\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Group' : 'حذف المجموعة', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (dialogResult) {\n          if (dialogResult == true) {\n            _this.chatService.deleteGroupParticipants(listOfUsers || [], id || '');\n            _this.listOfGroups = _this.chatService.allChatGroupsList;\n            _this.alertify.success(_this.translate.currentLang === LanguageEnum.en ? \"Group Deleted Successfully\" : 'تم حذف المجموعة بنجاح');\n            yield _this.chatService.getAllChatGroups();\n            if (_this.chatService.allChatGroupsList && _this.chatService.allChatGroupsList.length > 0) {\n              _this.selectedIndex = 0;\n              _this.getGroupDetails(_this.chatService.allChatGroupsList[_this.selectedIndex]);\n            } else {\n              _this.getGroupDetails({});\n            }\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n      // this.chatService.getAllChatGroups();    \n    }\n    editGroup(event) {\n      this.groupDetailsForEditEvent.emit(event);\n    }\n    showAdd() {\n      this.createGroupOverlayEvent.emit(true);\n    }\n    // deleteGroupDetails(event: IGroupChat){\n    //   this.groupDetailsForDeleteEvent.emit(event);\n    // }\n    getGroupDetails(event) {\n      this.groupDetailsEvent.emit(event);\n    }\n    filterByText(searchKey) {\n      if (searchKey.length > 0) {\n        this.chatService.allChatGroupsList = this.chatService.allChatGroupsList.filter(x => x.group_name?.includes(searchKey));\n      } else {\n        this.chatService.allChatGroupsList = this.chatService.allChatGroupsList;\n      }\n    }\n    static ɵfac = function GroupViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GroupViewComponent)(i0.ɵɵdirectiveInject(i1.AlertifyService), i0.ɵɵdirectiveInject(i2.ChatService), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.LoaderService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupViewComponent,\n      selectors: [[\"app-group-view\"]],\n      outputs: {\n        createGroupOverlayEvent: \"createGroupOverlayEvent\",\n        groupDetailsEvent: \"groupDetailsEvent\",\n        groupDetailsForEditEvent: \"groupDetailsForEditEvent\",\n        groupDetailsForDeleteEvent: \"groupDetailsForDeleteEvent\"\n      },\n      decls: 11,\n      vars: 7,\n      consts: [[1, \"list-group\", \"teacher-list-request\"], [1, \"mb-0\", \"head_groupName\"], [1, \"teacher-list-request__addGroup\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mx-2\"], [3, \"searchTerm\"], [1, \"internal_scroll_list_group\"], [4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"cursor-pointer\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"ellipsis\", \"p-0\", \"group_name\", 3, \"title\"], [1, \"last_date\"], [1, \"ellipsis\", \"p-0\", \"last_message\", 3, \"title\"], [1, \"trash_icon\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\"], [1, \"fas\", \"fa-edit\"]],\n      template: function GroupViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"p\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"a\", 2);\n          i0.ɵɵlistener(\"click\", function GroupViewComponent_Template_a_click_4_listener() {\n            return ctx.showAdd();\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelement(7, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"app-search-input\", 4);\n          i0.ɵɵlistener(\"searchTerm\", function GroupViewComponent_Template_app_search_input_searchTerm_8_listener($event) {\n            return ctx.filterByText($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 5);\n          i0.ɵɵtemplate(10, GroupViewComponent_ng_container_10_Template, 14, 9, \"ng-container\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"CHAT_GROUP.GROUPS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 5, \"CHAT_GROUP.ADD_NEW_GROUP\"), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.chatService.allChatGroupsList);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.TranslatePipe],\n      styles: [\".teacher-list-request[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:75vh;margin-top:1rem}.teacher-list-request[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]{color:#333;font-size:1.23rem;font-weight:700;padding-bottom:1rem}.teacher-list-request[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]{height:65vh;overflow-y:auto;height:63vh;overflow-x:hidden;padding:.3rem}.teacher-list-request[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.teacher-list-request[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.teacher-list-request__addGroup[_ngcontent-%COMP%]{color:var(--second_color);font-size:.85rem;font-weight:700;justify-content:flex-end;display:flex;align-items:center}.teacher-list-request[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;color:gray;border-radius:.75rem;letter-spacing:0;margin-bottom:1rem;opacity:1;margin-top:1rem;font-weight:700;font-size:1rem;justify-content:space-between;padding:.5rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.937rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .last_date[_ngcontent-%COMP%]{color:#333;font-size:.563rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .last_message[_ngcontent-%COMP%]{color:#333;font-size:.625rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .trash_icon[_ngcontent-%COMP%]{color:var(--main_color);margin-right:.1rem;margin-left:.2rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .trash_icon[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .last_message[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .last_date[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .trash_icon[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .last_message[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .last_date[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .group_name[_ngcontent-%COMP%]{color:#fff}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:80%}@media screen and (max-width: 64rem){.teacher-list-request[_ngcontent-%COMP%]{padding:.8rem}}\"]\n    });\n  }\n  return GroupViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}