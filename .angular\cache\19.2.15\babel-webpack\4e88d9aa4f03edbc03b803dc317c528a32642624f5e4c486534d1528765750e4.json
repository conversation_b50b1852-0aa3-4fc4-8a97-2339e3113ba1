{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/question-bank-services/question-bank-category.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i8 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/tooltip\";\nconst _c0 = a0 => [a0];\nconst _c1 = a0 => ({\n  \"display_hidden\": a0\n});\nconst _c2 = a0 => ({\n  \"white\": a0\n});\nfunction QuestionBankCategoriesViewComponent_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_a_4_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.newScientificProblem());\n    });\n    i0.ɵɵelementStart(1, \"span\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"img\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"QUESTION_BANK.ADD_SCIENTIFIC_PROBLEM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionBankCategoriesViewComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_a_5_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.newCatogry());\n    });\n    i0.ɵɵelementStart(1, \"span\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"img\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"QUESTION_BANK.ADD_DEPARTMENT\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionBankCategoriesViewComponent_mat_expansion_panel_14_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_mat_expansion_panel_14_div_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const QBC_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmDialog(QBC_r7.id));\n    });\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_mat_expansion_panel_14_div_7_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const QBC_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadCatogry(QBC_r7.id));\n    });\n    i0.ɵɵelement(4, \"i\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r8 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c2, i_r8 == 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, i_r8 == 0));\n  }\n}\nfunction QuestionBankCategoriesViewComponent_mat_expansion_panel_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 13)(1, \"mat-expansion-panel-header\", 14)(2, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_mat_expansion_panel_14_Template_a_click_2_listener() {\n      const ctx_r5 = i0.ɵɵrestoreView(_r5);\n      const QBC_r7 = ctx_r5.$implicit;\n      const i_r8 = ctx_r5.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.loadCatogryQuiestion(QBC_r7.id, QBC_r7.arabCatgName, QBC_r7.engCatgName);\n      return i0.ɵɵresetView(ctx_r2.selectedIndex = i_r8);\n    });\n    i0.ɵɵelementStart(3, \"p\", 7)(4, \"span\");\n    i0.ɵɵelement(5, \"img\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, QuestionBankCategoriesViewComponent_mat_expansion_panel_14_div_7_Template, 5, 6, \"div\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const QBC_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, i_r8 == 0));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedIndex === i_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r2.translate.currentLang === ctx_r2.langEnum.en ? QBC_r7.engCatgName : QBC_r7.arabCatgName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.arrange, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.translate.currentLang === ctx_r2.langEnum.en ? QBC_r7.engCatgName : QBC_r7.arabCatgName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.roleManagmentService.isAdmin());\n  }\n}\nexport let QuestionBankCategoriesViewComponent = /*#__PURE__*/(() => {\n  class QuestionBankCategoriesViewComponent {\n    questionBankCategoryService;\n    activeroute;\n    router;\n    translate;\n    fb;\n    alert;\n    dialog;\n    roleManagmentService;\n    imagesPathesService;\n    adminMode = false;\n    filterErrorMessage;\n    questionBankCategoryList = [];\n    questionBankCategoryFilter = {};\n    position = \"\";\n    title;\n    questionBankCategoryId = '';\n    questionBankCategory;\n    questionBankCategoryCreat = {};\n    questionBankCategoryUpdate = {};\n    isAdd = true;\n    errorMessage;\n    currentForm = new FormGroup({});\n    formImport;\n    successMessage;\n    isSubmit = false;\n    selectedCategoryId = new EventEmitter();\n    inputCategoryId = new EventEmitter();\n    openScientificProblem = new EventEmitter();\n    clickChangeCtogry = \"\";\n    resultMessage = {};\n    disableSaveButtons = false;\n    langEnum = LanguageEnum;\n    addCategory = false;\n    currentUser;\n    role = RoleEnum;\n    items1;\n    questionBankCategoryUpdateOrder = {};\n    listOrder;\n    constructor(questionBankCategoryService, activeroute, router, translate, fb, alert, dialog, roleManagmentService, imagesPathesService) {\n      this.questionBankCategoryService = questionBankCategoryService;\n      this.activeroute = activeroute;\n      this.router = router;\n      this.translate = translate;\n      this.fb = fb;\n      this.alert = alert;\n      this.dialog = dialog;\n      this.roleManagmentService = roleManagmentService;\n      this.imagesPathesService = imagesPathesService;\n      this.formImport = new FormGroup({\n        importFile: new FormControl('', Validators.required)\n      });\n    }\n    ngOnInit() {\n      this.selectedIndex = 0;\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.getQuestionBankCategories();\n      this.buildForm();\n      if (this.addCategory === true) {\n        this.getQuestionBankCategories();\n      }\n    }\n    ngOnChanges(changes) {\n      if (this.addCategory == true) {\n        this.getQuestionBankCategories();\n      }\n    }\n    get f() {\n      return this.currentForm?.controls;\n    }\n    buildForm() {\n      this.currentForm = this.fb.group({\n        nameAr: ['', Validators.required],\n        nameEn: ['', Validators.required]\n      });\n    }\n    getQuestionBankCategories(name) {\n      this.questionBankCategoryId = \"\";\n      this.filterErrorMessage = \"\";\n      if (name != null || name != \"\") {\n        this.questionBankCategoryFilter.catgName = name;\n      }\n      this.questionBankCategoryFilter.skip = 0;\n      this.questionBankCategoryFilter.take = **********;\n      this.questionBankCategoryService.getQuestionBankCategoriesFilter(this.questionBankCategoryFilter).subscribe(res => {\n        let response = res;\n        if (response.isSuccess) {\n          this.questionBankCategoryList = response.data;\n          if (this.addCategory === false) {\n            this.loadCatogryQuiestion(this.questionBankCategoryList[0]?.id, this.questionBankCategoryList[0]?.arabCatgName, this.questionBankCategoryList[0]?.engCatgName);\n          }\n        } else {\n          this.questionBankCategoryList = [];\n          this.filterErrorMessage = response.message;\n        }\n      }, error => {\n        //logging\n      });\n    }\n    clearFilter() {\n      this.questionBankCategoryFilter = {};\n      this.questionBankCategoryFilter.skip = 0;\n      this.questionBankCategoryFilter.take = 100;\n      this.getQuestionBankCategories();\n    }\n    ChangCTg(categoryId) {\n      this.router.navigateByUrl('/questionBank/question-bank-questions-view/' + categoryId);\n    }\n    loadQuestionBankCategoryDetails(id) {\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n      this.isAdd = false;\n      this.questionBankCategoryId = id || '';\n      this.questionBankCategoryService.getQuestionBankCategoryDetails(this.questionBankCategoryId).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.questionBankCategory = response.data;\n          this.f.nameAr.setValue(this.questionBankCategory?.arabCatgName);\n          this.f.nameEn.setValue(this.questionBankCategory?.engCatgName);\n        } else {\n          this.errorMessage = response.message;\n        }\n      }, error => {\n        //logging\n      });\n    }\n    Submit() {\n      this.isSubmit = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.resultMessage = {};\n      if (this.questionBankCategoryId) {\n        this.questionBankCategoryUpdate.id = this.questionBankCategoryId;\n        this.questionBankCategoryUpdate.no = this.questionBankCategory?.no;\n        this.questionBankCategoryUpdate.arabCatgName = this.f.nameAr.value;\n        this.questionBankCategoryUpdate.engCatgName = this.f.nameEn.value;\n        this.questionBankCategoryService.UpdateQuestionBankCategory(this.questionBankCategoryUpdate).subscribe(res => {\n          if (res.isSuccess) {\n            this.isSubmit = false;\n            // this.successMessage = res.message;\n            this.disableSaveButtons = true;\n            this.resultMessage = {\n              message: res.message || \"\",\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n            setTimeout(() => {\n              this.getQuestionBankCategories();\n            }, 1500);\n          } else {\n            // this.errorMessage = res.message;\n            this.disableSaveButtons = false;\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.questionBankCategoryCreat.arabCatgName = this.f.nameAr.value;\n        this.questionBankCategoryCreat.engCatgName = this.f.nameEn.value;\n        this.questionBankCategoryService.addQuestionBankCategory(this.questionBankCategoryCreat).subscribe(res => {\n          this.isSubmit = false;\n          if (res.isSuccess) {\n            this.disableSaveButtons = true;\n            this.resultMessage = {\n              message: res.message || \"\",\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n            setTimeout(() => {\n              this.getQuestionBankCategories();\n            }, 1500);\n          } else {\n            // this.errorMessage = res.message;\n            this.disableSaveButtons = false;\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    addCatogry() {\n      this.currentForm.reset();\n      this.isAdd = true;\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n    }\n    back_list_Catogry() {\n      this.isAdd = false;\n    }\n    selectedIndex;\n    loadCatogryQuiestion(id, arabCatgName, engCatgName) {\n      this.selectedCategoryId.emit({\n        id: id,\n        arabCatgName: arabCatgName,\n        engCatgName: engCatgName\n      });\n    }\n    result = '';\n    confirmDialog(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this department\" : \"هل متأكد من حذف هذا القسم\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Category' : 'حذف قسم', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n        if (dialogResult == true) {\n          this.questionBankCategoryService.deleteQuestionBankCategory(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alert.success(res.message || '');\n              this.getQuestionBankCategories();\n            } else {\n              this.alert.error(res.message || '');\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    selectedIndex;\n    loadCatogry(id) {\n      this.inputCategoryId?.emit(id);\n    }\n    newCatogry() {\n      this.inputCategoryId?.emit('');\n    }\n    newScientificProblem() {\n      this.openScientificProblem.emit(true);\n    }\n    drop(event) {\n      if (event.previousContainer === event.container) {\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n        this.listOrder = [];\n        for (let i = 0; i <= event.container.data.length - 1; i++) {\n          this.listOrder?.push(event.previousContainer.data[i].order || i + 1);\n        }\n        this.questionBankCategoryUpdateOrder.orderList = this.listOrder;\n        this.questionBankCategoryService.UpdateOrderQuestionBankCategories(this.questionBankCategoryUpdateOrder).subscribe(res => {\n          //if (res.isSuccess) {\n          //  this.getQuestionBankCategories();\n          //}\n          //else {\n          //}\n        }, error => {\n          //logging\n        });\n      } else {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      }\n    }\n    static ɵfac = function QuestionBankCategoriesViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionBankCategoriesViewComponent)(i0.ɵɵdirectiveInject(i1.QuestionBankCategoryService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.RoleManagementService), i0.ɵɵdirectiveInject(i8.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuestionBankCategoriesViewComponent,\n      selectors: [[\"app-question-bank-categories-view\"]],\n      inputs: {\n        addCategory: \"addCategory\"\n      },\n      outputs: {\n        selectedCategoryId: \"selectedCategoryId\",\n        inputCategoryId: \"inputCategoryId\",\n        openScientificProblem: \"openScientificProblem\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 15,\n      vars: 12,\n      consts: [[\"unassignedList\", \"cdkDropList\"], [1, \"list-group\", \"list-category\"], [1, \"mb-4\", \"p_header\", \"mb-2\"], [\"class\", \"mb-2 add_scientfic_problem\", 3, \"click\", 4, \"ngIf\"], [3, \"searchTerm\"], [1, \"internal_scroll_list_group\"], [1, \"list-group-item\", \"d-flex\", \"w-100\", \"mb-0\", \"selectedFirst\", 3, \"click\"], [1, \"ellipsis\", \"mb-0\", \"question-categories\", 3, \"matTooltip\"], [\"cdkDropList\", \"\", 1, \"ques_bank\", 3, \"cdkDropListDropped\", \"cdkDropListData\", \"cdkDropListConnectedTo\"], [\"cdkDrag\", \"\", \"disabled\", \"\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-2\", \"add_scientfic_problem\", 3, \"click\"], [1, \"link_Add\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [\"cdkDrag\", \"\", \"disabled\", \"\"], [3, \"ngClass\"], [1, \"list-group-item\", \"d-flex\", \"w-100\", 3, \"click\"], [1, \"arrange\", 3, \"src\"], [\"class\", \"d-flex \", 4, \"ngIf\"], [1, \"d-flex\"], [3, \"click\"], [1, \"far\", \"fa-trash-alt\", 3, \"ngClass\"], [1, \"fas\", \"fa-pencil-alt\", 3, \"ngClass\"]],\n      template: function QuestionBankCategoriesViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"p\", 2);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, QuestionBankCategoriesViewComponent_a_4_Template, 5, 4, \"a\", 3)(5, QuestionBankCategoriesViewComponent_a_5_Template, 5, 4, \"a\", 3);\n          i0.ɵɵelementStart(6, \"app-search-input\", 4);\n          i0.ɵɵlistener(\"searchTerm\", function QuestionBankCategoriesViewComponent_Template_app_search_input_searchTerm_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getQuestionBankCategories($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"a\", 6);\n          i0.ɵɵlistener(\"click\", function QuestionBankCategoriesViewComponent_Template_a_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadCatogryQuiestion(ctx.questionBankCategoryList[0].id, ctx.questionBankCategoryList[0].arabCatgName, ctx.questionBankCategoryList[0].engCatgName));\n          });\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementContainerStart(11);\n          i0.ɵɵelementStart(12, \"mat-accordion\", 8, 0);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function QuestionBankCategoriesViewComponent_Template_mat_accordion_cdkDropListDropped_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.drop($event));\n          });\n          i0.ɵɵtemplate(14, QuestionBankCategoriesViewComponent_mat_expansion_panel_14_Template, 8, 9, \"mat-expansion-panel\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 8, \"QUESTION_BANK.DEPARTMENTS\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.roleManagmentService.isStudent());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.roleManagmentService.isAdmin());\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx.translate.currentLang === ctx.langEnum.en ? ctx.questionBankCategoryList[0].engCatgName : ctx.questionBankCategoryList[0].arabCatgName);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.questionBankCategoryList[0].engCatgName : ctx.questionBankCategoryList[0].arabCatgName, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"cdkDropListData\", ctx.questionBankCategoryList)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(10, _c0, ctx.items1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.questionBankCategoryList);\n        }\n      },\n      dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i10.MatTooltip, i3.TranslatePipe],\n      styles: [\".list-category[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:85vh;margin-top:1rem}.list-category[_ngcontent-%COMP%]:lang(en){margin-left:1rem}.list-category[_ngcontent-%COMP%]:lang(ar){margin-right:0rem}.list-category[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-inline-start:0!important}.list-category[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-category[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:lang(ar){text-align:right}.list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-weight:700;font-size:1rem;justify-content:space-between}.list-category[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .fa-pencil-alt[_ngcontent-%COMP%]{color:var(--main_color);margin-right:.2rem;margin-left:.2rem}.list-category[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-pencil-alt[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-pencil-alt[_ngcontent-%COMP%]{color:#fff}.list-category[_ngcontent-%COMP%]   .add_scientfic_problem[_ngcontent-%COMP%]:lang(en){text-align:right}.list-category[_ngcontent-%COMP%]   .add_scientfic_problem[_ngcontent-%COMP%]:lang(ar){text-align:left}.list-category[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:61vh;overflow-y:auto}.list-category[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:61vh;overflow-y:auto}.list-category[_ngcontent-%COMP%]   .Catogry_Icon_Sort[_ngcontent-%COMP%]{padding:.5rem;background-color:#fbfbfb;margin-right:.3rem;margin-left:.3rem;border-radius:.5rem;width:1;height:1.813rem;align-self:center}.list-category[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:.3rem;margin-left:.3rem}.list-category[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{color:var(--second_color)}.list-category[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]{font-size:large;font-weight:700}.list-category[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{font: 1.25rem Almarai;color:#000;padding-top:2rem}.list-category[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(en){text-align:left;padding-left:2rem}.list-category[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(ar){text-align:right;padding-right:2rem}.list-category[_ngcontent-%COMP%]   .question-categories[_ngcontent-%COMP%]:lang(ar){text-align:right}.list-category[_ngcontent-%COMP%]   .ques_bank.mat-accordion[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]{margin-bottom:.8rem;border-radius:.75rem;box-shadow:none}.list-category[_ngcontent-%COMP%]   .ques_bank.mat-accordion[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{padding:0 0rem;width:100%}.list-category[_ngcontent-%COMP%]   .ques_bank.mat-accordion[_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]{justify-content:space-between;color:var(--main_color);margin-left:0;font-size:1rem;font-weight:700}.list-category[_ngcontent-%COMP%]   .display_hidden[_ngcontent-%COMP%]{display:none}.list-category[_ngcontent-%COMP%]   .selectedFirst[_ngcontent-%COMP%]{background-color:var(--main_color)!important;color:#fff!important;border:.063rem solid var(--main_color)}.list-category[_ngcontent-%COMP%]   .selectedFirst[_ngcontent-%COMP%]   .fa-pencil-alt.white[_ngcontent-%COMP%], .list-category[_ngcontent-%COMP%]   .selectedFirst[_ngcontent-%COMP%]   .fa-trash-alt.white[_ngcontent-%COMP%]{color:#fff!important}\"]\n    });\n  }\n  return QuestionBankCategoriesViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}