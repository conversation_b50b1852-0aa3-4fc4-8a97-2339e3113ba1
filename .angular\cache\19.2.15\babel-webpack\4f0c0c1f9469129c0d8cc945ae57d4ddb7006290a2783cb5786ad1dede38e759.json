{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../../util/number.js';\nvar each = zrUtil.each;\nexport default function boxplotLayout(ecModel) {\n  var groupResult = groupSeriesByAxis(ecModel);\n  each(groupResult, function (groupItem) {\n    var seriesModels = groupItem.seriesModels;\n    if (!seriesModels.length) {\n      return;\n    }\n    calculateBase(groupItem);\n    each(seriesModels, function (seriesModel, idx) {\n      layoutSingleSeries(seriesModel, groupItem.boxOffsetList[idx], groupItem.boxWidthList[idx]);\n    });\n  });\n}\n/**\r\n * Group series by axis.\r\n */\nfunction groupSeriesByAxis(ecModel) {\n  var result = [];\n  var axisList = [];\n  ecModel.eachSeriesByType('boxplot', function (seriesModel) {\n    var baseAxis = seriesModel.getBaseAxis();\n    var idx = zrUtil.indexOf(axisList, baseAxis);\n    if (idx < 0) {\n      idx = axisList.length;\n      axisList[idx] = baseAxis;\n      result[idx] = {\n        axis: baseAxis,\n        seriesModels: []\n      };\n    }\n    result[idx].seriesModels.push(seriesModel);\n  });\n  return result;\n}\n/**\r\n * Calculate offset and box width for each series.\r\n */\nfunction calculateBase(groupItem) {\n  var baseAxis = groupItem.axis;\n  var seriesModels = groupItem.seriesModels;\n  var seriesCount = seriesModels.length;\n  var boxWidthList = groupItem.boxWidthList = [];\n  var boxOffsetList = groupItem.boxOffsetList = [];\n  var boundList = [];\n  var bandWidth;\n  if (baseAxis.type === 'category') {\n    bandWidth = baseAxis.getBandWidth();\n  } else {\n    var maxDataCount_1 = 0;\n    each(seriesModels, function (seriesModel) {\n      maxDataCount_1 = Math.max(maxDataCount_1, seriesModel.getData().count());\n    });\n    var extent = baseAxis.getExtent();\n    bandWidth = Math.abs(extent[1] - extent[0]) / maxDataCount_1;\n  }\n  each(seriesModels, function (seriesModel) {\n    var boxWidthBound = seriesModel.get('boxWidth');\n    if (!zrUtil.isArray(boxWidthBound)) {\n      boxWidthBound = [boxWidthBound, boxWidthBound];\n    }\n    boundList.push([parsePercent(boxWidthBound[0], bandWidth) || 0, parsePercent(boxWidthBound[1], bandWidth) || 0]);\n  });\n  var availableWidth = bandWidth * 0.8 - 2;\n  var boxGap = availableWidth / seriesCount * 0.3;\n  var boxWidth = (availableWidth - boxGap * (seriesCount - 1)) / seriesCount;\n  var base = boxWidth / 2 - availableWidth / 2;\n  each(seriesModels, function (seriesModel, idx) {\n    boxOffsetList.push(base);\n    base += boxGap + boxWidth;\n    boxWidthList.push(Math.min(Math.max(boxWidth, boundList[idx][0]), boundList[idx][1]));\n  });\n}\n/**\r\n * Calculate points location for each series.\r\n */\nfunction layoutSingleSeries(seriesModel, offset, boxWidth) {\n  var coordSys = seriesModel.coordinateSystem;\n  var data = seriesModel.getData();\n  var halfWidth = boxWidth / 2;\n  var cDimIdx = seriesModel.get('layout') === 'horizontal' ? 0 : 1;\n  var vDimIdx = 1 - cDimIdx;\n  var coordDims = ['x', 'y'];\n  var cDim = data.mapDimension(coordDims[cDimIdx]);\n  var vDims = data.mapDimensionsAll(coordDims[vDimIdx]);\n  if (cDim == null || vDims.length < 5) {\n    return;\n  }\n  for (var dataIndex = 0; dataIndex < data.count(); dataIndex++) {\n    var axisDimVal = data.get(cDim, dataIndex);\n    var median = getPoint(axisDimVal, vDims[2], dataIndex);\n    var end1 = getPoint(axisDimVal, vDims[0], dataIndex);\n    var end2 = getPoint(axisDimVal, vDims[1], dataIndex);\n    var end4 = getPoint(axisDimVal, vDims[3], dataIndex);\n    var end5 = getPoint(axisDimVal, vDims[4], dataIndex);\n    var ends = [];\n    addBodyEnd(ends, end2, false);\n    addBodyEnd(ends, end4, true);\n    ends.push(end1, end2, end5, end4);\n    layEndLine(ends, end1);\n    layEndLine(ends, end5);\n    layEndLine(ends, median);\n    data.setItemLayout(dataIndex, {\n      initBaseline: median[vDimIdx],\n      ends: ends\n    });\n  }\n  function getPoint(axisDimVal, dim, dataIndex) {\n    var val = data.get(dim, dataIndex);\n    var p = [];\n    p[cDimIdx] = axisDimVal;\n    p[vDimIdx] = val;\n    var point;\n    if (isNaN(axisDimVal) || isNaN(val)) {\n      point = [NaN, NaN];\n    } else {\n      point = coordSys.dataToPoint(p);\n      point[cDimIdx] += offset;\n    }\n    return point;\n  }\n  function addBodyEnd(ends, point, start) {\n    var point1 = point.slice();\n    var point2 = point.slice();\n    point1[cDimIdx] += halfWidth;\n    point2[cDimIdx] -= halfWidth;\n    start ? ends.push(point1, point2) : ends.push(point2, point1);\n  }\n  function layEndLine(ends, endCenter) {\n    var from = endCenter.slice();\n    var to = endCenter.slice();\n    from[cDimIdx] -= halfWidth;\n    to[cDimIdx] += halfWidth;\n    ends.push(from, to);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}