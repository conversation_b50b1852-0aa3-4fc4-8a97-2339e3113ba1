{"ast": null, "code": "import { ProgramDetailsComponent } from './components/program-details/program-details.component';\nimport { RouterModule } from '@angular/router';\nimport { AddProgramComponent } from './components/add-program/add-program.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProgramDetailsComponent\n}, {\n  path: 'add-program',\n  component: AddProgramComponent\n}, {\n  path: 'edit-program/:id',\n  component: AddProgramComponent\n}];\nexport let ProgramRoutingModule = /*#__PURE__*/(() => {\n  class ProgramRoutingModule {\n    static ɵfac = function ProgramRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProgramRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return ProgramRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}