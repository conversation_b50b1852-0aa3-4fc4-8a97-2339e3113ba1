{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { WrapperComponent } from './wrapper/wrapper.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: WrapperComponent,\n  outlet: 'baseRouter'\n}];\nexport let LandingPageRoutingModule = /*#__PURE__*/(() => {\n  class LandingPageRoutingModule {\n    static ɵfac = function LandingPageRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LandingPageRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LandingPageRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return LandingPageRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}