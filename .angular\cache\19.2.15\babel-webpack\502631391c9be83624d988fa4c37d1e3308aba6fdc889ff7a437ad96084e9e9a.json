{"ast": null, "code": "import { Validators, FormGroup } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/auth-services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = a0 => ({\n  \"error_input\": a0\n});\nfunction RegisterComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_REQUIRED\"), \" \");\n  }\n}\nfunction RegisterComponent_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_MAX_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_MIN_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_PATTERN\"), \" \");\n  }\n}\nfunction RegisterComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, RegisterComponent_div_21_div_1_Template, 3, 3, \"div\", 32)(2, RegisterComponent_div_21_div_2_Template, 3, 3, \"div\", 32)(3, RegisterComponent_div_21_div_3_Template, 3, 3, \"div\", 32)(4, RegisterComponent_div_21_div_4_Template, 3, 3, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors == null ? null : ctx_r0.f.userName.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors == null ? null : ctx_r0.f.userName.errors.minlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors == null ? null : ctx_r0.f.userName.errors.pattern);\n  }\n}\nfunction RegisterComponent_div_31_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EMAIL_REQUIRED\"), \" \");\n  }\n}\nfunction RegisterComponent_div_31_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EMAIL_PATTERN\"), \" \");\n  }\n}\nfunction RegisterComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, RegisterComponent_div_31_div_1_Template, 3, 3, \"div\", 32)(2, RegisterComponent_div_31_div_2_Template, 3, 3, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.email.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.email.errors.pattern);\n  }\n}\nfunction RegisterComponent_div_46_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_REQUIRED\"), \" \");\n  }\n}\nfunction RegisterComponent_div_46_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_MAX_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_46_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_MIN_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_46_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_TO_CONTAINER\"), \" \");\n  }\n}\nfunction RegisterComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, RegisterComponent_div_46_div_1_Template, 3, 3, \"div\", 32)(2, RegisterComponent_div_46_div_2_Template, 3, 3, \"div\", 32)(3, RegisterComponent_div_46_div_3_Template, 3, 3, \"div\", 32)(4, RegisterComponent_div_46_div_4_Template, 3, 3, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.minlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.pattern);\n  }\n}\nfunction RegisterComponent_div_59_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_REQUIRED\"), \" \");\n  }\n}\nfunction RegisterComponent_div_59_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_MAX_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_59_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_MIN_LENGTH\"), \" \");\n  }\n}\nfunction RegisterComponent_div_59_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_TO_CONTAINER\"), \" \");\n  }\n}\nfunction RegisterComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, RegisterComponent_div_59_div_1_Template, 3, 3, \"div\", 32)(2, RegisterComponent_div_59_div_2_Template, 3, 3, \"div\", 32)(3, RegisterComponent_div_59_div_3_Template, 3, 3, \"div\", 32)(4, RegisterComponent_div_59_div_4_Template, 3, 3, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.minlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.pattern);\n  }\n}\nfunction RegisterComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resMessage.message, \" \");\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    authService;\n    router;\n    fb;\n    route;\n    translate;\n    imagesPathesService;\n    routeParams;\n    signup = false;\n    editProfile = false;\n    registrationModel = {};\n    registerform = new FormGroup({});\n    hidePassword = true;\n    resMessage = {};\n    currentLang;\n    isSubmit = false;\n    roleType;\n    hidePasswordConfirm = true;\n    constructor(authService, router, fb, route, translate, imagesPathesService) {\n      this.authService = authService;\n      this.router = router;\n      this.fb = fb;\n      this.route = route;\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.routeParams = this.router.url;\n      this.roleType = this.route.snapshot.queryParamMap.get('type') || '';\n      this.loadUserForm();\n      this.currentLang = this.translate.currentLang === LanguageEnum.ar ? LanguageEnum.en : LanguageEnum.ar;\n      if (this.routeParams === '/auth/register') {\n        this.signup = true;\n        this.editProfile = false;\n      } else if (this.routeParams === '/user/edit-profile') {\n        this.signup = false;\n        this.editProfile = true;\n      }\n    }\n    get f() {\n      return this.registerform.controls;\n    }\n    loadUserForm() {\n      // let emailReg = '/^([\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4})?$/';\n      //\"/^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,}$/\"\n      //const regex = new RegExp('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$');\n      this.registerform = this.fb.group({\n        email: [\"\", [Validators.required, Validators.pattern(\"^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\\\.[a-z]{2,4}$\")]],\n        userName: [\"\", [Validators.required, Validators.minLength(3), Validators.maxLength(20), Validators.pattern(BaseConstantModel.USER_NAME_ENGLISH_NUMBERS_CHAR)]],\n        password: [\"\", [Validators.required, Validators.minLength(6), Validators.maxLength(12) /* Validators.pattern(BaseConstantModel.passwordPattern)*/]],\n        confirmPassword: [\"\", [Validators.required, Validators.minLength(6), Validators.maxLength(12) /*Validators.pattern(BaseConstantModel.passwordPattern)*/]]\n      });\n    }\n    onSignup(value) {\n      this.isSubmit = true;\n      this.resMessage = {};\n      if (this.registerform.valid && this.roleType !== \"\" && this.roleType !== undefined) {\n        let us = JSON.parse(localStorage.getItem(\"user\") || '{}');\n        if (us) {\n          localStorage.removeItem('user');\n        }\n        this.registrationModel = {\n          uname: this.registerform.value.userName,\n          uemail: this.registerform.value.email,\n          ucpass: this.registerform.value.confirmPassword,\n          //\"\"\n          upass: this.registerform.value.password,\n          roletype: this.roleType\n        };\n        this.authService.register(this.registrationModel).subscribe(res => {\n          if (res.isSuccess) {\n            localStorage.setItem('user', JSON.stringify(res.data));\n            this.router.navigateByUrl('/auth/(baseRouter:activate-code)');\n            this.isSubmit = false;\n          } else {\n            this.isSubmit = false;\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    togglePassword() {\n      this.hidePassword = !this.hidePassword;\n    }\n    togglePasswordConfirm() {\n      this.hidePasswordConfirm = !this.hidePasswordConfirm;\n    }\n    static ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 68,\n      vars: 65,\n      consts: [[1, \"row\", \"UserLogin\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"LoginBackground_left_img\", \"pl-0\", \"pr-0\"], [1, \"col-12\", \"identity_img\", \"pl-0\", \"pr-0\"], [1, \"row\", \"justify-content-center\"], [1, \"box\"], [1, \"form-group\", \"UserLogin__LoginForm\", 3, \"ngSubmit\", \"formGroup\"], [1, \"content_form\"], [1, \"col-lg-12\", \"mb-2\", \"mt-2\"], [1, \"font-weight-bold\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mb-2\"], [\"for\", \"UserEmail\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\", \"bg-white\", \"pl-2\", \"pr-2\", \"border-md\", 3, \"ngClass\"], [1, \"UserLogin_icon_input\", 3, \"src\"], [\"id\", \"userName\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"userName\", \"name\", \"userName\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"placeholder\", \"ngClass\"], [\"class\", \"error_message\", 4, \"ngIf\"], [\"id\", \"email\", \"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"name\", \"email\", \"placeholder\", \"Ex. <EMAIL>\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\"], [1, \"row\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-6\", \"mb-2\"], [\"for\", \"password\"], [1, \"toggle-password\", 3, \"click\"], [\"id\", \"password\", \"pPassword\", \"\", \"type\", \"password\", \"formControlName\", \"password\", \"name\", \"password\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"type\", \"placeholder\", \"ngClass\"], [\"for\", \"confirmPassword\"], [\"id\", \"confirmPassword\", \"pPassword\", \"\", \"type\", \"password\", \"formControlName\", \"confirmPassword\", \"name\", \"confirmPassword\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\", \"type\", \"placeholder\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\", \"mb-0\"], [\"type\", \"submit\", 1, \"btn\", \"UserLogin__Submit\"], [3, \"class\", 4, \"ngIf\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"LoginBackground_Right_img\"], [1, \"test\"], [1, \"col-12\", \"repeater_img\"], [1, \"error_message\"], [4, \"ngIf\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_5_listener() {\n            return ctx.onSignup(ctx.registerform.value);\n          });\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"h1\", 8);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"label\", 10);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"span\", 13);\n          i0.ɵɵelement(18, \"img\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(19, \"input\", 15);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, RegisterComponent_div_21_Template, 5, 4, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"label\", 10);\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 11)(27, \"div\", 12)(28, \"span\", 13);\n          i0.ɵɵelement(29, \"img\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(30, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, RegisterComponent_div_31_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 9)(33, \"div\", 18)(34, \"div\", 19)(35, \"label\", 20);\n          i0.ɵɵtext(36);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 11)(39, \"div\", 12)(40, \"span\", 13);\n          i0.ɵɵelement(41, \"img\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"mat-icon\", 21);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_mat_icon_click_42_listener() {\n            return ctx.togglePassword();\n          });\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(44, \"input\", 22);\n          i0.ɵɵpipe(45, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, RegisterComponent_div_46_Template, 5, 4, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 19)(48, \"label\", 23);\n          i0.ɵɵtext(49);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 11)(52, \"div\", 12)(53, \"span\", 13);\n          i0.ɵɵelement(54, \"img\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"mat-icon\", 21);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_mat_icon_click_55_listener() {\n            return ctx.togglePasswordConfirm();\n          });\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(57, \"input\", 24);\n          i0.ɵɵpipe(58, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, RegisterComponent_div_59_Template, 5, 4, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 25)(61, \"button\", 26);\n          i0.ɵɵtext(62);\n          i0.ɵɵpipe(63, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(64, RegisterComponent_div_64_Template, 2, 4, \"div\", 27);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(65, \"div\", 28);\n          i0.ɵɵelement(66, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(67, \"div\", 30);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerform);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 31, \"SIGNUP_PG.SIGN_UP\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 33, \"SIGNUP_PG.USERNAME\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c0, ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.email, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(20, 35, \"GENERAL.USERNAME\"));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c0, ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 37, \"GENERAL.EMAIL\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c0, ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.email, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c0, ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(37, 39, \"GENERAL.PASSWORD\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c0, ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.password, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(45, 41, \"GENERAL.PASSWORD\"));\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngClass\", i0.ɵɵpureFunction1(59, _c0, ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(50, 43, \"GENERAL.CONFIRM_PASSWORD\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c0, ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.password, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePasswordConfirm ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(58, 45, \"GENERAL.CONFIRM_PASSWORD\"));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c0, ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit)))(\"type\", ctx.hidePasswordConfirm ? \"password\" : \"text\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(63, 47, \"SIGNUP_PG.NEXT\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resMessage.message);\n        }\n      },\n      styles: [\".UserRegister[_ngcontent-%COMP%]   .UserRegister__Label[_ngcontent-%COMP%]{color:var(--second_color)}.UserRegister[_ngcontent-%COMP%]   .UserRegister__FormControl[_ngcontent-%COMP%]::placeholder{color:#b3b3b3;font-size:.9rem}.UserRegister[_ngcontent-%COMP%]   .UserRegister__Submit[_ngcontent-%COMP%]{background-color:var(--main_color);width:19.063rem;height:3.191rem;margin-left:auto;margin-right:auto;border:none;border-radius:1rem}.UserRegister[_ngcontent-%COMP%]   .UserLogin__SocialMedia[_ngcontent-%COMP%]{margin-right:6rem!important}\"]\n    });\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}