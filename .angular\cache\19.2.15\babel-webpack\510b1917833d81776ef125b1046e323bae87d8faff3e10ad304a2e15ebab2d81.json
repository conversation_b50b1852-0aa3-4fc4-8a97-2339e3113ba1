{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ProgressTestComponent = /*#__PURE__*/(() => {\n  class ProgressTestComponent {\n    constructor() {}\n    ngOnInit() {}\n    static ɵfac = function ProgressTestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgressTestComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgressTestComponent,\n      selectors: [[\"app-progress-test\"]],\n      decls: 8,\n      vars: 0,\n      consts: [[1, \"tab_page\"], [1, \"pt-3\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"col-md-12\", \"max_h\"]],\n      template: function ProgressTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\");\n          i0.ɵɵtext(5, \"progress-test works!\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(6, \"div\", 4);\n          i0.ɵɵelement(7, \"div\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".tab_page[_ngcontent-%COMP%]{height:80vh;background:#b3b3b3;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{max-height:53vh;overflow-y:auto;overflow-x:hidden}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]{background-color:#fff;padding:1.5rem;color:#333;margin:1rem 1.8rem;border-radius:1.188rem;font-size:1rem}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:1rem;height:1rem;margin:0 .2rem;cursor:pointer;color:var(--main_color)}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{color:#333;font-size:1rem}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   .num[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700}.tab_page[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:var(--second_color);font-size:1rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .title_data[_ngcontent-%COMP%]{color:#4d4d4d;font-size:1rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .head_conditions[_ngcontent-%COMP%]{width:50%;color:var(--main_color);font-size:1.1rem;font-weight:700;border-bottom:.063rem solid rgba(242,241,241,.8705882353)}\"]\n    });\n  }\n  return ProgressTestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}