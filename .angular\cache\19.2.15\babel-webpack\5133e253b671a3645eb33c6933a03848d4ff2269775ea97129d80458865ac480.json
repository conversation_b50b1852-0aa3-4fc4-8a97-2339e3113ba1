{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tibetan [bo]\n//! author : <PERSON><PERSON><PERSON><PERSON> <PERSON> : https://github.com/vajradog\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '༡',\n      2: '༢',\n      3: '༣',\n      4: '༤',\n      5: '༥',\n      6: '༦',\n      7: '༧',\n      8: '༨',\n      9: '༩',\n      0: '༠'\n    },\n    numberMap = {\n      '༡': '1',\n      '༢': '2',\n      '༣': '3',\n      '༤': '4',\n      '༥': '5',\n      '༦': '6',\n      '༧': '7',\n      '༨': '8',\n      '༩': '9',\n      '༠': '0'\n    };\n  var bo = moment.defineLocale('bo', {\n    months: 'ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ'.split('_'),\n    monthsShort: 'ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12'.split('_'),\n    monthsShortRegex: /^(ཟླ་\\d{1,2})/,\n    monthsParseExact: true,\n    weekdays: 'གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་'.split('_'),\n    weekdaysShort: 'ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་'.split('_'),\n    weekdaysMin: 'ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm'\n    },\n    calendar: {\n      sameDay: '[དི་རིང] LT',\n      nextDay: '[སང་ཉིན] LT',\n      nextWeek: '[བདུན་ཕྲག་རྗེས་མ], LT',\n      lastDay: '[ཁ་སང] LT',\n      lastWeek: '[བདུན་ཕྲག་མཐའ་མ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ལ་',\n      past: '%s སྔན་ལ',\n      s: 'ལམ་སང',\n      ss: '%d སྐར་ཆ།',\n      m: 'སྐར་མ་གཅིག',\n      mm: '%d སྐར་མ',\n      h: 'ཆུ་ཚོད་གཅིག',\n      hh: '%d ཆུ་ཚོད',\n      d: 'ཉིན་གཅིག',\n      dd: '%d ཉིན་',\n      M: 'ཟླ་བ་གཅིག',\n      MM: '%d ཟླ་བ',\n      y: 'ལོ་གཅིག',\n      yy: '%d ལོ'\n    },\n    preparse: function (string) {\n      return string.replace(/[༡༢༣༤༥༦༧༨༩༠]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'མཚན་མོ' && hour >= 4 || meridiem === 'ཉིན་གུང' && hour < 5 || meridiem === 'དགོང་དག') {\n        return hour + 12;\n      } else {\n        return hour;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'མཚན་མོ';\n      } else if (hour < 10) {\n        return 'ཞོགས་ཀས';\n      } else if (hour < 17) {\n        return 'ཉིན་གུང';\n      } else if (hour < 20) {\n        return 'དགོང་དག';\n      } else {\n        return 'མཚན་མོ';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return bo;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}