{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { BaseSelectedDateModel } from '../../../../../../../core/ng-model/base-selected-date-model';\nimport { BaseConstantModel } from '../../../../../../../core/ng-model/base-constant-model';\nimport { StudentProgramVacationStatusEnum } from '../../../../../../../core/enums/StudentProgramVacationStatus/student-program-vacation-status.enum';\nlet StudentProgramVacationAdvancedSearchComponent = class StudentProgramVacationAdvancedSearchComponent {\n  programService;\n  dateFormatterService;\n  translate;\n  closeAdvancedSearch = new EventEmitter();\n  filter = {\n    page: 1,\n    skip: 0,\n    take: 2147483647,\n    statusNum: StudentProgramVacationStatusEnum.Pending\n  };\n  resultMessage = {};\n  calenderType = new BaseSelectedDateModel();\n  selectedDateType;\n  typeDateBinding;\n  datafromBinding;\n  dataToBinding;\n  hijri = false;\n  milady = false;\n  filterFromDate;\n  filterToDate;\n  constructor(programService, dateFormatterService, translate) {\n    this.programService = programService;\n    this.dateFormatterService = dateFormatterService;\n    this.translate = translate;\n  }\n  programsbyAdvancedFilter = {\n    skip: 0,\n    take: 2147483647\n  };\n  programsList = [];\n  maxGregDate;\n  ngOnInit() {\n    this.getAllProgram();\n    this.maxGregDate = this.dateFormatterService.GetTodayGregorian();\n    if (this.filter.fromDate) {\n      let date = new Date(this.filter.fromDate || '');\n      this.filterFromDate = {\n        year: date?.getFullYear(),\n        month: date?.getMonth() + 1,\n        day: date?.getDate()\n      };\n    }\n    if (this.filter.toDate) {\n      let date = new Date(this.filter.toDate || '');\n      this.filterToDate = {\n        year: date?.getFullYear(),\n        month: date?.getMonth() + 1,\n        day: date?.getDate()\n      };\n    }\n  }\n  SendDatafrom(data) {\n    this.typeDateBinding = data.selectedDateType;\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.datafromBinding = data.selectedDateValue;\n    this.filter ? this.filter.fromDate = this.datafromBinding : null;\n    this.selectedDateType = data.selectedDateType;\n    // let date = new Date( this.filter.fromDate  || '');\n    // this.filterFromDate = { year: date?.getFullYear(), month: date?.getMonth() +1, day: date?.getDate() }\n  }\n  SendDataTo(data) {\n    this.typeDateBinding = data.selectedDateType;\n    data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    this.dataToBinding = data.selectedDateValue;\n    this.filter ? this.filter.toDate = this.dataToBinding : null;\n    this.selectedDateType = data.selectedDateType;\n  }\n  closeStuAdvancedSearch() {\n    if (this.filter) {\n      this.filter.usrName = '';\n      this.filter.progId = '';\n      this.filter.numberRequest = undefined;\n      this.filter.fromDate = undefined;\n      this.filter.toDate = undefined;\n      this.filter.skip = 0;\n      this.filter.take = 9;\n      this.filter.page = 1;\n      this.filter.sortField = '';\n    }\n    this.closeAdvancedSearch.emit(this.filter);\n  }\n  sendAdvancedSearch() {\n    if (this.datafromBinding > this.dataToBinding) {\n      this.resultMessage = {\n        message: this.translate.instant('STUDENT_SUBSCRIBERS.VALIDATIONDATE'),\n        type: BaseConstantModel.DANGER_TYPE\n      };\n    } else this.closeAdvancedSearch.emit();\n  }\n  getAllProgram() {\n    this.programService.getProgramAdvancedFilter(this.programsbyAdvancedFilter || {}).subscribe(res => {\n      if (res.isSuccess) {\n        this.programsList = res.data;\n      } else {}\n    }, error => {\n      //logging\n    });\n  }\n};\n__decorate([Output()], StudentProgramVacationAdvancedSearchComponent.prototype, \"closeAdvancedSearch\", void 0);\n__decorate([Input()], StudentProgramVacationAdvancedSearchComponent.prototype, \"filter\", void 0);\nStudentProgramVacationAdvancedSearchComponent = __decorate([Component({\n  selector: 'app-student-program-vacation-advanced-search',\n  templateUrl: './student-program-vacation-advanced-search.component.html',\n  styleUrls: ['./student-program-vacation-advanced-search.component.scss']\n})], StudentProgramVacationAdvancedSearchComponent);\nexport { StudentProgramVacationAdvancedSearchComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}