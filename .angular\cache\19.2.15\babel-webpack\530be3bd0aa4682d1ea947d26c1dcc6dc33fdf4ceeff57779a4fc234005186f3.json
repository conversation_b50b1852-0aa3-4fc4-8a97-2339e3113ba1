{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction GroupDetailsComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"div\", 12);\n    i0.ɵɵelement(3, \"img\", 13);\n    i0.ɵɵelementStart(4, \"div\")(5, \"p\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const participant_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"src\", participant_r1.avatar_url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? participant_r1 == null ? null : participant_r1.name_en : participant_r1 == null ? null : participant_r1.name_ar);\n  }\n}\nexport let GroupDetailsComponent = /*#__PURE__*/(() => {\n  class GroupDetailsComponent {\n    translate;\n    chatService;\n    langEnum = LanguageEnum;\n    listOfParticipants = [];\n    allowed;\n    groupModel;\n    constructor(translate, chatService) {\n      this.translate = translate;\n      this.chatService = chatService;\n    }\n    ngOnInit() {\n      this.allowed = this.groupModel?.allowed;\n      this.listOfParticipants = this.groupModel?.participants || [];\n      // this.listOfParticipants = [];\n      // this.listOfParticipants = this.groupModel?.participants;\n      // this.allowed = this.groupModel?.allowed === null ? true : this.groupModel?.allowed;\n    }\n    filterByText(searchKey) {\n      if (searchKey.length > 0) {\n        this.listOfParticipants = this.listOfParticipants?.filter(x => x.name_ar?.includes(searchKey) || x.name_en?.includes(searchKey));\n      } else {\n        this.listOfParticipants = this.groupModel?.participants || [];\n      }\n    }\n    static ɵfac = function GroupDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GroupDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ChatService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupDetailsComponent,\n      selectors: [[\"app-group-details\"]],\n      inputs: {\n        groupModel: \"groupModel\"\n      },\n      decls: 15,\n      vars: 8,\n      consts: [[1, \"details_day\"], [1, \"head_groupName\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"mx-2\"], [1, \"dimmed\", \"break-all\"], [1, \"example-margin\", 3, \"ngModelChange\", \"ngModel\"], [1, \"row\"], [1, \"col-12\", \"mt-3\"], [3, \"searchTerm\"], [1, \"chat_details\", \"w-100\"], [1, \"chat_vontainer\", \"w-100\"], [4, \"ngFor\", \"ngForOf\"], [1, \"cardRecourd\", \"mb-2\"], [1, \"group_name\", \"d-flex\", \"pb-0\", \"align-items-center\", \"justify-content-between\"], [1, \"img_user\", 3, \"src\"], [1, \"ellipsis\", \"mb-0\"]],\n      template: function GroupDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"P\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"mat-checkbox\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailsComponent_Template_mat_checkbox_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.allowed, $event) || (ctx.allowed = $event);\n            return $event;\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6)(11, \"app-search-input\", 7);\n          i0.ɵɵlistener(\"searchTerm\", function GroupDetailsComponent_Template_app_search_input_searchTerm_11_listener($event) {\n            return ctx.filterByText($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9);\n          i0.ɵɵtemplate(14, GroupDetailsComponent_ng_container_14_Template, 7, 2, \"ng-container\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 4, \"CHAT_GROUP.GROUP_DETAILS\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.allowed);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(8, 6, \"CHAT_GROUP.ALLOW_USERS\"), \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listOfParticipants);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.NgControlStatus, i4.NgModel, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.list-group_program-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;margin-top:1rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:57vh;overflow-y:auto;padding-left:.5rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:57vh;overflow-y:auto;padding-right:.5rem}.list-group_program-list[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:pointer}.list-group_program-list[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.list-group_program-list[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-family:Almarai!important;font-weight:700;font-size:1rem;justify-content:space-between}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-copy[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-copy[_ngcontent-%COMP%]{color:#fff}@media (min-width: 37.5rem) and (max-width: 63.938rem){.list-group_program-list[_ngcontent-%COMP%]{padding:.9rem}}.tab_page[_ngcontent-%COMP%]{height:79vh;display:flex;justify-content:space-between;padding-bottom:1rem;background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:1.188rem;height:74vh;overflow-y:auto;overflow-x:hidden;margin-left:.5rem;margin-right:.5rem;margin-top:2rem;padding:1rem .5rem}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]   .name_prog[_ngcontent-%COMP%]{font-size:.875rem;color:gray}.tab_page[_ngcontent-%COMP%]   .part1[_ngcontent-%COMP%]{flex:0 0 30.333333%;max-width:30.333333%}.tab_page[_ngcontent-%COMP%]   .part2[_ngcontent-%COMP%]{flex:0 0 64.666667%;max-width:64.666667%}.tab_page[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;color:#333}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]{background:transparent;border-radius:0;padding:0;height:auto;margin-top:0}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color)}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-size:1.12rem}.list_exams[_ngcontent-%COMP%]{overflow-x:hidden!important;height:75vh!important}.tab_page[_ngcontent-%COMP%]{height:80vh}.tab_page-2[_ngcontent-%COMP%]{height:80vh;background:#fbfbfb;border-radius:0 0 .75rem .75rem}p.name[_ngcontent-%COMP%]{font-weight:700;font-size:1.125rem;letter-spacing:0;color:#333;opacity:1}.btn-container[_ngcontent-%COMP%]:lang(en){margin-right:2.3rem}.btn-container[_ngcontent-%COMP%]:lang(ar){margin-left:2.3rem}.back-btn[_ngcontent-%COMP%]{color:var(--second_color);background:#b3b3b3;border:.125rem solid var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.internal_scroll_list_group.order[_ngcontent-%COMP%]{height:25.9375rem!important}.show-exam[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;text-decoration:none;box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color);height:10%;background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;margin-bottom:1rem;opacity:1;margin-top:1.4rem .1rem;font-weight:700;font-size:1rem;justify-content:space-between;padding:.4rem}.show-exam[_ngcontent-%COMP%]   .show-space[_ngcontent-%COMP%]{padding:.5rem 0rem}.show-exam[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{min-width:3rem}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(ar){float:left}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(en){float:right}.cardRecourd[_ngcontent-%COMP%]{margin:1rem;justify-content:space-between;align-items:center;display:flex;padding:.5rem 1rem;background-color:#fff;border-radius:.438rem;border:.063rem solid rgba(242,241,241,.8705882353);box-shadow:0 .125rem .188rem #f2f1f1de}.redPoint[_ngcontent-%COMP%]{top:16.813rem;left:32.813rem;width:1.25rem;height:1.25rem;background:#ea5455 0% 0% no-repeat padding-box;opacity:1;border-radius:50%}.details_day[_ngcontent-%COMP%]{background:#f2f1f14d;border-radius:1.188rem;opacity:1;height:75vh;padding:1rem 1.7rem;margin-top:1rem}.details_day[_ngcontent-%COMP%]:lang(en){text-align:left}.details_day[_ngcontent-%COMP%]:lang(ar){text-align:right}.details_day[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]{color:#333;font-size:1.23rem;font-weight:700;padding-bottom:1rem}.details_day[_ngcontent-%COMP%]   .break-all[_ngcontent-%COMP%]{word-break:break-all}.details_day[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.937rem}.details_day[_ngcontent-%COMP%]   .chat_details[_ngcontent-%COMP%]{height:51vh;overflow-y:auto;overflow-x:hidden;padding:.3rem}.details_day[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:100%;cursor:auto}.details_day[_ngcontent-%COMP%]   .cardRecourd[_ngcontent-%COMP%]{padding:.5rem;background-color:transparent;border:none;border-bottom:.094rem solid #d6d7d8;border-radius:0;margin:.2rem;box-shadow:none}.details_day[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:3.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem}.details_day[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:var(--main_color)!important;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.5}\"]\n    });\n  }\n  return GroupDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}