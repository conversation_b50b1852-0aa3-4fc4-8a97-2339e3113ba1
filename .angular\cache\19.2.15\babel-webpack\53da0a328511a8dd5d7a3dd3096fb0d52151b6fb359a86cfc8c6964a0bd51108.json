{"ast": null, "code": "import { AdminStudentStatisticsCountsChartComponent } from \"../../../../../../../shared/components/admin-student-statistics-counts-chart/admin-student-statistics-counts-chart.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../../../core/services/studentStatistics/student-statistics.service\";\nimport * as i2 from \"../../../../../../../core/services/alertify-services/alertify.service\";\nimport * as i3 from \"../../../../../../../core/services/program-batches-service/program-batches.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"@ngx-translate/core\";\nfunction AdminStudentStatisticsCountsComponent_app_admin_student_statistics_counts_chart_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-admin-student-statistics-counts-chart\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"adminStudentTabStatistics\", ctx_r0.adminStudentTabStatistics);\n  }\n}\nexport let AdminStudentStatisticsCountsComponent = /*#__PURE__*/(() => {\n  class AdminStudentStatisticsCountsComponent {\n    studentStatisticsService;\n    alertify;\n    programBatchesService;\n    adminStudentTabStatistics;\n    allProgBatchs = [];\n    batId;\n    adminStudentNotificationsChartComponent;\n    constructor(studentStatisticsService, alertify, programBatchesService) {\n      this.studentStatisticsService = studentStatisticsService;\n      this.alertify = alertify;\n      this.programBatchesService = programBatchesService;\n    }\n    ngOnInit() {\n      this.getAllProgramBatches();\n    }\n    getAdminStudentTabStatistics() {\n      this.studentStatisticsService.getAdminStudentTabStatistics(this.batId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.adminStudentTabStatistics = res.data;\n          this.adminStudentNotificationsChartComponent?.initiate(this.adminStudentTabStatistics || {});\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    getAllProgramBatches() {\n      this.programBatchesService.getAllProgramBatches().subscribe(res => {\n        if (res.isSuccess) {\n          this.allProgBatchs = res.data;\n          this.batId = this.allProgBatchs[0].batId;\n          this.getAdminStudentTabStatistics();\n        } else {\n          this.alertify.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onChangeBatch() {\n      this.getAdminStudentTabStatistics();\n    }\n    static ɵfac = function AdminStudentStatisticsCountsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentStatisticsCountsComponent)(i0.ɵɵdirectiveInject(i1.StudentStatisticsService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.ProgramBatchesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentStatisticsCountsComponent,\n      selectors: [[\"app-admin-student-statistics-counts\"]],\n      viewQuery: function AdminStudentStatisticsCountsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AdminStudentStatisticsCountsChartComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.adminStudentNotificationsChartComponent = _t.first);\n        }\n      },\n      decls: 10,\n      vars: 10,\n      consts: [[1, \"row\", \"w-100\", \"program_result\", \"mx-0\", \"pt-3\", \"mt-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\"], [1, \"bold\"], [1, \"w-75\", \"select\"], [\"bindLabel\", \"progBatNameAr\", \"bindValue\", \"batId\", 3, \"ngModelChange\", \"change\", \"items\", \"ngModel\", \"placeholder\"], [3, \"adminStudentTabStatistics\", 4, \"ngIf\"], [3, \"adminStudentTabStatistics\"]],\n      template: function AdminStudentStatisticsCountsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"ng-select\", 4);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminStudentStatisticsCountsComponent_Template_ng_select_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.batId, $event) || (ctx.batId = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function AdminStudentStatisticsCountsComponent_Template_ng_select_change_6_listener() {\n            return ctx.onChangeBatch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 1);\n          i0.ɵɵtemplate(9, AdminStudentStatisticsCountsComponent_app_admin_student_statistics_counts_chart_9_Template, 1, 1, \"app-admin-student-statistics-counts-chart\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(4, 6, \"STUDENT_TAP.REGISTERED_STUDENTS\"), \" \", ctx.adminStudentTabStatistics == null ? null : ctx.adminStudentTabStatistics.registeredStdModel == null ? null : ctx.adminStudentTabStatistics.registeredStdModel.registeredStdCount, \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 8, \"PROGRAMS_LIST.PROGRAM_NAME\"));\n          i0.ɵɵproperty(\"items\", ctx.allProgBatchs);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.batId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.adminStudentTabStatistics);\n        }\n      },\n      dependencies: [i4.NgIf, i5.NgControlStatus, i5.NgModel, i6.NgSelectComponent, i7.TranslatePipe],\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .Register__Label[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}.program_result[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:.25rem}.part_two[_ngcontent-%COMP%], .part_one[_ngcontent-%COMP%]{height:73vh;overflow-y:auto;overflow-x:hidden}\"]\n    });\n  }\n  return AdminStudentStatisticsCountsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}