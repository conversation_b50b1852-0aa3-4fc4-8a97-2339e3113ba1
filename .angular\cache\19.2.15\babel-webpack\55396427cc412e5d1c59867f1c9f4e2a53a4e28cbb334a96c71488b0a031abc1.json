{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport prepareBoxplotData from './prepareBoxplotData.js';\nimport { throwError, makePrintable } from '../../util/log.js';\nimport { SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nexport var boxplotTransform = {\n  type: 'echarts:boxplot',\n  transform: function transform(params) {\n    var upstream = params.upstream;\n    if (upstream.sourceFormat !== SOURCE_FORMAT_ARRAY_ROWS) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = makePrintable('source data is not applicable for this boxplot transform. Expect number[][].');\n      }\n      throwError(errMsg);\n    }\n    var result = prepareBoxplotData(upstream.getRawData(), params.config);\n    return [{\n      dimensions: ['ItemName', 'Low', 'Q1', 'Q2', 'Q3', 'High'],\n      data: result.boxData\n    }, {\n      data: result.outliers\n    }];\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}