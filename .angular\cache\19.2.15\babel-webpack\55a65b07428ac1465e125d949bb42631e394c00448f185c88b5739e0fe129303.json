{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SettingAnswerTypeEnum } from 'src/app/core/enums/setting-answerType-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/lookup-services/lookup.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/program-services/program-conditions.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction AddConditionSettingComponent_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.ADD_CONDITIONS\"), \" \");\n  }\n}\nfunction AddConditionSettingComponent_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.EDIT_CONDITIONS\"), \"\");\n  }\n}\nfunction AddConditionSettingComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"label\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-radio-group\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddConditionSettingComponent_div_12_Template_mat_radio_group_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.conditionModel.answerType, $event) || (ctx_r1.conditionModel.answerType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(5, \"mat-radio-button\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-radio-button\", 16);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-radio-button\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 9, \"CONDITIONDS.ANSWER_TYPE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.conditionModel.answerType);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(11, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.answerTypeEnum.Choices);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.Choices(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.answerTypeEnum.Text);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.text(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.answerTypeEnum.Toggel);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.toggel(), \" \");\n  }\n}\nfunction AddConditionSettingComponent_ng_container_13_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddConditionSettingComponent_ng_container_13_ng_container_7_Template_input_ngModelChange_2_listener($event) {\n      const answer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(answer_r5.text, $event) || (answer_r5.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function AddConditionSettingComponent_ng_container_13_ng_container_7_Template_a_click_3_listener() {\n      const answer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteAnswerDialog(answer_r5));\n    });\n    i0.ɵɵelement(4, \"img\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const answer_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", answer_r5.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.trash, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddConditionSettingComponent_ng_container_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nfunction AddConditionSettingComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"label\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AddConditionSettingComponent_ng_container_13_ng_container_7_Template, 5, 2, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 18)(9, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AddConditionSettingComponent_ng_container_13_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addAnswer());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, AddConditionSettingComponent_ng_container_13_div_12_Template, 3, 4, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"CONDITIONDS.ANSWERS\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.conditionModel.answerList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 6, \"CONDITIONDS.ADD_ANSWER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage);\n  }\n}\nfunction AddConditionSettingComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AddConditionSettingComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveCondition());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.SAVE\"), \" \");\n  }\n}\nfunction AddConditionSettingComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AddConditionSettingComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.savingEdit());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EDIT\"), \" \");\n  }\n}\nexport let AddConditionSettingComponent = /*#__PURE__*/(() => {\n  class AddConditionSettingComponent {\n    translate;\n    lookupService;\n    alert;\n    dialog;\n    progCondService;\n    imagesPathesService;\n    closeOverlay = new EventEmitter();\n    addCustomCondition = new EventEmitter();\n    modelEdit;\n    model;\n    conditionModel = {\n      answerType: SettingAnswerTypeEnum.Choices,\n      answerList: [],\n      studAnsValues: []\n    };\n    answerTypeEnum = SettingAnswerTypeEnum;\n    langEnum = LanguageEnum;\n    collectionOfLookup = {};\n    listOfLookupConditions = ['PROG_COND_TYPES'];\n    resMessage = {};\n    currentLang = '';\n    MULTISELECT = '';\n    huff;\n    constructor(translate, lookupService, alert, dialog, progCondService, imagesPathesService) {\n      this.translate = translate;\n      this.lookupService = lookupService;\n      this.alert = alert;\n      this.dialog = dialog;\n      this.progCondService = progCondService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.MULTISELECT = this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.MULTI_SELECT') : this.translate.instant('GENERAL.MULTI_SELECT');\n      // in case edit form \n      if (this.modelEdit) {\n        this.getModel();\n      }\n    }\n    closeForm() {\n      this.closeOverlay.emit(false);\n    }\n    addAnswer() {\n      let id = BaseConstantModel.newGuid();\n      let answer = {\n        id: id\n      };\n      this.conditionModel.answerList?.push(answer);\n    }\n    validateAnswer(answerList, ansType) {\n      if (answerList.length < 2 && ansType === SettingAnswerTypeEnum.Choices) {\n        this.resMessage = {\n          message: this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.TWO_OPTIOPN') : this.translate.instant('GENERAL.TWO_OPTIOPN'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return false;\n      }\n      if (ansType === SettingAnswerTypeEnum.Choices && answerList.some(r => r.text === '' || !r.text)) {\n        this.resMessage = {\n          message: this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.TEXT_INPUT') : this.translate.instant('GENERAL.TEXT_INPUT'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return false;\n      }\n      if (this.getDuplicateAnswer(answerList).length > 0 && ansType === SettingAnswerTypeEnum.Choices) {\n        this.resMessage = {\n          message: this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.DUPLICATED_ANSWER') : this.translate.instant('GENERAL.DUPLICATED_ANSWER'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return false;\n      }\n      return true;\n    }\n    getDuplicateAnswer(arr) {\n      var sorted_arr = arr.slice().sort((a, b) => a.text > b.text && 1 || -1);\n      sorted_arr = sorted_arr.filter(x => x.text != \"\");\n      var results = [];\n      for (var i = 0; i < sorted_arr.length - 1; i++) {\n        if (sorted_arr[i + 1].text === sorted_arr[i].text) {\n          results.push(sorted_arr[i]);\n        }\n      }\n      return results;\n    }\n    Choices() {\n      return this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.MORE_CHOICES') : this.translate.instant('GENERAL.MORE_CHOICES');\n    }\n    text() {\n      return this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.TEXT') : this.translate.instant('GENERAL.TEXT');\n    }\n    toggel() {\n      return this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.Toggel') : this.translate.instant('GENERAL.Toggel');\n    }\n    saveCondition() {\n      if (this.conditionModel && this.conditionModel.answerList && this.conditionModel.answerType && this.validateAnswer(this.conditionModel.answerList, this.conditionModel.answerType)) {\n        this.updateModelTypeSubmition();\n        this.model = {\n          title: this.conditionModel.title,\n          conditionJson: JSON.stringify(this.conditionModel)\n        };\n        this.addSettingConditions();\n      }\n    }\n    updateModelTypeSubmition() {\n      if (this.conditionModel.answerType === SettingAnswerTypeEnum.Text) {\n        this.conditionModel.answerList = undefined;\n        this.conditionModel.studAnsValues = undefined;\n        this.conditionModel.studBoolAns = undefined;\n        this.conditionModel.studTxtAns = '';\n      } else if (this.conditionModel.answerType === SettingAnswerTypeEnum.Toggel) {\n        this.conditionModel.answerList = undefined;\n        this.conditionModel.studAnsValues = undefined;\n        this.conditionModel.studTxtAns = undefined;\n        this.conditionModel.studBoolAns = true;\n      } else {\n        this.conditionModel.studTxtAns = undefined;\n        this.conditionModel.studBoolAns = undefined;\n      }\n    }\n    addSettingConditions() {\n      this.progCondService.saveProgramPredefinedCustomConditions(this.model || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.addCustomCondition.emit();\n          this.closeForm();\n          this.alert.success(res.message || '');\n        } else {\n          // this.resMessage = {\n          //   message: res.message,\n          //   type: BaseConstantModel.DANGER_TYPE\n          // }\n          // this.closeForm();\n          if (this.conditionModel.answerType === SettingAnswerTypeEnum.Text || this.conditionModel.answerType === SettingAnswerTypeEnum.Toggel) {\n            this.conditionModel.answerList = [];\n          }\n          this.alert.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    // case in edit\n    getModel() {\n      if (this.modelEdit && this.modelEdit.conditionModel) {\n        this.conditionModel = this.modelEdit?.conditionModel;\n        if (this.answerTypeEnum.Choices != this.conditionModel.answerType) {\n          this.conditionModel.answerList = [];\n        }\n      }\n    }\n    savingEdit() {\n      if (this.conditionModel && this.conditionModel.answerList && this.conditionModel.answerType && this.validateAnswer(this.conditionModel.answerList, this.conditionModel.answerType)) {\n        this.updateModelTypeSubmition();\n        this.modelEdit = {\n          id: this.modelEdit?.id,\n          title: this.conditionModel.title,\n          conditionJson: JSON.stringify(this.conditionModel)\n        };\n        this.editSettingConditions();\n      }\n    }\n    editSettingConditions() {\n      this.progCondService.putProgramPredefinedCustomConditions(this.modelEdit || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.addCustomCondition.emit();\n          this.closeForm();\n          this.alert.success(res.message || '');\n        } else {\n          this.alert.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    // delete answer\n    deleteAnswerDialog(answer) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete answer\" : \"هل متأكد من حذف الإجابة\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Answer' : 'حذف الإجابة', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          // let question = this.exam.questions.filter(q => q.questionNo == no)[0];\n          const index = this.conditionModel?.answerList?.indexOf(answer);\n          if (index) this.conditionModel.answerList?.splice(index, 1);\n        }\n      });\n    }\n    radioChange(event) {\n      this.huff = this.collectionOfLookup.PROG_COND_TYPES?.filter(i => i.id == event.value)[0].huffazId;\n    }\n    static ɵfac = function AddConditionSettingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddConditionSettingComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.LookupService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.ProgramConditionsService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddConditionSettingComponent,\n      selectors: [[\"app-add-condition-setting\"]],\n      inputs: {\n        modelEdit: \"modelEdit\"\n      },\n      outputs: {\n        closeOverlay: \"closeOverlay\",\n        addCustomCondition: \"addCustomCondition\"\n      },\n      decls: 21,\n      vars: 12,\n      consts: [[1, \"form-group\", \"conditionForm\", \"mt-3\", \"pl-3\", \"pr-3\"], [\"class\", \" head  bold\", 4, \"ngIf\"], [1, \"form-group\"], [1, \"row\"], [1, \"col-12\"], [1, \"header_input\"], [\"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"col-12\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", \"class\", \"save-btn\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"head\", \"bold\"], [1, \"header_input\", \"mb-3\"], [1, \"example-radio-group\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [1, \"example-radio-button\", \"ml-1\", \"mr-1\", 3, \"value\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"mt-3\"], [\"type\", \"submit\", 1, \"cancel-btn\", \"m-0\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [1, \"d-flex\", \"mb-2\"], [\"type\", \"text\", 1, \"form-control\", \"w-90\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [1, \"btn_trash\", 3, \"src\"], [1, \"py-2\", \"my-4\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"]],\n      template: function AddConditionSettingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AddConditionSettingComponent_h3_1_Template, 3, 3, \"h3\", 1)(2, AddConditionSettingComponent_h3_2_Template, 3, 3, \"h3\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"label\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddConditionSettingComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.conditionModel.title, $event) || (ctx.conditionModel.title = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 2)(11, \"div\", 3);\n          i0.ɵɵtemplate(12, AddConditionSettingComponent_div_12_Template, 11, 12, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, AddConditionSettingComponent_ng_container_13_Template, 13, 8, \"ng-container\", 8);\n          i0.ɵɵelementStart(14, \"section\", 9)(15, \"div\", 10);\n          i0.ɵɵtemplate(16, AddConditionSettingComponent_button_16_Template, 3, 3, \"button\", 11)(17, AddConditionSettingComponent_button_17_Template, 3, 3, \"button\", 11);\n          i0.ɵɵelementStart(18, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AddConditionSettingComponent_Template_button_click_18_listener() {\n            return ctx.closeForm();\n          });\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.modelEdit);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 8, \"CONDITIONDS.CONDITION_TITLE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.conditionModel.title);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.conditionModel.answerType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.answerTypeEnum.Choices == ctx.conditionModel.answerType);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.modelEdit);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.modelEdit);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 10, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, FormsModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, ReactiveFormsModule, TranslateModule, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.conditionForm[_ngcontent-%COMP%]{height:70vh}.conditionForm[_ngcontent-%COMP%]:lang(en){text-align:left}.conditionForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.conditionForm[_ngcontent-%COMP%]   .header_input[_ngcontent-%COMP%]{font-size:.937rem;color:#333;display:block}.conditionForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.conditionForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-size:.875rem}.conditionForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin-right:1rem;margin-left:1rem}@media (max-width: 48rem){.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:3rem}}\"]\n    });\n  }\n  return AddConditionSettingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}