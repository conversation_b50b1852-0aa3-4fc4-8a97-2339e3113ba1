{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseConstantModel } from '../../../../core/ng-model/base-constant-model';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from '../../../../core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../core/services/user-services/user.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../../../core/services/alertify-services/alertify.service\";\nimport * as i7 from \"../../../../core/services/lookup-services/lookup.service\";\nimport * as i8 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/radio\";\nimport * as i11 from \"@angular/material/icon\";\nconst _c0 = a0 => ({\n  \"error_input\": a0\n});\nfunction UserManagementAddUserComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_REQUIRED\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_19_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.USERNAME_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, UserManagementAddUserComponent_div_19_div_1_Template, 3, 3, \"div\", 33)(2, UserManagementAddUserComponent_div_19_div_2_Template, 3, 3, \"div\", 33)(3, UserManagementAddUserComponent_div_19_div_3_Template, 3, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors == null ? null : ctx_r0.f.userName.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.userName.errors == null ? null : ctx_r0.f.userName.errors.minlength);\n  }\n}\nfunction UserManagementAddUserComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EMAIL_REQUIRED\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.EMAIL_PATTERN\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, UserManagementAddUserComponent_div_29_div_1_Template, 3, 3, \"div\", 33)(2, UserManagementAddUserComponent_div_29_div_2_Template, 3, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.email.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.email.errors.pattern);\n  }\n}\nfunction UserManagementAddUserComponent_mat_radio_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"value\", item_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang == ctx_r0.langEnum.en ? item_r2.nameEn : item_r2.nameAr, \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.GENDER_REQUIRED\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, UserManagementAddUserComponent_div_37_div_1_Template, 3, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.gender.errors.required);\n  }\n}\nfunction UserManagementAddUserComponent_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_REQUIRED\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_52_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_52_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_52_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.PASSWORD_TO_CONTAINER\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, UserManagementAddUserComponent_div_52_div_1_Template, 3, 3, \"div\", 33)(2, UserManagementAddUserComponent_div_52_div_2_Template, 3, 3, \"div\", 33)(3, UserManagementAddUserComponent_div_52_div_3_Template, 3, 3, \"div\", 33)(4, UserManagementAddUserComponent_div_52_div_4_Template, 3, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.minlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.password.errors == null ? null : ctx_r0.f.password.errors.pattern);\n  }\n}\nfunction UserManagementAddUserComponent_div_65_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_REQUIRED\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_65_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_65_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_65_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_PASSWORD_TO_CONTAINER\"), \" \");\n  }\n}\nfunction UserManagementAddUserComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, UserManagementAddUserComponent_div_65_div_1_Template, 3, 3, \"div\", 33)(2, UserManagementAddUserComponent_div_65_div_2_Template, 3, 3, \"div\", 33)(3, UserManagementAddUserComponent_div_65_div_3_Template, 3, 3, \"div\", 33)(4, UserManagementAddUserComponent_div_65_div_4_Template, 3, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.minlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.confirmPassword.errors == null ? null : ctx_r0.f.confirmPassword.errors.pattern);\n  }\n}\nfunction UserManagementAddUserComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resMessage.message, \" \");\n  }\n}\nexport let UserManagementAddUserComponent = /*#__PURE__*/(() => {\n  class UserManagementAddUserComponent {\n    dateFormatterService;\n    translate;\n    userService;\n    router;\n    fb;\n    alertifyService;\n    lookupService;\n    imagesPathesService;\n    closeAddUserOverLay = new EventEmitter();\n    selectedRoleId = '';\n    filter = {\n      skip: 0,\n      take: 9,\n      page: 1\n    };\n    addUserModel;\n    registerform = new FormGroup({});\n    hidePassword = true;\n    resMessage = {};\n    currentLang;\n    isSubmit = false;\n    hidePasswordConfirm = true;\n    langEnum = LanguageEnum;\n    collectionOfLookup = {};\n    listOfLookupProfile = ['GENDER'];\n    // telInputParam: ITelInputParams = {};\n    currentUser;\n    constructor(dateFormatterService, translate, userService, router, fb, alertifyService, lookupService, imagesPathesService) {\n      this.dateFormatterService = dateFormatterService;\n      this.translate = translate;\n      this.userService = userService;\n      this.router = router;\n      this.fb = fb;\n      this.alertifyService = alertifyService;\n      this.lookupService = lookupService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    get f() {\n      return this.registerform.controls;\n    }\n    ngOnInit() {\n      this.loadUserForm();\n      this.currentLang = this.translate.currentLang === LanguageEnum.ar ? LanguageEnum.en : LanguageEnum.ar;\n      this.getLookupByKey();\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      // this.f.gender.setValue(this.currentUser?.gndr)\n      // this.getCountryIsoCode();\n    }\n    closeAddUserOverLayMethod() {\n      this.closeAddUserOverLay.emit(this.filter);\n    }\n    loadUserForm() {\n      this.registerform = this.fb.group({\n        email: ['', [Validators.required, Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\\\.[a-z]{2,4}$')]],\n        userName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20)]],\n        password: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(12), Validators.pattern(BaseConstantModel.passwordPattern)]],\n        confirmPassword: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(12), Validators.pattern(BaseConstantModel.passwordPattern)]],\n        gender: [null, Validators.required]\n        //  mobile: [null, Validators.required],\n      });\n    }\n    onSignup(value) {\n      this.isSubmit = true;\n      this.resMessage = {};\n      this.addUserModel = {\n        uname: this.registerform.value.userName,\n        uemail: this.registerform.value.email,\n        ucpass: this.registerform.value.confirmPassword,\n        //\"\"\n        upass: this.registerform.value.password,\n        roletype: this.selectedRoleId,\n        gndr: this.registerform.value.gender\n        // mobile : this.registerform.value.mobile\n      };\n      this.userService.addUserFromUserManagement(this.addUserModel).subscribe(res => {\n        if (res.isSuccess) {\n          this.alertifyService.success(res.message || '');\n          this.closeAddUserOverLay.emit(this.filter);\n          this.isSubmit = false;\n        } else {\n          this.isSubmit = false;\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    togglePassword() {\n      this.hidePassword = !this.hidePassword;\n    }\n    togglePasswordConfirm() {\n      this.hidePasswordConfirm = !this.hidePasswordConfirm;\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookupProfile).subscribe(res => {\n        this.collectionOfLookup = res.data;\n        if (res.isSuccess) {} else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    static ɵfac = function UserManagementAddUserComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserManagementAddUserComponent)(i0.ɵɵdirectiveInject(i1.DateFormatterService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.AlertifyService), i0.ɵɵdirectiveInject(i7.LookupService), i0.ɵɵdirectiveInject(i8.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserManagementAddUserComponent,\n      selectors: [[\"app-user-management-add-user\"]],\n      inputs: {\n        selectedRoleId: \"selectedRoleId\",\n        filter: \"filter\"\n      },\n      outputs: {\n        closeAddUserOverLay: \"closeAddUserOverLay\"\n      },\n      decls: 75,\n      vars: 73,\n      consts: [[1, \"form-group\", \"DataForm\", \"mt-5\", \"pl-3\", \"pr-3\"], [1, \"row\", \"justify-content-center\"], [1, \"form-group\", \"UserLogin__LoginForm\", 3, \"ngSubmit\", \"formGroup\"], [1, \"content_form\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mb-2\", \"mt-2\"], [1, \"font-weight-bold\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mb-2\"], [\"for\", \"UserEmail\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\", \"bg-white\", \"pl-2\", \"pr-2\", \"border-md\", 3, \"ngClass\"], [1, \"UserLogin_icon_input\", 3, \"src\"], [\"formControlName\", \"userName\", \"id\", \"userName\", \"name\", \"userName\", \"pInputText\", \"\", \"type\", \"text\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\", \"placeholder\"], [\"class\", \"error_message\", 4, \"ngIf\"], [\"formControlName\", \"email\", \"id\", \"email\", \"name\", \"email\", \"pInputText\", \"\", \"placeholder\", \"Ex. <EMAIL>\", \"type\", \"email\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\", \"mt-3\"], [\"for\", \"gender\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-4\", \"col-xs-4\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"gender\", 1, \"example-radio-group\"], [\"class\", \"example-radio-button ml-3\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [1, \"row\"], [\"for\", \"password\"], [1, \"toggle-password\", 3, \"click\"], [\"formControlName\", \"password\", \"id\", \"password\", \"name\", \"password\", \"pPassword\", \"\", \"type\", \"password\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\", \"type\", \"placeholder\"], [\"for\", \"confirmPassword\"], [\"formControlName\", \"confirmPassword\", \"id\", \"confirmPassword\", \"name\", \"confirmPassword\", \"pPassword\", \"\", \"type\", \"password\", 1, \"px-1\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"ngClass\", \"type\", \"placeholder\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [1, \"error_message\"], [4, \"ngIf\"], [1, \"example-radio-button\", \"ml-3\", 3, \"value\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"]],\n      template: function UserManagementAddUserComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\")(3, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function UserManagementAddUserComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.onSignup(ctx.registerform.value);\n          });\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"p\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"label\", 7);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"span\", 10);\n          i0.ɵɵelement(16, \"img\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(17, \"input\", 12);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, UserManagementAddUserComponent_div_19_Template, 4, 3, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 6)(21, \"label\", 7);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 8)(25, \"div\", 9)(26, \"span\", 10);\n          i0.ɵɵelement(27, \"img\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(28, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, UserManagementAddUserComponent_div_29_Template, 3, 2, \"div\", 13);\n          i0.ɵɵelementStart(30, \"div\", 15)(31, \"label\", 16);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"section\", 17)(35, \"mat-radio-group\", 18);\n          i0.ɵɵtemplate(36, UserManagementAddUserComponent_mat_radio_button_36_Template, 2, 2, \"mat-radio-button\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, UserManagementAddUserComponent_div_37_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 6)(39, \"div\", 21)(40, \"div\", 6)(41, \"label\", 22);\n          i0.ɵɵtext(42);\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 9)(46, \"span\", 10);\n          i0.ɵɵelement(47, \"img\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"mat-icon\", 23);\n          i0.ɵɵlistener(\"click\", function UserManagementAddUserComponent_Template_mat_icon_click_48_listener() {\n            return ctx.togglePassword();\n          });\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(50, \"input\", 24);\n          i0.ɵɵpipe(51, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, UserManagementAddUserComponent_div_52_Template, 5, 4, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 6)(54, \"label\", 25);\n          i0.ɵɵtext(55);\n          i0.ɵɵpipe(56, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 8)(58, \"div\", 9)(59, \"span\", 10);\n          i0.ɵɵelement(60, \"img\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-icon\", 23);\n          i0.ɵɵlistener(\"click\", function UserManagementAddUserComponent_Template_mat_icon_click_61_listener() {\n            return ctx.togglePasswordConfirm();\n          });\n          i0.ɵɵtext(62);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(63, \"input\", 26);\n          i0.ɵɵpipe(64, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, UserManagementAddUserComponent_div_65_Template, 5, 4, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 27)(67, \"div\", 28)(68, \"button\", 29);\n          i0.ɵɵtext(69);\n          i0.ɵɵpipe(70, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function UserManagementAddUserComponent_Template_button_click_71_listener() {\n            return ctx.closeAddUserOverLayMethod();\n          });\n          i0.ɵɵtext(72);\n          i0.ɵɵpipe(73, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(74, UserManagementAddUserComponent_div_74_Template, 2, 4, \"div\", 31);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerform);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 35, \"SIGNUP_PG.SIGN_UP\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 37, \"SIGNUP_PG.USERNAME\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c0, ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.email, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(18, 39, \"GENERAL.USERNAME\"));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c0, ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.userName.errors && (ctx.f.userName.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 41, \"GENERAL.EMAIL\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c0, ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.email, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c0, ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.email.errors && (ctx.f.email.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(33, 43, \"GENERAL.GENDER\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.GENDER);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.gender.errors && (ctx.f.gender.touched || ctx.isSubmit));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(43, 45, \"GENERAL.PASSWORD\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(65, _c0, ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.password, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(51, 47, \"GENERAL.PASSWORD\"));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(67, _c0, ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit)))(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.password.errors && (ctx.f.password.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(56, 49, \"GENERAL.CONFIRM_PASSWORD\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(69, _c0, ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.password, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePasswordConfirm ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(64, 51, \"GENERAL.CONFIRM_PASSWORD\"));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(71, _c0, ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit)))(\"type\", ctx.hidePasswordConfirm ? \"password\" : \"text\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.f.confirmPassword.errors && (ctx.f.confirmPassword.touched || ctx.isSubmit));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(70, 53, \"USER_MANAGEMENT.ADD_USER\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(73, 55, \"GENERAL.CANCEL\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resMessage.message);\n        }\n      },\n      dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i10.MatRadioGroup, i10.MatRadioButton, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i11.MatIcon, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin:1rem}.DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-file[_ngcontent-%COMP%]:not(:last-child)   .custom-file-label[_ngcontent-%COMP%]:after, .DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-select[_ngcontent-%COMP%]:not(:last-child), .DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .form-control[_ngcontent-%COMP%]:not(:last-child){border:.313rem}.DataForm[_ngcontent-%COMP%]   .font-weight-bold[_ngcontent-%COMP%]{font-size:1.5rem}@media (max-width: 64rem){.box[_ngcontent-%COMP%]{background:transparent 0% 0% no-repeat padding-box;box-shadow:unset;padding:unset;margin-top:unset}.material-icons[_ngcontent-%COMP%]{font-size:1.125rem}.toggle-password[_ngcontent-%COMP%]:lang(en){right:.125rem;top:.25rem}.toggle-password[_ngcontent-%COMP%]:lang(ar){left:.125rem;top:.25rem}.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem!important}.font-weight-bold[_ngcontent-%COMP%]{font-size:1.3rem}}\"]\n    });\n  }\n  return UserManagementAddUserComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}