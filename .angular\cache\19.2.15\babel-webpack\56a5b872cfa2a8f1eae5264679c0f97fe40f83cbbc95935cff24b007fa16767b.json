{"ast": null, "code": "/*!\n* ZRender, a high performance 2d drawing library.\n*\n* Copyright (c) 2013, Baidu Inc.\n* All rights reserved.\n*\n* LICENSE\n* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt\n*/\nimport env from './core/env.js';\nimport * as zrUtil from './core/util.js';\nimport Handler from './Handler.js';\nimport Storage from './Storage.js';\nimport Animation, { getTime } from './animation/Animation.js';\nimport HandlerProxy from './dom/HandlerProxy.js';\nimport { lum } from './tool/color.js';\nimport { DARK_MODE_THRESHOLD } from './config.js';\nimport Group from './graphic/Group.js';\nvar painterCtors = {};\nvar instances = {};\nfunction delInstance(id) {\n  delete instances[id];\n}\nfunction isDarkMode(backgroundColor) {\n  if (!backgroundColor) {\n    return false;\n  }\n  if (typeof backgroundColor === 'string') {\n    return lum(backgroundColor, 1) < DARK_MODE_THRESHOLD;\n  } else if (backgroundColor.colorStops) {\n    var colorStops = backgroundColor.colorStops;\n    var totalLum = 0;\n    var len = colorStops.length;\n    for (var i = 0; i < len; i++) {\n      totalLum += lum(colorStops[i].color, 1);\n    }\n    totalLum /= len;\n    return totalLum < DARK_MODE_THRESHOLD;\n  }\n  return false;\n}\nvar ZRender = function () {\n  function ZRender(id, dom, opts) {\n    var _this = this;\n    this._sleepAfterStill = 10;\n    this._stillFrameAccum = 0;\n    this._needsRefresh = true;\n    this._needsRefreshHover = true;\n    this._darkMode = false;\n    opts = opts || {};\n    this.dom = dom;\n    this.id = id;\n    var storage = new Storage();\n    var rendererType = opts.renderer || 'canvas';\n    if (!painterCtors[rendererType]) {\n      rendererType = zrUtil.keys(painterCtors)[0];\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!painterCtors[rendererType]) {\n        throw new Error(\"Renderer '\" + rendererType + \"' is not imported. Please import it first.\");\n      }\n    }\n    opts.useDirtyRect = opts.useDirtyRect == null ? false : opts.useDirtyRect;\n    var painter = new painterCtors[rendererType](dom, storage, opts, id);\n    var ssrMode = opts.ssr || painter.ssrOnly;\n    this.storage = storage;\n    this.painter = painter;\n    var handlerProxy = !env.node && !env.worker && !ssrMode ? new HandlerProxy(painter.getViewportRoot(), painter.root) : null;\n    var useCoarsePointer = opts.useCoarsePointer;\n    var usePointerSize = useCoarsePointer == null || useCoarsePointer === 'auto' ? env.touchEventsSupported : !!useCoarsePointer;\n    var defaultPointerSize = 44;\n    var pointerSize;\n    if (usePointerSize) {\n      pointerSize = zrUtil.retrieve2(opts.pointerSize, defaultPointerSize);\n    }\n    this.handler = new Handler(storage, painter, handlerProxy, painter.root, pointerSize);\n    this.animation = new Animation({\n      stage: {\n        update: ssrMode ? null : function () {\n          return _this._flush(true);\n        }\n      }\n    });\n    if (!ssrMode) {\n      this.animation.start();\n    }\n  }\n  ZRender.prototype.add = function (el) {\n    if (this._disposed || !el) {\n      return;\n    }\n    this.storage.addRoot(el);\n    el.addSelfToZr(this);\n    this.refresh();\n  };\n  ZRender.prototype.remove = function (el) {\n    if (this._disposed || !el) {\n      return;\n    }\n    this.storage.delRoot(el);\n    el.removeSelfFromZr(this);\n    this.refresh();\n  };\n  ZRender.prototype.configLayer = function (zLevel, config) {\n    if (this._disposed) {\n      return;\n    }\n    if (this.painter.configLayer) {\n      this.painter.configLayer(zLevel, config);\n    }\n    this.refresh();\n  };\n  ZRender.prototype.setBackgroundColor = function (backgroundColor) {\n    if (this._disposed) {\n      return;\n    }\n    if (this.painter.setBackgroundColor) {\n      this.painter.setBackgroundColor(backgroundColor);\n    }\n    this.refresh();\n    this._backgroundColor = backgroundColor;\n    this._darkMode = isDarkMode(backgroundColor);\n  };\n  ZRender.prototype.getBackgroundColor = function () {\n    return this._backgroundColor;\n  };\n  ZRender.prototype.setDarkMode = function (darkMode) {\n    this._darkMode = darkMode;\n  };\n  ZRender.prototype.isDarkMode = function () {\n    return this._darkMode;\n  };\n  ZRender.prototype.refreshImmediately = function (fromInside) {\n    if (this._disposed) {\n      return;\n    }\n    if (!fromInside) {\n      this.animation.update(true);\n    }\n    this._needsRefresh = false;\n    this.painter.refresh();\n    this._needsRefresh = false;\n  };\n  ZRender.prototype.refresh = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._needsRefresh = true;\n    this.animation.start();\n  };\n  ZRender.prototype.flush = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._flush(false);\n  };\n  ZRender.prototype._flush = function (fromInside) {\n    var triggerRendered;\n    var start = getTime();\n    if (this._needsRefresh) {\n      triggerRendered = true;\n      this.refreshImmediately(fromInside);\n    }\n    if (this._needsRefreshHover) {\n      triggerRendered = true;\n      this.refreshHoverImmediately();\n    }\n    var end = getTime();\n    if (triggerRendered) {\n      this._stillFrameAccum = 0;\n      this.trigger('rendered', {\n        elapsedTime: end - start\n      });\n    } else if (this._sleepAfterStill > 0) {\n      this._stillFrameAccum++;\n      if (this._stillFrameAccum > this._sleepAfterStill) {\n        this.animation.stop();\n      }\n    }\n  };\n  ZRender.prototype.setSleepAfterStill = function (stillFramesCount) {\n    this._sleepAfterStill = stillFramesCount;\n  };\n  ZRender.prototype.wakeUp = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.start();\n    this._stillFrameAccum = 0;\n  };\n  ZRender.prototype.refreshHover = function () {\n    this._needsRefreshHover = true;\n  };\n  ZRender.prototype.refreshHoverImmediately = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._needsRefreshHover = false;\n    if (this.painter.refreshHover && this.painter.getType() === 'canvas') {\n      this.painter.refreshHover();\n    }\n  };\n  ZRender.prototype.resize = function (opts) {\n    if (this._disposed) {\n      return;\n    }\n    opts = opts || {};\n    this.painter.resize(opts.width, opts.height);\n    this.handler.resize();\n  };\n  ZRender.prototype.clearAnimation = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.clear();\n  };\n  ZRender.prototype.getWidth = function () {\n    if (this._disposed) {\n      return;\n    }\n    return this.painter.getWidth();\n  };\n  ZRender.prototype.getHeight = function () {\n    if (this._disposed) {\n      return;\n    }\n    return this.painter.getHeight();\n  };\n  ZRender.prototype.setCursorStyle = function (cursorStyle) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.setCursorStyle(cursorStyle);\n  };\n  ZRender.prototype.findHover = function (x, y) {\n    if (this._disposed) {\n      return;\n    }\n    return this.handler.findHover(x, y);\n  };\n  ZRender.prototype.on = function (eventName, eventHandler, context) {\n    if (!this._disposed) {\n      this.handler.on(eventName, eventHandler, context);\n    }\n    return this;\n  };\n  ZRender.prototype.off = function (eventName, eventHandler) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.off(eventName, eventHandler);\n  };\n  ZRender.prototype.trigger = function (eventName, event) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.trigger(eventName, event);\n  };\n  ZRender.prototype.clear = function () {\n    if (this._disposed) {\n      return;\n    }\n    var roots = this.storage.getRoots();\n    for (var i = 0; i < roots.length; i++) {\n      if (roots[i] instanceof Group) {\n        roots[i].removeSelfFromZr(this);\n      }\n    }\n    this.storage.delAllRoots();\n    this.painter.clear();\n  };\n  ZRender.prototype.dispose = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.stop();\n    this.clear();\n    this.storage.dispose();\n    this.painter.dispose();\n    this.handler.dispose();\n    this.animation = this.storage = this.painter = this.handler = null;\n    this._disposed = true;\n    delInstance(this.id);\n  };\n  return ZRender;\n}();\nexport function init(dom, opts) {\n  var zr = new ZRender(zrUtil.guid(), dom, opts);\n  instances[zr.id] = zr;\n  return zr;\n}\nexport function dispose(zr) {\n  zr.dispose();\n}\nexport function disposeAll() {\n  for (var key in instances) {\n    if (instances.hasOwnProperty(key)) {\n      instances[key].dispose();\n    }\n  }\n  instances = {};\n}\nexport function getInstance(id) {\n  return instances[id];\n}\nexport function registerPainter(name, Ctor) {\n  painterCtors[name] = Ctor;\n}\nvar ssrDataGetter;\nexport function getElementSSRData(el) {\n  if (typeof ssrDataGetter === 'function') {\n    return ssrDataGetter(el);\n  }\n}\nexport function registerSSRDataGetter(getter) {\n  ssrDataGetter = getter;\n}\nexport var version = '5.6.1';\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}