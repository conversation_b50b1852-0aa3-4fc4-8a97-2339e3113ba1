{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as symbolUtil from '../../util/symbol.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar RadarView = /** @class */function (_super) {\n  __extends(RadarView, _super);\n  function RadarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarView.type;\n    return _this;\n  }\n  RadarView.prototype.render = function (seriesModel, ecModel, api) {\n    var polar = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    function createSymbol(data, idx) {\n      var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';\n      if (symbolType === 'none') {\n        return;\n      }\n      var symbolSize = symbolUtil.normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n      var symbolPath = symbolUtil.createSymbol(symbolType, -1, -1, 2, 2);\n      var symbolRotate = data.getItemVisual(idx, 'symbolRotate') || 0;\n      symbolPath.attr({\n        style: {\n          strokeNoScale: true\n        },\n        z2: 100,\n        scaleX: symbolSize[0] / 2,\n        scaleY: symbolSize[1] / 2,\n        rotation: symbolRotate * Math.PI / 180 || 0\n      });\n      return symbolPath;\n    }\n    function updateSymbols(oldPoints, newPoints, symbolGroup, data, idx, isInit) {\n      // Simply rerender all\n      symbolGroup.removeAll();\n      for (var i = 0; i < newPoints.length - 1; i++) {\n        var symbolPath = createSymbol(data, idx);\n        if (symbolPath) {\n          symbolPath.__dimIdx = i;\n          if (oldPoints[i]) {\n            symbolPath.setPosition(oldPoints[i]);\n            graphic[isInit ? 'initProps' : 'updateProps'](symbolPath, {\n              x: newPoints[i][0],\n              y: newPoints[i][1]\n            }, seriesModel, idx);\n          } else {\n            symbolPath.setPosition(newPoints[i]);\n          }\n          symbolGroup.add(symbolPath);\n        }\n      }\n    }\n    function getInitialPoints(points) {\n      return zrUtil.map(points, function (pt) {\n        return [polar.cx, polar.cy];\n      });\n    }\n    data.diff(oldData).add(function (idx) {\n      var points = data.getItemLayout(idx);\n      if (!points) {\n        return;\n      }\n      var polygon = new graphic.Polygon();\n      var polyline = new graphic.Polyline();\n      var target = {\n        shape: {\n          points: points\n        }\n      };\n      polygon.shape.points = getInitialPoints(points);\n      polyline.shape.points = getInitialPoints(points);\n      graphic.initProps(polygon, target, seriesModel, idx);\n      graphic.initProps(polyline, target, seriesModel, idx);\n      var itemGroup = new graphic.Group();\n      var symbolGroup = new graphic.Group();\n      itemGroup.add(polyline);\n      itemGroup.add(polygon);\n      itemGroup.add(symbolGroup);\n      updateSymbols(polyline.shape.points, points, symbolGroup, data, idx, true);\n      data.setItemGraphicEl(idx, itemGroup);\n    }).update(function (newIdx, oldIdx) {\n      var itemGroup = oldData.getItemGraphicEl(oldIdx);\n      var polyline = itemGroup.childAt(0);\n      var polygon = itemGroup.childAt(1);\n      var symbolGroup = itemGroup.childAt(2);\n      var target = {\n        shape: {\n          points: data.getItemLayout(newIdx)\n        }\n      };\n      if (!target.shape.points) {\n        return;\n      }\n      updateSymbols(polyline.shape.points, target.shape.points, symbolGroup, data, newIdx, false);\n      saveOldStyle(polygon);\n      saveOldStyle(polyline);\n      graphic.updateProps(polyline, target, seriesModel);\n      graphic.updateProps(polygon, target, seriesModel);\n      data.setItemGraphicEl(newIdx, itemGroup);\n    }).remove(function (idx) {\n      group.remove(oldData.getItemGraphicEl(idx));\n    }).execute();\n    data.eachItemGraphicEl(function (itemGroup, idx) {\n      var itemModel = data.getItemModel(idx);\n      var polyline = itemGroup.childAt(0);\n      var polygon = itemGroup.childAt(1);\n      var symbolGroup = itemGroup.childAt(2);\n      // Radar uses the visual encoded from itemStyle.\n      var itemStyle = data.getItemVisual(idx, 'style');\n      var color = itemStyle.fill;\n      group.add(itemGroup);\n      polyline.useStyle(zrUtil.defaults(itemModel.getModel('lineStyle').getLineStyle(), {\n        fill: 'none',\n        stroke: color\n      }));\n      setStatesStylesFromModel(polyline, itemModel, 'lineStyle');\n      setStatesStylesFromModel(polygon, itemModel, 'areaStyle');\n      var areaStyleModel = itemModel.getModel('areaStyle');\n      var polygonIgnore = areaStyleModel.isEmpty() && areaStyleModel.parentModel.isEmpty();\n      polygon.ignore = polygonIgnore;\n      zrUtil.each(['emphasis', 'select', 'blur'], function (stateName) {\n        var stateModel = itemModel.getModel([stateName, 'areaStyle']);\n        var stateIgnore = stateModel.isEmpty() && stateModel.parentModel.isEmpty();\n        // Won't be ignore if normal state is not ignore.\n        polygon.ensureState(stateName).ignore = stateIgnore && polygonIgnore;\n      });\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: color,\n        opacity: 0.7,\n        decal: itemStyle.decal\n      }));\n      var emphasisModel = itemModel.getModel('emphasis');\n      var itemHoverStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n      symbolGroup.eachChild(function (symbolPath) {\n        if (symbolPath instanceof ZRImage) {\n          var pathStyle = symbolPath.style;\n          symbolPath.useStyle(zrUtil.extend({\n            // TODO other properties like x, y ?\n            image: pathStyle.image,\n            x: pathStyle.x,\n            y: pathStyle.y,\n            width: pathStyle.width,\n            height: pathStyle.height\n          }, itemStyle));\n        } else {\n          symbolPath.useStyle(itemStyle);\n          symbolPath.setColor(color);\n          symbolPath.style.strokeNoScale = true;\n        }\n        var pathEmphasisState = symbolPath.ensureState('emphasis');\n        pathEmphasisState.style = zrUtil.clone(itemHoverStyle);\n        var defaultText = data.getStore().get(data.getDimensionIndex(symbolPath.__dimIdx), idx);\n        (defaultText == null || isNaN(defaultText)) && (defaultText = '');\n        setLabelStyle(symbolPath, getLabelStatesModels(itemModel), {\n          labelFetcher: data.hostModel,\n          labelDataIndex: idx,\n          labelDimIndex: symbolPath.__dimIdx,\n          defaultText: defaultText,\n          inheritColor: color,\n          defaultOpacity: itemStyle.opacity\n        });\n      });\n      toggleHoverEmphasis(itemGroup, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n    });\n    this._data = data;\n  };\n  RadarView.prototype.remove = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  RadarView.type = 'radar';\n  return RadarView;\n}(ChartView);\nexport default RadarView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}