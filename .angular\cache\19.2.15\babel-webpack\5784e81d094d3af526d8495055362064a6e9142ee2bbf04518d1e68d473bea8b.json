{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/question-bank-services/question-bank-question.service\";\nimport * as i2 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"@angular/material/tooltip\";\nconst _c0 = a0 => [a0];\nfunction QuestionBankQuestionsViewComponent_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function QuestionBankQuestionsViewComponent_a_7_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.NewQuestion());\n    });\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"img\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"QUESTION_BANK.ADD_QUESTION\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 21);\n    i0.ɵɵlistener(\"opened\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_mat_expansion_panel_opened_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setOpened(i_r5));\n    })(\"closed\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_mat_expansion_panel_closed_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setClosed(i_r5));\n    });\n    i0.ɵɵelementStart(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\", 22)(3, \"div\", 23)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"label\", 25)(8, \"input\", 26);\n    i0.ɵɵlistener(\"change\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_input_change_8_listener() {\n      const QBQ_r6 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onCheckboxChange(QBQ_r6));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_input_ngModelChange_8_listener($event) {\n      const QBQ_r6 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(QBQ_r6.isActive, $event) || (QBQ_r6.isActive = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"span\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_a_click_10_listener() {\n      const QBQ_r6 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.confirmDialog(QBQ_r6.id));\n    });\n    i0.ɵɵelement(11, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template_a_click_12_listener() {\n      const QBQ_r6 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.loadQuestion(QBQ_r6.id));\n    });\n    i0.ɵɵelement(13, \"img\", 30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"p\", 31);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const QBQ_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? QBQ_r6.engQuestion ? QBQ_r6.engQuestion : QBQ_r6.arabQuestion : QBQ_r6.arabQuestion, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", QBQ_r6.isActive);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.edit, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? QBQ_r6.engAnswer ? QBQ_r6.engAnswer : QBQ_r6.arabAnswer : QBQ_r6.arabAnswer);\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" col-lg-12 my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \" \");\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"mat-accordion\", 18, 0);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function QuestionBankQuestionsViewComponent_ng_container_10_section_1_Template_mat_accordion_cdkDropListDropped_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.drop($event));\n    });\n    i0.ɵɵtemplate(5, QuestionBankQuestionsViewComponent_ng_container_10_section_1_mat_expansion_panel_5_Template, 16, 4, \"mat-expansion-panel\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, QuestionBankQuestionsViewComponent_ng_container_10_section_1_div_6_Template, 2, 4, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r1.questionBankQuestionList)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(4, _c0, ctx_r1.items1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionBankQuestionList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resultMessage);\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_2_mat_expansion_panel_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 34);\n    i0.ɵɵlistener(\"opened\", function QuestionBankQuestionsViewComponent_ng_container_10_section_2_mat_expansion_panel_4_Template_mat_expansion_panel_opened_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setOpened(i_r8));\n    })(\"closed\", function QuestionBankQuestionsViewComponent_ng_container_10_section_2_mat_expansion_panel_4_Template_mat_expansion_panel_closed_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setClosed(i_r8));\n    });\n    i0.ɵɵelementStart(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\", 22)(3, \"div\", 23)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(6, \"p\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const QBQ_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? QBQ_r9.engQuestion : QBQ_r9.arabQuestion, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? QBQ_r9.engAnswer : QBQ_r9.arabAnswer, \"\");\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" col-lg-12 my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \" \");\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 15)(1, \"div\", 32)(2, \"div\", 17)(3, \"mat-accordion\");\n    i0.ɵɵtemplate(4, QuestionBankQuestionsViewComponent_ng_container_10_section_2_mat_expansion_panel_4_Template, 8, 2, \"mat-expansion-panel\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, QuestionBankQuestionsViewComponent_ng_container_10_section_2_div_5_Template, 2, 4, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionBankQuestionList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resultMessage);\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuestionBankQuestionsViewComponent_ng_container_10_section_1_Template, 7, 6, \"section\", 14)(2, QuestionBankQuestionsViewComponent_ng_container_10_section_2_Template, 6, 2, \"section\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.roleManagmentService.isAdmin());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.roleManagmentService.isAdmin());\n  }\n}\nfunction QuestionBankQuestionsViewComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"QUESTION_BANK.NO_DATA\"), \" \");\n  }\n}\nexport let QuestionBankQuestionsViewComponent = /*#__PURE__*/(() => {\n  class QuestionBankQuestionsViewComponent {\n    questionBankQuestionService;\n    alert;\n    translate;\n    dialog;\n    roleService;\n    roleManagmentService;\n    imagesPathesService;\n    filterErrorMessage;\n    questionBankQuestionList = [];\n    questionBankQuestionFilter = {};\n    selectedCategoryId = {\n      id: '',\n      arabCatgName: '',\n      engCatgName: ''\n    };\n    selectedQuestionId = new EventEmitter();\n    isViewAdd = new EventEmitter();\n    // @Input() isQuestionSave={isSave:false,catogeryId:''}; \n    isQuestionSave;\n    panelOpenState = false;\n    currentlyOpenedItemIndex = -1;\n    langEnum = LanguageEnum;\n    isSave = false;\n    resultMessage = {};\n    items1;\n    questionBankQuestionUpdateOrderBy = {};\n    listOrder;\n    currentUser;\n    role = RoleEnum;\n    constructor(questionBankQuestionService, alert, translate, dialog, roleService, roleManagmentService, imagesPathesService) {\n      this.questionBankQuestionService = questionBankQuestionService;\n      this.alert = alert;\n      this.translate = translate;\n      this.dialog = dialog;\n      this.roleService = roleService;\n      this.roleManagmentService = roleManagmentService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getQuestionBankQuestions(\"\");\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n    }\n    ngOnChanges(changes) {\n      if (this.isQuestionSave === true) {\n        this.getQuestionBankQuestions(this.selectedCategoryId.id);\n      }\n      if (changes.selectedCategoryId?.currentValue.id !== undefined) {\n        this.getQuestionBankQuestions(changes.selectedCategoryId.currentValue.id);\n        this.selectedCategoryId.id = changes.selectedCategoryId.currentValue.id;\n      }\n      this.getQuestionBankQuestions(\"\");\n    }\n    searchQuestions(text) {\n      this.questionBankQuestionList = [];\n      this.getQuestionBankQuestions(this.selectedCategoryId.id, text);\n    }\n    getQuestionBankQuestions(CategoryId, text) {\n      this.filterErrorMessage = \"\";\n      this.resultMessage = {};\n      this.questionBankQuestionFilter.skip = 0;\n      this.questionBankQuestionFilter.take = **********;\n      this.questionBankQuestionFilter.catgyId = CategoryId;\n      this.questionBankQuestionFilter.text = text;\n      if (CategoryId != \"\") {\n        this.questionBankQuestionService.getQuestionBankQuestionsFilter(this.questionBankQuestionFilter).subscribe(res => {\n          let response = res;\n          if (response.isSuccess) {\n            let items = response.data;\n            if (this.roleService.isAdmin()) {\n              this.questionBankQuestionList = items;\n            } else {\n              this.questionBankQuestionList = items.filter(i => i.isActive);\n            }\n          } else {\n            this.questionBankQuestionList = [];\n            this.filterErrorMessage = response.message;\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.questionBankQuestionList = [];\n      }\n    }\n    clearFilter() {\n      this.questionBankQuestionFilter = {};\n      this.questionBankQuestionFilter.skip = 0;\n      this.questionBankQuestionFilter.take = 1000;\n      this.getQuestionBankQuestions(this.selectedCategoryId.id);\n    }\n    delete_Question(id) {\n      this.questionBankQuestionService.deleteQuestionBankQuestion(id || '').subscribe(res => {\n        alert(\"Delete Sucssed\");\n        this.getQuestionBankQuestions(this.selectedCategoryId.id);\n      }, error => {\n        //logging\n      });\n    }\n    loadQuestion(id) {\n      this.selectedQuestionId?.emit(id);\n    }\n    NewQuestion() {\n      this.selectedQuestionId?.emit('');\n    }\n    confirmDialog(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this question\" : \"هل متأكد من حذف هذا السؤال\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Question' : 'حذف سؤال', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.questionBankQuestionService.deleteQuestionBankQuestion(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alert.success(res.message || '');\n              this.getQuestionBankQuestions(this.selectedCategoryId.id);\n            } else {\n              this.alert.error(res.message || '');\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    setOpened(itemIndex) {\n      this.currentlyOpenedItemIndex = itemIndex;\n    }\n    setClosed(itemIndex) {\n      if (this.currentlyOpenedItemIndex === itemIndex) {\n        this.currentlyOpenedItemIndex = -1;\n      }\n    }\n    onCheckboxChange(questionBankQuestionUpdate) {\n      this.questionBankQuestionService.updateQuestionBankQuestion(questionBankQuestionUpdate).subscribe(res => {\n        if (res.isSuccess) {\n          this.resultMessage = {\n            message: res.message || \"\",\n            type: BaseConstantModel.SUCCESS_TYPE\n          };\n          setTimeout(() => {\n            this.resultMessage = {\n              message: \"\",\n              type: \"\"\n            };\n          }, 1500);\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          setTimeout(() => {\n            this.resultMessage = {\n              message: \"\",\n              type: \"\"\n            };\n          }, 1500);\n        }\n      }, error => {\n        //logging\n      });\n    }\n    drop(event) {\n      if (event.previousContainer === event.container) {\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n        this.listOrder = [];\n        for (let i = 0; i <= event.container.data.length - 1; i++) {\n          this.listOrder?.push(event.previousContainer.data[i].order || i + 1);\n        }\n        this.questionBankQuestionUpdateOrderBy.categoryId = this.selectedCategoryId.id;\n        this.questionBankQuestionUpdateOrderBy.orderList = this.listOrder;\n        this.questionBankQuestionService.updateOrderQuestionBankQuestion(this.questionBankQuestionUpdateOrderBy).subscribe(res => {\n          if (res.isSuccess) {\n            this.getQuestionBankQuestions(this.selectedCategoryId.id);\n          } else {}\n        }, error => {\n          //logging\n        });\n      } else {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      }\n    }\n    static ɵfac = function QuestionBankQuestionsViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionBankQuestionsViewComponent)(i0.ɵɵdirectiveInject(i1.QuestionBankQuestionService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.RoleManagementService), i0.ɵɵdirectiveInject(i5.RoleManagementService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuestionBankQuestionsViewComponent,\n      selectors: [[\"app-question-bank-questions-view\"]],\n      inputs: {\n        selectedCategoryId: \"selectedCategoryId\",\n        isQuestionSave: \"isQuestionSave\"\n      },\n      outputs: {\n        selectedQuestionId: \"selectedQuestionId\",\n        isViewAdd: \"isViewAdd\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 12,\n      vars: 5,\n      consts: [[\"unassignedList\", \"cdkDropList\"], [1, \"question-list\"], [1, \"row\", \"pl-3\", \"pr-3\", \"align-items-center\", \"mb-3\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"p_header\", \"mb-0\", \"ellipsis\", 3, \"matTooltip\"], [1, \"row\", \"align-items-center\", \"just-content-between\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-12\", \"add_question\"], [3, \"routerLink\", \"click\", 4, \"ngIf\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-12\"], [3, \"searchTerm\"], [4, \"ngIf\"], [3, \"click\", \"routerLink\"], [1, \"link_Add\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [\"class\", \"internal_scroll_list_group\", 4, \"ngIf\"], [1, \"internal_scroll_list_group\"], [1, \"w-100\", \"row\", \"contanier_matrial-card\", \"questionBank\"], [1, \"col-12\", \"mb-3\"], [\"cdkDropList\", \"\", 3, \"cdkDropListDropped\", \"cdkDropListData\", \"cdkDropListConnectedTo\"], [\"cdkDrag\", \"\", 3, \"opened\", \"closed\", 4, \"ngFor\", \"ngForOf\"], [3, \"class\", 4, \"ngIf\"], [\"cdkDrag\", \"\", 3, \"opened\", \"closed\"], [1, \"qestionHeader\"], [1, \"col\", \"span_question\"], [1, \"col-.5\", \"chicx_box\"], [1, \"switch\"], [\"type\", \"checkbox\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [1, \"slider\", \"round\"], [3, \"click\"], [1, \"far\", \"fa-trash-alt\"], [3, \"src\"], [1, \"p_answer\"], [1, \"w-100\", \"row\", \"contanier_matrial-card\", \"questionBank\", \"student\"], [3, \"opened\", \"closed\", 4, \"ngFor\", \"ngForOf\"], [3, \"opened\", \"closed\"], [1, \"No_data\"]],\n      template: function QuestionBankQuestionsViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"p\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtemplate(7, QuestionBankQuestionsViewComponent_a_7_Template, 5, 4, \"a\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"app-search-input\", 9);\n          i0.ɵɵlistener(\"searchTerm\", function QuestionBankQuestionsViewComponent_Template_app_search_input_searchTerm_9_listener($event) {\n            return ctx.searchQuestions($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, QuestionBankQuestionsViewComponent_ng_container_10_Template, 3, 2, \"ng-container\", 10)(11, QuestionBankQuestionsViewComponent_ng_container_11_Template, 4, 3, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx.translate.currentLang === ctx.langEnum.en ? ctx.selectedCategoryId.engCatgName : ctx.selectedCategoryId.arabCatgName);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.selectedCategoryId.engCatgName : ctx.selectedCategoryId.arabCatgName, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.roleManagmentService.isAdmin());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.questionBankQuestionList && ctx.questionBankQuestionList.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.questionBankQuestionList || ctx.questionBankQuestionList.length === 0);\n        }\n      },\n      dependencies: [i7.CheckboxControlValueAccessor, i7.NgControlStatus, i7.NgModel, i8.NgForOf, i8.NgIf, i9.RouterLink, i10.MatTooltip, i3.TranslatePipe],\n      styles: [\".question-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;margin-top:1rem;margin-bottom:0}.question-list[_ngcontent-%COMP%]:lang(en){margin-right:0rem}.question-list[_ngcontent-%COMP%]:lang(ar){margin-left:1rem}.question-list[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:.3rem;margin-left:.3rem}.question-list[_ngcontent-%COMP%]   .contanier_matrial-card[_ngcontent-%COMP%]{overflow-y:auto}.question-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]{height:69vh;overflow-y:auto;display:flex;justify-content:center}.question-list[_ngcontent-%COMP%]   .qestionHeader[_ngcontent-%COMP%]{background:#f2f1f1de 0% 0% no-repeat padding-box;opacity:1;border-radius:10rem;height:100%;width:100%}.question-list[_ngcontent-%COMP%]   .mat-expansion-panel-header.mat-expanded[_ngcontent-%COMP%]:focus, .question-list[_ngcontent-%COMP%]   .mat-expansion-panel-header.mat-expanded[_ngcontent-%COMP%]:hover, .question-list[_ngcontent-%COMP%]   .mat-expansion-panel-header._mat-animation-noopable[_ngcontent-%COMP%], .question-list[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{background-color:#f2f1f1de}.question-list[_ngcontent-%COMP%]   .mat-accordion[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]{margin-bottom:1rem}.question-list[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem;height:3.75rem;align-items:center}.question-list[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded):not([aria-disabled=true])   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover{background-color:#f2f1f1de}.question-list[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{color:var(--second_color);margin-bottom:3rem}.question-list[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]{color:#000;margin-bottom:1rem;font-size:1.3rem;font-weight:700}.question-list[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]:lang(en){padding:0;float:left;text-align:left}.question-list[_ngcontent-%COMP%]   .p_header[_ngcontent-%COMP%]:lang(ar){padding:0;float:right!important;text-align:right}.question-list[_ngcontent-%COMP%]   .span_icon[_ngcontent-%COMP%]:lang(en){float:right;text-align:right;padding:0;margin:0}.question-list[_ngcontent-%COMP%]   .span_question[_ngcontent-%COMP%]{padding-right:0;padding-left:0;font-size:1.2rem}.question-list[_ngcontent-%COMP%]   .span_question[_ngcontent-%COMP%]:lang(ar){text-align:right}.question-list[_ngcontent-%COMP%]   .span_question[_ngcontent-%COMP%]:lang(en){text-align:left}.question-list[_ngcontent-%COMP%]   .add_question[_ngcontent-%COMP%]:lang(ar){text-align:right}.question-list[_ngcontent-%COMP%]   .p_answer[_ngcontent-%COMP%]:lang(ar){text-align:right}.question-list[_ngcontent-%COMP%]   .p_answer[_ngcontent-%COMP%]:lang(en){text-align:left}.question-list[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .question-list[_ngcontent-%COMP%]   .fa-pencil-alt[_ngcontent-%COMP%]{color:#fff;margin-right:.5rem;margin-left:.5rem}.question-list[_ngcontent-%COMP%]   .chicx_box[_ngcontent-%COMP%]:lang(ar){float:right}.question-list[_ngcontent-%COMP%]   .chicx_box[_ngcontent-%COMP%]:lang(en){float:left}.question-list[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{font: 1.25rem Almarai;color:#000;margin-bottom:1rem}.question-list[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(en){text-align:left;padding-top:2rem;padding-left:7rem}.question-list[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]:lang(ar){text-align:right;padding-top:2rem;padding-right:7rem}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]{position:relative;display:inline-block;width:2.5rem;height:1.25rem;margin-bottom:0!important}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{opacity:0;width:0;height:0}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   .slider[_ngcontent-%COMP%]{position:absolute;cursor:pointer;inset:0;background-color:#d6d7d8;transition:.4s}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   .slider[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";height:1rem;width:1rem;left:.188rem;bottom:.156rem;background-color:#fff;transition:.4s}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]{background-color:var(--second_color)}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus + .slider[_ngcontent-%COMP%]{box-shadow:0 0 .063rem var(--second_color)}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]:before{transform:translate(1.25rem)}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   .slider.round[_ngcontent-%COMP%]{border-radius:2.125rem}.question-list[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   .slider.round[_ngcontent-%COMP%]:before{border-radius:50%}.question-list[_ngcontent-%COMP%]   .offset-4[_ngcontent-%COMP%]:lang(ar){margin-right:33.333333%;margin-left:0!important}.question-list[_ngcontent-%COMP%]   .offset-4[_ngcontent-%COMP%]:lang(en){margin-left:33.333333%;margin-right:0!important}.question-list[_ngcontent-%COMP%]   .col-4[_ngcontent-%COMP%]:lang(en){text-align:left}.question-list[_ngcontent-%COMP%]   .col-4[_ngcontent-%COMP%]:lang(ar){text-align:right}@media (max-width: 64rem){.ellipsis[_ngcontent-%COMP%]{width:unset}.question-list[_ngcontent-%COMP%]   .link_Add[_ngcontent-%COMP%]{font-size:1.2rem}}@media (min-width: 62rem){.offset-cu-21[_ngcontent-%COMP%]{margin-left:21%;margin-right:0}.offset-cu-21[_ngcontent-%COMP%]:lang(ar){margin-right:21%;margin-left:0}}\"]\n    });\n  }\n  return QuestionBankQuestionsViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}