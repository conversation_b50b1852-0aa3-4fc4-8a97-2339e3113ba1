{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { SettingAnswerTypeEnum } from 'src/app/core/enums/setting-answerType-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../../confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/program-services/program-conditions.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nfunction CustomConditionsComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.TEXT\"), \" \");\n  }\n}\nfunction CustomConditionsComponent_div_0_div_5_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", item_r2.text, \" \");\n  }\n}\nfunction CustomConditionsComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"select\", 11);\n    i0.ɵɵtemplate(2, CustomConditionsComponent_div_0_div_5_option_2_Template, 2, 1, \"option\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerList);\n  }\n}\nfunction CustomConditionsComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.TOGGEL\"), \" \");\n  }\n}\nfunction CustomConditionsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3);\n    i0.ɵɵtemplate(4, CustomConditionsComponent_div_0_div_4_Template, 3, 3, \"div\", 4)(5, CustomConditionsComponent_div_0_div_5_Template, 3, 1, \"div\", 5)(6, CustomConditionsComponent_div_0_div_6_Template, 3, 3, \"div\", 6);\n    i0.ɵɵelementContainerStart(7);\n    i0.ɵɵelementStart(8, \"span\", 7)(9, \"img\", 8);\n    i0.ɵɵlistener(\"click\", function CustomConditionsComponent_div_0_Template_img_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editCustomCondition());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"img\", 8);\n    i0.ɵɵlistener(\"click\", function CustomConditionsComponent_div_0_Template_img_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteCustomCondition());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.customConditionsModel.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Choices);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Toggel);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.edit, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.trash, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomConditionsComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.TEXT\"), \" \");\n  }\n}\nfunction CustomConditionsComponent_div_1_div_6_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", item_r5.text, \" \");\n  }\n}\nfunction CustomConditionsComponent_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"select\", 11);\n    i0.ɵɵtemplate(2, CustomConditionsComponent_div_1_div_6_option_2_Template, 2, 1, \"option\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerList);\n  }\n}\nfunction CustomConditionsComponent_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"CONDITIONDS.TOGGEL\"), \" \");\n  }\n}\nfunction CustomConditionsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"img\", 13);\n    i0.ɵɵlistener(\"click\", function CustomConditionsComponent_div_1_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmDialog());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 3);\n    i0.ɵɵtemplate(5, CustomConditionsComponent_div_1_div_5_Template, 3, 3, \"div\", 4)(6, CustomConditionsComponent_div_1_div_6_Template, 3, 1, \"div\", 14)(7, CustomConditionsComponent_div_1_div_7_Template, 3, 3, \"div\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.customConditionsModel.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.close, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Choices);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.customConditionsModel.conditionModel == null ? null : ctx_r2.customConditionsModel.conditionModel.answerType) === ctx_r2.answerType.Toggel);\n  }\n}\nexport let CustomConditionsComponent = /*#__PURE__*/(() => {\n  class CustomConditionsComponent {\n    languageService;\n    translate;\n    programConditionsService;\n    dialog;\n    alertify;\n    imagesPathesService;\n    progIdToLoadProgCond = new EventEmitter();\n    editcustomConditionsCard = new EventEmitter();\n    deleteCustomConditionsCard = new EventEmitter();\n    customConditionsModel = {};\n    isViewToProgCond = false;\n    answerType = SettingAnswerTypeEnum;\n    resultMessage = {};\n    updateProgramConditionDetailsModel = {};\n    result = '';\n    constructor(languageService, translate, programConditionsService, dialog, alertify, imagesPathesService) {\n      this.languageService = languageService;\n      this.translate = translate;\n      this.programConditionsService = programConditionsService;\n      this.dialog = dialog;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {}\n    // saveProgramConditions() {\n    //   this.updateProgramConditionDetailsModel.id = this.customConditionsModel.id;\n    //   this.updateProgramConditionDetailsModel.progCondDetails = JSON.stringify(this.customConditionsModel.conditionModel);\n    //   this.programConditionsService.updateProgramConditionDetails(this.updateProgramConditionDetailsModel).subscribe(res => {\n    //     let response = <BaseResponseModel>res;\n    //     if (response.isSuccess) {\n    //       this.resultMessage = {\n    //         message: res.message || \"\",\n    //         type: BaseConstantModel.SUCCESS_TYPE\n    //       }\n    //     }\n    //     else {\n    //       this.resultMessage = {\n    //         message: res.message,\n    //         type: BaseConstantModel.DANGER_TYPE\n    //       }\n    //     }\n    //   },\n    //     error => {\n    //       this.resultMessage = {\n    //         message: error,\n    //         type: BaseConstantModel.DANGER_TYPE\n    //       }\n    //     }\n    //   );\n    // }\n    confirmDialog() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this condition\" : \"هل متأكد من حذف هذا الشرط\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete condition' : 'حذف الشرط', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        this.result = dialogResult;\n        if (dialogResult == true) {\n          this.programConditionsService.deleteProgramCondition(this.customConditionsModel.id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.progIdToLoadProgCond.emit(this.customConditionsModel.id);\n              this.alertify.success(res.message || '');\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    editCustomCondition() {\n      this.editcustomConditionsCard.emit(this.customConditionsModel);\n    }\n    deleteCustomCondition() {\n      this.deleteCustomConditionsCard.emit(this.customConditionsModel?.id);\n    }\n    static ɵfac = function CustomConditionsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomConditionsComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ProgramConditionsService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomConditionsComponent,\n      selectors: [[\"app-custom-conditions\"]],\n      inputs: {\n        customConditionsModel: \"customConditionsModel\",\n        isViewToProgCond: \"isViewToProgCond\"\n      },\n      outputs: {\n        progIdToLoadProgCond: \"progIdToLoadProgCond\",\n        editcustomConditionsCard: \"editcustomConditionsCard\",\n        deleteCustomConditionsCard: \"deleteCustomConditionsCard\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"card-setting\", 4, \"ngIf\"], [1, \"card-setting\"], [1, \"head\"], [1, \"body\"], [\"class\", \"details\", 4, \"ngIf\"], [\"class\", \"form-group w-75\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"icon_actions\"], [3, \"click\", \"src\"], [1, \"details\"], [1, \"form-group\", \"w-75\"], [\"id\", \"sel1\", 1, \"form-control\"], [4, \"ngFor\", \"ngForOf\"], [1, \"close\", 3, \"click\", \"src\"], [\"class\", \"form-group w-100\", 4, \"ngIf\"], [1, \"form-group\", \"w-100\"]],\n      template: function CustomConditionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CustomConditionsComponent_div_0_Template, 11, 6, \"div\", 0)(1, CustomConditionsComponent_div_1_Template, 8, 5, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isViewToProgCond);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isViewToProgCond);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card-setting[_ngcontent-%COMP%]{padding:1rem;width:100%;height:100%;background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem;opacity:1}.card-setting[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:1rem;margin-bottom:.5rem;display:flex;justify-content:space-between}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-size:.875rem;color:#333}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(en){text-align:left}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]:lang(ar){text-align:right}.card-setting[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto}.card-setting[_ngcontent-%COMP%]   .w-90[_ngcontent-%COMP%]{width:90%!important}.card-setting[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0}.card-setting[_ngcontent-%COMP%]   .buttons-center[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.card-setting[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin-left:.4rem;margin-right:.4rem}.title[_ngcontent-%COMP%]{color:var(--second_color);font-size:.875rem;font-weight:700}.requiredFlag[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.875rem;font-family:normal normal normal;opacity:1}.card-setting[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]{align-items:flex-start}\"]\n    });\n  }\n  return CustomConditionsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}