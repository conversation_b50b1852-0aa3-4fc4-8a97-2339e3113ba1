{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from '../../../../../core/enums/language-enum.enum';\nimport { BaseConstantModel } from '../../../../../core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../../core/services/teacher-appointment-services/teacher-appointment.service\";\nimport * as i4 from \"../../../../../core/services/language-services/language.service\";\nimport * as i5 from \"../../../../../core/services/role-management/role-management.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/common\";\nfunction TeacherAvailableAppointmentsComponent_ng_container_7_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const t_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate4(\" \", i0.ɵɵpipeBind1(2, 4, \"GENERAL.FROM\"), \" \", t_r1.timeFrom, \" \", i0.ɵɵpipeBind1(3, 6, \"GENERAL.TO\"), \" \", t_r1.timeTo, \" \");\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"div\", 12)(3, \"div\", 13)(4, \"p\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\");\n    i0.ɵɵtemplate(7, TeacherAvailableAppointmentsComponent_ng_container_7_p_7_Template, 4, 8, \"p\", 15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"matTooltip\", \" \", ctx_r2.translate.currentLang === ctx_r2.lang.en ? item_r2 == null ? null : item_r2.availableDay == null ? null : item_r2.availableDay.nameEn : item_r2 == null ? null : item_r2.availableDay == null ? null : item_r2.availableDay.nameAr, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.translate.currentLang === ctx_r2.lang.en ? item_r2 == null ? null : item_r2.availableDay == null ? null : item_r2.availableDay.nameEn : item_r2 == null ? null : item_r2.availableDay == null ? null : item_r2.availableDay.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r2 == null ? null : item_r2.teacherAvailableTimes);\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_div_10_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TeacherAvailableAppointmentsComponent_div_10_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.appointmentRequestDetails());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"TEACHER_APPOINTMENT.APPOINTMENT_DETAILS\"), \" \");\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_div_10_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TeacherAvailableAppointmentsComponent_div_10_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.AddTeacherAppointmentRequest());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"TEACHER_APPOINTMENT.ADD_APPOINTMENT\"));\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, TeacherAvailableAppointmentsComponent_div_10_button_1_Template, 3, 3, \"button\", 18)(2, TeacherAvailableAppointmentsComponent_div_10_button_2_Template, 3, 3, \"button\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.teacherAvailableTimes == null ? null : ctx_r2.teacherAvailableTimes.isView);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.teacherAvailableTimes == null ? null : ctx_r2.teacherAvailableTimes.isView));\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"app-add-teacher-appointment-request\", 23);\n    i0.ɵɵlistener(\"openAddRequestOverlayRequest\", function TeacherAvailableAppointmentsComponent_div_11_Template_app_add_teacher_appointment_request_openAddRequestOverlayRequest_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeAddRequestOverlayRequest());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherAvailableAppointmentsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"app-teacher-appointment-request-datails\", 25);\n    i0.ɵɵlistener(\"closeDetailsRequest\", function TeacherAvailableAppointmentsComponent_div_12_Template_app_teacher_appointment_request_datails_closeDetailsRequest_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeRequestDetails());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TeacherAvailableAppointmentsComponent = /*#__PURE__*/(() => {\n  class TeacherAvailableAppointmentsComponent {\n    router;\n    translate;\n    teacherProfileService;\n    languageService;\n    roleService;\n    imagesPathesService;\n    lang = LanguageEnum;\n    teacherAvailableTimes = {};\n    currentUser;\n    resMessage = {};\n    currentLang;\n    itemStuReq = new EventEmitter();\n    AddTeacherRequest = new EventEmitter();\n    requestDetails = new EventEmitter();\n    openAddRequestOverlay = false;\n    openTeacherRequestDetailsOverlay = false;\n    studentSubscripModel = {\n      totalRows: 0\n    };\n    itemStuReq1 = {};\n    constructor(router, translate, teacherProfileService, languageService, roleService, imagesPathesService) {\n      this.router = router;\n      this.translate = translate;\n      this.teacherProfileService = teacherProfileService;\n      this.languageService = languageService;\n      this.roleService = roleService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.currentLang = this.translate.currentLang === LanguageEnum.ar ? LanguageEnum.en : LanguageEnum.ar;\n      this.setCurrentLang();\n      this.getTeacherAvailableTimesTeacherView();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('UPDATE_TEACHER_PG.TITLE'));\n    }\n    getTeacherAvailableTimesTeacherView() {\n      this.teacherProfileService.getTeacherAvailableTimesTeacherView(this.currentUser?.id).subscribe(res => {\n        if (res.isSuccess) {\n          this.teacherAvailableTimes = res.data;\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    AddTeacherAppointmentRequest() {\n      this.openAddRequestOverlay = !this.openAddRequestOverlay;\n    }\n    appointmentRequestDetails() {\n      this.openTeacherRequestDetailsOverlay = !this.openTeacherRequestDetailsOverlay;\n    }\n    closeAddRequestOverlayRequest() {\n      this.openAddRequestOverlay = !this.openAddRequestOverlay;\n      this.getTeacherAvailableTimesTeacherView();\n    }\n    closeRequestDetails() {\n      this.openTeacherRequestDetailsOverlay = !this.openTeacherRequestDetailsOverlay;\n      this.getTeacherAvailableTimesTeacherView();\n    }\n    static ɵfac = function TeacherAvailableAppointmentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherAvailableAppointmentsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.TeacherAppointmentService), i0.ɵɵdirectiveInject(i4.LanguageService), i0.ɵɵdirectiveInject(i5.RoleManagementService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherAvailableAppointmentsComponent,\n      selectors: [[\"app-teacher-available-appointments\"]],\n      inputs: {\n        studentSubscripModel: \"studentSubscripModel\"\n      },\n      outputs: {\n        itemStuReq: \"itemStuReq\",\n        AddTeacherRequest: \"AddTeacherRequest\",\n        requestDetails: \"requestDetails\"\n      },\n      decls: 13,\n      vars: 8,\n      consts: [[1, \"d-flex\", \"mb-4\", \"justify-content-between\", \"align-items-center\"], [1, \"UpdateUser__Label\", \"mb-0\", \"bold\"], [1, \"calender_img\", \"mt-2\", 3, \"src\"], [1, \"row\"], [1, \"cardTime_container\", \"w-100\"], [4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-3\", \"mx-3\"], [1, \"col-12\"], [\"class\", \"row  m--1\", 4, \"ngIf\"], [\"class\", \"overlay add_teacher_appointment_request\", 4, \"ngIf\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"col-12\", \"mb-2\"], [1, \"cardTime\", \"mx-3\"], [1, \"d-flex\", \"justify-content-between\", \"mb-0\", 3, \"matTooltip\"], [1, \"cardTime__day\", \"mb-0\"], [\"class\", \"cardTime__time  mb-0\", 4, \"ngFor\", \"ngForOf\"], [1, \"cardTime__time\", \"mb-0\"], [1, \"row\", \"m--1\"], [\"class\", \"cancel-btn px-4\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"save-btn px-4\", 3, \"click\", 4, \"ngIf\"], [1, \"cancel-btn\", \"px-4\", 3, \"click\"], [1, \"save-btn\", \"px-4\", 3, \"click\"], [1, \"overlay\", \"add_teacher_appointment_request\"], [3, \"openAddRequestOverlayRequest\"], [1, \"overlay\"], [3, \"closeDetailsRequest\"]],\n      template: function TeacherAvailableAppointmentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"p\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4);\n          i0.ɵɵtemplate(7, TeacherAvailableAppointmentsComponent_ng_container_7_Template, 8, 4, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵtemplate(10, TeacherAvailableAppointmentsComponent_div_10_Template, 3, 2, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, TeacherAvailableAppointmentsComponent_div_11_Template, 2, 0, \"div\", 9)(12, TeacherAvailableAppointmentsComponent_div_12_Template, 2, 0, \"div\", 10);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 6, \"UPDATE_TEACHER_PG.AVAILABLE_PROGRAMS_AND_TIMES\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.calender, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.teacherAvailableTimes == null ? null : ctx.teacherAvailableTimes.teacherProfileAvailableTimes);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.roleService.isTeacher() && ctx.roleService.isAccAcc());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openAddRequestOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openTeacherRequestDetailsOverlay);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.UpdateUser__Label[_ngcontent-%COMP%]{font-weight:700;color:#333;font-size:1rem}.save-btn[_ngcontent-%COMP%]{margin-left:0;margin-bottom:.5rem}.m--1[_ngcontent-%COMP%]{margin:-1rem;justify-content:center}.calender_img[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem}.times_programs[_ngcontent-%COMP%]{overflow-y:auto;overflow-x:hidden;height:26vh}.times_programs[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{max-width:15rem;width:98%;min-width:14rem}.cardTime_container[_ngcontent-%COMP%]{overflow-y:auto;height:25vh;overflow-x:hidden}.cardTime[_ngcontent-%COMP%]{background-color:#fff;padding:.5rem;border-radius:.313rem}.cardTime[_ngcontent-%COMP%]:lang(ar){text-align:right}.cardTime[_ngcontent-%COMP%]:lang(en){text-align:left}.cardTime__day[_ngcontent-%COMP%]{color:var(--main_color);font-size:.937rem;font-weight:700}.cardTime__time[_ngcontent-%COMP%]{color:var(--second_color);font-size:.937rem}.ellipsis[_ngcontent-%COMP%]{max-width:14rem;width:98%;min-width:10rem}\"]\n    });\n  }\n  return TeacherAvailableAppointmentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}