{"ast": null, "code": "import smoothBezier from './smoothBezier.js';\nexport function buildPath(ctx, shape, closePath) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (points && points.length >= 2) {\n    if (smooth) {\n      var controlPoints = smoothBezier(points, smooth, closePath, shape.smoothConstraint);\n      ctx.moveTo(points[0][0], points[0][1]);\n      var len = points.length;\n      for (var i = 0; i < (closePath ? len : len - 1); i++) {\n        var cp1 = controlPoints[i * 2];\n        var cp2 = controlPoints[i * 2 + 1];\n        var p = points[(i + 1) % len];\n        ctx.bezierCurveTo(cp1[0], cp1[1], cp2[0], cp2[1], p[0], p[1]);\n      }\n    } else {\n      ctx.moveTo(points[0][0], points[0][1]);\n      for (var i = 1, l = points.length; i < l; i++) {\n        ctx.lineTo(points[i][0], points[i][1]);\n      }\n    }\n    closePath && ctx.closePath();\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}