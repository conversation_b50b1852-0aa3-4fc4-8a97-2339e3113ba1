{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction TeacherProgramSubViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"app-teacher-submit-subscription\", 4);\n    i0.ɵɵlistener(\"closeOverlay\", function TeacherProgramSubViewComponent_div_2_Template_app_teacher_submit_subscription_closeOverlay_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeOverlay($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"programSubscriptionDetails\", ctx_r1.programsForSubscriptionsDetails);\n  }\n}\nexport let TeacherProgramSubViewComponent = /*#__PURE__*/(() => {\n  class TeacherProgramSubViewComponent {\n    router;\n    ShowSubscription = false;\n    showAddReplyOverlay = false;\n    programsForSubscriptionsDetails;\n    showSubscrption(event) {\n      this.ShowSubscription = event;\n      this.showAddReplyOverlay = !this.showAddReplyOverlay;\n    }\n    closeOverlay(event) {\n      this.ShowSubscription = event;\n      this.showAddReplyOverlay = !this.showAddReplyOverlay;\n      this.router.navigateByUrl('/teacher-for-subscription');\n    }\n    constructor(router) {\n      this.router = router;\n    }\n    ngOnInit() {}\n    programSubscriptionDetails(event) {\n      this.programsForSubscriptionsDetails = event;\n    }\n    static ɵfac = function TeacherProgramSubViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherProgramSubViewComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherProgramSubViewComponent,\n      selectors: [[\"app-teacher-program-sub-view\"]],\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"container-fluid\", \"overflow-y\"], [3, \"ShowSubscription\", \"programSubscriptionDetails\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeOverlay\", \"programSubscriptionDetails\"]],\n      template: function TeacherProgramSubViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-teacher-program-sub-details\", 1);\n          i0.ɵɵlistener(\"ShowSubscription\", function TeacherProgramSubViewComponent_Template_app_teacher_program_sub_details_ShowSubscription_1_listener($event) {\n            return ctx.showSubscrption($event);\n          })(\"programSubscriptionDetails\", function TeacherProgramSubViewComponent_Template_app_teacher_program_sub_details_programSubscriptionDetails_1_listener($event) {\n            return ctx.programSubscriptionDetails($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, TeacherProgramSubViewComponent_div_2_Template, 2, 1, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddReplyOverlay);\n        }\n      },\n      dependencies: [i2.NgIf],\n      styles: [\".overflow-y[_ngcontent-%COMP%]{overflow-y:auto}.container-fluid[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:1rem;margin-top:1rem;margin-bottom:0;width:98%;height:85vh}@media all and (min-width: 19in){.container-fluid[_ngcontent-%COMP%]{height:90vh}}\"]\n    });\n  }\n  return TeacherProgramSubViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}