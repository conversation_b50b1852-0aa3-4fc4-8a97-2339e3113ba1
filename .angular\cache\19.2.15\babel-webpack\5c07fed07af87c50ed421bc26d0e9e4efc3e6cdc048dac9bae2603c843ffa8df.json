{"ast": null, "code": "import { LanguageEnum } from \"../../../core/enums/language-enum.enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/environment-config-services/environment-config.service\";\nimport * as i3 from \"@angular/common\";\nfunction AdminStudentNotificationsChartComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelement(1, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.studentNotificationOption);\n  }\n}\nexport let AdminStudentNotificationsChartComponent = /*#__PURE__*/(() => {\n  class AdminStudentNotificationsChartComponent {\n    translate;\n    _environmentConfig;\n    studentNotificationsResponse;\n    studentNotificationOption;\n    currentLang;\n    tenantThemeColors;\n    constructor(translate, _environmentConfig) {\n      this.translate = translate;\n      this._environmentConfig = _environmentConfig;\n    }\n    ngOnInit() {\n      this.getTenantThemeColors();\n      this.currentLang = JSON.parse(localStorage.getItem(\"lang\") || '{}');\n      if (this.studentNotificationsResponse) {\n        this.initiate(this.studentNotificationsResponse);\n      }\n    }\n    getTenantThemeColors() {\n      this.tenantThemeColors = this._environmentConfig.getTenantThemeColors();\n    }\n    initiate(data) {\n      this.studentNotificationsResponse = data;\n      this.worldPopulationChart();\n    }\n    worldPopulationChart() {\n      this.studentNotificationOption = {\n        tooltip: {\n          show: false\n          // trigger: 'item'\n        },\n        legend: {\n          orient: 'vertical',\n          itemGap: 30,\n          right: 10,\n          top: 20,\n          bottom: 20,\n          textStyle: {\n            color: '#363636',\n            fontSize: '13',\n            fontFamily: 'Almarai-Bold'\n          }\n          // lineStyle:{\n          //   width: 20\n          // } \n        },\n        series: [{\n          name: 'Access From',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: true,\n            position: 'inside',\n            formatter: x => {\n              return x.percent?.toString() || \"\"; // Use sum variable here\n            },\n            valueAnimation: true\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '40',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          // dayOrder: 1, studTotalDegee: 36, TotalGradTask: 48, studPercentage: \n          data: [{\n            value: this.studentNotificationsResponse?.alarm1,\n            name: this.translate.currentLang == LanguageEnum.ar ? 'تنبية 1' : 'Alarm 1'\n          }, {\n            value: this.studentNotificationsResponse?.alarm2,\n            name: this.translate.currentLang == LanguageEnum.ar ? 'تنبية 2' : 'Alarm 2'\n          }, {\n            value: this.studentNotificationsResponse?.alarm3,\n            name: this.translate.currentLang == LanguageEnum.ar ? 'تنبية 3' : 'Alarm 3'\n          }, {\n            value: this.studentNotificationsResponse?.warning1,\n            name: this.translate.currentLang == LanguageEnum.ar ? ' إنذار 1' : 'Warning 1'\n          }, {\n            value: this.studentNotificationsResponse?.warning2,\n            name: this.translate.currentLang == LanguageEnum.ar ? ' إنذار 2' : 'Warning 2'\n          }, {\n            value: this.studentNotificationsResponse?.warning3,\n            name: this.translate.currentLang == LanguageEnum.ar ? ' إنذار 3' : 'Warning 3'\n          }, {\n            value: this.studentNotificationsResponse?.discontinued,\n            name: this.translate.currentLang == LanguageEnum.ar ? ' منقطع' : 'Discontinued'\n          }]\n        }],\n        color: ['#DD7676', `${this.tenantThemeColors?.secondColor}`, `${this.tenantThemeColors?.mainColor}`, '#EA7439', '#C9CFD6', '#3A3A3A', '#FF0000']\n      };\n    }\n    static ɵfac = function AdminStudentNotificationsChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentNotificationsChartComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.EnvironmentConfigService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentNotificationsChartComponent,\n      selectors: [[\"app-admin-student-notifications-chart\"]],\n      inputs: {\n        studentNotificationsResponse: \"studentNotificationsResponse\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"shape\", 4, \"ngIf\"], [1, \"shape\"], [\"echarts\", \"\", 1, \"demo-chart\", 3, \"options\"]],\n      template: function AdminStudentNotificationsChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AdminStudentNotificationsChartComponent_div_0_Template, 2, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.studentNotificationOption);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".demo-chart[_ngcontent-%COMP%]{padding:.5rem;height:25rem;width:36rem}.demo-chart[_ngcontent-%COMP%]:lang(en){width:34rem;direction:ltr}\"]\n    });\n  }\n  return AdminStudentNotificationsChartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}