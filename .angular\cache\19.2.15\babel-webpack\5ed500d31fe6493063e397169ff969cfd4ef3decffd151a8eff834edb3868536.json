{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { StudentGroupsViewComponent } from './components/student-groups-view/student-groups-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StudentGroupsViewComponent\n}];\nexport let StudentGroupsRoutingModule = /*#__PURE__*/(() => {\n  class StudentGroupsRoutingModule {\n    static ɵfac = function StudentGroupsRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentGroupsRoutingModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StudentGroupsRoutingModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return StudentGroupsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}