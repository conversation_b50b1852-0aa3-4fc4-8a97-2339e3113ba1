{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/question-bank-services/question-bank-question.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"red-border-class\": a0\n});\nfunction AddQuestionBankQuestionComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.QUESTION_AR_MAX_LENGHT\"), \" \");\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddQuestionBankQuestionComponent_div_6_div_1_Template, 3, 3, \"div\", 14)(2, AddQuestionBankQuestionComponent_div_6_div_2_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.QuestioAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.QuestioAr.errors == null ? null : ctx_r0.f.QuestioAr.errors.maxlength);\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.REQUIRED\"), \" \");\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"QUESTION_BANK.ANSWER_AR_MAX_LENGHT\"), \" \");\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, AddQuestionBankQuestionComponent_div_17_div_1_Template, 3, 3, \"div\", 14)(2, AddQuestionBankQuestionComponent_div_17_div_2_Template, 3, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.AnswerAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.AnswerAr.errors == null ? null : ctx_r0.f.AnswerAr.errors.maxlength);\n  }\n}\nfunction AddQuestionBankQuestionComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \" \");\n  }\n}\nexport let AddQuestionBankQuestionComponent = /*#__PURE__*/(() => {\n  class AddQuestionBankQuestionComponent {\n    questionBankQuestionService;\n    activeroute;\n    router;\n    translate;\n    fb;\n    _alertify;\n    Title;\n    questionBankQuestionId = '';\n    questionBankQuestions;\n    questionBankQuestionCreat = {};\n    questionBankQuestionUpdate = {};\n    qBGLst = [];\n    QBCid = \"7bfcdb33-b3a0-43fd-af30-12167ac46508\";\n    isAdd = true;\n    errorMessage;\n    successMessage;\n    isSubmit = false;\n    currentForm = new FormGroup({});\n    selectedCategoryId;\n    selectedQuestionId;\n    closeQuestionForm = new EventEmitter();\n    resultMessage = {};\n    disableSaveButtons = false;\n    submitSuccess = new EventEmitter();\n    //  @Output() isQuestionSave = new EventEmitter<{}>();\n    isQuestionSave = new EventEmitter();\n    constructor(questionBankQuestionService, activeroute, router, translate, fb, _alertify) {\n      this.questionBankQuestionService = questionBankQuestionService;\n      this.activeroute = activeroute;\n      this.router = router;\n      this.translate = translate;\n      this.fb = fb;\n      this._alertify = _alertify;\n    }\n    ngOnInit() {\n      this.currentForm.reset();\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n      this.questionBankQuestionId = this.selectedQuestionId || \"\";\n      if (this.selectedQuestionId !== \"\") {\n        this.Title = \"Edit QuestionBankQuestion\";\n        this.isAdd = false;\n        this.populate();\n      } else {\n        this.Title = \"Add QuestionBankQuestion\";\n        this.isAdd = true;\n        this.currentForm.reset();\n      }\n      this.buildForm();\n    }\n    ngOnChanges(changes) {\n      this.currentForm.reset();\n      this.questionBankQuestionId = this.selectedQuestionId || \"\";\n      if (this.questionBankQuestionId != \"\") {\n        this.populate();\n      }\n      if (this.questionBankQuestionId == \"\") {\n        this.currentForm.reset();\n      }\n      this.disableSaveButtons = false;\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n    }\n    get f() {\n      return this.currentForm?.controls;\n    }\n    buildForm() {\n      // const arabicWordPattern = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9]+$\";\n      // const englishWordPattern =\"^[a-zA-Z0-9' '-'\\s]{1,40}$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+\\\\-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITHOUT_EMOJI = \"^[\\u{1F600}\\u{1F6FF}A-Za-z 0-9_@./#&+-~؛)(÷*/'/!/$/u{1F600}/u{1F6FF}]*$\";\n      // const ARABIC_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[\\u0621-\\u064A\\u0660-\\u0669 0-9_@./#&+-~؛)(÷*/'/!/$]+$\";\n      // const ENGLISH_LETTERS_WITH_SPECIAL_CHAR_WITH_EMOJI = \"^[ A-Za-z0-9_@./#&+-~؛)(÷*/'/!/$]*$\";\n      this.currentForm = this.fb.group({\n        QuestioAr: ['', [Validators.required, Validators.maxLength(50)]],\n        QuestionEn: ['', [/*Validators.required,Validators.maxLength(50),*/]],\n        AnswerAr: ['', [Validators.required, Validators.maxLength(500)]],\n        AnswerEn: ['', [/*Validators.required,Validators.maxLength(500),*/]]\n      });\n    }\n    populate() {\n      this.resultMessage = {\n        message: '',\n        type: ''\n      };\n      this.questionBankQuestionService.getQuestionBankQuestionDetails(this.questionBankQuestionId).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.questionBankQuestions = response.data;\n          this.QBCid = this.questionBankQuestions?.categoryId;\n          this.f.QuestioAr.setValue(this.questionBankQuestions?.arabQuestion);\n          this.f.QuestionEn.setValue(this.questionBankQuestions?.engQuestion);\n          this.f.AnswerAr.setValue(this.questionBankQuestions?.arabAnswer);\n          this.f.AnswerEn.setValue(this.questionBankQuestions?.engAnswer);\n          this.disableSaveButtons = false;\n        } else {\n          this.errorMessage = response.message;\n        }\n      }, error => {\n        //logging\n      });\n    }\n    Submit() {\n      this.isSubmit = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.resultMessage = {};\n      if (this.currentForm.valid) {\n        if (this.questionBankQuestionId) {\n          this.questionBankQuestionUpdate.id = this.questionBankQuestionId;\n          this.questionBankQuestionUpdate.no = this.questionBankQuestions?.no;\n          this.questionBankQuestionUpdate.categoryId = this.QBCid;\n          this.questionBankQuestionUpdate.arabQuestion = this.f.QuestioAr.value;\n          this.questionBankQuestionUpdate.engQuestion = this.f.QuestionEn.value;\n          this.questionBankQuestionUpdate.arabAnswer = this.f.AnswerAr.value;\n          this.questionBankQuestionUpdate.engAnswer = this.f.AnswerEn.value;\n          this.questionBankQuestionService.updateQuestionBankQuestion(this.questionBankQuestionUpdate).subscribe(res => {\n            if (res.isSuccess) {\n              this.isSubmit = false;\n              this.disableSaveButtons = true;\n              // this.successMessage = res.message;\n              // setTimeout(() => {\n              //   this.router.navigateByUrl('/question-bank-question-details/question-bank-question-details/'+this.QuestionBankQuestionId);\n              // }, 1500)\n              this.resultMessage = {\n                message: res.message || \"\",\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.loodQuestionsListAfterAdd();\n              this.submitSuccess?.emit(false); //close form after submit is success\n              this._alertify.success(res.message || \"\");\n            } else {\n              // this.errorMessage = res.message;\n              this.disableSaveButtons = false;\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        } else {\n          this.QBCid = this.selectedCategoryId;\n          this.questionBankQuestionCreat.categoryId = this.QBCid;\n          this.questionBankQuestionCreat.arabQuestion = this.f.QuestioAr.value;\n          this.questionBankQuestionCreat.engQuestion = this.f.QuestionEn.value;\n          this.questionBankQuestionCreat.arabAnswer = this.f.AnswerAr.value;\n          this.questionBankQuestionCreat.engAnswer = this.f.AnswerEn.value;\n          this.questionBankQuestionService.addQuestionBankQuestion(this.questionBankQuestionCreat).subscribe(res => {\n            this.isSubmit = false;\n            if (res.isSuccess) {\n              this.disableSaveButtons = true;\n              this.resultMessage = {\n                message: res.message || \"\",\n                type: BaseConstantModel.SUCCESS_TYPE\n              };\n              this.loodQuestionsListAfterAdd();\n              this.submitSuccess?.emit(false); //close form after submit is success\n              this._alertify.success(res.message || \"\");\n            } else {\n              //this.errorMessage = res.message;\n              this.disableSaveButtons = false;\n              this.resultMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {});\n        }\n      }\n    }\n    backListQuestio() {\n      this.closeQuestionForm?.emit(false);\n    }\n    loodQuestionsListAfterAdd() {\n      // this.isQuestionSave.emit({isSave:isSave,catogeryId:catogeryId});\n      this.isQuestionSave.emit(true);\n    }\n    static ɵfac = function AddQuestionBankQuestionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddQuestionBankQuestionComponent)(i0.ɵɵdirectiveInject(i1.QuestionBankQuestionService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddQuestionBankQuestionComponent,\n      selectors: [[\"app-add-question-bank-question\"]],\n      inputs: {\n        selectedCategoryId: \"selectedCategoryId\",\n        selectedQuestionId: \"selectedQuestionId\"\n      },\n      outputs: {\n        closeQuestionForm: \"closeQuestionForm\",\n        submitSuccess: \"submitSuccess\",\n        isQuestionSave: \"isQuestionSave\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 32,\n      vars: 35,\n      consts: [[1, \"form-group\", \"material-form\", 3, \"submit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"comment\"], [\"cols\", \"2\", \"rows\", \"5\", \"id\", \"InputQuestioAr\", \"formControlName\", \"QuestioAr\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [\"cols\", \"2\", \"rows\", \"5\", \"id\", \"InputQuestionEn\", \"formControlName\", \"QuestionEn\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"exampleFormControlTextarea1\"], [\"rows\", \"10\", \"id\", \"InputAnswerAr\", \"formControlName\", \"AnswerAr\", 1, \"form-control\", 3, \"ngClass\"], [\"rows\", \"10\", \"id\", \"InputAnswerEn\", \"formControlName\", \"AnswerEn\", 1, \"form-control\", 3, \"ngClass\"], [1, \"row\"], [\"type\", \"submit\", 1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"disabled\"], [\"type\", \"button\", 1, \"cancel-btn\", \"btn\", \"btn-warning\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"]],\n      template: function AddQuestionBankQuestionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"submit\", function AddQuestionBankQuestionComponent_Template_form_submit_0_listener() {\n            return ctx.Submit();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"textarea\", 3);\n          i0.ɵɵtemplate(6, AddQuestionBankQuestionComponent_div_6_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 1)(8, \"label\", 2);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"textarea\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 1)(13, \"label\", 6);\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"textarea\", 7);\n          i0.ɵɵtemplate(17, AddQuestionBankQuestionComponent_div_17_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 1)(19, \"label\", 6);\n          i0.ɵɵtext(20);\n          i0.ɵɵpipe(21, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"textarea\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"section\")(24, \"div\", 9)(25, \"button\", 10);\n          i0.ɵɵtext(26);\n          i0.ɵɵpipe(27, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AddQuestionBankQuestionComponent_Template_button_click_28_listener() {\n            return ctx.backListQuestio();\n          });\n          i0.ɵɵtext(29);\n          i0.ɵɵpipe(30, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, AddQuestionBankQuestionComponent_div_31_Template, 2, 4, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.currentForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 15, \"QUESTION_BANK.QUESTION_AR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx.f.QuestioAr.errors && (ctx.f.QuestioAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.QuestioAr.errors && (ctx.f.QuestioAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 17, \"QUESTION_BANK.QUESTION_EN\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx.f.QuestionEn.errors && (ctx.f.QuestionEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 19, \"QUESTION_BANK.ANSWER_AR\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(31, _c0, ctx.f.AnswerAr.errors && (ctx.f.AnswerAr.touched || ctx.isSubmit)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.f.AnswerAr.errors && (ctx.f.AnswerAr.touched || ctx.isSubmit));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 21, \"QUESTION_BANK.ANSWER_EN\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx.f.AnswerEn.errors && (ctx.f.AnswerEn.touched || ctx.isSubmit)));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.disableSaveButtons);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 23, \"QUESTION_BANK.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(30, 25, \"QUESTION_BANK.CANCEL\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n        }\n      },\n      dependencies: [i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i6.NgClass, i6.NgIf, i3.TranslatePipe],\n      styles: [\".material-form[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border:.063rem solid #fbfbfb;opacity:1;padding:3rem 1rem 1rem}.material-form[_ngcontent-%COMP%]:lang(ar){text-align:right}.material-form[_ngcontent-%COMP%]:lang(en){text-align:left}.material-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:.875rem;color:gray;text-align:right;letter-spacing:0;opacity:1}.material-form[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--main_color) 0% 0% no-repeat padding-box;border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.material-form[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}\"]\n    });\n  }\n  return AddQuestionBankQuestionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}