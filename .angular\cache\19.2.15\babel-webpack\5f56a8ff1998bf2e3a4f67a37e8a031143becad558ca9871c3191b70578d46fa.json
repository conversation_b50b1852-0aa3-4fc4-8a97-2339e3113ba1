{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as RecordRTC from 'recordrtc';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i3 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = a0 => ({\n  \"input-group-text_with-audio\": a0\n});\nfunction VoiceRecordingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function VoiceRecordingComponent_div_1_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.initiateRecording());\n    });\n    i0.ɵɵelement(2, \"img\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r1.voiceUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.voice_blue, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction VoiceRecordingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function VoiceRecordingComponent_div_2_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.stopRecording());\n    });\n    i0.ɵɵelement(2, \"img\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r1.voiceUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.voice_red, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction VoiceRecordingComponent_audio_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"audio\", 6);\n    i0.ɵɵelement(1, \"source\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.sanitize(ctx_r1.voiceUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nexport let VoiceRecordingComponent = /*#__PURE__*/(() => {\n  class VoiceRecordingComponent {\n    domSanitizer;\n    attachmentService;\n    imagesPathesService;\n    getVoiceUrl = new EventEmitter();\n    viewMode = false;\n    voiceUrl = '';\n    record;\n    recording = false;\n    // url: any;\n    error;\n    voiceNoteAttachmentIds = [];\n    fileList = [];\n    fileUploadModel = [];\n    resMessage = {};\n    constructor(domSanitizer, attachmentService, imagesPathesService) {\n      this.domSanitizer = domSanitizer;\n      this.attachmentService = attachmentService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {}\n    sanitize(url) {\n      return this.domSanitizer.bypassSecurityTrustUrl(url);\n    }\n    /**\n    * Start recording.\n    */\n    initiateRecording() {\n      this.recording = true;\n      let mediaConstraints = {\n        video: false,\n        audio: true\n      };\n      navigator.mediaDevices.getUserMedia(mediaConstraints).then(this.successCallback.bind(this), this.errorCallback.bind(this));\n    }\n    /**\n     * Will be called automatically.\n     */\n    successCallback(stream) {\n      var options = {\n        mimeType: \"audio/wav\",\n        numberOfAudioChannels: 1\n      };\n      //Start Actuall Recording\n      var StereoAudioRecorder = RecordRTC.StereoAudioRecorder;\n      this.record = new StereoAudioRecorder(stream, options);\n      this.record.record();\n    }\n    /**\n     * Stop recording.\n     */\n    stopRecording() {\n      this.recording = false;\n      this.record.stop(this.processRecording.bind(this));\n    }\n    /**\n     * processRecording Do what ever you want with blob\n     * @param  {any} blob Blog\n     */\n    processRecording(blob) {\n      let files = Array();\n      files.push({\n        containerNameIndex: 1,\n        file: blob\n      });\n      this.UploadFiles(files);\n    }\n    /**\n     * Process Error.\n     */\n    errorCallback(error) {\n      this.error = 'Can not play audio in your browser';\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        Array.from(res.data).forEach(elm => {\n          //this.voiceNoteAttachmentIds.push(elm.id);\n          //this.fileList.push(elm);\n          this.voiceUrl = undefined;\n          setTimeout(() => {\n            this.voiceUrl = elm.url;\n            this.getVoiceUrl.emit(this.voiceUrl);\n          }, 500);\n        });\n        this.fileUploadModel = [];\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    static ɵfac = function VoiceRecordingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VoiceRecordingComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i2.AttachmentsService), i0.ɵɵdirectiveInject(i3.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VoiceRecordingComponent,\n      selectors: [[\"app-voice-recording\"]],\n      inputs: {\n        viewMode: \"viewMode\",\n        voiceUrl: \"voiceUrl\"\n      },\n      outputs: {\n        getVoiceUrl: \"getVoiceUrl\"\n      },\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"d-flex\"], [\"class\", \"input-group-prepend\", 4, \"ngIf\"], [\"controls\", \"\", 4, \"ngIf\"], [1, \"input-group-prepend\"], [1, \"input-group-text\", \"pl-2\", \"pr-2\", \"border-md\", 3, \"click\", \"ngClass\"], [1, \"UserLogin_icon_input\", 3, \"src\"], [\"controls\", \"\"], [\"type\", \"audio/wav\", 3, \"src\"]],\n      template: function VoiceRecordingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, VoiceRecordingComponent_div_1_Template, 3, 4, \"div\", 1)(2, VoiceRecordingComponent_div_2_Template, 3, 4, \"div\", 1)(3, VoiceRecordingComponent_audio_3_Template, 2, 1, \"audio\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.recording && !ctx.viewMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.recording);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.voiceUrl);\n        }\n      },\n      styles: [\".input-group-prepend[_ngcontent-%COMP%]{margin-right:-.25rem}.input-group-prepend[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{background-color:#fff}.input-group-prepend[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]:lang(ar){border-left-color:#b3b3b3!important;border-right-color:transparent;border-top-left-radius:.65rem;border-top-right-radius:0;border-bottom-left-radius:.65rem;border-bottom-right-radius:0}.input-group-prepend[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]:lang(en){border-right-color:#b3b3b3!important;border-left-color:transparent;border-top-left-radius:0;border-top-right-radius:.65rem;border-bottom-left-radius:0rem;border-bottom-right-radius:.65rem}.input-group-prepend[_ngcontent-%COMP%]   .UserLogin_icon_input[_ngcontent-%COMP%]{width:1rem;height:1.5rem}.input-group-prepend[_ngcontent-%COMP%]   .input-group-text_with-audio[_ngcontent-%COMP%]:lang(ar), .input-group-prepend[_ngcontent-%COMP%]   .input-group-text_with-audio[_ngcontent-%COMP%]:lang(en){border-right-color:#b3b3b3!important;border-left-color:#b3b3b3!important;border-radius:.65rem}audio[_ngcontent-%COMP%]{margin:0 1rem}\"]\n    });\n  }\n  return VoiceRecordingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}