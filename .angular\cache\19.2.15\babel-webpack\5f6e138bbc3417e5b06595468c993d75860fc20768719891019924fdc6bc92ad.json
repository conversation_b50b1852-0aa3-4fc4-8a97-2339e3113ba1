{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PushNotificationService = /*#__PURE__*/(() => {\n  class PushNotificationService {\n    http;\n    registerNotificationTokenURL = environment.baseUrl + 'PushNotification/register-notification-token';\n    getUserNotificationsURL = environment.baseUrl + 'PushNotification/get-user-notifications';\n    sendChatGroupNotificationMessageUrl = environment.baseUrl + 'PushNotification/send-chat-group-notification-message';\n    constructor(http) {\n      this.http = http;\n    }\n    registerNotificationToken(model) {\n      return this.http.post(this.registerNotificationTokenURL, model);\n    }\n    getUserNotifications(model) {\n      return this.http.post(this.getUserNotificationsURL, model);\n    }\n    sendChatGroupNotificationMessage(model) {\n      return this.http.post(this.sendChatGroupNotificationMessageUrl, model);\n    }\n    static ɵfac = function PushNotificationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PushNotificationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PushNotificationService,\n      factory: PushNotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PushNotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}