{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { AnswerTypeEnum } from 'src/app/core/enums/exam-builder-enums/answer-type-enum.enum';\nimport { QuestionTypeEnum } from 'src/app/core/enums/exam-builder-enums/question-type-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ProgramDutyDaysTaskViewMoodEnum } from 'src/app/core/enums/programs/program-duty-days-task-view-mood-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"src/app/core/services/exam-form-services/exam-form.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nconst _c0 = (a0, a1, a2) => ({\n  \" border-transparent\": a0,\n  \"input_with-voice\": a1,\n  \"dimmed_input\": a2\n});\nconst _c1 = () => ({\n  standalone: true\n});\nfunction QuestionTemplateComponent_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 19);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵlistener(\"input\", function QuestionTemplateComponent_input_7_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onQuestionTextChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_input_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.questionTemplate.text, $event) || (ctx_r1.questionTemplate.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r1.questionTemplate.text);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 5, \"EXAM_FORM.ADD_QUESTION\"));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.questionTemplate.text);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, !ctx_r1.questionTemplate.text, !ctx_r1.questionTemplate.text, ctx_r1.viewMode))(\"disabled\", ctx_r1.viewMode);\n  }\n}\nfunction QuestionTemplateComponent_app_voice_recording_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recording\", 20);\n    i0.ɵɵlistener(\"getVoiceUrl\", function QuestionTemplateComponent_app_voice_recording_8_Template_app_voice_recording_getVoiceUrl_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveVoiceUrl($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"viewMode\", ctx_r1.viewMode)(\"voiceUrl\", ctx_r1.questionTemplate == null ? null : ctx_r1.questionTemplate.voiceUrl);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_1_div_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function QuestionTemplateComponent_div_40_ng_container_1_div_1_a_4_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.stopPropagation($event));\n    })(\"click\", function QuestionTemplateComponent_div_40_ng_container_1_div_1_a_4_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const answer_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteAnswerDialog(answer_r5));\n    });\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.trash_2, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"mat-checkbox\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_div_40_ng_container_1_div_1_Template_mat_checkbox_ngModelChange_2_listener($event) {\n      const answer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(answer_r5.correct, $event) || (answer_r5.correct = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_div_40_ng_container_1_div_1_Template_input_ngModelChange_3_listener($event) {\n      const answer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(answer_r5.text, $event) || (answer_r5.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, QuestionTemplateComponent_div_40_ng_container_1_div_1_a_4_Template, 2, 1, \"a\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const answer_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", answer_r5.correct);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c1))(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", answer_r5.text);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.viewMode);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuestionTemplateComponent_div_40_ng_container_1_div_1_Template, 5, 7, \"div\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionTemplate.answers);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_2_div_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function QuestionTemplateComponent_div_40_ng_container_2_div_1_a_4_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.stopPropagation($event));\n    })(\"click\", function QuestionTemplateComponent_div_40_ng_container_2_div_1_a_4_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const answer_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteAnswerDialog(answer_r8));\n    });\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.trash_2, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"mat-checkbox\", 29);\n    i0.ɵɵlistener(\"change\", function QuestionTemplateComponent_div_40_ng_container_2_div_1_Template_mat_checkbox_change_2_listener($event) {\n      const answer_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onItemChecked(answer_r8, $event));\n    });\n    i0.ɵɵelementStart(3, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_div_40_ng_container_2_div_1_Template_input_ngModelChange_3_listener($event) {\n      const answer_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(answer_r8.text, $event) || (answer_r8.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, QuestionTemplateComponent_div_40_ng_container_2_div_1_a_4_Template, 2, 1, \"a\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const answer_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.verifyUserAnsw(answer_r8 == null ? null : answer_r8.answerNo, ctx_r1.questionTemplate.studentAnswersByAnswerNumbers))(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c1))(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", answer_r8.text);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.viewMode);\n  }\n}\nfunction QuestionTemplateComponent_div_40_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuestionTemplateComponent_div_40_ng_container_2_div_1_Template, 5, 7, \"div\", 21);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionTemplate.answers);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(4, 3, \"EXAM_FORM.CORRECT_ANSWER_IS\"), \" \", ctx_r1.correctAnswer, \" \");\n  }\n}\nfunction QuestionTemplateComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, QuestionTemplateComponent_div_40_ng_container_1_Template, 2, 1, \"ng-container\", 16)(2, QuestionTemplateComponent_div_40_ng_container_2_Template, 5, 5, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.questionViewMood === ctx_r1.questionViewMoodEnum.admin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.questionViewMood === ctx_r1.questionViewMoodEnum.student);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.stopPropagation($event));\n    })(\"click\", function QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const answer_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteAnswerDialog(answer_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.trash_2, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 32)(1, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_Template_input_ngModelChange_1_listener($event) {\n      const answer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      i0.ɵɵtwoWayBindingSet(answer_r12.text, $event) || (answer_r12.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_a_2_Template, 2, 1, \"a\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const answer_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"value\", answer_r12.answerNo);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", answer_r12.text);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.viewMode);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_41_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-radio-group\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_ng_container_41_div_2_Template_mat_radio_group_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.questionTemplate.correctAnswersByAnswerNumber, $event) || (ctx_r1.questionTemplate.correctAnswersByAnswerNumber = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_41_div_2_mat_radio_button_2_Template, 3, 4, \"mat-radio-button\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.questionTemplate.correctAnswersByAnswerNumber);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionTemplate.answers);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_41_div_2_Template, 3, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.questionTemplate.answerType === ctx_r1.answerTypeEnum.singleSelect);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.stopPropagation($event));\n    })(\"click\", function QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const answer_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteAnswerDialog(answer_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.trash_2, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 32);\n    i0.ɵɵelement(1, \"input\", 34);\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_a_2_Template, 2, 1, \"a\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const answer_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"value\", answer_r16.answerNo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", answer_r16.text)(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.viewMode);\n  }\n}\nfunction QuestionTemplateComponent_ng_container_42_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-radio-group\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_ng_container_42_div_2_Template_mat_radio_group_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.questionTemplate.studentAnswersByAnswerNumber, $event) || (ctx_r1.questionTemplate.studentAnswersByAnswerNumber = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_42_div_2_mat_radio_button_2_Template, 3, 4, \"mat-radio-button\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.questionTemplate.studentAnswersByAnswerNumber);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionTemplate.answers);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(5, 5, \"EXAM_FORM.CORRECT_ANSWER_IS\"), \" \", ctx_r1.questionTemplate.correctAnswersByAnswerNumber, \" \");\n  }\n}\nfunction QuestionTemplateComponent_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵtemplate(2, QuestionTemplateComponent_ng_container_42_div_2_Template, 6, 7, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.questionTemplate.answerType === ctx_r1.answerTypeEnum.singleSelect);\n  }\n}\nfunction QuestionTemplateComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function QuestionTemplateComponent_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addAnswer());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.viewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"GENERAL.ADD_ANSWE\"));\n  }\n}\nfunction QuestionTemplateComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \"\\n\");\n  }\n}\nexport let QuestionTemplateComponent = /*#__PURE__*/(() => {\n  class QuestionTemplateComponent {\n    translate;\n    dialog;\n    examFormService;\n    imagesPathesService;\n    degreeValueChange = new EventEmitter();\n    questionTemplate = {\n      answers: []\n    };\n    viewMode = false;\n    questionViewMood = ProgramDutyDaysTaskViewMoodEnum.admin;\n    questionViewMoodEnum = ProgramDutyDaysTaskViewMoodEnum;\n    answerTypeEnum = AnswerTypeEnum;\n    currentLang = '';\n    MULTISELECT = '';\n    errorMessage;\n    resultMessage = {};\n    correctAnswer;\n    constructor(translate, dialog, examFormService, imagesPathesService) {\n      this.translate = translate;\n      this.dialog = dialog;\n      this.examFormService = examFormService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.MULTISELECT = this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.MULTI_SELECT') : this.translate.instant('GENERAL.MULTI_SELECT');\n      this.questionTemplate.studentAnswersByAnswerNumber = this.questionTemplate.studentAnswersByAnswerNumber ? this.questionTemplate.studentAnswersByAnswerNumber : '';\n      this.questionTemplate.studentAnswersByAnswerNumbers = this.questionTemplate.studentAnswersByAnswerNumbers ? this.questionTemplate.studentAnswersByAnswerNumbers : [];\n      if (this.questionTemplate.answerType === this.answerTypeEnum.multiSelect) {\n        for (var i = 0; i < this.questionTemplate.answers.filter(x => x.correct == true).length; i++) {\n          let num = this.questionTemplate.answers.filter(x => x.correct == true)[i].answerNo;\n          if (i == 0) this.correctAnswer = ' ' + num;else {\n            this.correctAnswer = this.correctAnswer + \",\" + num;\n          }\n        }\n      }\n    }\n    addAnswer() {\n      this.resultMessage = {};\n      if (this.examFormService.validateAnswer(this.questionTemplate.answers) === true, false, this.questionTemplate.answerType) {\n        let id = BaseConstantModel.newGuid();\n        let answer = {\n          answerId: id,\n          answerNo: this.questionTemplate.answers?.length + 1,\n          correct: this.questionTemplate.answers?.length === 0 ? true : false\n        };\n        if (this.questionTemplate.answerType === AnswerTypeEnum.singleSelect && this.questionTemplate.answers.length === 0) {\n          this.questionTemplate.correctAnswersByAnswerNumber = \"1\";\n        }\n        this.questionTemplate.answers?.push(answer);\n      } else {\n        this.resultMessage = {\n          message: this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_AND_DUPLICATION_MESSAGE') : this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_AND_DUPLICATION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    onQuestionTextChange() {\n      this.questionTemplate.text ? this.questionTemplate.questionType = QuestionTypeEnum.text : null;\n    }\n    saveVoiceUrl(event) {\n      this.questionTemplate.voiceUrl = event;\n      this.questionTemplate.voiceUrl ? this.questionTemplate.questionType = QuestionTypeEnum.voice : null;\n    }\n    MULTI_SELECT() {\n      // return this.currentLang === LanguageEnum.ar ? LanguageEnum.en.split('-')[0].toUpperCase() : LanguageEnum.ar.split('-')[0].toUpperCase();\n      return this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.MULTI_SELECT') : this.translate.instant('GENERAL.MULTI_SELECT');\n    }\n    CHOICES() {\n      // return this.currentLang === LanguageEnum.ar ? LanguageEnum.en.split('-')[0].toUpperCase() : LanguageEnum.ar.split('-')[0].toUpperCase();\n      return this.currentLang === LanguageEnum.ar ? this.translate.instant('GENERAL.CHOICES') : this.translate.instant('GENERAL.CHOICES');\n    }\n    deleteAnswerDialog(answer) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete answer\" : \"هل متأكد من حذف الإجابة\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Answer' : 'حذف الإجابة', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          // let question = this.exam.questions.filter(q => q.questionNo == no)[0];\n          const index = this.questionTemplate.answers.indexOf(answer);\n          if (index > -1) {\n            this.questionTemplate.answers.splice(index, 1);\n            if (this.questionTemplate.answerType === AnswerTypeEnum.singleSelect) {\n              let numberAnswer = parseInt(this.questionTemplate.correctAnswersByAnswerNumber);\n              if (numberAnswer > 1) {\n                numberAnswer = numberAnswer - 1;\n                this.questionTemplate.correctAnswersByAnswerNumber = numberAnswer.toString();\n              } else {\n                this.questionTemplate.correctAnswersByAnswerNumber = numberAnswer.toString();\n              }\n            } else {\n              if (this.questionTemplate.answers.filter(x => x.correct == true).length < 1) {\n                this.questionTemplate.answers[0].correct = true;\n              }\n            }\n            this.questionTemplate.answers.forEach(element => {\n              element.answerNo = this.questionTemplate.answers.indexOf(element) + 1;\n            });\n          }\n        }\n      });\n    }\n    stopPropagation(event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    onItemChecked(item, event) {\n      if (event.checked) {\n        this.questionTemplate.studentAnswersByAnswerNumbers?.push(item.answerNo || 0);\n      } else {\n        let it = this.questionTemplate.studentAnswersByAnswerNumbers?.filter(i => i === item.answerNo)[0];\n        const ind = it ? this.questionTemplate.studentAnswersByAnswerNumbers?.indexOf(it) : -1;\n        if (ind && ind > -1) {\n          this.questionTemplate.studentAnswersByAnswerNumbers?.splice(ind, 1);\n        }\n      }\n    }\n    degreeChange() {\n      this.degreeValueChange.emit();\n    }\n    verifyUserAnsw(answerNum, studentAnswersByAnswerNumbers) {\n      return studentAnswersByAnswerNumbers?.includes(answerNum || 0);\n    }\n    static ɵfac = function QuestionTemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuestionTemplateComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.ExamFormService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuestionTemplateComponent,\n      selectors: [[\"app-question-template\"]],\n      inputs: {\n        questionTemplate: \"questionTemplate\",\n        viewMode: \"viewMode\",\n        questionViewMood: \"questionViewMood\"\n      },\n      outputs: {\n        degreeValueChange: \"degreeValueChange\"\n      },\n      decls: 45,\n      vars: 42,\n      consts: [[1, \"form-group\", \"add-question-template\", \"dynamic-radio-button-with-input-text-model\", \"mb-0\"], [1, \"form-group\", \"row\", \"question\", \"px-2\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-8\", \"col-sm-8\"], [1, \"label_item_question\"], [1, \"input-group\", \"input-sm\", \"input_search_voice\"], [\"type\", \"text\", \"class\", \"form-control  border-md \", 3, \"matTooltip\", \"ngModel\", \"ngClass\", \"placeholder\", \"disabled\", \"input\", \"ngModelChange\", 4, \"ngIf\"], [3, \"viewMode\", \"voiceUrl\", \"getVoiceUrl\", 4, \"ngIf\"], [1, \"col-xl-2\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\"], [\"type\", \"number\", \"min\", \"1\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\", \"disabled\"], [1, \"form-group\", \"row\"], [1, \"col-10\", \"pl-4\", \"pr-4\"], [1, \"input-group\", \"col-sm-10\"], [\"aria-label\", \"Select an option\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"px-2\", 3, \"value\"], [1, \"form-group\", \"row\", \"answerChick\", \"mb-0\"], [1, \"label_item_question\", \"item_label\"], [4, \"ngIf\"], [\"class\", \"col-12 add_answer mb-2\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"type\", \"text\", 1, \"form-control\", \"border-md\", 3, \"input\", \"ngModelChange\", \"matTooltip\", \"ngModel\", \"ngClass\", \"placeholder\", \"disabled\"], [3, \"getVoiceUrl\", \"viewMode\", \"voiceUrl\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-12\"], [1, \"example-margin\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\", \"disabled\"], [\"type\", \"text\", \"aria-label\", \"Text input with checkbox\", 1, \"form-control\", \"input_fullWidth\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [3, \"click\", 4, \"ngIf\"], [3, \"click\"], [1, \"btn_trash\", 3, \"src\"], [1, \"input_fullWidth\", \"bold\", \"CORRECT_ANSWER\"], [1, \"example-margin\", 3, \"change\", \"ngModel\", \"ngModelOptions\", \"disabled\"], [\"aria-label\", \"Select an option\", 1, \"input-group\", \"col-12\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \" px-4  col-12\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-4\", \"col-12\", 3, \"value\"], [1, \"input_fullWidth\", \"bold\", \"CORRECT_ANSWER\", \"mx-4\"], [\"type\", \"text\", \"aria-label\", \"Text input with checkbox\", 1, \"form-control\", \"input_fullWidth\", 3, \"ngModel\", \"disabled\"], [1, \"col-12\", \"add_answer\", \"mb-2\"], [\"type\", \"button\", 1, \"btn\", \"cancel-btn\", \"btn\", \"btn-warning\", 3, \"click\", \"disabled\"], [1, \"py-2\", \"my-4\"]],\n      template: function QuestionTemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4);\n          i0.ɵɵtemplate(7, QuestionTemplateComponent_input_7_Template, 2, 11, \"input\", 5)(8, QuestionTemplateComponent_app_voice_recording_8_Template, 1, 2, \"app-voice-recording\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"label\", 3);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 8);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_Template_input_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.questionTemplate.time, $event) || (ctx.questionTemplate.time = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"label\", 3);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 8);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_Template_input_ngModelChange_19_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.questionTemplate.degree, $event) || (ctx.questionTemplate.degree = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function QuestionTemplateComponent_Template_input_ngModelChange_19_listener() {\n            return ctx.degreeChange();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 10)(23, \"label\", 3);\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 11)(27, \"mat-radio-group\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function QuestionTemplateComponent_Template_mat_radio_group_ngModelChange_27_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.questionTemplate.answerType, $event) || (ctx.questionTemplate.answerType = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(28, \"mat-radio-button\", 13);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-radio-button\", 13);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(32, \"div\", 14)(33, \"div\", 10)(34, \"label\", 3);\n          i0.ɵɵtext(35);\n          i0.ɵɵpipe(36, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"label\", 15);\n          i0.ɵɵtext(38);\n          i0.ɵɵpipe(39, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, QuestionTemplateComponent_div_40_Template, 3, 2, \"div\", 16)(41, QuestionTemplateComponent_ng_container_41_Template, 3, 1, \"ng-container\", 16)(42, QuestionTemplateComponent_ng_container_42_Template, 3, 1, \"ng-container\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, QuestionTemplateComponent_div_43_Template, 4, 4, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, QuestionTemplateComponent_div_44_Template, 3, 4, \"div\", 18);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(5, 26, \"EXAM_FORM.QUESTION_NUMBER\"), \" \", ctx.questionTemplate.questionNo, \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.questionTemplate.voiceUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.questionTemplate.text);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(12, 28, \"EXAM_FORM.DURATION_ANSWER\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(14, 30, \"EXAM_FORM.DURATION\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.questionTemplate.time);\n          i0.ɵɵproperty(\"disabled\", ctx.viewMode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 32, \"EXAM_FORM.DGREE\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(20, 34, \"EXAM_FORM.DGREE\"));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.questionTemplate.degree);\n          i0.ɵɵproperty(\"disabled\", ctx.viewMode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 36, \"EXAM_FORM.ANSWER\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.questionTemplate.answerType);\n          i0.ɵɵproperty(\"disabled\", ctx.viewMode);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"value\", ctx.answerTypeEnum.singleSelect);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.CHOICES());\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"value\", ctx.answerTypeEnum.multiSelect);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.MULTI_SELECT(), \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(36, 38, \"EXAM_FORM.RIGHT_ANSWER\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(39, 40, \"EXAM_FORM.ANSWERS\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.questionTemplate.answerType === ctx.answerTypeEnum.multiSelect);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.questionViewMood === ctx.questionViewMoodEnum.admin);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.questionViewMood === ctx.questionViewMoodEnum.student);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.viewMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.add-question-template[_ngcontent-%COMP%]{border-bottom:.063rem solid rgb(193,199,208)}.add-question-template[_ngcontent-%COMP%]   .label_item_question[_ngcontent-%COMP%]{font-size:.875rem;font-weight:700;color:#333}.add-question-template[_ngcontent-%COMP%]   .label_item_question[_ngcontent-%COMP%]:lang(ar){float:right}.add-question-template[_ngcontent-%COMP%]   .add_answer[_ngcontent-%COMP%]:lang(ar){text-align:right}.add-question-template[_ngcontent-%COMP%]   .input_fullWidth[_ngcontent-%COMP%]{width:25rem}.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{min-width:0;border-top-left-radius:.65rem;border-top-right-radius:.65rem!important;border-bottom-left-radius:.65rem;border-bottom-right-radius:.65rem!important}.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   .input_with-voice[_ngcontent-%COMP%]:lang(ar){border-top-left-radius:0rem!important;border-bottom-left-radius:0rem!important}.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   .input_with-voice[_ngcontent-%COMP%]:lang(en){border-top-right-radius:0rem!important;border-bottom-right-radius:0rem!important}.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   .border-transparent[_ngcontent-%COMP%]:lang(ar){border-left:transparent}.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   .border-transparent[_ngcontent-%COMP%]:lang(en){border-right:transparent}.add-question-template[_ngcontent-%COMP%]   .item_label[_ngcontent-%COMP%]:lang(ar){margin-right:1rem}.add-question-template[_ngcontent-%COMP%]   .item_label[_ngcontent-%COMP%]:lang(en){margin-left:1rem}.day_detils.add-question-template[_ngcontent-%COMP%]   .input_search_voice[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{min-width:0!important}.CORRECT_ANSWER[_ngcontent-%COMP%]{color:green}.dimmed_input[_ngcontent-%COMP%]{background-color:#fbfbfb;opacity:1;border-color:#b3b3b3;cursor:no-drop}\"]\n    });\n  }\n  return QuestionTemplateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}