{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from '../../../../../core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from '../../../../../shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../../../../../core/services/corrupted-files-requests-services/corrupted-files-requests.service\";\nimport * as i4 from \"../../../../../core/services/language-services/language.service\";\nimport * as i5 from \"../../../../../core/services/alertify-services/alertify.service\";\nimport * as i6 from \"@angular/common\";\nfunction CorruptedFilesRequestsComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction CorruptedFilesRequestsComponent_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"app-corrupted-files-requests-grid\", 12);\n    i0.ɵɵlistener(\"adminFilterEvent\", function CorruptedFilesRequestsComponent_div_0_div_12_Template_app_corrupted_files_requests_grid_adminFilterEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.filterRequest($event));\n    })(\"deleteCorruptedFileRequests\", function CorruptedFilesRequestsComponent_div_0_div_12_Template_app_corrupted_files_requests_grid_deleteCorruptedFileRequests_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteCorruptedFileRequest($event));\n    })(\"deleteListOfCorruptedFilesRequests\", function CorruptedFilesRequestsComponent_div_0_div_12_Template_app_corrupted_files_requests_grid_deleteListOfCorruptedFilesRequests_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteSelectedCorruptedFilesRequests());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"totalCount\", ctx_r1.totalCount)(\"corruptedFilesRequests\", ctx_r1.corruptedFilesRequests)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"corruptedFilesRequestsFilter\", ctx_r1.corruptedFilesRequestsFilter);\n  }\n}\nfunction CorruptedFilesRequestsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"app-search-input\", 6);\n    i0.ɵɵlistener(\"searchTerm\", function CorruptedFilesRequestsComponent_div_0_Template_app_search_input_searchTerm_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function CorruptedFilesRequestsComponent_div_0_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAdvancedSearch());\n    });\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, CorruptedFilesRequestsComponent_div_0_div_11_Template, 4, 3, \"div\", 8)(12, CorruptedFilesRequestsComponent_div_0_div_12_Template, 2, 4, \"div\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"ADMIN_MESSAGING.CORRUPTED_ATTACHMENT\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 6, \"SCIENTIFIC_PROBLEM.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.corruptedFilesRequests || ctx_r1.corruptedFilesRequests.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.corruptedFilesRequests && ctx_r1.corruptedFilesRequests.length > 0);\n  }\n}\nexport let CorruptedFilesRequestsComponent = /*#__PURE__*/(() => {\n  class CorruptedFilesRequestsComponent {\n    translate;\n    dialog;\n    corruptedFilesRequestsService;\n    languageService;\n    alertify;\n    corruptedFilesRequestsFilter = {\n      skip: 0,\n      take: 12,\n      sortField: 'requestDate',\n      sortOrder: 1,\n      page: 1\n    };\n    corruptedFilesRequests;\n    numberItemsPerRow = 4;\n    totalCount = 0;\n    openAdvancedSearchOverLay = new EventEmitter();\n    constructor(translate, dialog, corruptedFilesRequestsService, languageService, alertify) {\n      this.translate = translate;\n      this.dialog = dialog;\n      this.corruptedFilesRequestsService = corruptedFilesRequestsService;\n      this.languageService = languageService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.corruptedFilesRequestsFilter.sortField = 'requestDate';\n      // this.translate.currentLang === LanguageEnum.ar ? 'userNameAr' : 'userNameEn'\n      this.getAllCorruptedFilesRequests();\n    }\n    getAllCorruptedFilesRequests() {\n      this.corruptedFilesRequestsService.getAllCorruptedFilesRequestsFilter(this.corruptedFilesRequestsFilter).subscribe(res => {\n        if (res.isSuccess) {\n          this.corruptedFilesRequests = res.data;\n          this.totalCount = res.count ? res.count : 0;\n          if (this.corruptedFilesRequestsFilter.skip > 0 && (!this.corruptedFilesRequests || this.corruptedFilesRequests.length === 0)) {\n            this.corruptedFilesRequestsFilter.page -= 1;\n            this.corruptedFilesRequestsFilter.skip = (this.corruptedFilesRequestsFilter.page - 1) * this.corruptedFilesRequestsFilter.take;\n            this.getAllCorruptedFilesRequests();\n          }\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    filterByText(searchKey) {\n      this.corruptedFilesRequestsFilter.usrName = searchKey;\n      this.getAllCorruptedFilesRequests();\n    }\n    filterRequest(event) {\n      this.corruptedFilesRequestsFilter = event;\n      this.getAllCorruptedFilesRequests();\n    }\n    deleteSelectedCorruptedFilesRequests() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this list of CorruptedFilesRequests\" : \"هل متأكد من حذف هذه القائمة من طلبات الملفات التالفة\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete CorruptedFilesRequests' : 'حذف  طلبات الملفات التالفة', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          let ids = this.corruptedFilesRequests?.filter(i => i.checked).map(a => a.id);\n          this.corruptedFilesRequestsService.deleteListOfCorruptedFilesRequests(ids).subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getAllCorruptedFilesRequests();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    deleteCorruptedFileRequest(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this Corrupted File Request\" : \"هل متأكد من حذف طلب الملف التالف\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Corrupted File Request' : 'حذف  طلب الملف التالف', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.corruptedFilesRequestsService.deleteCorruptedFileRequest(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getAllCorruptedFilesRequests();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    openAdvancedSearch() {\n      this.openAdvancedSearchOverLay.emit(this.corruptedFilesRequestsFilter);\n    }\n    static ɵfac = function CorruptedFilesRequestsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CorruptedFilesRequestsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.CorruptedFilesRequestsService), i0.ɵɵdirectiveInject(i4.LanguageService), i0.ɵɵdirectiveInject(i5.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CorruptedFilesRequestsComponent,\n      selectors: [[\"app-corrupted-files-requests\"]],\n      outputs: {\n        openAdvancedSearchOverLay: \"openAdvancedSearchOverLay\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"scientific-problem\", 4, \"ngIf\"], [1, \"scientific-problem\"], [1, \"row\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-5\", \"col-sm-12\", \"text-left\"], [1, \"bold\", \"header_sc\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-7\", \"col-sm-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [3, \"searchTerm\"], [1, \"advanced_search\", \"mb-3\", \"px-2\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"col-12 internal_scroll\", 4, \"ngIf\"], [1, \"No_data\"], [1, \"col-12\", \"internal_scroll\"], [3, \"adminFilterEvent\", \"deleteCorruptedFileRequests\", \"deleteListOfCorruptedFilesRequests\", \"totalCount\", \"corruptedFilesRequests\", \"numberPerRow\", \"corruptedFilesRequestsFilter\"]],\n      template: function CorruptedFilesRequestsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CorruptedFilesRequestsComponent_div_0_Template, 13, 8, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.corruptedFilesRequests);\n        }\n      },\n      dependencies: [i6.NgIf, i1.TranslatePipe],\n      styles: [\".scientific-problem[_ngcontent-%COMP%]{background:#fff;border-radius:1.188rem;opacity:1;padding:1rem;height:97%;margin-top:1rem;margin-bottom:0}.scientific-problem[_ngcontent-%COMP%]   .internal_scroll[_ngcontent-%COMP%]{height:62vh;overflow-y:auto;padding-left:.5rem;overflow-x:hidden;padding-top:.7rem}.scientific-problem[_ngcontent-%COMP%]   .header_sc[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.scientific-problem[_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%]:lang(en){text-align:left}.scientific-problem[_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%]:lang(ar){text-align:right}.scientific-problem[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}@media all and (min-width: 19in){.internal_scroll[_ngcontent-%COMP%]{height:68vh}}\"]\n    });\n  }\n  return CorruptedFilesRequestsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}