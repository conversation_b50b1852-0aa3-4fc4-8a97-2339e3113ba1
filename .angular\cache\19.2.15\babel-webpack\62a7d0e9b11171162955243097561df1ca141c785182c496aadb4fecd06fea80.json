{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseConstantModel } from '../../../../../../core/ng-model/base-constant-model';\nimport { LanguageEnum } from '../../../../../../core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/teacher-appointment-services/teacher-appointment.service\";\nimport * as i2 from \"../../../../../../core/services/alertify-services/alertify.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"../../../../../../core/services/lookup-services/lookup.service\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/material/input\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction AddTeacherAppointmentRequestComponent_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const interviewDayItem_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", interviewDayItem_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? interviewDayItem_r1.nameEn : interviewDayItem_r1.nameAr, \" \");\n  }\n}\nfunction AddTeacherAppointmentRequestComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.availabilityDaysMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.availabilityDaysMessage.message, \"\");\n  }\n}\nfunction AddTeacherAppointmentRequestComponent_span_35_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const availabilityDay_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" From \", availabilityDay_r4.timeFrom, \" To \", availabilityDay_r4.timeTo, \" \");\n  }\n}\nfunction AddTeacherAppointmentRequestComponent_span_35_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const availabilityDay_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u0645\\u0646 \", availabilityDay_r4.timeFrom, \" \\u0627\\u0644\\u064A \", availabilityDay_r4.timeTo, \" \");\n  }\n}\nfunction AddTeacherAppointmentRequestComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 28)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u00A0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AddTeacherAppointmentRequestComponent_span_35_span_5_Template, 2, 2, \"span\", 29)(6, AddTeacherAppointmentRequestComponent_span_35_span_6_Template, 2, 2, \"span\", 29);\n    i0.ɵɵelementStart(7, \"a\", 30);\n    i0.ɵɵlistener(\"click\", function AddTeacherAppointmentRequestComponent_span_35_Template_a_click_7_listener() {\n      const availabilityDay_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeItemFromSelectedAvailabilityDays(availabilityDay_r4));\n    });\n    i0.ɵɵelement(8, \"i\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const availabilityDay_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? availabilityDay_r4 == null ? null : availabilityDay_r4.dayEn : availabilityDay_r4 == null ? null : availabilityDay_r4.dayAr, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang === ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar);\n  }\n}\nexport let AddTeacherAppointmentRequestComponent = /*#__PURE__*/(() => {\n  class AddTeacherAppointmentRequestComponent {\n    teacherAppointmentService;\n    alertify;\n    translate;\n    lookupService;\n    imagesPathesService;\n    openAddRequestOverlayRequest = new EventEmitter();\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    collectionOfLookup = {};\n    listOfLookupProfile = ['DAYS'];\n    selectedAvailabilityDaysList;\n    availabilityDaysMessage = {};\n    availabilityDaysModel;\n    currentUser;\n    teacherAvailableTimes;\n    newAvailabilityDays = '';\n    newTimeFrom = '';\n    newTimeTo = '';\n    selectedAvailabilityDaysListTempCompare;\n    constructor(teacherAppointmentService, alertify, translate, lookupService, imagesPathesService) {\n      this.teacherAppointmentService = teacherAppointmentService;\n      this.alertify = alertify;\n      this.translate = translate;\n      this.lookupService = lookupService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.getLookupByKey();\n      this.getTeacherAvailableTimes();\n    }\n    closeAddRequest() {\n      this.openAddRequestOverlayRequest.emit();\n    }\n    saveTeacherAppointmentRequest() {\n      if (this.compareSelectedAvailabilityDaysList()) {\n        let listOfDays = [];\n        this.selectedAvailabilityDaysList?.forEach(x => listOfDays.push({\n          timeFrom: x.timeFrom,\n          timeTo: x.timeTo,\n          // usrId: this.currentUser?.id,\n          idAvailableDay: x.idAvailableDay\n        }));\n        let model = {\n          usrId: this.currentUser?.id,\n          listOfDays: listOfDays\n        };\n        if (model.listOfDays.length > 0) {\n          this.teacherAppointmentService.AddChangeTeacherAvailableTimesRequest(model).subscribe(res => {\n            if (res.isSuccess) {\n              this.closeAddRequest();\n              this.alertify.success(res.message || '');\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        } else {\n          this.availabilityDaysMessage = {\n            message: this.translate.instant('TEACHER_APPOINTMENT.EMPTY_DAYS'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      } else {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.UPDATE_AVAILABILITY_LIST'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookupProfile).subscribe(res => {\n        this.collectionOfLookup = res.data;\n        if (res.isSuccess) {} else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    addAvailabilityDays() {\n      if (!this.selectedAvailabilityDaysList && !this.newTimeFrom && !this.newTimeTo) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_AVAILABILITY'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.availabilityDaysMessage = {};\n      const existAvailabilityDays = this.selectedAvailabilityDaysList?.some(el => el.idAvailableDay === this.newAvailabilityDays);\n      const existFromTime = this.newTimeFrom;\n      const existtoTime = this.newTimeTo;\n      if (existFromTime != null && existtoTime != null && existtoTime < existFromTime) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.VALIDATION_TIME'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList?.some(el => el.timeFrom === existFromTime && el.timeTo === existtoTime)) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.TIME_EXIST_BEFORE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n      //overlap = a.start < b.end && b.start < a.end;\n      else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList?.some(el => {\n        if (el.timeTo && el.timeFrom) {\n          return existFromTime < el.timeTo && el.timeFrom < existtoTime;\n        }\n        return false;\n      })) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.OVERLAP_TIME'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else {\n        if (this.collectionOfLookup.DAYS) {\n          this.availabilityDaysModel = {\n            dayAr: this.collectionOfLookup.DAYS?.find(a => a.id == this.newAvailabilityDays)?.nameAr,\n            dayEn: this.collectionOfLookup.DAYS?.find(a => a.id == this.newAvailabilityDays)?.nameEn,\n            idAvailableDay: this.newAvailabilityDays,\n            timeFrom: this.newTimeFrom,\n            timeTo: this.newTimeTo\n          };\n          this.selectedAvailabilityDaysList?.push(this.availabilityDaysModel);\n        }\n      }\n    }\n    removeItemFromSelectedAvailabilityDays(item) {\n      if (this.selectedAvailabilityDaysList) {\n        let index = this.selectedAvailabilityDaysList.indexOf(item);\n        this.selectedAvailabilityDaysList.splice(index, 1);\n      }\n    }\n    getTeacherAvailableTimes() {\n      this.teacherAppointmentService.getTeacherAvailableTimesTeacherView(this.currentUser?.id).subscribe(value => {\n        if (value.isSuccess) {\n          this.teacherAvailableTimes = value.data;\n          if (this.teacherAvailableTimes.teacherProfileAvailableTimes) {\n            this.selectedAvailabilityDaysList = this.teacherAvailableTimes?.teacherAvailableTimes;\n            this.selectedAvailabilityDaysListTempCompare = Object.assign([], this.selectedAvailabilityDaysList);\n          }\n        }\n      });\n    }\n    compareSelectedAvailabilityDaysList() {\n      let result = this.selectedAvailabilityDaysListTempCompare?.filter(element => this.selectedAvailabilityDaysList?.every(item => item.idAvailableDay != element.idAvailableDay || item.timeFrom?.split(':')[0] != element.timeFrom?.split(':')[0] || item.timeFrom?.split(':')[1] != element.timeFrom?.split(':')[1] || item.timeTo?.split(':')[0] != element.timeTo?.split(\":\")[0] || item.timeTo?.split(':')[1] != element.timeTo?.split(\":\")[1]));\n      /* result for diff from tempcopare versus  daylist.\n      so adding condition for length compare to cover opposite  direction from daylist to tempcompare\n      */\n      return result && result?.length > 0 || this.selectedAvailabilityDaysList && this.selectedAvailabilityDaysListTempCompare && this.selectedAvailabilityDaysList?.length > this.selectedAvailabilityDaysListTempCompare?.length ? true : false;\n    }\n    static ɵfac = function AddTeacherAppointmentRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddTeacherAppointmentRequestComponent)(i0.ɵɵdirectiveInject(i1.TeacherAppointmentService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LookupService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddTeacherAppointmentRequestComponent,\n      selectors: [[\"app-add-teacher-appointment-request\"]],\n      outputs: {\n        openAddRequestOverlayRequest: \"openAddRequestOverlayRequest\"\n      },\n      decls: 44,\n      vars: 38,\n      consts: [[1, \"form-group\", \"DataForm\", \"mt-3\", \"pl-3\", \"pr-3\"], [1, \"head\", \"bold\"], [1, \"form-group\"], [1, \"row\", \"wrapper_addTime\"], [1, \"col-md-12\", \"col-sm-2\", \"col-xs-12\", \"mt-3\"], [\"for\", \"availabilityDays\", 1, \"Register__Label\"], [\"name\", \"nameEn\", 1, \"UpdateUser__Options\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-6\", \"mt-3\"], [\"for\", \"fromDayTimeinterview\", 1, \"Register__Label\"], [1, \"UpdateUser__Options\", \"form-group\", \"mb-0\"], [\"matInput\", \"\", \"type\", \"time\", \"placeholder\", \"HH:MM\", \"id\", \"end_time_hour\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [\"for\", \"toDayTimeinterview\", 1, \"Register__Label\"], [\"matInput\", \"\", \"type\", \"time\", \"placeholder\", \"HH:MM\", \"id\", \"end_time1_hour\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [1, \"col-12\", \"align-self-end\"], [\"type\", \"button\", 1, \"cancel-btn\", \"btn\", \"btn-block\", 3, \"click\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [3, \"class\", 4, \"ngIf\"], [1, \"col-12\", \"px-0\"], [1, \"badges\", \"mt-3\"], [\"class\", \" badge badge-info p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [3, \"value\"], [1, \"bold\"], [1, \"badge\", \"badge-info\", \"p-2\"], [4, \"ngIf\"], [3, \"click\"], [1, \"fas\", \"fa-times-circle\"]],\n      template: function AddTeacherAppointmentRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"label\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddTeacherAppointmentRequestComponent_Template_select_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newAvailabilityDays, $event) || (ctx.newAvailabilityDays = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, AddTeacherAppointmentRequestComponent_option_14_Template, 2, 2, \"option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddTeacherAppointmentRequestComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newTimeFrom, $event) || (ctx.newTimeFrom = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"label\", 13);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 11)(26, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddTeacherAppointmentRequestComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newTimeTo, $event) || (ctx.newTimeTo = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function AddTeacherAppointmentRequestComponent_Template_button_click_28_listener() {\n            return ctx.addAvailabilityDays();\n          });\n          i0.ɵɵelement(29, \"img\", 17);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(32, AddTeacherAppointmentRequestComponent_div_32_Template, 3, 4, \"div\", 18);\n          i0.ɵɵelementStart(33, \"div\", 19)(34, \"div\", 20);\n          i0.ɵɵtemplate(35, AddTeacherAppointmentRequestComponent_span_35_Template, 9, 3, \"span\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"section\", 22)(37, \"div\", 23)(38, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function AddTeacherAppointmentRequestComponent_Template_button_click_38_listener() {\n            return ctx.saveTeacherAppointmentRequest();\n          });\n          i0.ɵɵtext(39);\n          i0.ɵɵpipe(40, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function AddTeacherAppointmentRequestComponent_Template_button_click_41_listener() {\n            return ctx.closeAddRequest();\n          });\n          i0.ɵɵtext(42);\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 19, \"TEACHER_APPOINTMENT.MODIFY_APPOINTMENT\"), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 21, \"UPDATE_TEACHER_PG.DAY_AND_TIME\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newAvailabilityDays);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(35, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 23, \"UPDATE_TEACHER_PG.SELECT_DAY\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.DAYS);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 25, \"UPDATE_TEACHER_PG.FROM\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTimeFrom);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(36, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(24, 27, \"UPDATE_TEACHER_PG.TO\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTimeTo);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(37, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.white_add, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 29, \"UPDATE_TEACHER_PG.ADD\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.availabilityDaysMessage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.selectedAvailabilityDaysList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 31, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(43, 33, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.NgModel, i7.NgForm, i8.MatInput, i3.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right!important}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left!important}.DataForm[_ngcontent-%COMP%]   .UpdateUser__Label[_ngcontent-%COMP%]{color:var(--main_color);border-width:70%;border-bottom:aliceblue;font-weight:700;font-size:1.25rem}.DataForm[_ngcontent-%COMP%]   .btn-block[_ngcontent-%COMP%]{display:block;width:100%;margin:0;margin-top:1rem}.DataForm[_ngcontent-%COMP%]   .Catogry_Icon_Add[_ngcontent-%COMP%]{align-self:center;margin-right:.3rem;margin-left:.3rem}.DataForm[_ngcontent-%COMP%]   .UpdateUser__Options[_ngcontent-%COMP%]{width:100%;height:2.188rem;border-radius:.65rem;border-color:#b3b3b3}.DataForm[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{white-space:normal!important}.DataForm[_ngcontent-%COMP%]   .wrapper_addTime[_ngcontent-%COMP%]{border:#ffffff;background:#fff;margin-bottom:1rem;padding-bottom:2rem;border-radius:.625rem}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin:1rem}\"]\n    });\n  }\n  return AddTeacherAppointmentRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}