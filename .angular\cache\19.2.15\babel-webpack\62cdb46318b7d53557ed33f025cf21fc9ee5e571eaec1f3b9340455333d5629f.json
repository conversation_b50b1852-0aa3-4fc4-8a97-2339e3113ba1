{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nfunction StudentDashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"app-add-studentfeelings\", 7);\n    i0.ɵɵlistener(\"closeForm\", function StudentDashboardComponent_div_6_Template_app_add_studentfeelings_closeForm_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeFeelingsForm());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let StudentDashboardComponent = /*#__PURE__*/(() => {\n  class StudentDashboardComponent {\n    languageService;\n    translate;\n    openfeelingsOverlay = false;\n    constructor(languageService, translate) {\n      this.languageService = languageService;\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.DASHBORD'));\n    }\n    openFeelingsForm() {\n      this.openfeelingsOverlay = true;\n    }\n    closeFeelingsForm() {\n      this.openfeelingsOverlay = false;\n    }\n    static ɵfac = function StudentDashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentDashboardComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentDashboardComponent,\n      selectors: [[\"app-student-dashboard\"]],\n      decls: 7,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\", \"mt-3\"], [3, \"FeelingsForm\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-8\", \"col-sm-12\", \"col-xs-12\", \"mt-3\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeForm\"]],\n      template: function StudentDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-student-feelings\", 3);\n          i0.ɵɵlistener(\"FeelingsForm\", function StudentDashboardComponent_Template_app_student_feelings_FeelingsForm_3_listener() {\n            return ctx.openFeelingsForm();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"app-dashboard-students-program-result\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, StudentDashboardComponent_div_6_Template, 2, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.openfeelingsOverlay);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".commantbackgrond[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;margin-top:1rem}.overlay.add_teacher_appointment_request[_ngcontent-%COMP%]{width:50%}\"]\n    });\n  }\n  return StudentDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}