{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ProgramDutyDaysTaskViewMoodEnum } from 'src/app/core/enums/programs/program-duty-days-task-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/common\";\nfunction StudentsProgramResultDetailsComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-question-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const question_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionTemplate\", question_r1)(\"viewMode\", true)(\"questionViewMood\", ctx_r1.questionViewMoodEnum);\n  }\n}\nfunction StudentsProgramResultDetailsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, StudentsProgramResultDetailsComponent_div_7_div_1_Template, 2, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.questionItems);\n  }\n}\nexport let StudentsProgramResultDetailsComponent = /*#__PURE__*/(() => {\n  class StudentsProgramResultDetailsComponent {\n    translate;\n    examResultDetails;\n    questionItems = [];\n    langEnum = LanguageEnum;\n    questionViewMoodEnum = ProgramDutyDaysTaskViewMoodEnum.student;\n    constructor(translate) {\n      this.translate = translate;\n    }\n    ngOnInit() {\n      this.examParsing();\n    }\n    examParsing() {\n      this.questionItems = JSON.parse(this.examResultDetails?.answ || \"{}\").questions;\n    }\n    static ɵfac = function StudentsProgramResultDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StudentsProgramResultDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudentsProgramResultDetailsComponent,\n      selectors: [[\"app-students-program-result-details\"]],\n      inputs: {\n        examResultDetails: \"examResultDetails\"\n      },\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"w-100\", \"program_result_details\"], [1, \"col-12\", \"pt-4\"], [1, \"header\"], [1, \"col-12\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [3, \"questionTemplate\", \"viewMode\", \"questionViewMood\"]],\n      template: function StudentsProgramResultDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \" \\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C \");\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 3);\n          i0.ɵɵtemplate(7, StudentsProgramResultDetailsComponent_div_7_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.examResultDetails == null ? null : ctx.examResultDetails.enProgBatName : ctx.examResultDetails == null ? null : ctx.examResultDetails.arProgBatName, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.questionItems && ctx.questionItems.length > 0);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf],\n      styles: [\".program_result_details[_ngcontent-%COMP%]{background-color:#fbfbfb;border-radius:1.25rem;height:85vh;overflow-y:auto;overflow-x:hidden}.program_result_details[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result_details[_ngcontent-%COMP%]:lang(ar):lang(er){text-align:left}.program_result_details[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:1.3rem;color:#333;font-weight:700}@media (max-width: 64rem){.program_result_details[_ngcontent-%COMP%]{overflow-x:auto}}\"]\n    });\n  }\n  return StudentsProgramResultDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}