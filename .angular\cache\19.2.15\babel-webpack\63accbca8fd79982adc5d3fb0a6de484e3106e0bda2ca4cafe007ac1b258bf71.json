{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let TeacherProgramSubscriptionServicesService = /*#__PURE__*/(() => {\n  class TeacherProgramSubscriptionServicesService {\n    http;\n    getTeachersProgramsSubscriptionsFilterURL = environment.baseUrl + 'TeacherProgramSubscription/get-teacher-program-subscriptions-filter/';\n    teacherProgramSubscriptionsAcceptanceURL = environment.baseUrl + 'TeacherProgramSubscription/accept-teacher-program-subscription/';\n    rejectTeachersProgramSubscriptionURL = environment.baseUrl + 'TeacherProgramSubscription/reject-teacher-program-subscription/';\n    submitTeacherSubscriptionURL = environment.baseUrl + 'TeacherProgramSubscription/submit-teacher-subscription/';\n    getProgramsForTeachersSubscriptionsURL = environment.baseUrl + 'TeacherProgramSubscription/get-programs-for-teachers-subscriptions/';\n    getTeacherProgramsURL = environment.baseUrl + 'TeacherProgramSubscription/get-teacher-programs/';\n    // getSubscriptionProgramDetailsURL = environment.baseUrl + 'Programs/get-subscription-program-details/';\n    constructor(http) {\n      this.http = http;\n    }\n    getTeachersProgramsSubscriptionsFilter(model) {\n      return this.http.post(this.getTeachersProgramsSubscriptionsFilterURL, model);\n    }\n    teacherProgramSubscriptionsAcceptance(model) {\n      return this.http.put(this.teacherProgramSubscriptionsAcceptanceURL, model);\n    }\n    rejectTeachersProgramSubscription(model) {\n      return this.http.put(this.rejectTeachersProgramSubscriptionURL, model);\n    }\n    submitTeacherSubscription(model) {\n      return this.http.put(this.submitTeacherSubscriptionURL, model);\n    }\n    getProgramsForTeachersSubscriptions(model) {\n      return this.http.post(this.getProgramsForTeachersSubscriptionsURL, model);\n    }\n    getTeacherPrograms(model) {\n      return this.http.post(this.getTeacherProgramsURL, model);\n    }\n    static ɵfac = function TeacherProgramSubscriptionServicesService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherProgramSubscriptionServicesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TeacherProgramSubscriptionServicesService,\n      factory: TeacherProgramSubscriptionServicesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TeacherProgramSubscriptionServicesService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}