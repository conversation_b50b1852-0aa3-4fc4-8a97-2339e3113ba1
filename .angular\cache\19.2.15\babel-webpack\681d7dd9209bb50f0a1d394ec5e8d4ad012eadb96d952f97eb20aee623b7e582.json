{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { TeacherRequestEnum } from 'src/app/core/enums/teacher-subscription-enums/teacher-request-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction TeacherRequestDetailsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-teacher-join-request\", 1);\n    i0.ɵɵlistener(\"adminTaecherCallEvent\", function TeacherRequestDetailsComponent_div_0_Template_app_teacher_join_request_adminTaecherCallEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.adminCallToTeacher($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TeacherRequestDetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-teacher-join-request-program\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherRequestDetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-teacher-admin-drop-out-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherRequestDetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-chang-time-request\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let TeacherRequestDetailsComponent = /*#__PURE__*/(() => {\n  class TeacherRequestDetailsComponent {\n    selectedTeatcherRequest;\n    teacherRequestEnum = TeacherRequestEnum;\n    constructor() {}\n    ngOnInit() {}\n    adminTaecherCallEvent = new EventEmitter();\n    adminCallToTeacher(event) {\n      this.adminTaecherCallEvent.emit(event);\n    }\n    static ɵfac = function TeacherRequestDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherRequestDetailsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherRequestDetailsComponent,\n      selectors: [[\"app-teacher-request-details\"]],\n      inputs: {\n        selectedTeatcherRequest: \"selectedTeatcherRequest\"\n      },\n      outputs: {\n        adminTaecherCallEvent: \"adminTaecherCallEvent\"\n      },\n      decls: 4,\n      vars: 4,\n      consts: [[4, \"ngIf\"], [3, \"adminTaecherCallEvent\"]],\n      template: function TeacherRequestDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TeacherRequestDetailsComponent_div_0_Template, 2, 0, \"div\", 0)(1, TeacherRequestDetailsComponent_div_1_Template, 2, 0, \"div\", 0)(2, TeacherRequestDetailsComponent_div_2_Template, 2, 0, \"div\", 0)(3, TeacherRequestDetailsComponent_div_3_Template, 2, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.teacherRequestEnum.JoinRequest === ctx.selectedTeatcherRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.teacherRequestEnum.JoinRequestProgram === ctx.selectedTeatcherRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.teacherRequestEnum.CancelRequest === ctx.selectedTeatcherRequest);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.teacherRequestEnum.ChangTimeRequest === ctx.selectedTeatcherRequest);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return TeacherRequestDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}