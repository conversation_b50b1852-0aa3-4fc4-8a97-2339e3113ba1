{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar RingShape = function () {\n  function RingShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r = 0;\n    this.r0 = 0;\n  }\n  return RingShape;\n}();\nexport { RingShape };\nvar Ring = function (_super) {\n  __extends(Ring, _super);\n  function Ring(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Ring.prototype.getDefaultShape = function () {\n    return new RingShape();\n  };\n  Ring.prototype.buildPath = function (ctx, shape) {\n    var x = shape.cx;\n    var y = shape.cy;\n    var PI2 = Math.PI * 2;\n    ctx.moveTo(x + shape.r, y);\n    ctx.arc(x, y, shape.r, 0, PI2, false);\n    ctx.moveTo(x + shape.r0, y);\n    ctx.arc(x, y, shape.r0, 0, PI2, true);\n  };\n  return Ring;\n}(Path);\nRing.prototype.type = 'ring';\nexport default Ring;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}