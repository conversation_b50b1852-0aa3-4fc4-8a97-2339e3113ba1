{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { DateType } from 'ngx-hijri-gregorian-datepicker';\nimport { BaseSelectedDateModel } from \"../../../../core/ng-model/base-selected-date-model\";\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/services/lookup-services/lookup.service\";\nimport * as i3 from \"src/app/core/services/teacher-profile/teacher-profile.service\";\nimport * as i4 from \"src/app/core/services/user-services/user.service\";\nimport * as i5 from \"src/app/core/services/role-management/role-management.service\";\nimport * as i6 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"src/app/core/services/program-services/program.service\";\nimport * as i9 from \"src/app/core/services/language-services/language.service\";\nimport * as i10 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i11 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i12 from \"src/app/core/services/auth-services/auth.service\";\nimport * as i13 from \"@angular/material/dialog\";\nimport * as i14 from \"@angular/router\";\nimport * as i15 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i16 from \"@angular/common\";\nimport * as i17 from \"@angular/material/tooltip\";\nimport * as i18 from \"@angular/material/radio\";\nconst _c0 = a0 => ({\n  \"dimmed\": a0\n});\nconst _c1 = a0 => ({\n  \"dimmed-btn\": a0\n});\nconst _c2 = a0 => ({\n  \"disableDiv\": a0\n});\nfunction UpdateTeacherProfileComponent_div_0_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 138);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"GENERAL.CONFIRM_LOGOUT_MESSAGE_TEACHER\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_20_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_20_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_20_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_20_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_20_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_20_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_20_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstAr.errors == null ? null : ctx_r1.f.firstAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstAr.errors == null ? null : ctx_r1.f.firstAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstAr.errors == null ? null : ctx_r1.f.firstAr.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 139);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 140);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_20_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FIRST_NAME\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstAr.errors && (ctx_r1.f.firstAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_21_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_21_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_21_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_21_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_21_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_21_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_21_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleAr.errors == null ? null : ctx_r1.f.middleAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleAr.errors == null ? null : ctx_r1.f.middleAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleAr.errors == null ? null : ctx_r1.f.middleAr.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 143);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 144);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_21_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.FATHER_NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FATHER_NAME_EN\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleAr.errors && (ctx_r1.f.middleAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_22_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_22_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_22_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_22_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_22_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_22_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_22_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyAr.errors == null ? null : ctx_r1.f.familyAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyAr.errors == null ? null : ctx_r1.f.familyAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyAr.errors == null ? null : ctx_r1.f.familyAr.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 145);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 146);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_22_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyAr.errors && (ctx_r1.f.familyAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_23_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_23_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_23_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_23_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_23_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_23_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_23_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstEn.errors == null ? null : ctx_r1.f.firstEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstEn.errors == null ? null : ctx_r1.f.firstEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstEn.errors == null ? null : ctx_r1.f.firstEn.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 147);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 148);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_23_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FIRST_NAME\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstEn.errors && (ctx_r1.f.firstEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_24_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_24_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_24_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FATHER_NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_24_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_24_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_24_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_24_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleEn.errors == null ? null : ctx_r1.f.middleEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleEn.errors == null ? null : ctx_r1.f.middleEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleEn.errors == null ? null : ctx_r1.f.middleEn.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 150);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_24_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.FATHER_NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FATHER_NAME_EN\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleEn.errors && (ctx_r1.f.middleEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_25_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_25_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_25_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_25_div_6_div_1_Template, 3, 3, \"div\", 142)(2, UpdateTeacherProfileComponent_div_0_div_25_div_6_div_2_Template, 3, 3, \"div\", 142)(3, UpdateTeacherProfileComponent_div_0_div_25_div_6_div_3_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyEn.errors == null ? null : ctx_r1.f.familyEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyEn.errors == null ? null : ctx_r1.f.familyEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyEn.errors == null ? null : ctx_r1.f.familyEn.errors.minlength);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\", 151);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 152);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateTeacherProfileComponent_div_0_div_25_div_6_Template, 4, 3, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyEn.errors && (ctx_r1.f.familyEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-milady-hijri-calendar\", 153);\n    i0.ɵɵlistener(\"sendDate\", function UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_31_Template_app_milady_hijri_calendar_sendDate_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.SendData($event));\n    })(\"keypress\", function UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_31_Template_app_milady_hijri_calendar_keypress_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"dateTo\", ctx_r1.hijriBirthDateInputParam)(\"hijri\", ctx_r1.hijri)(\"milady\", ctx_r1.milady)(\"maxHijri\", ctx_r1.maxHijriDate)(\"maxGreg\", ctx_r1.maxGregDate)(\"minGreg\", ctx_r1.minGregDate)(\"minHijri\", ctx_r1.minHijriDate)(\"editcalenderType\", ctx_r1.updateCalenderType);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.HIJRIBIRTHDATE_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_32_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hijriBinding);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_mat_radio_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 154);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r5.nameEn : item_r5.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.GENDER_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_41_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.gender.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_53_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.PHONE_NUMBER_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_53_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.mobile.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const nationalityItem_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", nationalityItem_r6.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? nationalityItem_r6.nameEn : nationalityItem_r6.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_69_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.NATIONALITY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_69_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.nationality.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const countryItem_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", countryItem_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? countryItem_r7.nameEn : countryItem_r7.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.THISFIELD_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_79_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.country.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cityItem_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", cityItem_r8.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? cityItem_r8.nameEn : cityItem_r8.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_89_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.THISFIELD_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_89_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.city.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-milady-hijri-calendar\", 156);\n    i0.ɵɵlistener(\"sendDate\", function UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_99_Template_app_milady_hijri_calendar_sendDate_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.HijriInterviewDay($event));\n    })(\"keypress\", function UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_99_Template_app_milady_hijri_calendar_keypress_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"dateTo\", ctx_r1.hijriInterviewDayInputParam)(\"hijri\", ctx_r1.hijri)(\"milady\", ctx_r1.milady)(\"minHijri\", ctx_r1.minHijriInterviewDate)(\"minGreg\", ctx_r1.minGregInterviewDate)(\"editcalenderType\", ctx_r1.updateInterviewCalenderType);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_100_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.DAY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_100_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.hijriInterviewDay.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_107_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FROM_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_107_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.interviewTime.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_116_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.ADDRESS_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_116_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors == null ? null : ctx_r1.f.address.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_133_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"div\", 158)(2, \"p\", 159);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 160);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_div_133_Template_a_click_4_listener() {\n      const ctx_r10 = i0.ɵɵrestoreView(_r10);\n      const item_r12 = ctx_r10.$implicit;\n      const i_r13 = ctx_r10.index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.DeleteAttachment(i_r13, item_r12.id));\n    });\n    i0.ɵɵelement(5, \"i\", 161);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", item_r12.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r12.fileName, \"\");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const edulevelItem_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", edulevelItem_r14.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? edulevelItem_r14.nameEn : edulevelItem_r14.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_149_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.EDU_LEVEL_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_149_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.edulevel.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const qualifiItem_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", qualifiItem_r15.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? qualifiItem_r15.nameEn : qualifiItem_r15.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_159_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.QUALIFICATION_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_159_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.qualifi.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_168_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const special_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", special_r16.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? special_r16.nameEn : special_r16.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_169_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.SPECTALIZATION_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_169_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.specia.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_mat_radio_button_176_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 154);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r17.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r17.nameEn : item_r17.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_177_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.AGENCY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_177_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_177_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.agency.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_184_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.ENTITY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_184_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_184_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.entity.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_191_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.EDU_NUM_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_191_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_191_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.eduNum.errors == null ? null : ctx_r1.f.eduNum.errors.min);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_207_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_QURAN_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_207_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_207_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasQuranExp.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_220_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_TEACH_SUNNA_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_220_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_220_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasTeachSunnaExp.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_233_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_INTERNET_TEACH_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_233_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_233_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasInternetTeachExp.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_246_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_TEACH_FOREIGNER_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_246_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_246_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasTeachForeignerExp.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_259_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_EJAZA_HAFZ_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_259_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_259_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasEjazaHafz.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_274_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.IS_HAS_EJAZA_TELAWA_EXP_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_274_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_274_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasEjazaTelawa.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_282_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rewayat_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", rewayat_r18.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? rewayat_r18.nameEn : rewayat_r18.nameAr, \"\");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_286_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.REWAYATS_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_286_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_286_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.rewayats.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_span_288_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"a\", 160);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_span_288_Template_a_click_2_listener() {\n      const p_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeItemFromSelectedTeacherRewayats(p_r20));\n    });\n    i0.ɵɵelement(3, \"i\", 161);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const p_r20 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? p_r20.nameEn : p_r20.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_299_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const language_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", language_r21.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? language_r21.nameEn : language_r21.nameAr, \"\");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_303_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.LANGUAGES_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_303_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_303_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.languages.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_span_305_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"a\", 160);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_span_305_Template_a_click_2_listener() {\n      const p_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeItemFromSelectedTeacherLanguages(p_r23));\n    });\n    i0.ɵɵelement(3, \"i\", 161);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const p_r23 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? p_r23.nameEn : p_r23.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_mat_radio_button_320_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 154);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r24.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r24.nameEn : item_r24.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_321_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.WORKING_PLATFORM_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_321_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_321_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.workingPlatForm.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_option_347_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 155);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const interviewDayItem_r25 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", interviewDayItem_r25.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? interviewDayItem_r25.nameEn : interviewDayItem_r25.nameAr, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_351_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.DAY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_351_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_351_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.availabilityDays.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_358_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.FROM_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_358_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_358_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.fromDayTimeinterview.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_365_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.TO_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_365_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, UpdateTeacherProfileComponent_div_0_div_365_div_1_Template, 3, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.toDayTimeinterview.errors.required);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_373_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.availabilityDaysMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.availabilityDaysMessage.message, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_span_376_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" From \", p_r27.fromTime, \" To \", p_r27.toTime, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_span_376_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u0645\\u0646 \", p_r27.fromTime, \" \\u0627\\u0644\\u064A \", p_r27.toTime, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_span_376_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u00A0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, UpdateTeacherProfileComponent_div_0_span_376_span_4_Template, 2, 2, \"span\", 142)(5, UpdateTeacherProfileComponent_div_0_span_376_span_5_Template, 2, 2, \"span\", 142);\n    i0.ɵɵelementStart(6, \"a\", 160);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_span_376_Template_a_click_6_listener() {\n      const p_r27 = i0.ɵɵrestoreView(_r26).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeItemFromSelectedAvailabilityDays(p_r27));\n    });\n    i0.ɵɵelement(7, \"i\", 161);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const p_r27 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? p_r27 == null ? null : p_r27.nameEn : p_r27 == null ? null : p_r27.nameAr, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang === ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar);\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_div_377_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nfunction UpdateTeacherProfileComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function UpdateTeacherProfileComponent_div_0_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit(ctx_r1.profileForm.value));\n    });\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵlistener(\"dragover\", function UpdateTeacherProfileComponent_div_0_Template_div_dragover_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragOver($event));\n    })(\"drop\", function UpdateTeacherProfileComponent_div_0_Template_div_drop_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDropSuccess($event));\n    });\n    i0.ɵɵelement(5, \"div\", 7)(6, \"img\", 8);\n    i0.ɵɵelementStart(7, \"input\", 9, 0);\n    i0.ɵɵlistener(\"change\", function UpdateTeacherProfileComponent_div_0_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const fileInput_r3 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(fileInput_r3.click());\n    });\n    i0.ɵɵelement(10, \"img\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"div\", 13);\n    i0.ɵɵtemplate(13, UpdateTeacherProfileComponent_div_0_p_13_Template, 3, 3, \"p\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 15)(15, \"label\", 16);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 17)(19, \"div\", 18);\n    i0.ɵɵtemplate(20, UpdateTeacherProfileComponent_div_0_div_20_Template, 7, 7, \"div\", 19)(21, UpdateTeacherProfileComponent_div_0_div_21_Template, 7, 7, \"div\", 19)(22, UpdateTeacherProfileComponent_div_0_div_22_Template, 7, 7, \"div\", 19)(23, UpdateTeacherProfileComponent_div_0_div_23_Template, 7, 7, \"div\", 19)(24, UpdateTeacherProfileComponent_div_0_div_24_Template, 7, 7, \"div\", 19)(25, UpdateTeacherProfileComponent_div_0_div_25_Template, 7, 7, \"div\", 19);\n    i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"label\", 22);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_31_Template, 1, 8, \"app-milady-hijri-calendar\", 23)(32, UpdateTeacherProfileComponent_div_0_div_32_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 25)(34, \"label\", 26);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 3)(38, \"section\", 27)(39, \"mat-radio-group\", 28);\n    i0.ɵɵtemplate(40, UpdateTeacherProfileComponent_div_0_mat_radio_button_40_Template, 2, 2, \"mat-radio-button\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, UpdateTeacherProfileComponent_div_0_div_41_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 30)(43, \"label\", 31);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 32)(47, \"div\", 18)(48, \"div\", 33)(49, \"label\", 34);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"app-tel-input\", 35);\n    i0.ɵɵlistener(\"getPhonNumber\", function UpdateTeacherProfileComponent_div_0_Template_app_tel_input_getPhonNumber_52_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyPhoneNumber($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(53, UpdateTeacherProfileComponent_div_0_div_53_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 33)(55, \"label\", 36);\n    i0.ɵɵtext(56);\n    i0.ɵɵpipe(57, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 33)(60, \"label\", 38);\n    i0.ɵɵtext(61);\n    i0.ɵɵpipe(62, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\")(64, \"select\", 39)(65, \"option\", 40);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(68, UpdateTeacherProfileComponent_div_0_option_68_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(69, UpdateTeacherProfileComponent_div_0_div_69_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 33)(71, \"label\", 42);\n    i0.ɵɵtext(72);\n    i0.ɵɵpipe(73, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"select\", 43);\n    i0.ɵɵlistener(\"change\", function UpdateTeacherProfileComponent_div_0_Template_select_change_74_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.getCitiesLookupByCountry());\n    });\n    i0.ɵɵelementStart(75, \"option\", 40);\n    i0.ɵɵtext(76);\n    i0.ɵɵpipe(77, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(78, UpdateTeacherProfileComponent_div_0_option_78_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(79, UpdateTeacherProfileComponent_div_0_div_79_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 33)(81, \"label\", 44);\n    i0.ɵɵtext(82);\n    i0.ɵɵpipe(83, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"select\", 45)(85, \"option\", 40);\n    i0.ɵɵtext(86);\n    i0.ɵɵpipe(87, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(88, UpdateTeacherProfileComponent_div_0_option_88_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(89, UpdateTeacherProfileComponent_div_0_div_89_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 46)(91, \"p\", 47);\n    i0.ɵɵtext(92);\n    i0.ɵɵpipe(93, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"div\", 48)(95, \"label\", 49);\n    i0.ɵɵtext(96);\n    i0.ɵɵpipe(97, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 50);\n    i0.ɵɵtemplate(99, UpdateTeacherProfileComponent_div_0_app_milady_hijri_calendar_99_Template, 1, 6, \"app-milady-hijri-calendar\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(100, UpdateTeacherProfileComponent_div_0_div_100_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(101, \"div\", 52)(102, \"label\", 53);\n    i0.ɵɵtext(103);\n    i0.ɵɵpipe(104, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\", 54);\n    i0.ɵɵelement(106, \"input\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(107, UpdateTeacherProfileComponent_div_0_div_107_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(108, \"div\", 32)(109, \"div\", 18)(110, \"div\", 56)(111, \"label\", 57);\n    i0.ɵɵtext(112);\n    i0.ɵɵpipe(113, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(114, \"input\", 58);\n    i0.ɵɵpipe(115, \"translate\");\n    i0.ɵɵtemplate(116, UpdateTeacherProfileComponent_div_0_div_116_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(117, \"div\", 59)(118, \"label\", 60);\n    i0.ɵɵtext(119);\n    i0.ɵɵpipe(120, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"label\", 61)(122, \"div\", 3)(123, \"div\", 62);\n    i0.ɵɵelement(124, \"input\", 63);\n    i0.ɵɵpipe(125, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"div\", 64);\n    i0.ɵɵelement(127, \"img\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(128, \"div\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(129, \"input\", 67);\n    i0.ɵɵlistener(\"change\", function UpdateTeacherProfileComponent_div_0_Template_input_change_129_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onEjazaFileChange($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 3)(131, \"div\", 68)(132, \"div\", 69);\n    i0.ɵɵtemplate(133, UpdateTeacherProfileComponent_div_0_div_133_Template, 6, 2, \"div\", 70);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(134, \"div\", 30)(135, \"label\", 31);\n    i0.ɵɵtext(136);\n    i0.ɵɵpipe(137, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(138, \"div\", 32)(139, \"div\", 18)(140, \"div\", 71)(141, \"label\", 72);\n    i0.ɵɵtext(142);\n    i0.ɵɵpipe(143, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(144, \"select\", 73)(145, \"option\", 40);\n    i0.ɵɵtext(146);\n    i0.ɵɵpipe(147, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(148, UpdateTeacherProfileComponent_div_0_option_148_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(149, UpdateTeacherProfileComponent_div_0_div_149_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(150, \"div\", 71)(151, \"label\", 74);\n    i0.ɵɵtext(152);\n    i0.ɵɵpipe(153, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(154, \"select\", 75)(155, \"option\", 40);\n    i0.ɵɵtext(156);\n    i0.ɵɵpipe(157, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(158, UpdateTeacherProfileComponent_div_0_option_158_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(159, UpdateTeacherProfileComponent_div_0_div_159_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(160, \"div\", 71)(161, \"label\", 76);\n    i0.ɵɵtext(162);\n    i0.ɵɵpipe(163, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(164, \"select\", 77)(165, \"option\", 40);\n    i0.ɵɵtext(166);\n    i0.ɵɵpipe(167, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(168, UpdateTeacherProfileComponent_div_0_option_168_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(169, UpdateTeacherProfileComponent_div_0_div_169_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"div\", 78)(171, \"label\", 79);\n    i0.ɵɵtext(172);\n    i0.ɵɵpipe(173, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(174, \"section\")(175, \"mat-radio-group\", 80);\n    i0.ɵɵtemplate(176, UpdateTeacherProfileComponent_div_0_mat_radio_button_176_Template, 2, 2, \"mat-radio-button\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(177, UpdateTeacherProfileComponent_div_0_div_177_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"div\", 81)(179, \"label\", 82);\n    i0.ɵɵtext(180);\n    i0.ɵɵpipe(181, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(182, \"input\", 83);\n    i0.ɵɵpipe(183, \"translate\");\n    i0.ɵɵtemplate(184, UpdateTeacherProfileComponent_div_0_div_184_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(185, \"div\", 84)(186, \"label\", 85);\n    i0.ɵɵtext(187);\n    i0.ɵɵpipe(188, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(189, \"input\", 86);\n    i0.ɵɵpipe(190, \"translate\");\n    i0.ɵɵtemplate(191, UpdateTeacherProfileComponent_div_0_div_191_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(192, \"div\", 87);\n    i0.ɵɵelement(193, \"br\")(194, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(195, \"div\", 81)(196, \"label\", 88);\n    i0.ɵɵtext(197);\n    i0.ɵɵpipe(198, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(199, \"section\")(200, \"mat-radio-group\", 89)(201, \"mat-radio-button\", 90);\n    i0.ɵɵtext(202);\n    i0.ɵɵpipe(203, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(204, \"mat-radio-button\", 91);\n    i0.ɵɵtext(205);\n    i0.ɵɵpipe(206, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(207, UpdateTeacherProfileComponent_div_0_div_207_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(208, \"div\", 81)(209, \"label\", 92);\n    i0.ɵɵtext(210);\n    i0.ɵɵpipe(211, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(212, \"section\")(213, \"mat-radio-group\", 93)(214, \"mat-radio-button\", 90);\n    i0.ɵɵtext(215);\n    i0.ɵɵpipe(216, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(217, \"mat-radio-button\", 91);\n    i0.ɵɵtext(218);\n    i0.ɵɵpipe(219, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(220, UpdateTeacherProfileComponent_div_0_div_220_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(221, \"div\", 81)(222, \"label\", 94);\n    i0.ɵɵtext(223);\n    i0.ɵɵpipe(224, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(225, \"section\")(226, \"mat-radio-group\", 95)(227, \"mat-radio-button\", 90);\n    i0.ɵɵtext(228);\n    i0.ɵɵpipe(229, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(230, \"mat-radio-button\", 91);\n    i0.ɵɵtext(231);\n    i0.ɵɵpipe(232, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(233, UpdateTeacherProfileComponent_div_0_div_233_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(234, \"div\", 81)(235, \"label\", 96);\n    i0.ɵɵtext(236);\n    i0.ɵɵpipe(237, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(238, \"section\")(239, \"mat-radio-group\", 97)(240, \"mat-radio-button\", 90);\n    i0.ɵɵtext(241);\n    i0.ɵɵpipe(242, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(243, \"mat-radio-button\", 91);\n    i0.ɵɵtext(244);\n    i0.ɵɵpipe(245, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(246, UpdateTeacherProfileComponent_div_0_div_246_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(247, \"div\", 81)(248, \"label\", 98);\n    i0.ɵɵtext(249);\n    i0.ɵɵpipe(250, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(251, \"section\")(252, \"mat-radio-group\", 99)(253, \"mat-radio-button\", 90);\n    i0.ɵɵtext(254);\n    i0.ɵɵpipe(255, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(256, \"mat-radio-button\", 91);\n    i0.ɵɵtext(257);\n    i0.ɵɵpipe(258, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(259, UpdateTeacherProfileComponent_div_0_div_259_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(260, \"div\", 87);\n    i0.ɵɵelement(261, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(262, \"div\", 81)(263, \"label\", 100);\n    i0.ɵɵtext(264);\n    i0.ɵɵpipe(265, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(266, \"section\")(267, \"mat-radio-group\", 101)(268, \"mat-radio-button\", 90);\n    i0.ɵɵtext(269);\n    i0.ɵɵpipe(270, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(271, \"mat-radio-button\", 91);\n    i0.ɵɵtext(272);\n    i0.ɵɵpipe(273, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(274, UpdateTeacherProfileComponent_div_0_div_274_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(275, \"div\", 102)(276, \"label\", 103);\n    i0.ɵɵtext(277);\n    i0.ɵɵpipe(278, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(279, \"div\", 104)(280, \"div\", 105)(281, \"select\", 106);\n    i0.ɵɵtemplate(282, UpdateTeacherProfileComponent_div_0_option_282_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(283, \"span\", 107)(284, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_Template_button_click_284_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addTeacherRewayats());\n    });\n    i0.ɵɵelement(285, \"img\", 109);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(286, UpdateTeacherProfileComponent_div_0_div_286_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementStart(287, \"div\", 69);\n    i0.ɵɵtemplate(288, UpdateTeacherProfileComponent_div_0_span_288_Template, 4, 1, \"span\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(289, \"div\", 102)(290, \"label\", 111);\n    i0.ɵɵtext(291);\n    i0.ɵɵpipe(292, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(293, \"div\", 104)(294, \"div\", 105)(295, \"select\", 112)(296, \"option\", 40);\n    i0.ɵɵtext(297);\n    i0.ɵɵpipe(298, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(299, UpdateTeacherProfileComponent_div_0_option_299_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(300, \"span\", 107)(301, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_Template_button_click_301_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addTeacherLanguages());\n    });\n    i0.ɵɵelement(302, \"img\", 109);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(303, UpdateTeacherProfileComponent_div_0_div_303_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementStart(304, \"div\", 69);\n    i0.ɵɵtemplate(305, UpdateTeacherProfileComponent_div_0_span_305_Template, 4, 1, \"span\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(306, \"div\", 113);\n    i0.ɵɵelement(307, \"br\")(308, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(309, \"div\", 114)(310, \"label\", 31);\n    i0.ɵɵtext(311);\n    i0.ɵɵpipe(312, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(313, \"div\", 113);\n    i0.ɵɵelementStart(314, \"div\", 115)(315, \"label\", 116);\n    i0.ɵɵtext(316);\n    i0.ɵɵpipe(317, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(318, \"section\", 27)(319, \"mat-radio-group\", 117);\n    i0.ɵɵtemplate(320, UpdateTeacherProfileComponent_div_0_mat_radio_button_320_Template, 2, 2, \"mat-radio-button\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(321, UpdateTeacherProfileComponent_div_0_div_321_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(322, \"div\", 102)(323, \"label\", 118);\n    i0.ɵɵtext(324);\n    i0.ɵɵpipe(325, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(326, \"input\", 119);\n    i0.ɵɵpipe(327, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(328, \"div\", 102)(329, \"label\", 120);\n    i0.ɵɵtext(330);\n    i0.ɵɵpipe(331, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(332, \"input\", 121);\n    i0.ɵɵpipe(333, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(334, \"div\", 113);\n    i0.ɵɵelement(335, \"br\")(336, \"br\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(337, \"div\", 113)(338, \"div\", 3)(339, \"div\", 122)(340, \"label\", 123);\n    i0.ɵɵtext(341);\n    i0.ɵɵpipe(342, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(343, \"select\", 124)(344, \"option\", 40);\n    i0.ɵɵtext(345);\n    i0.ɵɵpipe(346, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(347, UpdateTeacherProfileComponent_div_0_option_347_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementStart(348, \"option\", 125);\n    i0.ɵɵtext(349);\n    i0.ɵɵpipe(350, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(351, UpdateTeacherProfileComponent_div_0_div_351_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(352, \"div\", 126)(353, \"label\", 127);\n    i0.ɵɵtext(354);\n    i0.ɵɵpipe(355, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(356, \"div\", 54);\n    i0.ɵɵelement(357, \"input\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(358, UpdateTeacherProfileComponent_div_0_div_358_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(359, \"div\", 126)(360, \"label\", 129);\n    i0.ɵɵtext(361);\n    i0.ɵɵpipe(362, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(363, \"div\", 54);\n    i0.ɵɵelement(364, \"input\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(365, UpdateTeacherProfileComponent_div_0_div_365_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(366, \"div\", 131)(367, \"label\", 132);\n    i0.ɵɵtext(368);\n    i0.ɵɵpipe(369, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(370, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function UpdateTeacherProfileComponent_div_0_Template_button_click_370_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addAvailabilityDays());\n    });\n    i0.ɵɵtext(371);\n    i0.ɵɵpipe(372, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(373, UpdateTeacherProfileComponent_div_0_div_373_Template, 2, 4, \"div\", 134);\n    i0.ɵɵelementStart(374, \"div\", 135)(375, \"div\", 69);\n    i0.ɵɵtemplate(376, UpdateTeacherProfileComponent_div_0_span_376_Template, 8, 3, \"span\", 110);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(377, UpdateTeacherProfileComponent_div_0_div_377_Template, 2, 4, \"div\", 134);\n    i0.ɵɵelementStart(378, \"div\", 136)(379, \"button\", 137);\n    i0.ɵɵtext(380);\n    i0.ɵɵpipe(381, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.proPic, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.upload_image_white, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.roleService.isAccAcc() && ctx_r1.roleService.isTeacher());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(17, 146, \"UPDATE_TEACHER_PG.PERSONAL_INFORMATION\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 148, \"UPDATE_TEACHER_PG.HIJRIBIRTHDATE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hijriBirthDateInputParam && ctx_r1.updateCalenderType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hijriBinding && (ctx_r1.f.hijriBirthDate.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 150, \"GENERAL.GENDER\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", (ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId) != null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.GENDER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.gender.errors && (ctx_r1.f.gender.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(45, 152, \"UPDATE_TEACHER_PG.CONTACT_INFO\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(51, 154, \"GENERAL.PHONE_NUMBER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"telInputParam\", ctx_r1.telInputParam);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.mobile.errors && (ctx_r1.f.mobile.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(57, 156, \"GENERAL.EMAIL\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(62, 158, \"GENERAL.NATIONALITY\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(67, 160, \"UPDATE_TEACHER_PG.SELECT_NATIONALITY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.NATIONALITY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.nationality.errors && (ctx_r1.f.nationality.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(73, 162, \"UPDATE_TEACHER_PG.SELECT_Country\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(77, 164, \"UPDATE_TEACHER_PG.SELECT_Country\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.COUNTRY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.country.errors && (ctx_r1.f.country.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(83, 166, \"UPDATE_TEACHER_PG.CITY\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(87, 168, \"UPDATE_TEACHER_PG.SELEC_CITY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.CITY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.city.errors && (ctx_r1.f.city.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(93, 170, \"UPDATE_TEACHER_PG.INFO_CONTACT\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(97, 172, \"UPDATE_TEACHER_PG.DAY\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(278, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hijriInterviewDayInputParam && ctx_r1.updateInterviewCalenderType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.hijriInterviewDay.errors && (ctx_r1.f.hijriInterviewDay.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(104, 174, \"UPDATE_TEACHER_PG.FROM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(280, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(282, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.interviewTime.errors && (ctx_r1.f.interviewTime.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(113, 176, \"UPDATE_TEACHER_PG.ADDRESS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(115, 178, \"UPDATE_TEACHER_PG.SELECT_ADDRESS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors && (ctx_r1.f.address.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(120, 180, \"UPDATE_TEACHER_PG.MEMORIES_QURAN\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(125, 182, \"UPDATE_TEACHER_PG.JOIN_MEMORIES_QURAN\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.upload_icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(137, 184, \"UPDATE_TEACHER_PG.EDU\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(143, 186, \"UPDATE_TEACHER_PG.EDU\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(147, 188, \"UPDATE_TEACHER_PG.SELECT_EDU\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.EDU_LEVEL);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.edulevel.errors && (ctx_r1.f.edulevel.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(153, 190, \"UPDATE_TEACHER_PG.QUALIFICATION\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(157, 192, \"UPDATE_TEACHER_PG.SELECT_EDU_LEVEL\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.QUALIFI);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.qualifi.errors && (ctx_r1.f.qualifi.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(163, 194, \"UPDATE_TEACHER_PG.SPECTALIZATION\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(167, 196, \"UPDATE_TEACHER_PG.SELECT_SPECTALIZATION\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.SPECIAL);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.specia.errors && (ctx_r1.f.specia.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(173, 198, \"UPDATE_TEACHER_PG.LEARN_THEQURAN\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.AGENCY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.agency.errors && (ctx_r1.f.agency.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(181, 200, \"UPDATE_TEACHER_PG.ENTITY\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(183, 202, \"UPDATE_TEACHER_PG.NAME_ENTITY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.entity.errors && (ctx_r1.f.entity.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(188, 204, \"UPDATE_TEACHER_PG.DURATION\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(190, 206, \"UPDATE_TEACHER_PG.EDUCATION_YEARS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.eduNum.errors && (ctx_r1.f.eduNum.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(198, 208, \"UPDATE_TEACHER_PG.IMPROVING_QURAN_WITH_TAJWEED\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(203, 210, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(206, 212, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasQuranExp.errors && (ctx_r1.f.isHasQuranExp.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(211, 214, \"UPDATE_TEACHER_PG.EXPERIENCE_TEACHING_SUNNAH\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(216, 216, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(219, 218, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasTeachSunnaExp.errors && (ctx_r1.f.isHasTeachSunnaExp.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(224, 220, \"UPDATE_TEACHER_PG.EXPERIENCE_TEACHING_ONLINE\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(229, 222, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(232, 224, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasInternetTeachExp.errors && (ctx_r1.f.isHasInternetTeachExp.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(237, 226, \"UPDATE_TEACHER_PG.EXPERIENCE_TEACHING_FOREIGNERS\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(242, 228, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(245, 230, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasTeachForeignerExp.errors && (ctx_r1.f.isHasTeachForeignerExp.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(250, 232, \"UPDATE_TEACHER_PG.LEAVE_SAVE\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(255, 234, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(258, 236, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasEjazaHafz.errors && (ctx_r1.f.isHasEjazaHafz.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(265, 238, \"UPDATE_TEACHER_PG.LEAVE_RECITATION\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(270, 240, \"UPDATE_TEACHER_PG.YES\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(273, 242, \"UPDATE_TEACHER_PG.NO\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.isHasEjazaTelawa.errors && (ctx_r1.f.isHasEjazaTelawa.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(278, 244, \"UPDATE_TEACHER_PG.TYPE_READING\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.REWAYAT);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.rewayats.errors && (ctx_r1.f.rewayats.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedRewayatsList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(292, 246, \"UPDATE_TEACHER_PG.LANGUAGES\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(298, 248, \"UPDATE_TEACHER_PG.SELECT_languages\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.LANG);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.add, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.languages.errors && (ctx_r1.f.languages.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedLanguagesList);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(312, 250, \"UPDATE_TEACHER_PG.AVAILABLE_PROGRAMS_AND_TIMES\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(317, 252, \"UPDATE_TEACHER_PG.WORK_PLATFORM\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.WORKING_PLATFORM);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.workingPlatForm.errors && (ctx_r1.f.workingPlatForm.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(325, 254, \"UPDATE_TEACHER_PG.BANK_NAME\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(327, 256, \"UPDATE_TEACHER_PG.INPUT_BANK_NMAE\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(331, 258, \"UPDATE_TEACHER_PG.ACCOUNT_NUMBER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(333, 260, \"UPDATE_TEACHER_PG.INPUT_ACCOUNT_NUMBER\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(342, 262, \"UPDATE_TEACHER_PG.DAY_AND_TIME\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(284, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(346, 264, \"UPDATE_TEACHER_PG.SELECT_DAY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.DAYS);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(350, 266, \"UPDATE_TEACHER_PG.ALL_DAYS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.availabilityDays.errors && (ctx_r1.f.availabilityDays.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(355, 268, \"UPDATE_TEACHER_PG.FROM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(286, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(288, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.fromDayTimeinterview.errors && (ctx_r1.f.fromDayTimeinterview.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(362, 270, \"UPDATE_TEACHER_PG.TO\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(290, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(292, _c0, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.toDayTimeinterview.errors && (ctx_r1.f.toDayTimeinterview.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(369, 272, \"UPDATE_TEACHER_PG.ADD\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(294, _c1, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(372, 274, \"UPDATE_TEACHER_PG.ADD\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availabilityDaysMessage.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(296, _c2, ctx_r1.teacherProfileDetails == null ? null : ctx_r1.teacherProfileDetails.profileId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedAvailabilityDaysList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage.message);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(381, 276, \"GENERAL.SAVE\"), \" \");\n  }\n}\nexport let UpdateTeacherProfileComponent = /*#__PURE__*/(() => {\n  class UpdateTeacherProfileComponent {\n    fb;\n    lookupService;\n    teacherService;\n    userProfilePicService;\n    userService;\n    roleService;\n    attachmentService;\n    translate;\n    ProgramService;\n    languageService;\n    dateFormatterService;\n    chatService;\n    authService;\n    dialog;\n    router;\n    imagesPathesService;\n    profileForm = new FormGroup({});\n    currentUser;\n    telInputParam = {};\n    teacherProfileDetails = {};\n    updateTeacherModel = {};\n    teacherProgramModel = {};\n    availabilityDaysModel = {};\n    langEnum = LanguageEnum;\n    collectionOfLookup = {};\n    listOfLookupProfile = ['GENDER', 'EDU_LEVEL', 'NATIONALITY', 'COUNTRY', 'DEGREE', 'EDU_DATE', 'DAYS', 'LANG', 'QUALIFI', 'SPECIAL', 'REWAYAT', 'AGENCY', 'WORKING_PLATFORM'];\n    resMessage = {};\n    selecteddrgreeList = Array();\n    drgreeList__addition = Array();\n    degreeMessage = {};\n    isSubmit = false;\n    hijri = false;\n    milady = false;\n    hijriBinding;\n    hijriBirthDateInputParam; //= { year: 0, day: 0, month: 0 };\n    hijriInterviewDayInputParam; //{ year: 0, day: 0, month: 0 };\n    ProgramsList = [];\n    ProgramFilter = {};\n    rewayatsMessage = {};\n    selectedRewayatsList = Array();\n    languagesMessage = {};\n    selectedLanguagesList = Array();\n    availabilityDaysMessage = {};\n    selectedAvailabilityDaysList = Array();\n    teacherProgramsMessage = {};\n    selectedTeacherProgramsList = Array();\n    fileUploadModel = [];\n    fileList = [];\n    ejazaAttachmentIds = [];\n    programFilterByNameFilterRequest = {};\n    participantModel;\n    event = {\n      eampm: \"AM\"\n    };\n    selectedDateType;\n    selectedInterviewDateType;\n    //selectedDateType_Melady = DateType.Gregorian;  // or DateType.Gregorian\n    //selectedDateType_Hijri = DateType.Hijri;\n    updateCalenderType = new BaseSelectedDateModel();\n    updateInterviewCalenderType = new BaseSelectedDateModel();\n    maxHijriDate;\n    maxGregDate;\n    minHijriInterviewDate;\n    minGregInterviewDate;\n    minGregDate;\n    minHijriDate;\n    constructor(fb, lookupService, teacherService, userProfilePicService, userService, roleService, attachmentService, translate, ProgramService, languageService, dateFormatterService, chatService, authService, dialog, router, imagesPathesService) {\n      this.fb = fb;\n      this.lookupService = lookupService;\n      this.teacherService = teacherService;\n      this.userProfilePicService = userProfilePicService;\n      this.userService = userService;\n      this.roleService = roleService;\n      this.attachmentService = attachmentService;\n      this.translate = translate;\n      this.ProgramService = ProgramService;\n      this.languageService = languageService;\n      this.dateFormatterService = dateFormatterService;\n      this.chatService = chatService;\n      this.authService = authService;\n      this.dialog = dialog;\n      this.router = router;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.chatService.getParticipantById(this.currentUser?.id || '');\n      this.setCurrentLang();\n      this.getCountryIsoCode();\n      this.buildForm();\n      this.getPrograms();\n      this.getLookupByKey();\n      this.setHijri();\n      this.setGreg();\n    }\n    setScssImages() {\n      this.imagesPathesService.setBackgroundPannerInStyle();\n    }\n    unsavedDataCheck() {\n      return this.profileForm.value.firstAr != this.teacherProfileDetails?.fnameAr || this.profileForm.value.firstNameEn != this.teacherProfileDetails?.faNameEn || this.profileForm.value.middleAr != this.teacherProfileDetails?.mnameAr || this.profileForm.value.middleNameEn != this.teacherProfileDetails?.mnameEn || this.profileForm.value.familyAr != this.teacherProfileDetails?.fanameAr || this.profileForm.value.familyNameEn != this.teacherProfileDetails?.faNameEn || this.profileForm.value.nationality != this.teacherProfileDetails?.nationality\n      // || this.profileForm.value.hijriBirthDate != this.teacherProfileDetails?.hijriBirthDate\n      || this.profileForm.value.gender != this.teacherProfileDetails?.gender || this.profileForm.value.mobile != this.teacherProfileDetails?.mobile || this.profileForm.value.country != this.teacherProfileDetails?.country || this.profileForm.value.city != this.teacherProfileDetails?.city || this.profileForm.value.nationality != this.teacherProfileDetails?.nationality || this.profileForm.value.edulevel != this.teacherProfileDetails?.eduLevel || this.profileForm.value.qualifi != this.teacherProfileDetails?.qualifi || this.profileForm.value.specia != this.teacherProfileDetails?.specia\n      // || this.profileForm.value.eduDate != this.teacherProfileDetails?.eduDate\n      || this.profileForm.value.eduNum != this.teacherProfileDetails?.eduNum || this.profileForm.value.isHasQuranExp != this.teacherProfileDetails?.isHasQuranExp?.toString() || this.profileForm.value.isHasTeachSunnaExp != this.teacherProfileDetails?.isHasTeachSunnaExp?.toString() || this.profileForm.value.isHasInternetTeachExp != this.teacherProfileDetails?.isHasInternetTeachExp?.toString() || this.profileForm.value.isHasTeachForeignerExp != this.teacherProfileDetails?.isHasTeachForeignerExp?.toString() || this.profileForm.value.isHasEjazaHafz != this.teacherProfileDetails?.isHasEjazaHafz?.toString() || this.profileForm.value.workingPlatForm != this.teacherProfileDetails?.workingPlatForm || this.profileForm.value.isHasEjazaTelawa != this.teacherProfileDetails?.isHasEjazaTelawa?.toString() || this.profileForm.value.bankName != this.teacherProfileDetails?.bankName || this.profileForm.value.agency != this.teacherProfileDetails?.agency || this.profileForm.value.address != this.teacherProfileDetails?.address || this.profileForm.value.bankNumber != this.teacherProfileDetails?.bankNumber;\n      //    || this.profileForm.value.ejazaAttachmentIds!= this.teacherProfileDetails?.ejazaAttachments\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n        this.getLookupByKey();\n        this.buildForm();\n        this.PopulateForm();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('UPDATE_TEACHER_PG.TITLE'));\n    }\n    getPrograms() {\n      this.programFilterByNameFilterRequest = {\n        name: \"\"\n      };\n      this.ProgramService.getAllPrograms(this.programFilterByNameFilterRequest).subscribe(res => {\n        let response = res;\n        if (response.isSuccess) {\n          this.ProgramsList = response.data;\n        } else {\n          this.ProgramsList = [];\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookupProfile).subscribe(res => {\n        this.collectionOfLookup = res.data;\n        if (res.isSuccess) {\n          this.getTeacherProfile(this.currentUser?.id);\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    getCitiesLookupByCountry(id) {\n      let countryId = this.f['country'].value;\n      this.lookupService.getCitiesByCountryId(countryId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.collectionOfLookup.CITY = res.data;\n          this.collectionOfLookup && this.collectionOfLookup.CITY ? this.f.city.setValue(this.collectionOfLookup.CITY[0].id) : '';\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    get f() {\n      return this.profileForm.controls;\n    }\n    buildForm() {\n      if (this.translate.currentLang === LanguageEnum.ar) {\n        this.profileForm = this.fb.group({\n          firstAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          middleAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          familyAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          hijriBirthDate: [null, Validators.required],\n          gender: [null, Validators.required],\n          mobile: [null, Validators.required],\n          nationality: [null, Validators.required],\n          address: [],\n          country: [null, Validators.required],\n          city: [null, Validators.required],\n          email: [null, Validators.required],\n          qualifi: [null, Validators.required],\n          specia: [null, Validators.required],\n          // eduDate: [null, Validators.required],\n          eduNum: [null, [Validators.min(1)]],\n          entity: [null, Validators.required],\n          agency: [null, Validators.required],\n          edulevel: [null, Validators.required],\n          isHasQuranExp: [null, Validators.required],\n          isHasTeachSunnaExp: [null, Validators.required],\n          isHasInternetTeachExp: [null, Validators.required],\n          isHasTeachForeignerExp: [null, Validators.required],\n          isHasEjazaHafz: [null, Validators.required],\n          isHasEjazaTelawa: [null, Validators.required],\n          workingPlatForm: [null, Validators.required],\n          hijriInterviewDay: [null, Validators.required],\n          bankName: [null],\n          bankNumber: [null],\n          ejazaAttachments: [],\n          teacherPrograms: [],\n          teacherProgramDegrees: [],\n          availabilityDays: [],\n          interviewDay: [],\n          interviewTime: [],\n          fromDayTimeinterview: [],\n          toDayTimeinterview: [],\n          rewayats: [],\n          languages: []\n        });\n      } else {\n        this.profileForm = this.fb.group({\n          firstEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          middleEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          familyEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          hijriBirthDate: [null, Validators.required],\n          gender: [null, Validators.required],\n          mobile: [null, Validators.required],\n          nationality: [null, Validators.required],\n          address: [],\n          country: [null, Validators.required],\n          city: [null, Validators.required],\n          email: [null, Validators.required],\n          qualifi: [null, Validators.required],\n          specia: [null, Validators.required],\n          // eduDate: [null, Validators.required],\n          eduNum: [null, [Validators.min(1)]],\n          entity: [null, Validators.required],\n          agency: [null, Validators.required],\n          edulevel: [null, Validators.required],\n          isHasQuranExp: [null, Validators.required],\n          isHasTeachSunnaExp: [null, Validators.required],\n          isHasInternetTeachExp: [null, Validators.required],\n          isHasTeachForeignerExp: [null, Validators.required],\n          isHasEjazaHafz: [null, Validators.required],\n          isHasEjazaTelawa: [null, Validators.required],\n          workingPlatForm: [null, Validators.required],\n          hijriInterviewDay: [null, Validators.required],\n          bankName: [null],\n          bankNumber: [null],\n          ejazaAttachments: [],\n          teacherPrograms: [],\n          teacherProgramDegrees: [],\n          availabilityDays: [],\n          interviewDay: [],\n          interviewTime: [],\n          fromDayTimeinterview: [],\n          toDayTimeinterview: [],\n          rewayats: [],\n          languages: []\n        });\n      }\n    }\n    getTeacherProfile(id) {\n      this.teacherService.viewTeacherProfileDetails(id || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.teacherProfileDetails = res.data;\n          if (!this.teacherProfileDetails?.proPic) {\n            this.teacherProfileDetails.proPic = this.imagesPathesService.profile;\n          }\n          this.PopulateForm();\n          if (this.teacherProfileDetails.country) {\n            this.getCitiesLookupByCountry(this.teacherProfileDetails.country);\n          }\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    isRtlMode() {\n      return this.translate.currentLang == LanguageEnum.ar ? true : false;\n    }\n    PopulateForm() {\n      if (this.translate.currentLang === LanguageEnum.ar) {\n        this.f.firstAr.setValue(this.teacherProfileDetails?.fnameAr);\n        this.f.middleAr.setValue(this.teacherProfileDetails?.mnameAr);\n        this.f.familyAr.setValue(this.teacherProfileDetails?.fanameAr);\n      }\n      if (this.translate.currentLang === LanguageEnum.en) {\n        this.f.firstEn.setValue(this.teacherProfileDetails?.fnameEn);\n        this.f.middleEn.setValue(this.teacherProfileDetails?.mnameEn);\n        this.f.familyEn.setValue(this.teacherProfileDetails?.faNameEn);\n      }\n      if (this.teacherProfileDetails.birthDispMode == 1) {\n        this.updateCalenderType.selectedDateType = DateType.Hijri;\n        this.selectedDateType = DateType.Hijri;\n        let date = new Date(this.teacherProfileDetails?.birthdate || '');\n        this.hijriBirthDateInputParam = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n        this.f.hijriBirthDate.setValue(this.teacherProfileDetails.birthdate);\n      } else if (this.teacherProfileDetails.birthDispMode == 2) {\n        this.updateCalenderType.selectedDateType = DateType.Gregorian;\n        this.selectedDateType = DateType.Gregorian;\n        let date = new Date(this.teacherProfileDetails?.birthGregorian || '');\n        this.hijriBirthDateInputParam = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n        this.f.hijriBirthDate.setValue(this.teacherProfileDetails.birthGregorian);\n      } else {\n        this.updateCalenderType.selectedDateType = DateType.Hijri;\n        this.hijriBirthDateInputParam = {\n          year: NaN,\n          month: NaN,\n          day: NaN\n        };\n      }\n      if (this.teacherProfileDetails.interviewDisplayMode == 1) {\n        this.updateInterviewCalenderType.selectedDateType = DateType.Hijri;\n        this.selectedInterviewDateType = DateType.Hijri;\n        let date = new Date(this.teacherProfileDetails?.interviewHijri || '');\n        this.hijriInterviewDayInputParam = {\n          year: date.getFullYear(),\n          month: date.getMonth() + 1,\n          day: date.getDate()\n        };\n        this.f.hijriInterviewDay.setValue(this.teacherProfileDetails.interviewHijri);\n      } else if (this.teacherProfileDetails.interviewDisplayMode == 2) {\n        this.updateInterviewCalenderType.selectedDateType = DateType.Gregorian;\n        this.selectedInterviewDateType = DateType.Hijri;\n        let date = new Date(this.teacherProfileDetails?.interviewGregorian || '');\n        this.hijriInterviewDayInputParam = {\n          year: date.getFullYear(),\n          month: date.getMonth() + 1,\n          day: date.getDate()\n        };\n        this.f.hijriInterviewDay.setValue(this.teacherProfileDetails.interviewGregorian);\n      } else {\n        this.updateInterviewCalenderType.selectedDateType = DateType.Hijri;\n        this.hijriInterviewDayInputParam = {\n          year: NaN,\n          month: NaN,\n          day: NaN\n        };\n      }\n      this.f.nationality.setValue(this.teacherProfileDetails?.nationality);\n      this.f.country.setValue(this.teacherProfileDetails?.country);\n      this.f.address.setValue(this.teacherProfileDetails?.address);\n      this.f.city.setValue(this.teacherProfileDetails?.city);\n      this.f.email.setValue(this.teacherProfileDetails?.usrEmail);\n      this.f.mobile.setValue(this.teacherProfileDetails?.mobile);\n      this.telInputParam.phoneNumber = this.teacherProfileDetails?.mobile;\n      this.f.gender.setValue(this.teacherProfileDetails?.gender);\n      this.f.qualifi.setValue(this.teacherProfileDetails?.qualifi);\n      this.f.specia.setValue(this.teacherProfileDetails?.specia);\n      //this.f.eduDate.setValue(this.teacherProfileDetails?.eduDate)\n      this.f.edulevel.setValue(this.teacherProfileDetails?.eduLevel);\n      this.f.entity.setValue(this.teacherProfileDetails?.entity);\n      this.f.agency.setValue(this.teacherProfileDetails?.agency);\n      this.f.eduNum.setValue(this.teacherProfileDetails?.eduNum);\n      this.f.isHasQuranExp.setValue(this.teacherProfileDetails.isHasQuranExp?.toString());\n      this.f.isHasTeachSunnaExp.setValue(this.teacherProfileDetails.isHasTeachSunnaExp?.toString());\n      this.f.isHasInternetTeachExp.setValue(this.teacherProfileDetails.isHasInternetTeachExp?.toString());\n      this.f.isHasTeachForeignerExp.setValue(this.teacherProfileDetails.isHasTeachForeignerExp?.toString());\n      this.f.isHasEjazaHafz.setValue(this.teacherProfileDetails.isHasEjazaHafz?.toString());\n      this.f.isHasEjazaTelawa.setValue(this.teacherProfileDetails.isHasEjazaTelawa?.toString());\n      this.f.workingPlatForm.setValue(this.teacherProfileDetails?.workingPlatForm);\n      this.f.bankName.setValue(this.teacherProfileDetails?.bankName);\n      this.f.bankNumber.setValue(this.teacherProfileDetails?.bankNumber);\n      // let dateInterviewDay = new Date(this.teacherProfileDetails?.interviewHijri || '');\n      // this.hijriInterviewDayInputParam = { year: dateInterviewDay.getFullYear(), month: dateInterviewDay.getMonth() + 1, day: dateInterviewDay.getDay() }\n      // this.f.hijriInterviewDay.setValue(dateInterviewDay);\n      this.f.interviewTime.setValue(this.teacherProfileDetails?.interviewTime);\n      this.fileList = this.teacherProfileDetails?.ejazaAttachments;\n      this.teacherProfileDetails?.ejazaAttachments?.forEach(element => {\n        this.ejazaAttachmentIds.push(element.id);\n      });\n      if (this.teacherProfileDetails?.rewayats) {\n        this.selectedRewayatsList = this.teacherProfileDetails?.rewayats;\n      }\n      if (this.teacherProfileDetails?.teacherPrograms) {\n        this.selectedTeacherProgramsList = this.teacherProfileDetails?.teacherPrograms;\n      }\n      if (this.teacherProfileDetails?.availabilityDays) {\n        this.selectedAvailabilityDaysList = this.teacherProfileDetails?.availabilityDays;\n      }\n      if (this.teacherProfileDetails?.languages) {\n        this.selectedLanguagesList = this.teacherProfileDetails?.languages;\n      }\n    }\n    onFileChange(files) {\n      let profImagModel = {\n        usrId: this.currentUser?.id,\n        image: files[0]\n      };\n      this.updateProfilePic(profImagModel);\n    }\n    updateProfilePic(profImagModel) {\n      const formData = new FormData();\n      formData.append('UserProfilePictureModel.UserId', profImagModel.usrId || '');\n      formData.append('UserProfilePictureModel.ProfileImage', profImagModel.image);\n      this.userProfilePicService.updateUserProfilePic(formData).subscribe(res => {\n        if (res.isSuccess) {\n          this.teacherProfileDetails.proPic = res.data;\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onSubmit(value) {\n      this.isSubmit = true;\n      this.resMessage = {};\n      if (this.profileForm.valid) {\n        this.updateTeacherModel = {\n          usrId: this.currentUser?.id,\n          firstAr: this.profileForm.value.firstAr != null ? this.profileForm.value.firstAr : this.teacherProfileDetails.fnameAr,\n          firstEn: this.profileForm.value.firstEn != null ? this.profileForm.value.firstEn : this.teacherProfileDetails.faNameEn,\n          middleAr: this.profileForm.value.middleAr != null ? this.profileForm.value.middleAr : this.teacherProfileDetails.mnameAr,\n          middleEn: this.profileForm.value.middleEn != null ? this.profileForm.value.middleEn : this.teacherProfileDetails.mnameEn,\n          familyAr: this.profileForm.value.familyAr != null ? this.profileForm.value.familyAr : this.teacherProfileDetails.fanameAr,\n          familyEn: this.profileForm.value.familyEn != null ? this.profileForm.value.familyEn : this.teacherProfileDetails.fanameAr,\n          nationality: this.profileForm.value.nationality,\n          birthDate: this.selectedDateType == 1 ? this.profileForm.value.hijriBirthDate : null,\n          birthGregorian: this.selectedDateType == 2 ? this.profileForm.value.hijriBirthDate : null,\n          gender: this.profileForm.value.gender,\n          mobile: this.profileForm.value.mobile,\n          country: this.profileForm.value.country,\n          city: this.profileForm.value.city,\n          edulevel: this.profileForm.value.edulevel,\n          qualifi: this.profileForm.value.qualifi,\n          specia: this.profileForm.value.specia,\n          workingPlatForm: this.profileForm.value.workingPlatForm,\n          entity: this.profileForm.value.entity,\n          //eduDate: this.profileForm.value.eduDate,  commented because client Requests to be education with years only\n          eduNum: this.profileForm.value.eduNum,\n          isHasQuranExp: this.profileForm.value.isHasQuranExp,\n          isHasTeachSunnaExp: this.profileForm.value.isHasTeachSunnaExp,\n          isHasInternetTeachExp: this.profileForm.value.isHasInternetTeachExp,\n          isHasTeachForeignerExp: this.profileForm.value.isHasTeachForeignerExp,\n          isHasEjazaHafz: this.profileForm.value.isHasEjazaHafz,\n          isHasEjazaTelawa: this.profileForm.value.isHasEjazaTelawa,\n          agency: this.profileForm.value.agency,\n          bankName: this.profileForm.value.bankName,\n          bankNumber: this.profileForm.value.bankNumber,\n          // interviewHijri: this.profileForm.value.hijriInterviewDay,\n          interviewHijri: this.selectedInterviewDateType == 1 ? this.profileForm.value.hijriInterviewDay : null,\n          interviewGregorian: this.selectedInterviewDateType == 2 ? this.profileForm.value.hijriInterviewDay : null,\n          interviewTime: this.profileForm.value.interviewTime,\n          address: this.profileForm.value.address,\n          ejazaAttachments: this.ejazaAttachmentIds,\n          birthDispMode: this.selectedDateType,\n          interviewDisplayMode: this.selectedInterviewDateType\n        };\n        this.rewayatsMessage = {};\n        this.updateTeacherModel.rewayats = [];\n        if (this.selectedRewayatsList.length) {\n          Array.from(this.selectedRewayatsList).forEach(elm => {\n            if (this.updateTeacherModel.rewayats) {\n              this.updateTeacherModel.rewayats.push({\n                rewayat: elm.id\n              });\n            }\n          });\n        }\n        this.languagesMessage = {};\n        this.updateTeacherModel.languages = [];\n        if (this.selectedLanguagesList.length) {\n          Array.from(this.selectedLanguagesList).forEach(elm => {\n            if (this.updateTeacherModel.languages) {\n              this.updateTeacherModel.languages.push({\n                language: elm.id\n              });\n            }\n          });\n        }\n        this.availabilityDaysMessage = {};\n        this.updateTeacherModel.availabilityDays = [];\n        if (this.selectedAvailabilityDaysList.length > 0) {\n          Array.from(this.selectedAvailabilityDaysList).forEach(elm => {\n            if (this.updateTeacherModel.availabilityDays) {\n              this.updateTeacherModel.availabilityDays.push({\n                availableDay: elm.id,\n                fromTime: elm.fromTime,\n                toTime: elm.toTime\n              });\n            }\n          });\n        } else {\n          this.resMessage = {\n            message: this.translate.instant('UPDATE_TEACHER_PG.AVAILABLE_TIMES_ERROR'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        this.teacherProgramsMessage = {};\n        this.updateTeacherModel.teacherPrograms = [];\n        if (this.selectedTeacherProgramsList.length) {\n          Array.from(this.selectedTeacherProgramsList).forEach(elm => {\n            if (this.updateTeacherModel.teacherPrograms) {\n              this.updateTeacherModel.teacherPrograms.push({\n                program: elm.programId,\n                degree: elm.degreeId\n              });\n            }\n          });\n        }\n        this.participantModel = {\n          id: this.updateTeacherModel.usrId,\n          name_ar: this.updateTeacherModel.firstAr + \" \" + this.updateTeacherModel.middleAr + \" \" + this.updateTeacherModel.familyAr,\n          name_en: this.updateTeacherModel.firstEn === null ? '' : this.updateTeacherModel.firstEn + \" \" + this.updateTeacherModel.middleEn === null ? '' : this.updateTeacherModel.middleEn + \" \" + this.updateTeacherModel.familyEn === null ? '' : this.updateTeacherModel.familyEn,\n          key: this.updateTeacherModel.usrId,\n          hoffazId: \"1\",\n          avatar_url: this.teacherProfileDetails.proPic,\n          role: RoleEnum.Teacher,\n          gender: this.updateTeacherModel.gender\n        };\n        if (this.chatService.participantData && this.teacherProfileDetails) {\n          this.chatService.participantData.id = this.updateTeacherModel.usrId;\n          this.chatService.participantData.name_ar = (this.updateTeacherModel.firstAr === null ? this.teacherProfileDetails.fnameAr : this.updateTeacherModel.firstAr) + \" \" + (this.updateTeacherModel.middleAr === null ? this.teacherProfileDetails.mnameAr : this.updateTeacherModel.middleAr) + \" \" + (this.updateTeacherModel.familyAr === null ? this.teacherProfileDetails.fanameAr : this.updateTeacherModel.familyAr);\n          this.chatService.participantData.name_en = (this.updateTeacherModel.firstEn === null ? this.teacherProfileDetails.fnameEn : this.updateTeacherModel.firstEn) + \" \" + (this.updateTeacherModel.middleEn === null ? this.teacherProfileDetails.mnameEn : this.updateTeacherModel.middleEn) + \" \" + (this.updateTeacherModel.familyEn === null ? this.teacherProfileDetails.faNameEn : this.updateTeacherModel.familyEn);\n          this.chatService.participantData.key = this.updateTeacherModel.usrId;\n          this.chatService.participantData.hoffazId = \"1\";\n          this.chatService.participantData.avatar_url = this.teacherProfileDetails.proPic;\n          this.chatService.participantData.role = RoleEnum.Teacher;\n          this.chatService.participantData.gender = this.updateTeacherModel.gender;\n          this.chatService.updateParticipant(this.chatService.participantData);\n        } else {\n          this.chatService.addParticipant(this.participantModel);\n        }\n        this.teacherService.updateTeacher(this.updateTeacherModel).subscribe(res => {\n          if (res.isSuccess) {\n            this.isSubmit = false;\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n            this.router.navigateByUrl('teacher/view-teacher-profile-details');\n          } else {\n            this.isSubmit = false;\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    addTeacherRewayats() {\n      if (!this.profileForm.value.rewayats) {\n        this.rewayatsMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_REWAYAT'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.rewayatsMessage = {};\n      var x = this.profileForm.value.rewayats;\n      const exist = this.selectedRewayatsList.some(el => el.id === this.profileForm.value.rewayats);\n      if (!exist) {\n        if (this.collectionOfLookup.REWAYAT) {\n          this.selectedRewayatsList.push(this.collectionOfLookup.REWAYAT.filter(el => el.id == this.profileForm.value.rewayats)[0]);\n        }\n      }\n    }\n    removeItemFromSelectedTeacherRewayats(item) {\n      let index = this.selectedRewayatsList.indexOf(item);\n      this.selectedRewayatsList.splice(index, 1);\n    }\n    addTeacherLanguages() {\n      if (!this.profileForm.value.languages) {\n        this.languagesMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_LANGUAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.languagesMessage = {};\n      const exist = this.selectedLanguagesList.some(el => el.id === this.profileForm.value.languages);\n      if (!exist) {\n        if (this.collectionOfLookup.LANG) {\n          this.selectedLanguagesList.push(this.collectionOfLookup.LANG.filter(el => el.id == this.profileForm.value.languages)[0]);\n        }\n      }\n    }\n    removeItemFromSelectedTeacherLanguages(item) {\n      let index = this.selectedLanguagesList.indexOf(item);\n      this.selectedLanguagesList.splice(index, 1);\n    }\n    addAvailabilityDays() {\n      if (!this.profileForm.value.availabilityDays && !this.profileForm.value.timeinterview) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_AVAILABILITY'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.availabilityDaysMessage = {};\n      const existAvailabilityDays = this.selectedAvailabilityDaysList.some(el => el.id === this.profileForm.value.availabilityDays);\n      const existFromTime = this.profileForm.value.fromDayTimeinterview;\n      const existtoTime = this.profileForm.value.toDayTimeinterview;\n      if (existFromTime != null && existtoTime != null && existtoTime < existFromTime) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.VALIDATION_TIME'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => el.fromTime === existFromTime && el.toTime === existtoTime)) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.TIME_EXIST_BEFORE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n      //overlap = a.start < b.end && b.start < a.end;\n      else if (existAvailabilityDays && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => {\n        if (el.toTime && el.fromTime) {\n          return existFromTime < el.toTime && el.fromTime < existtoTime;\n        }\n        return false;\n      })) {\n        this.availabilityDaysMessage = {\n          message: this.translate.instant('TEACHER_APPOINTMENT.OVERLAP_TIME'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else {\n        if (this.profileForm.value.availabilityDays == \"all\" && this.collectionOfLookup.DAYS) {\n          const verifyAllTimes = this.collectionOfLookup.DAYS.some(obj => {\n            let eAvailabilityDay = this.selectedAvailabilityDaysList.some(el => el.id === obj.id);\n            return eAvailabilityDay && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => el.fromTime === existFromTime && el.toTime === existtoTime) || existFromTime != null && existtoTime != null && existtoTime < existFromTime || eAvailabilityDay && existFromTime != null && existtoTime != null && this.selectedAvailabilityDaysList.some(el => {\n              if (el.toTime && el.fromTime) {\n                return existFromTime < el.toTime && el.fromTime < existtoTime;\n              }\n              return false;\n            });\n          });\n          if (verifyAllTimes) {\n            this.availabilityDaysMessage = {\n              message: this.translate.instant('TEACHER_APPOINTMENT.INVALID_TIME'),\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          } else {\n            if (this.collectionOfLookup.DAYS && this.profileForm.value.availabilityDays == \"all\") {\n              this.collectionOfLookup.DAYS.forEach(obj => {\n                this.availabilityDaysModel = {\n                  id: obj.id,\n                  nameAr: obj.nameAr,\n                  nameEn: obj.nameEn,\n                  fromTime: this.profileForm.value.fromDayTimeinterview,\n                  toTime: this.profileForm.value.toDayTimeinterview\n                };\n                this.selectedAvailabilityDaysList.push(this.availabilityDaysModel);\n              });\n            }\n          }\n        } else {\n          if (this.collectionOfLookup.DAYS) {\n            this.availabilityDaysModel = {\n              id: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].id,\n              nameAr: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].nameAr,\n              nameEn: this.collectionOfLookup.DAYS.filter(el => el.id == this.profileForm.value.availabilityDays)[0].nameEn,\n              fromTime: this.profileForm.value.fromDayTimeinterview,\n              toTime: this.profileForm.value.toDayTimeinterview\n            };\n            this.selectedAvailabilityDaysList.push(this.availabilityDaysModel);\n          }\n        }\n      }\n    }\n    removeItemFromSelectedAvailabilityDays(item) {\n      let index = this.selectedAvailabilityDaysList.indexOf(item);\n      this.selectedAvailabilityDaysList.splice(index, 1);\n    }\n    addTeacherPrograms() {\n      if (!this.profileForm.value.teacherPrograms && !this.profileForm.value.teacherProgramDegrees) {\n        this.teacherProgramsMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_TEACHER_PROGRAM'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.teacherProgramsMessage = {};\n      const existDegree = this.selectedTeacherProgramsList.some(el => el.degreeId === this.profileForm.value.teacherProgramDegrees);\n      const existProgram = this.selectedTeacherProgramsList.some(el => el.programId === this.profileForm.value.teacherPrograms);\n      if (!existDegree && !existProgram) {\n        if (this.collectionOfLookup.DEGREE && this.ProgramsList) {\n          this.teacherProgramModel = {\n            programId: this.ProgramsList.filter(el => el.id == this.profileForm.value.teacherPrograms)[0].id,\n            degreeId: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].id,\n            progName: this.ProgramsList.filter(el => el.id == this.profileForm.value.teacherPrograms)[0].progName,\n            degreeNameEn: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].nameEn,\n            degreeNameAr: this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.teacherProgramDegrees)[0].nameAr\n          };\n          this.selectedTeacherProgramsList.push(this.teacherProgramModel);\n        }\n      }\n    }\n    removeItemFromSelectedTeacherPrograms(item) {\n      let index = this.selectedTeacherProgramsList.indexOf(item);\n      this.selectedTeacherProgramsList.splice(index, 1);\n    }\n    applyPhoneNumber(phoneNumber) {\n      this.f.mobile.setValue(phoneNumber);\n    }\n    getCountryIsoCode() {\n      this.userService.getCountryIsoCode().subscribe(res => {\n        let code = res.data;\n        this.telInputParam = {\n          // phoneNumber:'+201062100486',\n          isRequired: true,\n          countryIsoCode: '{\"initialCountry\": \"' + code.toLowerCase() + '\"}'\n        };\n        // this.telInputParam.countryIsoCode = '{\"initialCountry\": \"' + code.toLowerCase() +'\"}';\n      });\n    }\n    SendData(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.hijriBinding = data.selectedDateValue;\n      this.selectedDateType = data.selectedDateType;\n      this.f.hijriBirthDate.setValue(data.selectedDateValue);\n    }\n    HijriInterviewDay(data) {\n      // date = date.year + '/' + date.month + '/' + date.day;\n      // this.hijriBinding = date\n      // this.f.hijriInterviewDay.setValue(date);\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.hijriBinding = data.selectedDateValue;\n      this.selectedInterviewDateType = data.selectedDateType;\n      this.f.hijriInterviewDay.setValue(data.selectedDateValue);\n    }\n    addDrgree() {\n      if (!this.profileForm.value.proficiencyDegree) {\n        this.degreeMessage = {\n          message: this.translate.instant('UPDATE_TEACHER_PG.CHOOSE_DEGREE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else {\n        this.degreeMessage = {};\n        const exist = this.selecteddrgreeList.some(el => el.id === this.profileForm.value.proficiencyDegree);\n        if (!exist) {\n          if (this.collectionOfLookup.DEGREE) {\n            this.selecteddrgreeList.push(this.collectionOfLookup.DEGREE.filter(el => el.id == this.profileForm.value.proficiencyDegree)[0]);\n          }\n        }\n      }\n    }\n    removeItemFromselecteddrgreeList(item) {\n      let index = this.selecteddrgreeList.indexOf(item);\n      this.selecteddrgreeList.splice(index, 1);\n    }\n    DeleteAttachment(index, id) {\n      this.fileList?.splice(index, 1);\n      this.ejazaAttachmentIds = this.ejazaAttachmentIds.filter(a => a !== id);\n    }\n    listExt = [\"jpg\", \"png\", \"jpeg\", \"gif\", \"bmp\", \"tif\", \"tiff\", \"docx\", \"doc\"];\n    onEjazaFileChange(files) {\n      if (files.length > 0) {\n        if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {\n          this.resMessage = {\n            message: this.translate.instant('GENERAL.EXTENTION_FILE'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n          return;\n        }\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 4,\n            // need to be changed based on file type\n            file: element\n          };\n          this.fileUploadModel?.push(fileUploadObj);\n        });\n        this.UploadFiles(this.fileUploadModel);\n      }\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        Array.from(res.data).forEach(elm => {\n          this.ejazaAttachmentIds.push(elm.id);\n          this.fileList?.push(elm);\n        });\n        this.fileUploadModel = [];\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    // From drag and drop\n    onDropSuccess(event) {\n      event.preventDefault();\n      this.onFileChange(event.dataTransfer.files);\n    }\n    // From attachment link\n    onChange(event) {\n      this.onFileChange(event.target.files);\n    }\n    setHijri() {\n      let toDayHijriDate = this.dateFormatterService.GetTodayHijri();\n      //  toDayHijriDate.day= toDayHijriDate.day;\n      toDayHijriDate.day = toDayHijriDate.day - 1;\n      this.maxHijriDate = toDayHijriDate;\n      this.minHijriDate = this.dateFormatterService.GetTodayHijri();\n      this.minHijriDate.year = this.minHijriDate.year - 150;\n      let toDayHijriInterviewDate = this.dateFormatterService.GetTodayHijri();\n      //  toDayHijriInterviewDate.day= toDayHijriInterviewDate.day + 1 ;\n      //  toDayHijriInterviewDate.month= toDayHijriInterviewDate.month + 1 ;\n      //  toDayHijriInterviewDate.year= toDayHijriInterviewDate.year;\n      this.minHijriInterviewDate = toDayHijriInterviewDate;\n    }\n    setGreg() {\n      let toDayGreDate = this.dateFormatterService.GetTodayGregorian();\n      // toDayGreDate.day= toDayGreDate.day;\n      toDayGreDate.day = toDayGreDate.day - 1;\n      this.maxGregDate = toDayGreDate;\n      this.minGregDate = this.dateFormatterService.GetTodayGregorian();\n      this.minGregDate.year = this.maxGregDate.year - 150;\n      let toDayGreInterviewDate = this.dateFormatterService.GetTodayGregorian();\n      // toDayGreInterviewDate.day= toDayGreInterviewDate.day + 1 ;\n      // toDayGreInterviewDate.month= toDayGreInterviewDate.month + 1 ;\n      // toDayGreInterviewDate.year= toDayGreInterviewDate.year  ;\n      this.minGregInterviewDate = toDayGreInterviewDate;\n    }\n    static ɵfac = function UpdateTeacherProfileComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UpdateTeacherProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LookupService), i0.ɵɵdirectiveInject(i3.TeacherProfileService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.RoleManagementService), i0.ɵɵdirectiveInject(i6.AttachmentsService), i0.ɵɵdirectiveInject(i7.TranslateService), i0.ɵɵdirectiveInject(i8.ProgramService), i0.ɵɵdirectiveInject(i9.LanguageService), i0.ɵɵdirectiveInject(i10.DateFormatterService), i0.ɵɵdirectiveInject(i11.ChatService), i0.ɵɵdirectiveInject(i12.AuthService), i0.ɵɵdirectiveInject(i13.MatDialog), i0.ɵɵdirectiveInject(i14.Router), i0.ɵɵdirectiveInject(i15.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateTeacherProfileComponent,\n      selectors: [[\"app-update-teacher-profile\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"fileInput\", \"\"], [\"class\", \"container-fluid UpdateUser\", 4, \"ngIf\"], [1, \"container-fluid\", \"UpdateUser\"], [1, \"row\"], [1, \"box_container\"], [1, \"form-group\", 3, \"ngSubmit\", \"formGroup\"], [1, \"col-12\", \"profile\", \"p-0\", \"mb-50\", 3, \"dragover\", \"drop\"], [1, \"profile_background\"], [1, \"img_profile\", 3, \"src\"], [\"type\", \"file\", 2, \"display\", \"none\", 3, \"change\"], [\"id\", \"upload_link\", 1, \"custom_file_input\", 2, \"text-decoration\", \"none\", 3, \"click\"], [3, \"src\"], [1, \"internal_scroll\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-6\", \"col-sm-12\", \"col-xs-12\", \"px-0\"], [\"class\", \"UpdateUser__Label mb-0\", 4, \"ngIf\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-6\", \"col-sm-12\", \"col-xs-12\", \"mb-3\", \"pl-0\", \"pr-0\", \"border_bottom\"], [1, \"UpdateUser__Label\", \"mt-5\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mb-4\", \"pl-0\", \"pr-0\"], [1, \"row\", \"pl-0\", \"pr-0\"], [\"class\", \"col-xl-2 col-lg-2 col-md-2 col-sm-2 col-xs-2\", 4, \"ngIf\"], [1, \"col-xl-4\", \"col-lg-6\", \"col-md-6\", \"col-sm-12\", \"col-xs-12\", \"updateTeacher_hijriBirthDate\"], [1, \"hijriBirthDate_contanier\"], [\"for\", \"hijriBirthDate\", 1, \"Register__Label\"], [3, \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"minGreg\", \"minHijri\", \"editcalenderType\", \"sendDate\", \"keypress\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [1, \"pl-3\", \"pr-3\", \"gender_contanier\", \"col-xl-3\", \"col-lg-4\", \"col-md-3\", \"col-sm-3\", \"col-xs-3\"], [\"for\", \"gender\", 1, \"Register__Label\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mt-3\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"gender\", 1, \"example-radio-group\", 3, \"disabled\"], [\"class\", \"example-radio-button ml-3\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\", \"mt-4\", \"mb-3\", \"pl-0\", \"pr-0\", \"mt-3\", \"mb-4\", \"border_bottom\"], [1, \"UpdateUser__Label\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"pl-0\", \"pr-0\"], [1, \"col-xl-3\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\"], [\"for\", \"phoneNumber\", 1, \"Register__Label\"], [3, \"getPhonNumber\", \"telInputParam\"], [\"for\", \"email\", 1, \"Register__Label\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"readonly\", \"\", \"name\", \"email\", 1, \"form-control\", \"UserRegister__FormControl\", 2, \"width\", \"100%\"], [\"for\", \"nationality\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"nationality\", 1, \"UpdateUser__Options\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"country\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"country\", 1, \"UpdateUser__Options\", 3, \"change\"], [\"for\", \"city\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"city\", 1, \"UpdateUser__Options\"], [1, \"col-xl-3\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\", \"align-self-center\"], [1, \"info_contact\", \"mb-0\"], [1, \"col-xl-4\", \"col-lg-6\", \"col-md-6\", \"col-sm-2\", \"col-xs-2\", \"mt-3\"], [\"for\", \"hijriInterviewDay\", 1, \"Register__Label\"], [3, \"ngClass\"], [3, \"dateTo\", \"hijri\", \"milady\", \"minHijri\", \"minGreg\", \"editcalenderType\", \"sendDate\", \"keypress\", 4, \"ngIf\"], [1, \"col-xl-2\", \"col-lg-6\", \"col-md-6\", \"col-sm-2\", \"col-xs-2\", \"mt-3\", \"align-self-center\"], [\"for\", \"interviewTime\", 1, \"Register__Label\"], [1, \"UpdateUser__Options\", \"form-group\", \"mb-0\", 3, \"ngClass\"], [\"matInput\", \"\", \"type\", \"time\", \"placeholder\", \"HH:MM\", \"id\", \"end_time_hour\", \"formControlName\", \"interviewTime\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-6\", \"col-sm-4\", \"col-xs-4\"], [\"for\", \"address\", 1, \"Register__Label\"], [\"id\", \"address\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"address\", \"name\", \"address\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-6\"], [\"for\", \"educationalLevel\", 1, \"Register__Label\"], [\"for\", \"importFile\", 1, \"custom-file\", \"custom-file__text\"], [1, \"col-xl-9\", \"col-lg-9\", \"col-md-9\", \"col-sm-9\", \"col-xs-9\"], [\"type\", \"text\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-2\", \"col-sm-2\", \"col-xs-2\", \"UpdateUserIcon\"], [1, \"upload-icon\", 3, \"src\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-3\", \"col-xs-3\", \"m-2\", \"UpdateUserIcon\"], [\"type\", \"file\", \"id\", \"importFile\", \"multiple\", \"\", \"hidden\", \"\", 1, \"custom-file-input\", 3, \"change\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-8\", \"col-sm-12\", \"col-xs-12\"], [1, \"badges\", \"mt-3\"], [\"class\", \"badge badge-info p-2 \", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"col-xs-12\"], [\"for\", \"edulevel\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"edulevel\", 1, \"UpdateUser__Options\"], [\"for\", \"qualifi\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"qualifi\", 1, \"UpdateUser__Options\"], [\"for\", \"specia\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"specia\", 1, \"UpdateUser__Options\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-5\", \"col-sm-12\", \"col-xs-12\"], [\"for\", \"learnQuran\", 1, \"Register__Label\"], [\"formControlName\", \"agency\", \"aria-label\", \"Select an option\", 1, \"example-radio-group\"], [1, \"col-20\"], [\"for\", \"entity\", 1, \"Register__Label\"], [\"id\", \"entity\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"entity\", \"name\", \"entity\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-2\", \"col-sm-2\", \"col-xs-2\"], [\"for\", \"eduNum\", 1, \"Register__Label\"], [\"id\", \"eduNum\", \"pInputText\", \"\", \"type\", \"number\", \"formControlName\", \"eduNum\", \"name\", \"eduNum\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-lx-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [\"for\", \"isHasQuranExp\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasQuranExp\", 1, \"example-radio-group\", \"update_teacher_pg\"], [\"value\", \"true\", 1, \"example-radio-button\"], [\"value\", \"false\", 1, \"example-radio-button\"], [\"for\", \" isHasTeachSunnaExp \", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasTeachSunnaExp\", 1, \"example-radio-group\", \"update_teacher_pg\"], [\"for\", \"isHasInternetTeachExp\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasInternetTeachExp\", 1, \"example-radio-group\", \"update_teacher_pg\"], [\"for\", \"isHasTeachForeignerExp\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasTeachForeignerExp\", 1, \"example-radio-group\", \"update_teacher_pg\"], [\"for\", \"isHasEjazaHafz\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasEjazaHafz\", 1, \"example-radio-group\", \"update_teacher_pg\"], [\"for\", \"isHasEjazaTelawa\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"isHasEjazaTelawa\", 1, \"example-radio-group\", \"update_teacher_pg\"], [1, \"col-20\", \"col-md-2\", \"col-sm-2\", \"col-xs-2\"], [\"for\", \"rewayats\", 1, \"Register__Label\"], [1, \"input-group\"], [1, \"input-group\", \"pr-2rem\"], [\"id\", \"rewayats\", \"formControlName\", \"rewayats\", 1, \"form-control\"], [1, \"plus-btn\", \"position-absolute\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-add\", 3, \"click\"], [1, \"cursor-pointer\", \"Catogry_Icon_Add\", 3, \"src\"], [\"class\", \"badge badge-info p-2\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"languages\", 1, \"Register__Label\"], [\"id\", \"languages\", \"formControlName\", \"languages\", 1, \"form-control\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\", \"mt-4\", \"mb-3\", \"mt-3\", \"mb-4\", \"border_bottom\"], [1, \"col-xl-3\", \"col-lg-4\", \"col-md-3\", \"col-sm-12\", \"col-xs-12\"], [\"for\", \"workPlatform\", 1, \"Register__Label\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"workingPlatForm\", 1, \"example-radio-group\"], [\"for\", \"bankName\", 1, \"Register__Label\"], [\"id\", \"bankName\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bankName\", \"name\", \"bankName\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"bankNumber\", 1, \"Register__Label\"], [\"id\", \"bankNumber\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bankNumber\", \"name\", \"bankNumber\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"mt-3\"], [\"for\", \"availabilityDays\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"availabilityDays\", 1, \"UpdateUser__Options\", 3, \"ngClass\"], [\"value\", \"all\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-3\", \"col-xs-12\", \"mt-3\"], [\"for\", \"fromDayTimeinterview\", 1, \"Register__Label\"], [\"matInput\", \"\", \"type\", \"time\", \"placeholder\", \"HH:MM\", \"id\", \"end_time_hour\", \"formControlName\", \"fromDayTimeinterview\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"toDayTimeinterview\", 1, \"Register__Label\"], [\"matInput\", \"\", \"type\", \"time\", \"placeholder\", \"HH:MM\", \"id\", \"end_time_hour\", \"formControlName\", \"toDayTimeinterview\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-2\", \"col-sm-2\", \"col-xs-2\", \"align-self-end\"], [1, \"Register__Label\", \"invisible\"], [\"type\", \"button\", 1, \"saveButton\", \"btn\", 3, \"click\", \"ngClass\"], [3, \"class\", 4, \"ngIf\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", 3, \"ngClass\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\", \"mb-0\", \"mt-4\"], [\"type\", \"submit\", 1, \"btn\", \"UserLogin__Submit\"], [1, \"UpdateUser__Label\", \"mb-0\"], [\"for\", \"firstAr\", 1, \"Register__Label\"], [\"id\", \"firstAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"firstAr\", \"name\", \"firstAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"], [\"for\", \"middleAr\", 1, \"Register__Label\"], [\"id\", \"middleAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"middleAr\", \"name\", \"middleAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"familyAr\", 1, \"Register__Label\"], [\"id\", \"familyAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"familyAr\", \"name\", \"familyAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"firstEn\", 1, \"Register__Label\"], [\"id\", \"firstEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"firstEn\", \"name\", \"firstEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"middleEn\", 1, \"Register__Label\"], [\"id\", \"middleEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"middleEn\", \"name\", \"middleEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"familyEn\", 1, \"Register__Label\"], [\"id\", \"familyEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"familyEn\", \"name\", \"familyEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [3, \"sendDate\", \"keypress\", \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"minGreg\", \"minHijri\", \"editcalenderType\"], [1, \"example-radio-button\", \"ml-3\", 3, \"value\"], [3, \"value\"], [3, \"sendDate\", \"keypress\", \"dateTo\", \"hijri\", \"milady\", \"minHijri\", \"minGreg\", \"editcalenderType\"], [1, \"badge\", \"badge-info\", \"p-2\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"ellipsis\", \"mb-0\", 3, \"matTooltip\"], [3, \"click\"], [1, \"fas\", \"fa-times-circle\"]],\n      template: function UpdateTeacherProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, UpdateTeacherProfileComponent_div_0_Template, 382, 298, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.telInputParam == null ? null : ctx.telInputParam.countryIsoCode);\n        }\n      },\n      dependencies: [i16.NgClass, i16.NgForOf, i16.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i17.MatTooltip, i18.MatRadioGroup, i18.MatRadioButton, i7.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.UpdateUser__Label[_ngcontent-%COMP%]{color:var(--main_color);border-width:70%;border-bottom:aliceblue;font-weight:700;font-size:1.25rem}.UpdateUser__Options[_ngcontent-%COMP%]{width:100%;height:2.1875rem;border-radius:.65rem;border-color:#b3b3b3}.UpdateUser[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.UpdateUser[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{white-space:normal!important}.UpdateUser[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]{width:2rem}.UpdateUser[_ngcontent-%COMP%]   .box_container[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.1875rem;padding:1.625rem 6rem;height:85vh;margin:auto auto 0;margin-top:1rem}.UpdateUser[_ngcontent-%COMP%]   .box_container[_ngcontent-%COMP%]:lang(ar){text-align:right}.UpdateUser[_ngcontent-%COMP%]   .PlusIcon[_ngcontent-%COMP%]{margin-top:.5rem;margin-left:.5rem}.UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{color:#fff;background-color:#fff}.UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .UpdateUser[_ngcontent-%COMP%]   .show[_ngcontent-%COMP%] > .btn-primary.dropdown-toggle[_ngcontent-%COMP%]{color:#fff;background-color:#fff}.UpdateUser[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:400;text-align:center;vertical-align:middle;-webkit-user-select:none;user-select:none;border:.063rem solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem}.UpdateUser[_ngcontent-%COMP%]   .profile_background[_ngcontent-%COMP%]{background:var(--background_panner);background-size:cover;height:11.9375rem;width:100%;border-radius:1.875rem}.UpdateUser[_ngcontent-%COMP%]   .img_profile[_ngcontent-%COMP%]{position:absolute;top:0;left:40%!important;top:20%;width:13.313rem;height:13.313rem;border-radius:1rem;z-index:1}.UpdateUser[_ngcontent-%COMP%]   .mb-50[_ngcontent-%COMP%]{margin-bottom:3.125rem}.UpdateUser[_ngcontent-%COMP%]   .internal_scroll[_ngcontent-%COMP%]{height:47vh;overflow-y:auto;overflow-x:hidden;padding-left:.5rem;padding-right:.5rem}.UpdateUser[_ngcontent-%COMP%]   .border_bottom[_ngcontent-%COMP%]{border-bottom:.063rem solid #808080}.UpdateUser[_ngcontent-%COMP%]   .custom_file_input[_ngcontent-%COMP%]:lang(ar){left:53%!important;position:absolute;bottom:-2.875rem;cursor:pointer;z-index:1}.UpdateUser[_ngcontent-%COMP%]   .custom_file_input[_ngcontent-%COMP%]:lang(en){left:41%!important;position:absolute;bottom:-2.875rem;cursor:pointer;z-index:1}@media all and (min-width: 19in){.box_container[_ngcontent-%COMP%]{height:90vh;width:100%}.internal_scroll[_ngcontent-%COMP%]{height:60vh}.custom_file_input[_ngcontent-%COMP%]:lang(ar){left:51%!important}.profile_background[_ngcontent-%COMP%]{height:13.9375rem}}@media (max-width: 64rem){.custom_file_input[_ngcontent-%COMP%]:lang(ar){left:58%!important;bottom:-1.25rem}.custom_file_input[_ngcontent-%COMP%]:lang(en){right:58%!important;bottom:-1.25rem}.box_container[_ngcontent-%COMP%]{padding:1.625rem 5rem}.alert-danger[_ngcontent-%COMP%]{line-height:1rem}.UpdateUser__Options[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 48rem){.img_profile[_ngcontent-%COMP%]{top:50%}.internal_scroll[_ngcontent-%COMP%]{height:40rem}}[_ngcontent-%COMP%]:root{--background_panner: \\\"\\\"}.UpdateUser[_ngcontent-%COMP%]   .profile_background[_ngcontent-%COMP%]{background:var(--background_panner);height:27vh;background-size:cover;width:100%;border-radius:1.875rem}.UpdateUser[_ngcontent-%COMP%]   .dimmed-btn[_ngcontent-%COMP%]{opacity:.5!important}.UpdateUser[_ngcontent-%COMP%]   .box_container[_ngcontent-%COMP%]{width:99%;height:85vh}.UpdateUser[_ngcontent-%COMP%]   .internal_scroll[_ngcontent-%COMP%]{height:47vh}.UpdateUser[_ngcontent-%COMP%]   .col-20[_ngcontent-%COMP%]{flex:0 0 20%;max-width:20%;position:relative;width:100%;padding-right:1rem;padding-left:1rem}.UpdateUser[_ngcontent-%COMP%]   .saveButton[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:7.5rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.UpdateUser[_ngcontent-%COMP%]   .info_contact[_ngcontent-%COMP%]{color:#666;font-size:.875rem;margin-top:1rem}.UpdateUser[_ngcontent-%COMP%]   .disableDiv[_ngcontent-%COMP%]{pointer-events:none}@media all and (min-width: 19in){.internal_scroll[_ngcontent-%COMP%]{height:57vh!important}.profile_background[_ngcontent-%COMP%]{height:23vh!important;background-size:cover!important}.box_container[_ngcontent-%COMP%]{width:99%;height:90vh!important}}@media (max-width: 64rem){.UpdateUser[_ngcontent-%COMP%]   .col-20[_ngcontent-%COMP%]{flex:0 0 33.33%;max-width:33.33%;position:relative;width:100%;padding-right:1rem;padding-left:1rem}}@media (max-width: 48rem){.internal_scroll[_ngcontent-%COMP%]{height:50vh!important;max-height:25.9375rem}}\"]\n    });\n  }\n  return UpdateTeacherProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}