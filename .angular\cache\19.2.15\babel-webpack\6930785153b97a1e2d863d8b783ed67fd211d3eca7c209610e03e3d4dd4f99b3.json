{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let AdminStudentStatisticsComponent = /*#__PURE__*/(() => {\n  class AdminStudentStatisticsComponent {\n    constructor() {}\n    ngOnInit() {}\n    static ɵfac = function AdminStudentStatisticsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminStudentStatisticsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminStudentStatisticsComponent,\n      selectors: [[\"app-admin-student-statistics\"]],\n      decls: 8,\n      vars: 0,\n      consts: [[1, \"part_one\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"col-7\"], [1, \"col-5\"], [1, \"col-12\", \"mt-3\"]],\n      template: function AdminStudentStatisticsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-admin-student-statistics-counts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"app-admin-student-statistics-notification-chart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4);\n          i0.ɵɵelement(7, \"app-admin-student-total-exam-task-degree\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".program_result[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:.75rem}.program_result[_ngcontent-%COMP%]:lang(ar){text-align:right}.program_result[_ngcontent-%COMP%]:lang(en){text-align:left}.program_result[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%], .program_result[_ngcontent-%COMP%]   .Register__Label[_ngcontent-%COMP%]{color:#333;font-size:1.0275rem;font-weight:700;margin:.5rem 0}.program_result[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{box-shadow:0 .188rem 1rem #fbfbfb}.program_result[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:.25rem}.part_two[_ngcontent-%COMP%], .part_one[_ngcontent-%COMP%]{height:73vh;overflow-y:auto;overflow-x:hidden}\"]\n    });\n  }\n  return AdminStudentStatisticsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}