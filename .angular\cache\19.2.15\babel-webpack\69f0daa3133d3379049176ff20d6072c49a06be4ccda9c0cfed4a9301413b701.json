{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport Model from '../../model/Model.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport { defaultSeriesFormatTooltip } from '../../component/tooltip/seriesFormatTooltip.js';\nimport { initCurvenessList, createEdgeMapForCurveness } from '../helper/multipleGraphEdgeHelper.js';\nvar GraphSeriesModel = /** @class */function (_super) {\n  __extends(GraphSeriesModel, _super);\n  function GraphSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  GraphSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    var self = this;\n    function getCategoriesData() {\n      return self._categoriesData;\n    }\n    // Provide data for legend select\n    this.legendVisualProvider = new LegendVisualProvider(getCategoriesData, getCategoriesData);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeDefaultAndTheme = function (option) {\n    _super.prototype.mergeDefaultAndTheme.apply(this, arguments);\n    defaultEmphasis(option, 'edgeLabel', ['show']);\n  };\n  GraphSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var edges = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var self = this;\n    if (nodes && edges) {\n      // auto curveness\n      initCurvenessList(this);\n      var graph = createGraphFromNodeEdge(nodes, edges, this, true, beforeLink);\n      zrUtil.each(graph.edges, function (edge) {\n        createEdgeMapForCurveness(edge.node1, edge.node2, this, edge.dataIndex);\n      }, this);\n      return graph.data;\n    }\n    function beforeLink(nodeData, edgeData) {\n      // Overwrite nodeData.getItemModel to\n      nodeData.wrapMethod('getItemModel', function (model) {\n        var categoriesModels = self._categoriesModels;\n        var categoryIdx = model.getShallow('category');\n        var categoryModel = categoriesModels[categoryIdx];\n        if (categoryModel) {\n          categoryModel.parentModel = model.parentModel;\n          model.parentModel = categoryModel;\n        }\n        return model;\n      });\n      // TODO Inherit resolveParentPath by default in Model#getModel?\n      var oldGetModel = Model.prototype.getModel;\n      function newGetModel(path, parentModel) {\n        var model = oldGetModel.call(this, path, parentModel);\n        model.resolveParentPath = resolveParentPath;\n        return model;\n      }\n      edgeData.wrapMethod('getItemModel', function (model) {\n        model.resolveParentPath = resolveParentPath;\n        model.getModel = newGetModel;\n        return model;\n      });\n      function resolveParentPath(pathArr) {\n        if (pathArr && (pathArr[0] === 'label' || pathArr[1] === 'label')) {\n          var newPathArr = pathArr.slice();\n          if (pathArr[0] === 'label') {\n            newPathArr[0] = 'edgeLabel';\n          } else if (pathArr[1] === 'label') {\n            newPathArr[1] = 'edgeLabel';\n          }\n          return newPathArr;\n        }\n        return pathArr;\n      }\n    }\n  };\n  GraphSeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  GraphSeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  GraphSeriesModel.prototype.getCategoriesData = function () {\n    return this._categoriesData;\n  };\n  GraphSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    if (dataType === 'edge') {\n      var nodeData = this.getData();\n      var params = this.getDataParams(dataIndex, dataType);\n      var edge = nodeData.graph.getEdgeByIndex(dataIndex);\n      var sourceName = nodeData.getName(edge.node1.dataIndex);\n      var targetName = nodeData.getName(edge.node2.dataIndex);\n      var nameArr = [];\n      sourceName != null && nameArr.push(sourceName);\n      targetName != null && nameArr.push(targetName);\n      return createTooltipMarkup('nameValue', {\n        name: nameArr.join(' > '),\n        value: params.value,\n        noValue: params.value == null\n      });\n    }\n    // dataType === 'node' or empty\n    var nodeMarkup = defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n    return nodeMarkup;\n  };\n  GraphSeriesModel.prototype._updateCategoriesData = function () {\n    var categories = zrUtil.map(this.option.categories || [], function (category) {\n      // Data must has value\n      return category.value != null ? category : zrUtil.extend({\n        value: 0\n      }, category);\n    });\n    var categoriesData = new SeriesData(['value'], this);\n    categoriesData.initData(categories);\n    this._categoriesData = categoriesData;\n    this._categoriesModels = categoriesData.mapArray(function (idx) {\n      return categoriesData.getItemModel(idx);\n    });\n  };\n  GraphSeriesModel.prototype.setZoom = function (zoom) {\n    this.option.zoom = zoom;\n  };\n  GraphSeriesModel.prototype.setCenter = function (center) {\n    this.option.center = center;\n  };\n  GraphSeriesModel.prototype.isAnimationEnabled = function () {\n    return _super.prototype.isAnimationEnabled.call(this)\n    // Not enable animation when do force layout\n    && !(this.get('layout') === 'force' && this.get(['force', 'layoutAnimation']));\n  };\n  GraphSeriesModel.type = 'series.graph';\n  GraphSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  GraphSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    // Default option for all coordinate systems\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // polarIndex: 0,\n    // geoIndex: 0,\n    legendHoverLink: true,\n    layout: null,\n    // Configuration of circular layout\n    circular: {\n      rotateLabel: false\n    },\n    // Configuration of force directed layout\n    force: {\n      initLayout: null,\n      // Node repulsion. Can be an array to represent range.\n      repulsion: [0, 50],\n      gravity: 0.1,\n      // Initial friction\n      friction: 0.6,\n      // Edge length. Can be an array to represent range.\n      edgeLength: 30,\n      layoutAnimation: true\n    },\n    left: 'center',\n    top: 'center',\n    // right: null,\n    // bottom: null,\n    // width: '80%',\n    // height: '80%',\n    symbol: 'circle',\n    symbolSize: 10,\n    edgeSymbol: ['none', 'none'],\n    edgeSymbolSize: 10,\n    edgeLabel: {\n      position: 'middle',\n      distance: 5\n    },\n    draggable: false,\n    roam: false,\n    // Default on center of graph\n    center: null,\n    zoom: 1,\n    // Symbol size scale ratio in roam\n    nodeScaleRatio: 0.6,\n    // cursor: null,\n    // categories: [],\n    // data: []\n    // Or\n    // nodes: []\n    //\n    // links: []\n    // Or\n    // edges: []\n    label: {\n      show: false,\n      formatter: '{b}'\n    },\n    itemStyle: {},\n    lineStyle: {\n      color: '#aaa',\n      width: 1,\n      opacity: 0.5\n    },\n    emphasis: {\n      scale: true,\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return GraphSeriesModel;\n}(SeriesModel);\nexport default GraphSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}