{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\n// (24*60*60*1000)\nvar PROXIMATE_ONE_DAY = 86400000;\nvar Calendar = /** @class */function () {\n  function Calendar(calendarModel, ecModel, api) {\n    this.type = 'calendar';\n    this.dimensions = Calendar.dimensions;\n    // Required in createListFromData\n    this.getDimensionsInfo = Calendar.getDimensionsInfo;\n    this._model = calendarModel;\n  }\n  Calendar.getDimensionsInfo = function () {\n    return [{\n      name: 'time',\n      type: 'time'\n    }, 'value'];\n  };\n  Calendar.prototype.getRangeInfo = function () {\n    return this._rangeInfo;\n  };\n  Calendar.prototype.getModel = function () {\n    return this._model;\n  };\n  Calendar.prototype.getRect = function () {\n    return this._rect;\n  };\n  Calendar.prototype.getCellWidth = function () {\n    return this._sw;\n  };\n  Calendar.prototype.getCellHeight = function () {\n    return this._sh;\n  };\n  Calendar.prototype.getOrient = function () {\n    return this._orient;\n  };\n  /**\r\n   * getFirstDayOfWeek\r\n   *\r\n   * @example\r\n   *     0 : start at Sunday\r\n   *     1 : start at Monday\r\n   *\r\n   * @return {number}\r\n   */\n  Calendar.prototype.getFirstDayOfWeek = function () {\n    return this._firstDayOfWeek;\n  };\n  /**\r\n   * get date info\r\n   * }\r\n   */\n  Calendar.prototype.getDateInfo = function (date) {\n    date = numberUtil.parseDate(date);\n    var y = date.getFullYear();\n    var m = date.getMonth() + 1;\n    var mStr = m < 10 ? '0' + m : '' + m;\n    var d = date.getDate();\n    var dStr = d < 10 ? '0' + d : '' + d;\n    var day = date.getDay();\n    day = Math.abs((day + 7 - this.getFirstDayOfWeek()) % 7);\n    return {\n      y: y + '',\n      m: mStr,\n      d: dStr,\n      day: day,\n      time: date.getTime(),\n      formatedDate: y + '-' + mStr + '-' + dStr,\n      date: date\n    };\n  };\n  Calendar.prototype.getNextNDay = function (date, n) {\n    n = n || 0;\n    if (n === 0) {\n      return this.getDateInfo(date);\n    }\n    date = new Date(this.getDateInfo(date).time);\n    date.setDate(date.getDate() + n);\n    return this.getDateInfo(date);\n  };\n  Calendar.prototype.update = function (ecModel, api) {\n    this._firstDayOfWeek = +this._model.getModel('dayLabel').get('firstDay');\n    this._orient = this._model.get('orient');\n    this._lineWidth = this._model.getModel('itemStyle').getItemStyle().lineWidth || 0;\n    this._rangeInfo = this._getRangeInfo(this._initRangeOption());\n    var weeks = this._rangeInfo.weeks || 1;\n    var whNames = ['width', 'height'];\n    var cellSize = this._model.getCellSize().slice();\n    var layoutParams = this._model.getBoxLayoutParams();\n    var cellNumbers = this._orient === 'horizontal' ? [weeks, 7] : [7, weeks];\n    zrUtil.each([0, 1], function (idx) {\n      if (cellSizeSpecified(cellSize, idx)) {\n        layoutParams[whNames[idx]] = cellSize[idx] * cellNumbers[idx];\n      }\n    });\n    var whGlobal = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var calendarRect = this._rect = layout.getLayoutRect(layoutParams, whGlobal);\n    zrUtil.each([0, 1], function (idx) {\n      if (!cellSizeSpecified(cellSize, idx)) {\n        cellSize[idx] = calendarRect[whNames[idx]] / cellNumbers[idx];\n      }\n    });\n    function cellSizeSpecified(cellSize, idx) {\n      return cellSize[idx] != null && cellSize[idx] !== 'auto';\n    }\n    // Has been calculated out number.\n    this._sw = cellSize[0];\n    this._sh = cellSize[1];\n  };\n  /**\r\n   * Convert a time data(time, value) item to (x, y) point.\r\n   */\n  // TODO Clamp of calendar is not same with cartesian coordinate systems.\n  // It will return NaN if data exceeds.\n  Calendar.prototype.dataToPoint = function (data, clamp) {\n    zrUtil.isArray(data) && (data = data[0]);\n    clamp == null && (clamp = true);\n    var dayInfo = this.getDateInfo(data);\n    var range = this._rangeInfo;\n    var date = dayInfo.formatedDate;\n    // if not in range return [NaN, NaN]\n    if (clamp && !(dayInfo.time >= range.start.time && dayInfo.time < range.end.time + PROXIMATE_ONE_DAY)) {\n      return [NaN, NaN];\n    }\n    var week = dayInfo.day;\n    var nthWeek = this._getRangeInfo([range.start.time, date]).nthWeek;\n    if (this._orient === 'vertical') {\n      return [this._rect.x + week * this._sw + this._sw / 2, this._rect.y + nthWeek * this._sh + this._sh / 2];\n    }\n    return [this._rect.x + nthWeek * this._sw + this._sw / 2, this._rect.y + week * this._sh + this._sh / 2];\n  };\n  /**\r\n   * Convert a (x, y) point to time data\r\n   */\n  Calendar.prototype.pointToData = function (point) {\n    var date = this.pointToDate(point);\n    return date && date.time;\n  };\n  /**\r\n   * Convert a time date item to (x, y) four point.\r\n   */\n  Calendar.prototype.dataToRect = function (data, clamp) {\n    var point = this.dataToPoint(data, clamp);\n    return {\n      contentShape: {\n        x: point[0] - (this._sw - this._lineWidth) / 2,\n        y: point[1] - (this._sh - this._lineWidth) / 2,\n        width: this._sw - this._lineWidth,\n        height: this._sh - this._lineWidth\n      },\n      center: point,\n      tl: [point[0] - this._sw / 2, point[1] - this._sh / 2],\n      tr: [point[0] + this._sw / 2, point[1] - this._sh / 2],\n      br: [point[0] + this._sw / 2, point[1] + this._sh / 2],\n      bl: [point[0] - this._sw / 2, point[1] + this._sh / 2]\n    };\n  };\n  /**\r\n   * Convert a (x, y) point to time date\r\n   *\r\n   * @param  {Array} point point\r\n   * @return {Object}       date\r\n   */\n  Calendar.prototype.pointToDate = function (point) {\n    var nthX = Math.floor((point[0] - this._rect.x) / this._sw) + 1;\n    var nthY = Math.floor((point[1] - this._rect.y) / this._sh) + 1;\n    var range = this._rangeInfo.range;\n    if (this._orient === 'vertical') {\n      return this._getDateByWeeksAndDay(nthY, nthX - 1, range);\n    }\n    return this._getDateByWeeksAndDay(nthX, nthY - 1, range);\n  };\n  Calendar.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  Calendar.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  Calendar.prototype.containPoint = function (point) {\n    console.warn('Not implemented.');\n    return false;\n  };\n  /**\r\n   * initRange\r\n   * Normalize to an [start, end] array\r\n   */\n  Calendar.prototype._initRangeOption = function () {\n    var range = this._model.get('range');\n    var normalizedRange;\n    // Convert [1990] to 1990\n    if (zrUtil.isArray(range) && range.length === 1) {\n      range = range[0];\n    }\n    if (!zrUtil.isArray(range)) {\n      var rangeStr = range.toString();\n      // One year.\n      if (/^\\d{4}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr + '-01-01', rangeStr + '-12-31'];\n      }\n      // One month\n      if (/^\\d{4}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        var start = this.getDateInfo(rangeStr);\n        var firstDay = start.date;\n        firstDay.setMonth(firstDay.getMonth() + 1);\n        var end = this.getNextNDay(firstDay, -1);\n        normalizedRange = [start.formatedDate, end.formatedDate];\n      }\n      // One day\n      if (/^\\d{4}[\\/|-]\\d{1,2}[\\/|-]\\d{1,2}$/.test(rangeStr)) {\n        normalizedRange = [rangeStr, rangeStr];\n      }\n    } else {\n      normalizedRange = range;\n    }\n    if (!normalizedRange) {\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.logError('Invalid date range.');\n      }\n      // Not handling it.\n      return range;\n    }\n    var tmp = this._getRangeInfo(normalizedRange);\n    if (tmp.start.time > tmp.end.time) {\n      normalizedRange.reverse();\n    }\n    return normalizedRange;\n  };\n  /**\r\n   * range info\r\n   *\r\n   * @private\r\n   * @param  {Array} range range ['2017-01-01', '2017-07-08']\r\n   *  If range[0] > range[1], they will not be reversed.\r\n   * @return {Object}       obj\r\n   */\n  Calendar.prototype._getRangeInfo = function (range) {\n    var parsedRange = [this.getDateInfo(range[0]), this.getDateInfo(range[1])];\n    var reversed;\n    if (parsedRange[0].time > parsedRange[1].time) {\n      reversed = true;\n      parsedRange.reverse();\n    }\n    var allDay = Math.floor(parsedRange[1].time / PROXIMATE_ONE_DAY) - Math.floor(parsedRange[0].time / PROXIMATE_ONE_DAY) + 1;\n    // Consider case1 (#11677 #10430):\n    // Set the system timezone as \"UK\", set the range to `['2016-07-01', '2016-12-31']`\n    // Consider case2:\n    // Firstly set system timezone as \"Time Zone: America/Toronto\",\n    // ```\n    // let first = new Date(1478412000000 - 3600 * 1000 * 2.5);\n    // let second = new Date(1478412000000);\n    // let allDays = Math.floor(second / ONE_DAY) - Math.floor(first / ONE_DAY) + 1;\n    // ```\n    // will get wrong result because of DST. So we should fix it.\n    var date = new Date(parsedRange[0].time);\n    var startDateNum = date.getDate();\n    var endDateNum = parsedRange[1].date.getDate();\n    date.setDate(startDateNum + allDay - 1);\n    // The bias can not over a month, so just compare date.\n    var dateNum = date.getDate();\n    if (dateNum !== endDateNum) {\n      var sign = date.getTime() - parsedRange[1].time > 0 ? 1 : -1;\n      while ((dateNum = date.getDate()) !== endDateNum && (date.getTime() - parsedRange[1].time) * sign > 0) {\n        allDay -= sign;\n        date.setDate(dateNum - sign);\n      }\n    }\n    var weeks = Math.floor((allDay + parsedRange[0].day + 6) / 7);\n    var nthWeek = reversed ? -weeks + 1 : weeks - 1;\n    reversed && parsedRange.reverse();\n    return {\n      range: [parsedRange[0].formatedDate, parsedRange[1].formatedDate],\n      start: parsedRange[0],\n      end: parsedRange[1],\n      allDay: allDay,\n      weeks: weeks,\n      // From 0.\n      nthWeek: nthWeek,\n      fweek: parsedRange[0].day,\n      lweek: parsedRange[1].day\n    };\n  };\n  /**\r\n   * get date by nthWeeks and week day in range\r\n   *\r\n   * @private\r\n   * @param  {number} nthWeek the week\r\n   * @param  {number} day   the week day\r\n   * @param  {Array} range [d1, d2]\r\n   * @return {Object}\r\n   */\n  Calendar.prototype._getDateByWeeksAndDay = function (nthWeek, day, range) {\n    var rangeInfo = this._getRangeInfo(range);\n    if (nthWeek > rangeInfo.weeks || nthWeek === 0 && day < rangeInfo.fweek || nthWeek === rangeInfo.weeks && day > rangeInfo.lweek) {\n      return null;\n    }\n    var nthDay = (nthWeek - 1) * 7 - rangeInfo.fweek + day;\n    var date = new Date(rangeInfo.start.time);\n    date.setDate(+rangeInfo.start.d + nthDay);\n    return this.getDateInfo(date);\n  };\n  Calendar.create = function (ecModel, api) {\n    var calendarList = [];\n    ecModel.eachComponent('calendar', function (calendarModel) {\n      var calendar = new Calendar(calendarModel, ecModel, api);\n      calendarList.push(calendar);\n      calendarModel.coordinateSystem = calendar;\n    });\n    ecModel.eachSeries(function (calendarSeries) {\n      if (calendarSeries.get('coordinateSystem') === 'calendar') {\n        // Inject coordinate system\n        calendarSeries.coordinateSystem = calendarList[calendarSeries.get('calendarIndex') || 0];\n      }\n    });\n    return calendarList;\n  };\n  Calendar.dimensions = ['time', 'value'];\n  return Calendar;\n}();\nfunction getCoordSys(finder) {\n  var calendarModel = finder.calendarModel;\n  var seriesModel = finder.seriesModel;\n  var coordSys = calendarModel ? calendarModel.coordinateSystem : seriesModel ? seriesModel.coordinateSystem : null;\n  return coordSys;\n}\nexport default Calendar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}