{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { map, isString, isFunction, eqNaN, isRegExp } from 'zrender/lib/core/util.js';\nvar ECHARTS_PREFIX = '[ECharts] ';\nvar storedLogs = {};\nvar hasConsole = typeof console !== 'undefined'\n// eslint-disable-next-line\n&& console.warn && console.log;\nfunction outputLog(type, str, onlyOnce) {\n  if (hasConsole) {\n    if (onlyOnce) {\n      if (storedLogs[str]) {\n        return;\n      }\n      storedLogs[str] = true;\n    }\n    // eslint-disable-next-line\n    console[type](ECHARTS_PREFIX + str);\n  }\n}\nexport function log(str, onlyOnce) {\n  outputLog('log', str, onlyOnce);\n}\nexport function warn(str, onlyOnce) {\n  outputLog('warn', str, onlyOnce);\n}\nexport function error(str, onlyOnce) {\n  outputLog('error', str, onlyOnce);\n}\nexport function deprecateLog(str) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Not display duplicate message.\n    outputLog('warn', 'DEPRECATED: ' + str, true);\n  }\n}\nexport function deprecateReplaceLog(oldOpt, newOpt, scope) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog((scope ? \"[\" + scope + \"]\" : '') + (oldOpt + \" is deprecated, use \" + newOpt + \" instead.\"));\n  }\n}\n/**\r\n * If in __DEV__ environment, get console printable message for users hint.\r\n * Parameters are separated by ' '.\r\n * @usage\r\n * makePrintable('This is an error on', someVar, someObj);\r\n *\r\n * @param hintInfo anything about the current execution context to hint users.\r\n * @throws Error\r\n */\nexport function makePrintable() {\n  var hintInfo = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    hintInfo[_i] = arguments[_i];\n  }\n  var msg = '';\n  if (process.env.NODE_ENV !== 'production') {\n    // Fuzzy stringify for print.\n    // This code only exist in dev environment.\n    var makePrintableStringIfPossible_1 = function (val) {\n      return val === void 0 ? 'undefined' : val === Infinity ? 'Infinity' : val === -Infinity ? '-Infinity' : eqNaN(val) ? 'NaN' : val instanceof Date ? 'Date(' + val.toISOString() + ')' : isFunction(val) ? 'function () { ... }' : isRegExp(val) ? val + '' : null;\n    };\n    msg = map(hintInfo, function (arg) {\n      if (isString(arg)) {\n        // Print without quotation mark for some statement.\n        return arg;\n      } else {\n        var printableStr = makePrintableStringIfPossible_1(arg);\n        if (printableStr != null) {\n          return printableStr;\n        } else if (typeof JSON !== 'undefined' && JSON.stringify) {\n          try {\n            return JSON.stringify(arg, function (n, val) {\n              var printableStr = makePrintableStringIfPossible_1(val);\n              return printableStr == null ? val : printableStr;\n            });\n            // In most cases the info object is small, so do not line break.\n          } catch (err) {\n            return '?';\n          }\n        } else {\n          return '?';\n        }\n      }\n    }).join(' ');\n  }\n  return msg;\n}\n/**\r\n * @throws Error\r\n */\nexport function throwError(msg) {\n  throw new Error(msg);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}