{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Layout helpers for each component positioning\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent } from './number.js';\nimport * as formatUtil from './format.js';\nvar each = zrUtil.each;\n/**\r\n * @public\r\n */\nexport var LOCATION_PARAMS = ['left', 'right', 'top', 'bottom', 'width', 'height'];\n/**\r\n * @public\r\n */\nexport var HV_NAMES = [['width', 'left', 'right'], ['height', 'top', 'bottom']];\nfunction boxLayout(orient, group, gap, maxWidth, maxHeight) {\n  var x = 0;\n  var y = 0;\n  if (maxWidth == null) {\n    maxWidth = Infinity;\n  }\n  if (maxHeight == null) {\n    maxHeight = Infinity;\n  }\n  var currentLineMaxSize = 0;\n  group.eachChild(function (child, idx) {\n    var rect = child.getBoundingRect();\n    var nextChild = group.childAt(idx + 1);\n    var nextChildRect = nextChild && nextChild.getBoundingRect();\n    var nextX;\n    var nextY;\n    if (orient === 'horizontal') {\n      var moveX = rect.width + (nextChildRect ? -nextChildRect.x + rect.x : 0);\n      nextX = x + moveX;\n      // Wrap when width exceeds maxWidth or meet a `newline` group\n      // FIXME compare before adding gap?\n      if (nextX > maxWidth || child.newline) {\n        x = 0;\n        nextX = moveX;\n        y += currentLineMaxSize + gap;\n        currentLineMaxSize = rect.height;\n      } else {\n        // FIXME: consider rect.y is not `0`?\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.height);\n      }\n    } else {\n      var moveY = rect.height + (nextChildRect ? -nextChildRect.y + rect.y : 0);\n      nextY = y + moveY;\n      // Wrap when width exceeds maxHeight or meet a `newline` group\n      if (nextY > maxHeight || child.newline) {\n        x += currentLineMaxSize + gap;\n        y = 0;\n        nextY = moveY;\n        currentLineMaxSize = rect.width;\n      } else {\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.width);\n      }\n    }\n    if (child.newline) {\n      return;\n    }\n    child.x = x;\n    child.y = y;\n    child.markRedraw();\n    orient === 'horizontal' ? x = nextX + gap : y = nextY + gap;\n  });\n}\n/**\r\n * VBox or HBox layouting\r\n * @param {string} orient\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var box = boxLayout;\n/**\r\n * VBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var vbox = zrUtil.curry(boxLayout, 'vertical');\n/**\r\n * HBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var hbox = zrUtil.curry(boxLayout, 'horizontal');\n/**\r\n * If x or x2 is not specified or 'center' 'left' 'right',\r\n * the width would be as long as possible.\r\n * If y or y2 is not specified or 'middle' 'top' 'bottom',\r\n * the height would be as long as possible.\r\n */\nexport function getAvailableSize(positionInfo, containerRect, margin) {\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var x = parsePercent(positionInfo.left, containerWidth);\n  var y = parsePercent(positionInfo.top, containerHeight);\n  var x2 = parsePercent(positionInfo.right, containerWidth);\n  var y2 = parsePercent(positionInfo.bottom, containerHeight);\n  (isNaN(x) || isNaN(parseFloat(positionInfo.left))) && (x = 0);\n  (isNaN(x2) || isNaN(parseFloat(positionInfo.right))) && (x2 = containerWidth);\n  (isNaN(y) || isNaN(parseFloat(positionInfo.top))) && (y = 0);\n  (isNaN(y2) || isNaN(parseFloat(positionInfo.bottom))) && (y2 = containerHeight);\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  return {\n    width: Math.max(x2 - x - margin[1] - margin[3], 0),\n    height: Math.max(y2 - y - margin[0] - margin[2], 0)\n  };\n}\n/**\r\n * Parse position info.\r\n */\nexport function getLayoutRect(positionInfo, containerRect, margin) {\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var left = parsePercent(positionInfo.left, containerWidth);\n  var top = parsePercent(positionInfo.top, containerHeight);\n  var right = parsePercent(positionInfo.right, containerWidth);\n  var bottom = parsePercent(positionInfo.bottom, containerHeight);\n  var width = parsePercent(positionInfo.width, containerWidth);\n  var height = parsePercent(positionInfo.height, containerHeight);\n  var verticalMargin = margin[2] + margin[0];\n  var horizontalMargin = margin[1] + margin[3];\n  var aspect = positionInfo.aspect;\n  // If width is not specified, calculate width from left and right\n  if (isNaN(width)) {\n    width = containerWidth - right - horizontalMargin - left;\n  }\n  if (isNaN(height)) {\n    height = containerHeight - bottom - verticalMargin - top;\n  }\n  if (aspect != null) {\n    // If width and height are not given\n    // 1. Graph should not exceeds the container\n    // 2. Aspect must be keeped\n    // 3. Graph should take the space as more as possible\n    // FIXME\n    // Margin is not considered, because there is no case that both\n    // using margin and aspect so far.\n    if (isNaN(width) && isNaN(height)) {\n      if (aspect > containerWidth / containerHeight) {\n        width = containerWidth * 0.8;\n      } else {\n        height = containerHeight * 0.8;\n      }\n    }\n    // Calculate width or height with given aspect\n    if (isNaN(width)) {\n      width = aspect * height;\n    }\n    if (isNaN(height)) {\n      height = width / aspect;\n    }\n  }\n  // If left is not specified, calculate left from right and width\n  if (isNaN(left)) {\n    left = containerWidth - right - width - horizontalMargin;\n  }\n  if (isNaN(top)) {\n    top = containerHeight - bottom - height - verticalMargin;\n  }\n  // Align left and top\n  switch (positionInfo.left || positionInfo.right) {\n    case 'center':\n      left = containerWidth / 2 - width / 2 - margin[3];\n      break;\n    case 'right':\n      left = containerWidth - width - horizontalMargin;\n      break;\n  }\n  switch (positionInfo.top || positionInfo.bottom) {\n    case 'middle':\n    case 'center':\n      top = containerHeight / 2 - height / 2 - margin[0];\n      break;\n    case 'bottom':\n      top = containerHeight - height - verticalMargin;\n      break;\n  }\n  // If something is wrong and left, top, width, height are calculated as NaN\n  left = left || 0;\n  top = top || 0;\n  if (isNaN(width)) {\n    // Width may be NaN if only one value is given except width\n    width = containerWidth - horizontalMargin - left - (right || 0);\n  }\n  if (isNaN(height)) {\n    // Height may be NaN if only one value is given except height\n    height = containerHeight - verticalMargin - top - (bottom || 0);\n  }\n  var rect = new BoundingRect(left + margin[3], top + margin[0], width, height);\n  rect.margin = margin;\n  return rect;\n}\n/**\r\n * Position a zr element in viewport\r\n *  Group position is specified by either\r\n *  {left, top}, {right, bottom}\r\n *  If all properties exists, right and bottom will be igonred.\r\n *\r\n * Logic:\r\n *     1. Scale (against origin point in parent coord)\r\n *     2. Rotate (against origin point in parent coord)\r\n *     3. Translate (with el.position by this method)\r\n * So this method only fixes the last step 'Translate', which does not affect\r\n * scaling and rotating.\r\n *\r\n * If be called repeatedly with the same input el, the same result will be gotten.\r\n *\r\n * Return true if the layout happened.\r\n *\r\n * @param el Should have `getBoundingRect` method.\r\n * @param positionInfo\r\n * @param positionInfo.left\r\n * @param positionInfo.top\r\n * @param positionInfo.right\r\n * @param positionInfo.bottom\r\n * @param positionInfo.width Only for opt.boundingModel: 'raw'\r\n * @param positionInfo.height Only for opt.boundingModel: 'raw'\r\n * @param containerRect\r\n * @param margin\r\n * @param opt\r\n * @param opt.hv Only horizontal or only vertical. Default to be [1, 1]\r\n * @param opt.boundingMode\r\n *        Specify how to calculate boundingRect when locating.\r\n *        'all': Position the boundingRect that is transformed and uioned\r\n *               both itself and its descendants.\r\n *               This mode simplies confine the elements in the bounding\r\n *               of their container (e.g., using 'right: 0').\r\n *        'raw': Position the boundingRect that is not transformed and only itself.\r\n *               This mode is useful when you want a element can overflow its\r\n *               container. (Consider a rotated circle needs to be located in a corner.)\r\n *               In this mode positionInfo.width/height can only be number.\r\n */\nexport function positionElement(el, positionInfo, containerRect, margin, opt, out) {\n  var h = !opt || !opt.hv || opt.hv[0];\n  var v = !opt || !opt.hv || opt.hv[1];\n  var boundingMode = opt && opt.boundingMode || 'all';\n  out = out || el;\n  out.x = el.x;\n  out.y = el.y;\n  if (!h && !v) {\n    return false;\n  }\n  var rect;\n  if (boundingMode === 'raw') {\n    rect = el.type === 'group' ? new BoundingRect(0, 0, +positionInfo.width || 0, +positionInfo.height || 0) : el.getBoundingRect();\n  } else {\n    rect = el.getBoundingRect();\n    if (el.needLocalTransform()) {\n      var transform = el.getLocalTransform();\n      // Notice: raw rect may be inner object of el,\n      // which should not be modified.\n      rect = rect.clone();\n      rect.applyTransform(transform);\n    }\n  }\n  // The real width and height can not be specified but calculated by the given el.\n  var layoutRect = getLayoutRect(zrUtil.defaults({\n    width: rect.width,\n    height: rect.height\n  }, positionInfo), containerRect, margin);\n  // Because 'tranlate' is the last step in transform\n  // (see zrender/core/Transformable#getLocalTransform),\n  // we can just only modify el.position to get final result.\n  var dx = h ? layoutRect.x - rect.x : 0;\n  var dy = v ? layoutRect.y - rect.y : 0;\n  if (boundingMode === 'raw') {\n    out.x = dx;\n    out.y = dy;\n  } else {\n    out.x += dx;\n    out.y += dy;\n  }\n  if (out === el) {\n    el.markRedraw();\n  }\n  return true;\n}\n/**\r\n * @param option Contains some of the properties in HV_NAMES.\r\n * @param hvIdx 0: horizontal; 1: vertical.\r\n */\nexport function sizeCalculable(option, hvIdx) {\n  return option[HV_NAMES[hvIdx][0]] != null || option[HV_NAMES[hvIdx][1]] != null && option[HV_NAMES[hvIdx][2]] != null;\n}\nexport function fetchLayoutMode(ins) {\n  var layoutMode = ins.layoutMode || ins.constructor.layoutMode;\n  return zrUtil.isObject(layoutMode) ? layoutMode : layoutMode ? {\n    type: layoutMode\n  } : null;\n}\n/**\r\n * Consider Case:\r\n * When default option has {left: 0, width: 100}, and we set {right: 0}\r\n * through setOption or media query, using normal zrUtil.merge will cause\r\n * {right: 0} does not take effect.\r\n *\r\n * @example\r\n * ComponentModel.extend({\r\n *     init: function () {\r\n *         ...\r\n *         let inputPositionParams = layout.getLayoutParams(option);\r\n *         this.mergeOption(inputPositionParams);\r\n *     },\r\n *     mergeOption: function (newOption) {\r\n *         newOption && zrUtil.merge(thisOption, newOption, true);\r\n *         layout.mergeLayoutParam(thisOption, newOption);\r\n *     }\r\n * });\r\n *\r\n * @param targetOption\r\n * @param newOption\r\n * @param opt\r\n */\nexport function mergeLayoutParam(targetOption, newOption, opt) {\n  var ignoreSize = opt && opt.ignoreSize;\n  !zrUtil.isArray(ignoreSize) && (ignoreSize = [ignoreSize, ignoreSize]);\n  var hResult = merge(HV_NAMES[0], 0);\n  var vResult = merge(HV_NAMES[1], 1);\n  copy(HV_NAMES[0], targetOption, hResult);\n  copy(HV_NAMES[1], targetOption, vResult);\n  function merge(names, hvIdx) {\n    var newParams = {};\n    var newValueCount = 0;\n    var merged = {};\n    var mergedValueCount = 0;\n    var enoughParamNumber = 2;\n    each(names, function (name) {\n      merged[name] = targetOption[name];\n    });\n    each(names, function (name) {\n      // Consider case: newOption.width is null, which is\n      // set by user for removing width setting.\n      hasProp(newOption, name) && (newParams[name] = merged[name] = newOption[name]);\n      hasValue(newParams, name) && newValueCount++;\n      hasValue(merged, name) && mergedValueCount++;\n    });\n    if (ignoreSize[hvIdx]) {\n      // Only one of left/right is premitted to exist.\n      if (hasValue(newOption, names[1])) {\n        merged[names[2]] = null;\n      } else if (hasValue(newOption, names[2])) {\n        merged[names[1]] = null;\n      }\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // or targetOption: {right: ...} and newOption: {width: ...},\n    // There is no conflict when merged only has params count\n    // little than enoughParamNumber.\n    if (mergedValueCount === enoughParamNumber || !newValueCount) {\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // Than we can make sure user only want those two, and ignore\n    // all origin params in targetOption.\n    else if (newValueCount >= enoughParamNumber) {\n      return newParams;\n    } else {\n      // Chose another param from targetOption by priority.\n      for (var i = 0; i < names.length; i++) {\n        var name_1 = names[i];\n        if (!hasProp(newParams, name_1) && hasProp(targetOption, name_1)) {\n          newParams[name_1] = targetOption[name_1];\n          break;\n        }\n      }\n      return newParams;\n    }\n  }\n  function hasProp(obj, name) {\n    return obj.hasOwnProperty(name);\n  }\n  function hasValue(obj, name) {\n    return obj[name] != null && obj[name] !== 'auto';\n  }\n  function copy(names, target, source) {\n    each(names, function (name) {\n      target[name] = source[name];\n    });\n  }\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n */\nexport function getLayoutParams(source) {\n  return copyLayoutParams({}, source);\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n * @param {Object} source\r\n * @return {Object} Result contains those props.\r\n */\nexport function copyLayoutParams(target, source) {\n  source && target && each(LOCATION_PARAMS, function (name) {\n    source.hasOwnProperty(name) && (target[name] = source[name]);\n  });\n  return target;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}