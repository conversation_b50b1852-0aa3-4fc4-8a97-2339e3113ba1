{"ast": null, "code": "var DEFAULT_MIN_MERGE = 32;\nvar DEFAULT_MIN_GALLOPING = 7;\nfunction minRunLength(n) {\n  var r = 0;\n  while (n >= DEFAULT_MIN_MERGE) {\n    r |= n & 1;\n    n >>= 1;\n  }\n  return n + r;\n}\nfunction makeAscendingRun(array, lo, hi, compare) {\n  var runHi = lo + 1;\n  if (runHi === hi) {\n    return 1;\n  }\n  if (compare(array[runHi++], array[lo]) < 0) {\n    while (runHi < hi && compare(array[runHi], array[runHi - 1]) < 0) {\n      runHi++;\n    }\n    reverseRun(array, lo, runHi);\n  } else {\n    while (runHi < hi && compare(array[runHi], array[runHi - 1]) >= 0) {\n      runHi++;\n    }\n  }\n  return runHi - lo;\n}\nfunction reverseRun(array, lo, hi) {\n  hi--;\n  while (lo < hi) {\n    var t = array[lo];\n    array[lo++] = array[hi];\n    array[hi--] = t;\n  }\n}\nfunction binaryInsertionSort(array, lo, hi, start, compare) {\n  if (start === lo) {\n    start++;\n  }\n  for (; start < hi; start++) {\n    var pivot = array[start];\n    var left = lo;\n    var right = start;\n    var mid;\n    while (left < right) {\n      mid = left + right >>> 1;\n      if (compare(pivot, array[mid]) < 0) {\n        right = mid;\n      } else {\n        left = mid + 1;\n      }\n    }\n    var n = start - left;\n    switch (n) {\n      case 3:\n        array[left + 3] = array[left + 2];\n      case 2:\n        array[left + 2] = array[left + 1];\n      case 1:\n        array[left + 1] = array[left];\n        break;\n      default:\n        while (n > 0) {\n          array[left + n] = array[left + n - 1];\n          n--;\n        }\n    }\n    array[left] = pivot;\n  }\n}\nfunction gallopLeft(value, array, start, length, hint, compare) {\n  var lastOffset = 0;\n  var maxOffset = 0;\n  var offset = 1;\n  if (compare(value, array[start + hint]) > 0) {\n    maxOffset = length - hint;\n    while (offset < maxOffset && compare(value, array[start + hint + offset]) > 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    lastOffset += hint;\n    offset += hint;\n  } else {\n    maxOffset = hint + 1;\n    while (offset < maxOffset && compare(value, array[start + hint - offset]) <= 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    var tmp = lastOffset;\n    lastOffset = hint - offset;\n    offset = hint - tmp;\n  }\n  lastOffset++;\n  while (lastOffset < offset) {\n    var m = lastOffset + (offset - lastOffset >>> 1);\n    if (compare(value, array[start + m]) > 0) {\n      lastOffset = m + 1;\n    } else {\n      offset = m;\n    }\n  }\n  return offset;\n}\nfunction gallopRight(value, array, start, length, hint, compare) {\n  var lastOffset = 0;\n  var maxOffset = 0;\n  var offset = 1;\n  if (compare(value, array[start + hint]) < 0) {\n    maxOffset = hint + 1;\n    while (offset < maxOffset && compare(value, array[start + hint - offset]) < 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    var tmp = lastOffset;\n    lastOffset = hint - offset;\n    offset = hint - tmp;\n  } else {\n    maxOffset = length - hint;\n    while (offset < maxOffset && compare(value, array[start + hint + offset]) >= 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    lastOffset += hint;\n    offset += hint;\n  }\n  lastOffset++;\n  while (lastOffset < offset) {\n    var m = lastOffset + (offset - lastOffset >>> 1);\n    if (compare(value, array[start + m]) < 0) {\n      offset = m;\n    } else {\n      lastOffset = m + 1;\n    }\n  }\n  return offset;\n}\nfunction TimSort(array, compare) {\n  var minGallop = DEFAULT_MIN_GALLOPING;\n  var runStart;\n  var runLength;\n  var stackSize = 0;\n  var tmp = [];\n  runStart = [];\n  runLength = [];\n  function pushRun(_runStart, _runLength) {\n    runStart[stackSize] = _runStart;\n    runLength[stackSize] = _runLength;\n    stackSize += 1;\n  }\n  function mergeRuns() {\n    while (stackSize > 1) {\n      var n = stackSize - 2;\n      if (n >= 1 && runLength[n - 1] <= runLength[n] + runLength[n + 1] || n >= 2 && runLength[n - 2] <= runLength[n] + runLength[n - 1]) {\n        if (runLength[n - 1] < runLength[n + 1]) {\n          n--;\n        }\n      } else if (runLength[n] > runLength[n + 1]) {\n        break;\n      }\n      mergeAt(n);\n    }\n  }\n  function forceMergeRuns() {\n    while (stackSize > 1) {\n      var n = stackSize - 2;\n      if (n > 0 && runLength[n - 1] < runLength[n + 1]) {\n        n--;\n      }\n      mergeAt(n);\n    }\n  }\n  function mergeAt(i) {\n    var start1 = runStart[i];\n    var length1 = runLength[i];\n    var start2 = runStart[i + 1];\n    var length2 = runLength[i + 1];\n    runLength[i] = length1 + length2;\n    if (i === stackSize - 3) {\n      runStart[i + 1] = runStart[i + 2];\n      runLength[i + 1] = runLength[i + 2];\n    }\n    stackSize--;\n    var k = gallopRight(array[start2], array, start1, length1, 0, compare);\n    start1 += k;\n    length1 -= k;\n    if (length1 === 0) {\n      return;\n    }\n    length2 = gallopLeft(array[start1 + length1 - 1], array, start2, length2, length2 - 1, compare);\n    if (length2 === 0) {\n      return;\n    }\n    if (length1 <= length2) {\n      mergeLow(start1, length1, start2, length2);\n    } else {\n      mergeHigh(start1, length1, start2, length2);\n    }\n  }\n  function mergeLow(start1, length1, start2, length2) {\n    var i = 0;\n    for (i = 0; i < length1; i++) {\n      tmp[i] = array[start1 + i];\n    }\n    var cursor1 = 0;\n    var cursor2 = start2;\n    var dest = start1;\n    array[dest++] = array[cursor2++];\n    if (--length2 === 0) {\n      for (i = 0; i < length1; i++) {\n        array[dest + i] = tmp[cursor1 + i];\n      }\n      return;\n    }\n    if (length1 === 1) {\n      for (i = 0; i < length2; i++) {\n        array[dest + i] = array[cursor2 + i];\n      }\n      array[dest + length2] = tmp[cursor1];\n      return;\n    }\n    var _minGallop = minGallop;\n    var count1;\n    var count2;\n    var exit;\n    while (1) {\n      count1 = 0;\n      count2 = 0;\n      exit = false;\n      do {\n        if (compare(array[cursor2], tmp[cursor1]) < 0) {\n          array[dest++] = array[cursor2++];\n          count2++;\n          count1 = 0;\n          if (--length2 === 0) {\n            exit = true;\n            break;\n          }\n        } else {\n          array[dest++] = tmp[cursor1++];\n          count1++;\n          count2 = 0;\n          if (--length1 === 1) {\n            exit = true;\n            break;\n          }\n        }\n      } while ((count1 | count2) < _minGallop);\n      if (exit) {\n        break;\n      }\n      do {\n        count1 = gallopRight(array[cursor2], tmp, cursor1, length1, 0, compare);\n        if (count1 !== 0) {\n          for (i = 0; i < count1; i++) {\n            array[dest + i] = tmp[cursor1 + i];\n          }\n          dest += count1;\n          cursor1 += count1;\n          length1 -= count1;\n          if (length1 <= 1) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest++] = array[cursor2++];\n        if (--length2 === 0) {\n          exit = true;\n          break;\n        }\n        count2 = gallopLeft(tmp[cursor1], array, cursor2, length2, 0, compare);\n        if (count2 !== 0) {\n          for (i = 0; i < count2; i++) {\n            array[dest + i] = array[cursor2 + i];\n          }\n          dest += count2;\n          cursor2 += count2;\n          length2 -= count2;\n          if (length2 === 0) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest++] = tmp[cursor1++];\n        if (--length1 === 1) {\n          exit = true;\n          break;\n        }\n        _minGallop--;\n      } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n      if (exit) {\n        break;\n      }\n      if (_minGallop < 0) {\n        _minGallop = 0;\n      }\n      _minGallop += 2;\n    }\n    minGallop = _minGallop;\n    minGallop < 1 && (minGallop = 1);\n    if (length1 === 1) {\n      for (i = 0; i < length2; i++) {\n        array[dest + i] = array[cursor2 + i];\n      }\n      array[dest + length2] = tmp[cursor1];\n    } else if (length1 === 0) {\n      throw new Error();\n    } else {\n      for (i = 0; i < length1; i++) {\n        array[dest + i] = tmp[cursor1 + i];\n      }\n    }\n  }\n  function mergeHigh(start1, length1, start2, length2) {\n    var i = 0;\n    for (i = 0; i < length2; i++) {\n      tmp[i] = array[start2 + i];\n    }\n    var cursor1 = start1 + length1 - 1;\n    var cursor2 = length2 - 1;\n    var dest = start2 + length2 - 1;\n    var customCursor = 0;\n    var customDest = 0;\n    array[dest--] = array[cursor1--];\n    if (--length1 === 0) {\n      customCursor = dest - (length2 - 1);\n      for (i = 0; i < length2; i++) {\n        array[customCursor + i] = tmp[i];\n      }\n      return;\n    }\n    if (length2 === 1) {\n      dest -= length1;\n      cursor1 -= length1;\n      customDest = dest + 1;\n      customCursor = cursor1 + 1;\n      for (i = length1 - 1; i >= 0; i--) {\n        array[customDest + i] = array[customCursor + i];\n      }\n      array[dest] = tmp[cursor2];\n      return;\n    }\n    var _minGallop = minGallop;\n    while (true) {\n      var count1 = 0;\n      var count2 = 0;\n      var exit = false;\n      do {\n        if (compare(tmp[cursor2], array[cursor1]) < 0) {\n          array[dest--] = array[cursor1--];\n          count1++;\n          count2 = 0;\n          if (--length1 === 0) {\n            exit = true;\n            break;\n          }\n        } else {\n          array[dest--] = tmp[cursor2--];\n          count2++;\n          count1 = 0;\n          if (--length2 === 1) {\n            exit = true;\n            break;\n          }\n        }\n      } while ((count1 | count2) < _minGallop);\n      if (exit) {\n        break;\n      }\n      do {\n        count1 = length1 - gallopRight(tmp[cursor2], array, start1, length1, length1 - 1, compare);\n        if (count1 !== 0) {\n          dest -= count1;\n          cursor1 -= count1;\n          length1 -= count1;\n          customDest = dest + 1;\n          customCursor = cursor1 + 1;\n          for (i = count1 - 1; i >= 0; i--) {\n            array[customDest + i] = array[customCursor + i];\n          }\n          if (length1 === 0) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest--] = tmp[cursor2--];\n        if (--length2 === 1) {\n          exit = true;\n          break;\n        }\n        count2 = length2 - gallopLeft(array[cursor1], tmp, 0, length2, length2 - 1, compare);\n        if (count2 !== 0) {\n          dest -= count2;\n          cursor2 -= count2;\n          length2 -= count2;\n          customDest = dest + 1;\n          customCursor = cursor2 + 1;\n          for (i = 0; i < count2; i++) {\n            array[customDest + i] = tmp[customCursor + i];\n          }\n          if (length2 <= 1) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest--] = array[cursor1--];\n        if (--length1 === 0) {\n          exit = true;\n          break;\n        }\n        _minGallop--;\n      } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n      if (exit) {\n        break;\n      }\n      if (_minGallop < 0) {\n        _minGallop = 0;\n      }\n      _minGallop += 2;\n    }\n    minGallop = _minGallop;\n    if (minGallop < 1) {\n      minGallop = 1;\n    }\n    if (length2 === 1) {\n      dest -= length1;\n      cursor1 -= length1;\n      customDest = dest + 1;\n      customCursor = cursor1 + 1;\n      for (i = length1 - 1; i >= 0; i--) {\n        array[customDest + i] = array[customCursor + i];\n      }\n      array[dest] = tmp[cursor2];\n    } else if (length2 === 0) {\n      throw new Error();\n    } else {\n      customCursor = dest - (length2 - 1);\n      for (i = 0; i < length2; i++) {\n        array[customCursor + i] = tmp[i];\n      }\n    }\n  }\n  return {\n    mergeRuns: mergeRuns,\n    forceMergeRuns: forceMergeRuns,\n    pushRun: pushRun\n  };\n}\nexport default function sort(array, compare, lo, hi) {\n  if (!lo) {\n    lo = 0;\n  }\n  if (!hi) {\n    hi = array.length;\n  }\n  var remaining = hi - lo;\n  if (remaining < 2) {\n    return;\n  }\n  var runLength = 0;\n  if (remaining < DEFAULT_MIN_MERGE) {\n    runLength = makeAscendingRun(array, lo, hi, compare);\n    binaryInsertionSort(array, lo, hi, lo + runLength, compare);\n    return;\n  }\n  var ts = TimSort(array, compare);\n  var minRun = minRunLength(remaining);\n  do {\n    runLength = makeAscendingRun(array, lo, hi, compare);\n    if (runLength < minRun) {\n      var force = remaining;\n      if (force > minRun) {\n        force = minRun;\n      }\n      binaryInsertionSort(array, lo, lo + force, lo + runLength, compare);\n      runLength = force;\n    }\n    ts.pushRun(lo, runLength);\n    ts.mergeRuns();\n    remaining -= runLength;\n    lo += runLength;\n  } while (remaining !== 0);\n  ts.forceMergeRuns();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}