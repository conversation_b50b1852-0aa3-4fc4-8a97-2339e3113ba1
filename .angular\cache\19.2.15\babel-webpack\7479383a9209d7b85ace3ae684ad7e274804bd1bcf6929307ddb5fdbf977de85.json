{"ast": null, "code": "import { ScientificProblemsComponent } from './scientific-problems-view/scientific-problems/scientific-problems.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/language-services/language.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"item_group_active\": a0\n});\nfunction AdminMessagingViewComponent_app_teacher_program_request_view_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-teacher-program-request-view\");\n  }\n}\nfunction AdminMessagingViewComponent_app_student_program_request_view_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-student-program-request-view\");\n  }\n}\nfunction AdminMessagingViewComponent_app_scientific_problems_view_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-scientific-problems-view\");\n  }\n}\nfunction AdminMessagingViewComponent_app_chat_view_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-view\");\n  }\n}\nfunction AdminMessagingViewComponent_app_corrupted_files_requests_view_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-corrupted-files-requests-view\");\n  }\n}\nexport let AdminMessagingViewComponent = /*#__PURE__*/(() => {\n  class AdminMessagingViewComponent {\n    translate;\n    languageService;\n    scientificProblmChild;\n    showtap = 'teacher';\n    // showAddReplyOverlay = false;\n    // showAddScProbToQuestionBankOverlay = false;\n    // scProbObjForAddReplyView : IScientificProblemGridItems = {}\n    // scProbObjForAddToQuestionBankView : IScientificProblemGridItems = {}\n    constructor(translate, languageService) {\n      this.translate = translate;\n      this.languageService = languageService;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SIDENAVBAR.REQUEST'));\n    }\n    static ɵfac = function AdminMessagingViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminMessagingViewComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.LanguageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminMessagingViewComponent,\n      selectors: [[\"app-admin-messaging-view\"]],\n      viewQuery: function AdminMessagingViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ScientificProblemsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scientificProblmChild = _t.first);\n        }\n      },\n      decls: 26,\n      vars: 35,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [1, \"row\", \"group_header\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [4, \"ngIf\"]],\n      template: function AdminMessagingViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function AdminMessagingViewComponent_Template_div_click_4_listener() {\n            return ctx.showtap = \"teacher\";\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function AdminMessagingViewComponent_Template_div_click_7_listener() {\n            return ctx.showtap = \"student\";\n          });\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function AdminMessagingViewComponent_Template_div_click_10_listener() {\n            return ctx.showtap = \"scientificProblem\";\n          });\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function AdminMessagingViewComponent_Template_div_click_13_listener() {\n            return ctx.showtap = \"chat\";\n          });\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function AdminMessagingViewComponent_Template_div_click_16_listener() {\n            return ctx.showtap = \"corruptedFiles\";\n          });\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 1)(20, \"div\", 2);\n          i0.ɵɵtemplate(21, AdminMessagingViewComponent_app_teacher_program_request_view_21_Template, 1, 0, \"app-teacher-program-request-view\", 5)(22, AdminMessagingViewComponent_app_student_program_request_view_22_Template, 1, 0, \"app-student-program-request-view\", 5)(23, AdminMessagingViewComponent_app_scientific_problems_view_23_Template, 1, 0, \"app-scientific-problems-view\", 5)(24, AdminMessagingViewComponent_app_chat_view_24_Template, 1, 0, \"app-chat-view\", 5)(25, AdminMessagingViewComponent_app_corrupted_files_requests_view_25_Template, 1, 0, \"app-corrupted-files-requests-view\", 5);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ctx.showtap == \"teacher\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 15, \"TEACHER_SUBSCRIBERS.TEACHERS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx.showtap == \"student\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 17, \"TEACHER_SUBSCRIBERS.STUDENTS\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx.showtap == \"scientificProblem\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 19, \"ADMIN_MESSAGING.SCIENTIFIC_PROBLEM\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(31, _c0, ctx.showtap == \"chat\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 21, \"ADMIN_MESSAGING.CHAT\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx.showtap == \"corruptedFiles\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 23, \"ADMIN_MESSAGING.CORRUPTED_ATTACHMENT\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showtap == \"teacher\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showtap == \"student\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showtap == \"scientificProblem\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showtap == \"chat\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showtap == \"corruptedFiles\");\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i1.TranslatePipe],\n      styles: [\".container-fluid[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:1rem;margin-top:1rem;margin-bottom:0;width:98%;height:85vh}.container-fluid[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.container-fluid[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin:0 .25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.container-fluid[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}\"]\n    });\n  }\n  return AdminMessagingViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}