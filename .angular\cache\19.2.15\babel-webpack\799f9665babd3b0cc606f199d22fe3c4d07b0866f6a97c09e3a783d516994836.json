{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createSeriesDataSimply from './createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nvar WhiskerBoxCommonMixin = /** @class */function () {\n  function WhiskerBoxCommonMixin() {}\n  /**\r\n   * @private\r\n   */\n  WhiskerBoxCommonMixin.prototype._hasEncodeRule = function (key) {\n    var encodeRules = this.getEncode();\n    return encodeRules && encodeRules.get(key) != null;\n  };\n  /**\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getInitialData = function (option, ecModel) {\n    // When both types of xAxis and yAxis are 'value', layout is\n    // needed to be specified by user. Otherwise, layout can be\n    // judged by which axis is category.\n    var ordinalMeta;\n    var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));\n    var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));\n    var xAxisType = xAxisModel.get('type');\n    var yAxisType = yAxisModel.get('type');\n    var addOrdinal;\n    // FIXME\n    // Consider time axis.\n    if (xAxisType === 'category') {\n      option.layout = 'horizontal';\n      ordinalMeta = xAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('x');\n    } else if (yAxisType === 'category') {\n      option.layout = 'vertical';\n      ordinalMeta = yAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('y');\n    } else {\n      option.layout = option.layout || 'horizontal';\n    }\n    var coordDims = ['x', 'y'];\n    var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;\n    var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];\n    var otherAxisDim = coordDims[1 - baseAxisDimIndex];\n    var axisModels = [xAxisModel, yAxisModel];\n    var baseAxisType = axisModels[baseAxisDimIndex].get('type');\n    var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');\n    var data = option.data;\n    // Clone a new data for next setOption({}) usage.\n    // Avoid modifying current data will affect further update.\n    if (data && addOrdinal) {\n      var newOptionData_1 = [];\n      zrUtil.each(data, function (item, index) {\n        var newItem;\n        if (zrUtil.isArray(item)) {\n          newItem = item.slice();\n          // Modify current using data.\n          item.unshift(index);\n        } else if (zrUtil.isArray(item.value)) {\n          newItem = zrUtil.extend({}, item);\n          newItem.value = newItem.value.slice();\n          // Modify current using data.\n          item.value.unshift(index);\n        } else {\n          newItem = item;\n        }\n        newOptionData_1.push(newItem);\n      });\n      option.data = newOptionData_1;\n    }\n    var defaultValueDimensions = this.defaultValueDimensions;\n    var coordDimensions = [{\n      name: baseAxisDim,\n      type: getDimensionTypeByAxis(baseAxisType),\n      ordinalMeta: ordinalMeta,\n      otherDims: {\n        tooltip: false,\n        itemName: 0\n      },\n      dimsDef: ['base']\n    }, {\n      name: otherAxisDim,\n      type: getDimensionTypeByAxis(otherAxisType),\n      dimsDef: defaultValueDimensions.slice()\n    }];\n    return createSeriesDataSimply(this, {\n      coordDimensions: coordDimensions,\n      dimensionsCount: defaultValueDimensions.length + 1,\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordDimensions, this)\n    });\n  };\n  /**\r\n   * If horizontal, base axis is x, otherwise y.\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getBaseAxis = function () {\n    var dim = this._baseAxisDim;\n    return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;\n  };\n  return WhiskerBoxCommonMixin;\n}();\n;\nexport { WhiskerBoxCommonMixin };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}