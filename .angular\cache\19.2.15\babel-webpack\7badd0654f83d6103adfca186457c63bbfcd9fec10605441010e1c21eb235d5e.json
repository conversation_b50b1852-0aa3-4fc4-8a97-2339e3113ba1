{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nfunction diffData(oldData, newData) {\n  var diffResult = [];\n  newData.diff(oldData).add(function (idx) {\n    diffResult.push({\n      cmd: '+',\n      idx: idx\n    });\n  }).update(function (newIdx, oldIdx) {\n    diffResult.push({\n      cmd: '=',\n      idx: oldIdx,\n      idx1: newIdx\n    });\n  }).remove(function (idx) {\n    diffResult.push({\n      cmd: '-',\n      idx: idx\n    });\n  }).execute();\n  return diffResult;\n}\nexport default function lineAnimationDiff(oldData, newData, oldStackedOnPoints, newStackedOnPoints, oldCoordSys, newCoordSys, oldValueOrigin, newValueOrigin) {\n  var diff = diffData(oldData, newData);\n  // let newIdList = newData.mapArray(newData.getId);\n  // let oldIdList = oldData.mapArray(oldData.getId);\n  // convertToIntId(newIdList, oldIdList);\n  // // FIXME One data ?\n  // diff = arrayDiff(oldIdList, newIdList);\n  var currPoints = [];\n  var nextPoints = [];\n  // Points for stacking base line\n  var currStackedPoints = [];\n  var nextStackedPoints = [];\n  var status = [];\n  var sortedIndices = [];\n  var rawIndices = [];\n  var newDataOldCoordInfo = prepareDataCoordInfo(oldCoordSys, newData, oldValueOrigin);\n  // const oldDataNewCoordInfo = prepareDataCoordInfo(newCoordSys, oldData, newValueOrigin);\n  var oldPoints = oldData.getLayout('points') || [];\n  var newPoints = newData.getLayout('points') || [];\n  for (var i = 0; i < diff.length; i++) {\n    var diffItem = diff[i];\n    var pointAdded = true;\n    var oldIdx2 = void 0;\n    var newIdx2 = void 0;\n    // FIXME, animation is not so perfect when dataZoom window moves fast\n    // Which is in case remvoing or add more than one data in the tail or head\n    switch (diffItem.cmd) {\n      case '=':\n        oldIdx2 = diffItem.idx * 2;\n        newIdx2 = diffItem.idx1 * 2;\n        var currentX = oldPoints[oldIdx2];\n        var currentY = oldPoints[oldIdx2 + 1];\n        var nextX = newPoints[newIdx2];\n        var nextY = newPoints[newIdx2 + 1];\n        // If previous data is NaN, use next point directly\n        if (isNaN(currentX) || isNaN(currentY)) {\n          currentX = nextX;\n          currentY = nextY;\n        }\n        currPoints.push(currentX, currentY);\n        nextPoints.push(nextX, nextY);\n        currStackedPoints.push(oldStackedOnPoints[oldIdx2], oldStackedOnPoints[oldIdx2 + 1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(diffItem.idx1));\n        break;\n      case '+':\n        var newIdx = diffItem.idx;\n        var newDataDimsForPoint = newDataOldCoordInfo.dataDimsForPoint;\n        var oldPt = oldCoordSys.dataToPoint([newData.get(newDataDimsForPoint[0], newIdx), newData.get(newDataDimsForPoint[1], newIdx)]);\n        newIdx2 = newIdx * 2;\n        currPoints.push(oldPt[0], oldPt[1]);\n        nextPoints.push(newPoints[newIdx2], newPoints[newIdx2 + 1]);\n        var stackedOnPoint = getStackedOnPoint(newDataOldCoordInfo, oldCoordSys, newData, newIdx);\n        currStackedPoints.push(stackedOnPoint[0], stackedOnPoint[1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(newIdx));\n        break;\n      case '-':\n        pointAdded = false;\n    }\n    // Original indices\n    if (pointAdded) {\n      status.push(diffItem);\n      sortedIndices.push(sortedIndices.length);\n    }\n  }\n  // Diff result may be crossed if all items are changed\n  // Sort by data index\n  sortedIndices.sort(function (a, b) {\n    return rawIndices[a] - rawIndices[b];\n  });\n  var len = currPoints.length;\n  var sortedCurrPoints = createFloat32Array(len);\n  var sortedNextPoints = createFloat32Array(len);\n  var sortedCurrStackedPoints = createFloat32Array(len);\n  var sortedNextStackedPoints = createFloat32Array(len);\n  var sortedStatus = [];\n  for (var i = 0; i < sortedIndices.length; i++) {\n    var idx = sortedIndices[i];\n    var i2 = i * 2;\n    var idx2 = idx * 2;\n    sortedCurrPoints[i2] = currPoints[idx2];\n    sortedCurrPoints[i2 + 1] = currPoints[idx2 + 1];\n    sortedNextPoints[i2] = nextPoints[idx2];\n    sortedNextPoints[i2 + 1] = nextPoints[idx2 + 1];\n    sortedCurrStackedPoints[i2] = currStackedPoints[idx2];\n    sortedCurrStackedPoints[i2 + 1] = currStackedPoints[idx2 + 1];\n    sortedNextStackedPoints[i2] = nextStackedPoints[idx2];\n    sortedNextStackedPoints[i2 + 1] = nextStackedPoints[idx2 + 1];\n    sortedStatus[i] = status[idx];\n  }\n  return {\n    current: sortedCurrPoints,\n    next: sortedNextPoints,\n    stackedOnCurrent: sortedCurrStackedPoints,\n    stackedOnNext: sortedNextStackedPoints,\n    status: sortedStatus\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}