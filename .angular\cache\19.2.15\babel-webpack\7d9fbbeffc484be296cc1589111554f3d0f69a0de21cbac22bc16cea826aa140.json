{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString, createHashMap, hasOwn } from 'zrender/lib/core/util.js';\nimport parseGeoJson from './parseGeoJson.js';\n// Built-in GEO fixer.\nimport fixNanhai from './fix/nanhai.js';\nimport fixTextCoord from './fix/textCoord.js';\nimport fixDiaoyuIsland from './fix/diaoyuIsland.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nvar DEFAULT_NAME_PROPERTY = 'name';\nvar GeoJSONResource = /** @class */function () {\n  function GeoJSONResource(mapName, geoJSON, specialAreas) {\n    this.type = 'geoJSON';\n    this._parsedMap = createHashMap();\n    this._mapName = mapName;\n    this._specialAreas = specialAreas;\n    // PENDING: delay the parse to the first usage to rapid up the FMP?\n    this._geoJSON = parseInput(geoJSON);\n  }\n  /**\r\n   * @param nameMap can be null/undefined\r\n   * @param nameProperty can be null/undefined\r\n   */\n  GeoJSONResource.prototype.load = function (nameMap, nameProperty) {\n    nameProperty = nameProperty || DEFAULT_NAME_PROPERTY;\n    var parsed = this._parsedMap.get(nameProperty);\n    if (!parsed) {\n      var rawRegions = this._parseToRegions(nameProperty);\n      parsed = this._parsedMap.set(nameProperty, {\n        regions: rawRegions,\n        boundingRect: calculateBoundingRect(rawRegions)\n      });\n    }\n    var regionsMap = createHashMap();\n    var finalRegions = [];\n    each(parsed.regions, function (region) {\n      var regionName = region.name;\n      // Try use the alias in geoNameMap\n      if (nameMap && hasOwn(nameMap, regionName)) {\n        region = region.cloneShallow(regionName = nameMap[regionName]);\n      }\n      finalRegions.push(region);\n      regionsMap.set(regionName, region);\n    });\n    return {\n      regions: finalRegions,\n      boundingRect: parsed.boundingRect || new BoundingRect(0, 0, 0, 0),\n      regionsMap: regionsMap\n    };\n  };\n  GeoJSONResource.prototype._parseToRegions = function (nameProperty) {\n    var mapName = this._mapName;\n    var geoJSON = this._geoJSON;\n    var rawRegions;\n    // https://jsperf.com/try-catch-performance-overhead\n    try {\n      rawRegions = geoJSON ? parseGeoJson(geoJSON, nameProperty) : [];\n    } catch (e) {\n      throw new Error('Invalid geoJson format\\n' + e.message);\n    }\n    fixNanhai(mapName, rawRegions);\n    each(rawRegions, function (region) {\n      var regionName = region.name;\n      fixTextCoord(mapName, region);\n      fixDiaoyuIsland(mapName, region);\n      // Some area like Alaska in USA map needs to be tansformed\n      // to look better\n      var specialArea = this._specialAreas && this._specialAreas[regionName];\n      if (specialArea) {\n        region.transformTo(specialArea.left, specialArea.top, specialArea.width, specialArea.height);\n      }\n    }, this);\n    return rawRegions;\n  };\n  /**\r\n   * Only for exporting to users.\r\n   * **MUST NOT** used internally.\r\n   */\n  GeoJSONResource.prototype.getMapForUser = function () {\n    return {\n      // For backward compatibility, use geoJson\n      // PENDING: it has been returning them without clone.\n      // do we need to avoid outsite modification?\n      geoJson: this._geoJSON,\n      geoJSON: this._geoJSON,\n      specialAreas: this._specialAreas\n    };\n  };\n  return GeoJSONResource;\n}();\nexport { GeoJSONResource };\nfunction calculateBoundingRect(regions) {\n  var rect;\n  for (var i = 0; i < regions.length; i++) {\n    var regionRect = regions[i].getBoundingRect();\n    rect = rect || regionRect.clone();\n    rect.union(regionRect);\n  }\n  return rect;\n}\nfunction parseInput(source) {\n  return !isString(source) ? source : typeof JSON !== 'undefined' && JSON.parse ? JSON.parse(source) : new Function('return (' + source + ');')();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}