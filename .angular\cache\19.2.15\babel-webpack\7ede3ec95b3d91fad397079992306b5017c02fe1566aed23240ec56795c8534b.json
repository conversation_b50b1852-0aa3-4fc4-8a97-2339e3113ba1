{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\r\n * @param extent Both extent[0] and extent[1] should be valid number.\r\n *               Should be extent[0] < extent[1].\r\n * @param splitNumber splitNumber should be >= 1.\r\n */\nexport function intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var span = extent[1] - extent[0];\n  var interval = result.interval = nice(span / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\r\n * @return interval precision\r\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nexport function normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nexport function scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}