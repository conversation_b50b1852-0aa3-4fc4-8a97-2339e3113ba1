{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Malayalam [ml]\n//! author : <PERSON> : https://github.com/floydpink\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ml = moment.defineLocale('ml', {\n    months: 'ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ'.split('_'),\n    monthsShort: 'ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച'.split('_'),\n    weekdaysShort: 'ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി'.split('_'),\n    weekdaysMin: 'ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm -നു',\n      LTS: 'A h:mm:ss -നു',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm -നു',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm -നു'\n    },\n    calendar: {\n      sameDay: '[ഇന്ന്] LT',\n      nextDay: '[നാളെ] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[ഇന്നലെ] LT',\n      lastWeek: '[കഴിഞ്ഞ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s കഴിഞ്ഞ്',\n      past: '%s മുൻപ്',\n      s: 'അൽപ നിമിഷങ്ങൾ',\n      ss: '%d സെക്കൻഡ്',\n      m: 'ഒരു മിനിറ്റ്',\n      mm: '%d മിനിറ്റ്',\n      h: 'ഒരു മണിക്കൂർ',\n      hh: '%d മണിക്കൂർ',\n      d: 'ഒരു ദിവസം',\n      dd: '%d ദിവസം',\n      M: 'ഒരു മാസം',\n      MM: '%d മാസം',\n      y: 'ഒരു വർഷം',\n      yy: '%d വർഷം'\n    },\n    meridiemParse: /രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'രാത്രി' && hour >= 4 || meridiem === 'ഉച്ച കഴിഞ്ഞ്' || meridiem === 'വൈകുന്നേരം') {\n        return hour + 12;\n      } else {\n        return hour;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'രാത്രി';\n      } else if (hour < 12) {\n        return 'രാവിലെ';\n      } else if (hour < 17) {\n        return 'ഉച്ച കഴിഞ്ഞ്';\n      } else if (hour < 20) {\n        return 'വൈകുന്നേരം';\n      } else {\n        return 'രാത്രി';\n      }\n    }\n  });\n  return ml;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}