{"ast": null, "code": "import { normalizeRadian } from './util.js';\nvar PI2 = Math.PI * 2;\nexport function containStroke(cx, cy, r, startAngle, endAngle, anticlockwise, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  if (d - _l > r || d + _l < r) {\n    return false;\n  }\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    return true;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  return angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}