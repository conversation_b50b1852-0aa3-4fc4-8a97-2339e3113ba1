{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/walk-through/view-all-walk-through\"];\nconst _c1 = () => [\"/user-management/user-management-view\"];\nconst _c2 = () => [\"/content-management/content-management-system-view\"];\nconst _c3 = () => [\"/role-management/role-management-view\"];\nconst _c4 = () => [\"/setting/bank-account\"];\nconst _c5 = () => [\"/exam-form/exam-form-view\"];\nconst _c6 = () => [\"/setting/prog-cond-sett\"];\nconst _c7 = () => [\"/setting/program-categories\"];\nexport let SettingDashboardComponent = /*#__PURE__*/(() => {\n  class SettingDashboardComponent {\n    languageService;\n    translate;\n    imagesPathesService;\n    constructor(languageService, translate, imagesPathesService) {\n      this.languageService = languageService;\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.setCurrentLang();\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('SETTING_PG.TITLE'));\n    }\n    static ɵfac = function SettingDashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingDashboardComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingDashboardComponent,\n      selectors: [[\"app-setting-dashboard\"]],\n      decls: 81,\n      vars: 45,\n      consts: [[1, \"row\", \"setting-list\"], [1, \"row\", \"contanier_setting-card\"], [1, \"setting-card\", \"col-xl-5\", \"col-lg-5\", \"col-md-5\", \"col-sm-12\", \"col-xs-12\", \"m-2\", 3, \"routerLink\"], [1, \"row\"], [1, \"col-xl-7\", \"col-lg-7\", \"col-md-7\", \"col-sm-7\", \"col-xs-7\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-4\"], [\"alt\", \"\", 1, \"MaskGroup\", 3, \"src\"], [1, \"setting-text\"]],\n      template: function SettingDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"a\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"p\");\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"span\");\n          i0.ɵɵelement(11, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"a\", 2)(13, \"div\", 3)(14, \"div\", 4)(15, \"div\", 5)(16, \"p\");\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 6)(20, \"span\");\n          i0.ɵɵelement(21, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"a\", 2)(23, \"div\", 3)(24, \"div\", 4)(25, \"div\", 5)(26, \"p\");\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 6)(30, \"span\");\n          i0.ɵɵelement(31, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(32, \"a\", 2)(33, \"div\", 3)(34, \"div\", 4)(35, \"div\", 5)(36, \"p\");\n          i0.ɵɵtext(37);\n          i0.ɵɵpipe(38, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 6)(40, \"span\");\n          i0.ɵɵelement(41, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"a\", 2)(43, \"div\", 3)(44, \"div\", 4)(45, \"div\", 5)(46, \"p\", 8);\n          i0.ɵɵtext(47);\n          i0.ɵɵpipe(48, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 6)(50, \"span\");\n          i0.ɵɵelement(51, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(52, \"a\", 2)(53, \"div\", 3)(54, \"div\", 4)(55, \"div\", 5)(56, \"p\");\n          i0.ɵɵtext(57);\n          i0.ɵɵpipe(58, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 6)(60, \"span\");\n          i0.ɵɵelement(61, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(62, \"a\", 2)(63, \"div\", 3)(64, \"div\", 4)(65, \"div\", 5)(66, \"p\");\n          i0.ɵɵtext(67);\n          i0.ɵɵpipe(68, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"div\", 6)(70, \"span\");\n          i0.ɵɵelement(71, \"img\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"a\", 2)(73, \"div\", 3)(74, \"div\", 4)(75, \"div\", 5)(76, \"p\");\n          i0.ɵɵtext(77, \"\\u062A\\u0642\\u0633\\u064A\\u0645 \\u0627\\u0644\\u0628\\u0631\\u0646\\u0627\\u0645\\u062C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"div\", 6)(79, \"span\");\n          i0.ɵɵelement(80, \"img\", 7);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(37, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 23, \"SETTING_PG.PROVIDED_SCREEN\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.provided_screens, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(38, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 25, \"SETTING_PG.USER_MANAGEMENT\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.user_management, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(39, _c2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 27, \"SETTING_PG.ABOUT_MOSTANIR\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.about, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(40, _c3));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 29, \"SETTING_PG.POWER\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.permissions, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(41, _c4));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(48, 31, \"SETTING_PG.BANK_ACCOUNT\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.bank_accounts, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(42, _c5));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(58, 33, \"SETTING_PG.EXAMS\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.forms, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(43, _c6));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(68, 35, \"SETTING_PG.PROGRAM_COND\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.program_conditions, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(44, _c7));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.programes, i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [CommonModule, TranslateModule, i2.TranslatePipe, RouterModule, i4.RouterLink],\n      styles: [\".setting-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem 2rem 10rem;margin:1rem;margin-bottom:0;max-height:85vh;min-height:80vh}.setting-list[_ngcontent-%COMP%]   .setting-card[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border:.063rem solid #d6d7d8;border-radius:.75rem;opacity:1;padding:0rem;text-decoration:none}.setting-list[_ngcontent-%COMP%]   .MaskGroup[_ngcontent-%COMP%]{height:8rem}.setting-list[_ngcontent-%COMP%]   .contanier_setting-card[_ngcontent-%COMP%]{height:78vh;overflow-y:auto}.setting-list[_ngcontent-%COMP%]   .setting-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:700;font-size:1.125rem;letter-spacing:-.016rem;color:var(--main_color);opacity:1;margin-bottom:15%!important;margin-top:15%;margin-right:15%;text-align:right}.setting-list[_ngcontent-%COMP%]   .setting-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:lang(en){text-align:left}@media all and (min-width: 19in){.setting-list[_ngcontent-%COMP%]{max-height:90vh}.contanier_setting-card[_ngcontent-%COMP%]{height:80vh}}@media (min-width: 48rem){.col-lg-5[_ngcontent-%COMP%]{flex:0 0 47.666667%;max-width:47.666667%}}@media (max-width: 64rem){.contanier_setting-card[_ngcontent-%COMP%]{height:80vh}.MaskGroup[_ngcontent-%COMP%]{height:7rem}.setting-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.15rem}}@media (max-width: 48rem){.contanier_setting-card[_ngcontent-%COMP%]{height:80vh}}\"]\n    });\n  }\n  return SettingDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}