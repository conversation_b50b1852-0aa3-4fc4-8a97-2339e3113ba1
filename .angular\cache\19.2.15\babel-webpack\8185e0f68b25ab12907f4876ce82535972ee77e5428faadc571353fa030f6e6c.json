{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../helper/BrushController.js';\nimport { layoutCovers } from './visualEncoding.js';\nimport ComponentView from '../../view/Component.js';\nvar BrushView = /** @class */function (_super) {\n  __extends(BrushView, _super);\n  function BrushView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BrushView.type;\n    return _this;\n  }\n  BrushView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n    this.model;\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this)).mount();\n  };\n  BrushView.prototype.render = function (brushModel, ecModel, api, payload) {\n    this.model = brushModel;\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateTransform = function (brushModel, ecModel, api, payload) {\n    // PENDING: `updateTransform` is a little tricky, whose layout need\n    // to be calculate mandatorily and other stages will not be performed.\n    // Take care the correctness of the logic. See #11754 .\n    layoutCovers(ecModel);\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateVisual = function (brushModel, ecModel, api, payload) {\n    this.updateTransform(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateView = function (brushModel, ecModel, api, payload) {\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype._updateController = function (brushModel, ecModel, api, payload) {\n    // Do not update controller when drawing.\n    (!payload || payload.$from !== brushModel.id) && this._brushController.setPanels(brushModel.brushTargetManager.makePanelOpts(api)).enableBrush(brushModel.brushOption).updateCovers(brushModel.areas.slice());\n  };\n  // updateLayout: updateController,\n  // updateVisual: updateController,\n  BrushView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  BrushView.prototype._onBrush = function (eventParam) {\n    var modelId = this.model.id;\n    var areas = this.model.brushTargetManager.setOutputRanges(eventParam.areas, this.ecModel);\n    // Action is not dispatched on drag end, because the drag end\n    // emits the same params with the last drag move event, and\n    // may have some delay when using touch pad, which makes\n    // animation not smooth (when using debounce).\n    (!eventParam.isEnd || eventParam.removeOnClick) && this.api.dispatchAction({\n      type: 'brush',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n    eventParam.isEnd && this.api.dispatchAction({\n      type: 'brushEnd',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n  };\n  BrushView.type = 'brush';\n  return BrushView;\n}(ComponentView);\nexport default BrushView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}