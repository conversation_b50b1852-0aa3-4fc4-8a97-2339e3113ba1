{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ViewConditionSettingComponent } from './view-condition-setting/view-condition-setting.component';\nimport { AddConditionSettingComponent } from './add-condition-setting/add-condition-setting.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ProgramConditionSettingComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-add-condition-setting\", 6);\n    i0.ɵɵlistener(\"closeOverlay\", function ProgramConditionSettingComponent_div_4_Template_app_add_condition_setting_closeOverlay_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForm());\n    })(\"addCustomCondition\", function ProgramConditionSettingComponent_div_4_Template_app_add_condition_setting_addCustomCondition_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addCustomCondition());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"modelEdit\", ctx_r1.modelEdit);\n  }\n}\nexport let ProgramConditionSettingComponent = /*#__PURE__*/(() => {\n  class ProgramConditionSettingComponent {\n    CustomCondition;\n    modelEdit = {};\n    showAddForm = false;\n    constructor() {}\n    ngOnInit() {}\n    closeForm() {\n      this.showAddForm = false;\n    }\n    addCustomCondition() {\n      this.closeForm();\n      this.CustomCondition?.getProgramConditionsLis();\n    }\n    openAddEditOverLay(event) {\n      this.showAddForm = true;\n      //  pass object deatils to form \n      this.modelEdit = event;\n    }\n    static ɵfac = function ProgramConditionSettingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgramConditionSettingComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgramConditionSettingComponent,\n      selectors: [[\"app-program-condition-setting\"]],\n      viewQuery: function ProgramConditionSettingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ViewConditionSettingComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.CustomCondition = _t.first);\n        }\n      },\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [3, \"addEditCoidition\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeOverlay\", \"addCustomCondition\", \"modelEdit\"]],\n      template: function ProgramConditionSettingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-view-condition-setting\", 3);\n          i0.ɵɵlistener(\"addEditCoidition\", function ProgramConditionSettingComponent_Template_app_view_condition_setting_addEditCoidition_3_listener($event) {\n            return ctx.openAddEditOverLay($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, ProgramConditionSettingComponent_div_4_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, ViewConditionSettingComponent, AddConditionSettingComponent],\n      encapsulation: 2\n    });\n  }\n  return ProgramConditionSettingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}