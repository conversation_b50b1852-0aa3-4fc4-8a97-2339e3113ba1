{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar BarSeriesModel = /** @class */function (_super) {\n  __extends(BarSeriesModel, _super);\n  function BarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BarSeriesModel.type;\n    return _this;\n  }\n  BarSeriesModel.prototype.getInitialData = function () {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true,\n      createInvertedIndices: !!this.get('realtimeSort', true) || null\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  BarSeriesModel.prototype.getProgressive = function () {\n    // Do not support progressive in normal mode.\n    return this.get('large') ? this.get('progressive') : false;\n  };\n  /**\r\n   * @override\r\n   */\n  BarSeriesModel.prototype.getProgressiveThreshold = function () {\n    // Do not support progressive in normal mode.\n    var progressiveThreshold = this.get('progressiveThreshold');\n    var largeThreshold = this.get('largeThreshold');\n    if (largeThreshold > progressiveThreshold) {\n      progressiveThreshold = largeThreshold;\n    }\n    return progressiveThreshold;\n  };\n  BarSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.rect(data.getItemLayout(dataIndex));\n  };\n  BarSeriesModel.type = 'series.bar';\n  BarSeriesModel.dependencies = ['grid', 'polar'];\n  BarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    // If clipped\n    // Only available on cartesian2d\n    clip: true,\n    roundCap: false,\n    showBackground: false,\n    backgroundStyle: {\n      color: 'rgba(180, 180, 180, 0.2)',\n      borderColor: null,\n      borderWidth: 0,\n      borderType: 'solid',\n      borderRadius: 0,\n      shadowBlur: 0,\n      shadowColor: null,\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    realtimeSort: false\n  });\n  return BarSeriesModel;\n}(BaseBarSeriesModel);\nexport default BarSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}