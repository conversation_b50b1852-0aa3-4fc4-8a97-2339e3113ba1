{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeToArray } from '../../util/model.js';\nvar DEFAULT_TOOLBOX_BTNS = ['rect', 'polygon', 'keep', 'clear'];\nexport default function brushPreprocessor(option, isNew) {\n  var brushComponents = normalizeToArray(option ? option.brush : []);\n  if (!brushComponents.length) {\n    return;\n  }\n  var brushComponentSpecifiedBtns = [];\n  zrUtil.each(brushComponents, function (brushOpt) {\n    var tbs = brushOpt.hasOwnProperty('toolbox') ? brushOpt.toolbox : [];\n    if (tbs instanceof Array) {\n      brushComponentSpecifiedBtns = brushComponentSpecifiedBtns.concat(tbs);\n    }\n  });\n  var toolbox = option && option.toolbox;\n  if (zrUtil.isArray(toolbox)) {\n    toolbox = toolbox[0];\n  }\n  if (!toolbox) {\n    toolbox = {\n      feature: {}\n    };\n    option.toolbox = [toolbox];\n  }\n  var toolboxFeature = toolbox.feature || (toolbox.feature = {});\n  var toolboxBrush = toolboxFeature.brush || (toolboxFeature.brush = {});\n  var brushTypes = toolboxBrush.type || (toolboxBrush.type = []);\n  brushTypes.push.apply(brushTypes, brushComponentSpecifiedBtns);\n  removeDuplicate(brushTypes);\n  if (isNew && !brushTypes.length) {\n    brushTypes.push.apply(brushTypes, DEFAULT_TOOLBOX_BTNS);\n  }\n}\nfunction removeDuplicate(arr) {\n  var map = {};\n  zrUtil.each(arr, function (val) {\n    map[val] = 1;\n  });\n  arr.length = 0;\n  zrUtil.each(map, function (flag, val) {\n    arr.push(val);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}