{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO Axis scale\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Polar, { polarDimensions } from './Polar.js';\nimport { parsePercent } from '../../util/number.js';\nimport { createScaleByModel, niceScaleExtent, getDataDimensionsOnAxis } from '../../coord/axisHelper.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\n/**\r\n * Resize method bound to the polar\r\n */\nfunction resizePolar(polar, polarModel, api) {\n  var center = polarModel.get('center');\n  var width = api.getWidth();\n  var height = api.getHeight();\n  polar.cx = parsePercent(center[0], width);\n  polar.cy = parsePercent(center[1], height);\n  var radiusAxis = polar.getRadiusAxis();\n  var size = Math.min(width, height) / 2;\n  var radius = polarModel.get('radius');\n  if (radius == null) {\n    radius = [0, '100%'];\n  } else if (!zrUtil.isArray(radius)) {\n    // r0 = 0\n    radius = [0, radius];\n  }\n  var parsedRadius = [parsePercent(radius[0], size), parsePercent(radius[1], size)];\n  radiusAxis.inverse ? radiusAxis.setExtent(parsedRadius[1], parsedRadius[0]) : radiusAxis.setExtent(parsedRadius[0], parsedRadius[1]);\n}\n/**\r\n * Update polar\r\n */\nfunction updatePolarScale(ecModel, api) {\n  var polar = this;\n  var angleAxis = polar.getAngleAxis();\n  var radiusAxis = polar.getRadiusAxis();\n  // Reset scale\n  angleAxis.scale.setExtent(Infinity, -Infinity);\n  radiusAxis.scale.setExtent(Infinity, -Infinity);\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.coordinateSystem === polar) {\n      var data_1 = seriesModel.getData();\n      zrUtil.each(getDataDimensionsOnAxis(data_1, 'radius'), function (dim) {\n        radiusAxis.scale.unionExtentFromData(data_1, dim);\n      });\n      zrUtil.each(getDataDimensionsOnAxis(data_1, 'angle'), function (dim) {\n        angleAxis.scale.unionExtentFromData(data_1, dim);\n      });\n    }\n  });\n  niceScaleExtent(angleAxis.scale, angleAxis.model);\n  niceScaleExtent(radiusAxis.scale, radiusAxis.model);\n  // Fix extent of category angle axis\n  if (angleAxis.type === 'category' && !angleAxis.onBand) {\n    var extent = angleAxis.getExtent();\n    var diff = 360 / angleAxis.scale.count();\n    angleAxis.inverse ? extent[1] += diff : extent[1] -= diff;\n    angleAxis.setExtent(extent[0], extent[1]);\n  }\n}\nfunction isAngleAxisModel(axisModel) {\n  return axisModel.mainType === 'angleAxis';\n}\n/**\r\n * Set common axis properties\r\n */\nfunction setAxis(axis, axisModel) {\n  var _a;\n  axis.type = axisModel.get('type');\n  axis.scale = createScaleByModel(axisModel);\n  axis.onBand = axisModel.get('boundaryGap') && axis.type === 'category';\n  axis.inverse = axisModel.get('inverse');\n  if (isAngleAxisModel(axisModel)) {\n    axis.inverse = axis.inverse !== axisModel.get('clockwise');\n    var startAngle = axisModel.get('startAngle');\n    var endAngle = (_a = axisModel.get('endAngle')) !== null && _a !== void 0 ? _a : startAngle + (axis.inverse ? -360 : 360);\n    axis.setExtent(startAngle, endAngle);\n  }\n  // Inject axis instance\n  axisModel.axis = axis;\n  axis.model = axisModel;\n}\nvar polarCreator = {\n  dimensions: polarDimensions,\n  create: function (ecModel, api) {\n    var polarList = [];\n    ecModel.eachComponent('polar', function (polarModel, idx) {\n      var polar = new Polar(idx + '');\n      // Inject resize and update method\n      polar.update = updatePolarScale;\n      var radiusAxis = polar.getRadiusAxis();\n      var angleAxis = polar.getAngleAxis();\n      var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n      var angleAxisModel = polarModel.findAxisModel('angleAxis');\n      setAxis(radiusAxis, radiusAxisModel);\n      setAxis(angleAxis, angleAxisModel);\n      resizePolar(polar, polarModel, api);\n      polarList.push(polar);\n      polarModel.coordinateSystem = polar;\n      polar.model = polarModel;\n    });\n    // Inject coordinateSystem to series\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.get('coordinateSystem') === 'polar') {\n        var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!polarModel) {\n            throw new Error('Polar \"' + zrUtil.retrieve(seriesModel.get('polarIndex'), seriesModel.get('polarId'), 0) + '\" not found');\n          }\n        }\n        seriesModel.coordinateSystem = polarModel.coordinateSystem;\n      }\n    });\n    return polarList;\n  }\n};\nexport default polarCreator;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}