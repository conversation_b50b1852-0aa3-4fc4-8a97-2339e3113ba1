{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SettingRoutingModule } from './setting-routing.module';\nimport { SettingDashboardComponent } from './components/setting-dashboard/setting-dashboard.component';\nimport { ProgramConditionSettingComponent } from './components/program-condition-setting/program-condition-setting.component';\nimport { AddConditionSettingComponent } from './components/program-condition-setting/add-condition-setting/add-condition-setting.component';\nimport { ViewConditionSettingComponent } from './components/program-condition-setting/view-condition-setting/view-condition-setting.component';\nimport { ProgramCategoriesComponent } from './components/program-categories/program-categories.component';\nimport { ViewProgramCategoriesComponent } from './components/program-categories/view-program-categories/view-program-categories.component';\nimport { AddProgramCategoriesComponent } from './components/program-categories/add-program-categories/add-program-categories.component';\nimport { BankAccountViewComponent } from './components/bank-account-setting/bank-account-view/bank-account-view.component';\nimport { AddEditBankAccountComponent } from './components/bank-account-setting/bank-account-view/add-edit-bank-account/add-edit-bank-account.component';\nimport { BankAccountListComponent } from './components/bank-account-setting/bank-account-view/bank-account-list/bank-account-list.component';\nlet SettingModule = class SettingModule {};\nSettingModule = __decorate([NgModule({\n  declarations: [ProgramConditionSettingComponent, AddConditionSettingComponent, ViewConditionSettingComponent, ProgramCategoriesComponent, ViewProgramCategoriesComponent, AddProgramCategoriesComponent, BankAccountViewComponent, AddEditBankAccountComponent, BankAccountListComponent],\n  imports: [CommonModule, SettingRoutingModule, ReactiveFormsModule, FormsModule, TranslateModule, SettingDashboardComponent\n  // SharedModule\n  ],\n  exports: [SettingDashboardComponent]\n})], SettingModule);\nexport { SettingModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}