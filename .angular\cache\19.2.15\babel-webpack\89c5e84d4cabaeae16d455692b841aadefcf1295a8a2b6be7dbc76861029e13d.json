{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewChild, Output, Input, NgModule } from '@angular/core';\nimport { fromEvent, Subject, from } from 'rxjs';\nimport { takeUntil, debounceTime, filter } from 'rxjs/operators';\nimport * as PDFJS from 'pdfjs-dist/build/pdf';\nimport * as PDFJSViewer from 'pdfjs-dist/web/pdf_viewer';\nconst _c0 = [\"pdfViewerContainer\"];\nfunction createEventBus(pdfJsViewer, destroy$) {\n  const globalEventBus = new pdfJsViewer.EventBus();\n  attachDOMEventsToEventBus(globalEventBus, destroy$);\n  return globalEventBus;\n}\nfunction attachDOMEventsToEventBus(eventBus, destroy$) {\n  fromEvent(eventBus, 'documentload').pipe(takeUntil(destroy$)).subscribe(() => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('documentload', true, true, {});\n    window.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'pagerendered').pipe(takeUntil(destroy$)).subscribe(({\n    pageNumber,\n    cssTransform,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('pagerendered', true, true, {\n      pageNumber,\n      cssTransform\n    });\n    source.div.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'textlayerrendered').pipe(takeUntil(destroy$)).subscribe(({\n    pageNumber,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('textlayerrendered', true, true, {\n      pageNumber\n    });\n    source.textLayerDiv.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'pagechanging').pipe(takeUntil(destroy$)).subscribe(({\n    pageNumber,\n    source\n  }) => {\n    const event = document.createEvent('UIEvents');\n    event.initEvent('pagechanging', true, true);\n    /* tslint:disable:no-string-literal */\n    event['pageNumber'] = pageNumber;\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'pagesinit').pipe(takeUntil(destroy$)).subscribe(({\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('pagesinit', true, true, null);\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'pagesloaded').pipe(takeUntil(destroy$)).subscribe(({\n    pagesCount,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('pagesloaded', true, true, {\n      pagesCount\n    });\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'scalechange').pipe(takeUntil(destroy$)).subscribe(({\n    scale,\n    presetValue,\n    source\n  }) => {\n    const event = document.createEvent('UIEvents');\n    event.initEvent('scalechange', true, true);\n    /* tslint:disable:no-string-literal */\n    event['scale'] = scale;\n    /* tslint:disable:no-string-literal */\n    event['presetValue'] = presetValue;\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'updateviewarea').pipe(takeUntil(destroy$)).subscribe(({\n    location,\n    source\n  }) => {\n    const event = document.createEvent('UIEvents');\n    event.initEvent('updateviewarea', true, true);\n    event['location'] = location;\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'find').pipe(takeUntil(destroy$)).subscribe(({\n    source,\n    type,\n    query,\n    phraseSearch,\n    caseSensitive,\n    highlightAll,\n    findPrevious\n  }) => {\n    if (source === window) {\n      return; // event comes from FirefoxCom, no need to replicate\n    }\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('find' + type, true, true, {\n      query,\n      phraseSearch,\n      caseSensitive,\n      highlightAll,\n      findPrevious\n    });\n    window.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'attachmentsloaded').pipe(takeUntil(destroy$)).subscribe(({\n    attachmentsCount,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('attachmentsloaded', true, true, {\n      attachmentsCount\n    });\n    source.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'sidebarviewchanged').pipe(takeUntil(destroy$)).subscribe(({\n    view,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('sidebarviewchanged', true, true, {\n      view\n    });\n    source.outerContainer.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'pagemode').pipe(takeUntil(destroy$)).subscribe(({\n    mode,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('pagemode', true, true, {\n      mode\n    });\n    source.pdfViewer.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'namedaction').pipe(takeUntil(destroy$)).subscribe(({\n    action,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('namedaction', true, true, {\n      action\n    });\n    source.pdfViewer.container.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'presentationmodechanged').pipe(takeUntil(destroy$)).subscribe(({\n    active,\n    switchInProgress\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('presentationmodechanged', true, true, {\n      active,\n      switchInProgress\n    });\n    window.dispatchEvent(event);\n  });\n  fromEvent(eventBus, 'outlineloaded').pipe(takeUntil(destroy$)).subscribe(({\n    outlineCount,\n    source\n  }) => {\n    const event = document.createEvent('CustomEvent');\n    event.initCustomEvent('outlineloaded', true, true, {\n      outlineCount\n    });\n    source.container.dispatchEvent(event);\n  });\n}\nfunction assign(obj, prop, value) {\n  obj[prop] = value;\n}\nfunction isSSR() {\n  return typeof window === 'undefined';\n}\n\n/**\n * Created by vadimdez on 21/06/16.\n */\nif (!isSSR()) {\n  assign(PDFJS, 'verbosity', PDFJS.VerbosityLevel.INFOS);\n}\nvar RenderTextMode = /*#__PURE__*/function (RenderTextMode) {\n  RenderTextMode[RenderTextMode[\"DISABLED\"] = 0] = \"DISABLED\";\n  RenderTextMode[RenderTextMode[\"ENABLED\"] = 1] = \"ENABLED\";\n  RenderTextMode[RenderTextMode[\"ENHANCED\"] = 2] = \"ENHANCED\";\n  return RenderTextMode;\n}(RenderTextMode || {});\nlet PdfViewerComponent = /*#__PURE__*/(() => {\n  class PdfViewerComponent {\n    constructor(element, ngZone) {\n      this.element = element;\n      this.ngZone = ngZone;\n      this.isVisible = false;\n      this._cMapsUrl = typeof PDFJS !== 'undefined' ? `https://unpkg.com/pdfjs-dist@${PDFJS.version}/cmaps/` : null;\n      this._imageResourcesPath = typeof PDFJS !== 'undefined' ? `https://unpkg.com/pdfjs-dist@${PDFJS.version}/web/images/` : null;\n      this._renderText = true;\n      this._renderTextMode = RenderTextMode.ENABLED;\n      this._stickToPage = false;\n      this._originalSize = true;\n      this._page = 1;\n      this._zoom = 1;\n      this._zoomScale = 'page-width';\n      this._rotation = 0;\n      this._showAll = true;\n      this._canAutoResize = true;\n      this._fitToPage = false;\n      this._externalLinkTarget = 'blank';\n      this._showBorders = false;\n      this.resizeTimeout = null;\n      this.pageScrollTimeout = null;\n      this.isInitialized = false;\n      this.destroy$ = new Subject();\n      this.afterLoadComplete = new EventEmitter();\n      this.pageRendered = new EventEmitter();\n      this.pageInitialized = new EventEmitter();\n      this.textLayerRendered = new EventEmitter();\n      this.onError = new EventEmitter();\n      this.onProgress = new EventEmitter();\n      this.pageChange = new EventEmitter(true);\n      if (isSSR()) {\n        return;\n      }\n      let pdfWorkerSrc;\n      const pdfJsVersion = PDFJS.version;\n      const versionSpecificPdfWorkerUrl = window[`pdfWorkerSrc${pdfJsVersion}`];\n      if (versionSpecificPdfWorkerUrl) {\n        pdfWorkerSrc = versionSpecificPdfWorkerUrl;\n      } else if (window.hasOwnProperty('pdfWorkerSrc') && typeof window.pdfWorkerSrc === 'string' && window.pdfWorkerSrc) {\n        pdfWorkerSrc = window.pdfWorkerSrc;\n      } else {\n        pdfWorkerSrc = `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfJsVersion}/legacy/build/pdf.worker.min.js`;\n      }\n      assign(PDFJS.GlobalWorkerOptions, 'workerSrc', pdfWorkerSrc);\n    }\n    set cMapsUrl(cMapsUrl) {\n      this._cMapsUrl = cMapsUrl;\n    }\n    set page(_page) {\n      _page = parseInt(_page, 10) || 1;\n      const originalPage = _page;\n      if (this._pdf) {\n        _page = this.getValidPageNumber(_page);\n      }\n      this._page = _page;\n      if (originalPage !== _page) {\n        this.pageChange.emit(_page);\n      }\n    }\n    set renderText(renderText) {\n      this._renderText = renderText;\n    }\n    set renderTextMode(renderTextMode) {\n      this._renderTextMode = renderTextMode;\n    }\n    set originalSize(originalSize) {\n      this._originalSize = originalSize;\n    }\n    set showAll(value) {\n      this._showAll = value;\n    }\n    set stickToPage(value) {\n      this._stickToPage = value;\n    }\n    set zoom(value) {\n      if (value <= 0) {\n        return;\n      }\n      this._zoom = value;\n    }\n    get zoom() {\n      return this._zoom;\n    }\n    set zoomScale(value) {\n      this._zoomScale = value;\n    }\n    get zoomScale() {\n      return this._zoomScale;\n    }\n    set rotation(value) {\n      if (!(typeof value === 'number' && value % 90 === 0)) {\n        console.warn('Invalid pages rotation angle.');\n        return;\n      }\n      this._rotation = value;\n    }\n    set externalLinkTarget(value) {\n      this._externalLinkTarget = value;\n    }\n    set autoresize(value) {\n      this._canAutoResize = Boolean(value);\n    }\n    set fitToPage(value) {\n      this._fitToPage = Boolean(value);\n    }\n    set showBorders(value) {\n      this._showBorders = Boolean(value);\n    }\n    static getLinkTarget(type) {\n      switch (type) {\n        case 'blank':\n          return PDFJSViewer.LinkTarget.BLANK;\n        case 'none':\n          return PDFJSViewer.LinkTarget.NONE;\n        case 'self':\n          return PDFJSViewer.LinkTarget.SELF;\n        case 'parent':\n          return PDFJSViewer.LinkTarget.PARENT;\n        case 'top':\n          return PDFJSViewer.LinkTarget.TOP;\n      }\n      return null;\n    }\n    ngAfterViewChecked() {\n      if (this.isInitialized) {\n        return;\n      }\n      const offset = this.pdfViewerContainer.nativeElement.offsetParent;\n      if (this.isVisible === true && offset == null) {\n        this.isVisible = false;\n        return;\n      }\n      if (this.isVisible === false && offset != null) {\n        this.isVisible = true;\n        setTimeout(() => {\n          this.initialize();\n          this.ngOnChanges({\n            src: this.src\n          });\n        });\n      }\n    }\n    ngOnInit() {\n      this.initialize();\n      this.setupResizeListener();\n    }\n    ngOnDestroy() {\n      this.clear();\n      this.destroy$.next();\n      this.loadingTask = null;\n    }\n    ngOnChanges(changes) {\n      if (isSSR() || !this.isVisible) {\n        return;\n      }\n      if ('src' in changes) {\n        this.loadPDF();\n      } else if (this._pdf) {\n        if ('renderText' in changes) {\n          this.pdfViewer.textLayerMode = this._renderText ? this._renderTextMode : RenderTextMode.DISABLED;\n          this.resetPdfDocument();\n        } else if ('showAll' in changes) {\n          this.setupViewer();\n          this.resetPdfDocument();\n        }\n        if ('page' in changes) {\n          const {\n            page\n          } = changes;\n          if (page.currentValue === this._latestScrolledPage) {\n            return;\n          }\n          // New form of page changing: The viewer will now jump to the specified page when it is changed.\n          // This behavior is introduced by using the PDFSinglePageViewer\n          this.pdfViewer.scrollPageIntoView({\n            pageNumber: this._page\n          });\n        }\n        this.update();\n      }\n    }\n    updateSize() {\n      from(this._pdf.getPage(this.pdfViewer.currentPageNumber)).pipe(takeUntil(this.destroy$)).subscribe({\n        next: page => {\n          const rotation = this._rotation + page.rotate;\n          const viewportWidth = page.getViewport({\n            scale: this._zoom,\n            rotation\n          }).width * PdfViewerComponent.CSS_UNITS;\n          let scale = this._zoom;\n          let stickToPage = true;\n          // Scale the document when it shouldn't be in original size or doesn't fit into the viewport\n          if (!this._originalSize || this._fitToPage && viewportWidth > this.pdfViewerContainer.nativeElement.clientWidth) {\n            const viewPort = page.getViewport({\n              scale: 1,\n              rotation\n            });\n            scale = this.getScale(viewPort.width, viewPort.height);\n            stickToPage = !this._stickToPage;\n          }\n          this.pdfViewer._setScale(scale, stickToPage);\n        }\n      });\n    }\n    clear() {\n      if (this.loadingTask && !this.loadingTask.destroyed) {\n        this.loadingTask.destroy();\n      }\n      if (this._pdf) {\n        this._latestScrolledPage = 0;\n        this._pdf.destroy();\n        this._pdf = null;\n        this.pdfViewer.setDocument(null);\n        this.pdfLinkService.setDocument(null, null);\n        this.pdfFindController.setDocument(null);\n      }\n    }\n    getPDFLinkServiceConfig() {\n      const linkTarget = PdfViewerComponent.getLinkTarget(this._externalLinkTarget);\n      if (linkTarget) {\n        return {\n          externalLinkTarget: linkTarget\n        };\n      }\n      return {};\n    }\n    initEventBus() {\n      this.eventBus = createEventBus(PDFJSViewer, this.destroy$);\n      fromEvent(this.eventBus, 'pagerendered').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        this.pageRendered.emit(event);\n      });\n      fromEvent(this.eventBus, 'pagesinit').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        this.pageInitialized.emit(event);\n      });\n      fromEvent(this.eventBus, 'pagechanging').pipe(takeUntil(this.destroy$)).subscribe(({\n        pageNumber\n      }) => {\n        if (this.pageScrollTimeout) {\n          clearTimeout(this.pageScrollTimeout);\n        }\n        this.pageScrollTimeout = window.setTimeout(() => {\n          this._latestScrolledPage = pageNumber;\n          this.pageChange.emit(pageNumber);\n        }, 100);\n      });\n      fromEvent(this.eventBus, 'textlayerrendered').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        this.textLayerRendered.emit(event);\n      });\n    }\n    initPDFServices() {\n      this.pdfLinkService = new PDFJSViewer.PDFLinkService({\n        eventBus: this.eventBus,\n        ...this.getPDFLinkServiceConfig()\n      });\n      this.pdfFindController = new PDFJSViewer.PDFFindController({\n        eventBus: this.eventBus,\n        linkService: this.pdfLinkService\n      });\n    }\n    getPDFOptions() {\n      return {\n        eventBus: this.eventBus,\n        container: this.element.nativeElement.querySelector('div'),\n        removePageBorders: !this._showBorders,\n        linkService: this.pdfLinkService,\n        textLayerMode: this._renderText ? this._renderTextMode : RenderTextMode.DISABLED,\n        findController: this.pdfFindController,\n        renderer: 'canvas',\n        l10n: undefined,\n        imageResourcesPath: this._imageResourcesPath\n      };\n    }\n    setupViewer() {\n      assign(PDFJS, 'disableTextLayer', !this._renderText);\n      this.initPDFServices();\n      if (this._showAll) {\n        this.pdfViewer = new PDFJSViewer.PDFViewer(this.getPDFOptions());\n      } else {\n        this.pdfViewer = new PDFJSViewer.PDFSinglePageViewer(this.getPDFOptions());\n      }\n      this.pdfLinkService.setViewer(this.pdfViewer);\n      this.pdfViewer._currentPageNumber = this._page;\n    }\n    getValidPageNumber(page) {\n      if (page < 1) {\n        return 1;\n      }\n      if (page > this._pdf.numPages) {\n        return this._pdf.numPages;\n      }\n      return page;\n    }\n    getDocumentParams() {\n      const srcType = typeof this.src;\n      if (!this._cMapsUrl) {\n        return this.src;\n      }\n      const params = {\n        cMapUrl: this._cMapsUrl,\n        cMapPacked: true,\n        enableXfa: true\n      };\n      if (srcType === 'string') {\n        params.url = this.src;\n      } else if (srcType === 'object') {\n        if (this.src.byteLength !== undefined) {\n          params.data = this.src;\n        } else {\n          Object.assign(params, this.src);\n        }\n      }\n      return params;\n    }\n    loadPDF() {\n      if (!this.src) {\n        return;\n      }\n      if (this.lastLoaded === this.src) {\n        this.update();\n        return;\n      }\n      this.clear();\n      this.setupViewer();\n      this.loadingTask = PDFJS.getDocument(this.getDocumentParams());\n      this.loadingTask.onProgress = progressData => {\n        this.onProgress.emit(progressData);\n      };\n      const src = this.src;\n      from(this.loadingTask.promise).pipe(takeUntil(this.destroy$)).subscribe({\n        next: pdf => {\n          this._pdf = pdf;\n          this.lastLoaded = src;\n          this.afterLoadComplete.emit(pdf);\n          this.resetPdfDocument();\n          this.update();\n        },\n        error: error => {\n          this.lastLoaded = null;\n          this.onError.emit(error);\n        }\n      });\n    }\n    update() {\n      this.page = this._page;\n      this.render();\n    }\n    render() {\n      this._page = this.getValidPageNumber(this._page);\n      if (this._rotation !== 0 || this.pdfViewer.pagesRotation !== this._rotation) {\n        setTimeout(() => {\n          this.pdfViewer.pagesRotation = this._rotation;\n        });\n      }\n      if (this._stickToPage) {\n        setTimeout(() => {\n          this.pdfViewer.currentPageNumber = this._page;\n        });\n      }\n      this.updateSize();\n    }\n    getScale(viewportWidth, viewportHeight) {\n      const borderSize = this._showBorders ? 2 * PdfViewerComponent.BORDER_WIDTH : 0;\n      const pdfContainerWidth = this.pdfViewerContainer.nativeElement.clientWidth - borderSize;\n      const pdfContainerHeight = this.pdfViewerContainer.nativeElement.clientHeight - borderSize;\n      if (pdfContainerHeight === 0 || viewportHeight === 0 || pdfContainerWidth === 0 || viewportWidth === 0) {\n        return 1;\n      }\n      let ratio = 1;\n      switch (this._zoomScale) {\n        case 'page-fit':\n          ratio = Math.min(pdfContainerHeight / viewportHeight, pdfContainerWidth / viewportWidth);\n          break;\n        case 'page-height':\n          ratio = pdfContainerHeight / viewportHeight;\n          break;\n        case 'page-width':\n        default:\n          ratio = pdfContainerWidth / viewportWidth;\n          break;\n      }\n      return this._zoom * ratio / PdfViewerComponent.CSS_UNITS;\n    }\n    resetPdfDocument() {\n      this.pdfLinkService.setDocument(this._pdf, null);\n      this.pdfFindController.setDocument(this._pdf);\n      this.pdfViewer.setDocument(this._pdf);\n    }\n    initialize() {\n      if (isSSR() || !this.isVisible) {\n        return;\n      }\n      this.isInitialized = true;\n      this.initEventBus();\n      this.setupViewer();\n    }\n    setupResizeListener() {\n      if (isSSR()) {\n        return;\n      }\n      this.ngZone.runOutsideAngular(() => {\n        fromEvent(window, 'resize').pipe(debounceTime(100), filter(() => this._canAutoResize && !!this._pdf), takeUntil(this.destroy$)).subscribe(() => {\n          this.updateSize();\n        });\n      });\n    }\n  }\n  PdfViewerComponent.CSS_UNITS = 96.0 / 72.0;\n  PdfViewerComponent.BORDER_WIDTH = 9;\n  PdfViewerComponent.ɵfac = function PdfViewerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PdfViewerComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  PdfViewerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PdfViewerComponent,\n    selectors: [[\"pdf-viewer\"]],\n    viewQuery: function PdfViewerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pdfViewerContainer = _t.first);\n      }\n    },\n    inputs: {\n      src: \"src\",\n      cMapsUrl: [0, \"c-maps-url\", \"cMapsUrl\"],\n      page: \"page\",\n      renderText: [0, \"render-text\", \"renderText\"],\n      renderTextMode: [0, \"render-text-mode\", \"renderTextMode\"],\n      originalSize: [0, \"original-size\", \"originalSize\"],\n      showAll: [0, \"show-all\", \"showAll\"],\n      stickToPage: [0, \"stick-to-page\", \"stickToPage\"],\n      zoom: \"zoom\",\n      zoomScale: [0, \"zoom-scale\", \"zoomScale\"],\n      rotation: \"rotation\",\n      externalLinkTarget: [0, \"external-link-target\", \"externalLinkTarget\"],\n      autoresize: \"autoresize\",\n      fitToPage: [0, \"fit-to-page\", \"fitToPage\"],\n      showBorders: [0, \"show-borders\", \"showBorders\"]\n    },\n    outputs: {\n      afterLoadComplete: \"after-load-complete\",\n      pageRendered: \"page-rendered\",\n      pageInitialized: \"pages-initialized\",\n      textLayerRendered: \"text-layer-rendered\",\n      onError: \"error\",\n      onProgress: \"on-progress\",\n      pageChange: \"pageChange\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[\"pdfViewerContainer\", \"\"], [1, \"ng2-pdf-viewer-container\"], [1, \"pdfViewer\"]],\n    template: function PdfViewerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\".ng2-pdf-viewer-container[_ngcontent-%COMP%]{overflow-x:auto;position:absolute;height:100%;width:100%;-webkit-overflow-scrolling:touch}[_nghost-%COMP%]{display:block;position:relative}[_nghost-%COMP%]    {--pdfViewer-padding-bottom: 0;--page-margin: 1px auto -8px;--page-border: 9px solid transparent;--spreadHorizontalWrapped-margin-LR: -3.5px;--zoom-factor: 1;--viewport-scale-factor: 1;--shadow: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAA1ElEQVQ4jbWUWw6EIAxFy2NFs/8NzR4UJhpqLsdi5mOmSSMUOfYWqv3S0gMr4XlYH/64gZa/gN3ANYA7KAXALt4ktoQ5MI9YxqaG8bWmsIysMuT6piSQCa4whZThCu8CM4zP9YJaKci9jicPq3NcBWYoPMGUlhG7ivtkB+gVyFY75wXghOvh8t5mto1Mdim6e+MBqH6XsY+YAwjpq3vGF7weTWQptLEDVCZvPTMl5JZZsdh47FHW6qFMyvLYqjcnmdFfY9Xk/KDOlzCusX2mi/ofM7MPkzBcSp4Q1/wAAAAASUVORK5CYII=);--viewer-container-height: 0;--annotation-unfocused-field-background: url(\\\"data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>\\\");--xfa-unfocused-field-background: url(\\\"data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>\\\")}@media screen and (forced-colors: active){[_nghost-%COMP%]    {--pdfViewer-padding-bottom: 9px;--page-margin: 9px auto 0;--page-border: none;--spreadHorizontalWrapped-margin-LR: 4.5px}}[_nghost-%COMP%]     .textLayer{position:absolute;text-align:initial;left:0;top:0;right:0;bottom:0;overflow:hidden;opacity:.2;line-height:1;-webkit-text-size-adjust:none;text-size-adjust:none;forced-color-adjust:none}[_nghost-%COMP%]     .textLayer span, [_nghost-%COMP%]     .textLayer br{color:transparent;position:absolute;white-space:pre;cursor:text;transform-origin:0% 0%}[_nghost-%COMP%]     .textLayer span.markedContent{top:0;height:0}[_nghost-%COMP%]     .textLayer .highlight{margin:-1px;padding:1px;background-color:#b400aa;border-radius:4px}[_nghost-%COMP%]     .textLayer .highlight.appended{position:initial}[_nghost-%COMP%]     .textLayer .highlight.begin{border-radius:4px 0 0 4px}[_nghost-%COMP%]     .textLayer .highlight.end{border-radius:0 4px 4px 0}[_nghost-%COMP%]     .textLayer .highlight.middle{border-radius:0}[_nghost-%COMP%]     .textLayer .highlight.selected{background-color:#006400}[_nghost-%COMP%]     .textLayer ::selection{background:blue}[_nghost-%COMP%]     .textLayer br::selection{background:transparent}[_nghost-%COMP%]     .textLayer .endOfContent{display:block;position:absolute;left:0;top:100%;right:0;bottom:0;z-index:-1;cursor:default;-webkit-user-select:none;user-select:none}[_nghost-%COMP%]     .textLayer .endOfContent.active{top:0}[_nghost-%COMP%]     .annotationLayer section{position:absolute;text-align:initial}[_nghost-%COMP%]     .annotationLayer .linkAnnotation>a, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.pushButton>a{position:absolute;font-size:1em;top:0;left:0;width:100%;height:100%}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.pushButton>canvas{position:relative;top:0;left:0;z-index:-1}[_nghost-%COMP%]     .annotationLayer .linkAnnotation>a:hover, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.pushButton>a:hover{opacity:.2;background:yellow;box-shadow:0 2px 10px #ff0}[_nghost-%COMP%]     .annotationLayer .textAnnotation img{position:absolute;cursor:pointer}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input, [_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea, [_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input{background-image:var(--annotation-unfocused-field-background);border:1px solid transparent;box-sizing:border-box;font-size:9px;height:100%;margin:0;padding:0 3px;vertical-align:top;width:100%}[_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select option{padding:0}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input{border-radius:50%}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea{font:message-box;font-size:9px;resize:none}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input[disabled], [_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea[disabled], [_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select[disabled], [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input[disabled], [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input[disabled]{background:none;border:1px solid transparent;cursor:not-allowed}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input:hover, [_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea:hover, [_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select:hover, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:hover, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input:hover{border:1px solid black}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input:focus, [_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea:focus, [_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select:focus{background:none;border:1px solid transparent}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input :focus, [_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation textarea :focus, [_nghost-%COMP%]     .annotationLayer .choiceWidgetAnnotation select :focus, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox :focus, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton :focus{background-image:none;background-color:transparent;outline:auto}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before{background-color:#000;content:\\\"\\\";display:block;position:absolute}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after{height:80%;left:45%;width:1px}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before{transform:rotate(45deg)}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after{transform:rotate(-45deg)}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before{border-radius:50%;height:50%;left:30%;top:20%;width:50%}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input.comb{font-family:monospace;padding-left:2px;padding-right:0}[_nghost-%COMP%]     .annotationLayer .textWidgetAnnotation input.comb:focus{width:103%}[_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.checkBox input, [_nghost-%COMP%]     .annotationLayer .buttonWidgetAnnotation.radioButton input{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding:0}[_nghost-%COMP%]     .annotationLayer .popupWrapper{position:absolute;width:20em}[_nghost-%COMP%]     .annotationLayer .popup{position:absolute;z-index:200;max-width:20em;background-color:#ff9;box-shadow:0 2px 5px #888;border-radius:2px;padding:6px;margin-left:5px;cursor:pointer;font:message-box;font-size:9px;white-space:normal;word-wrap:break-word}[_nghost-%COMP%]     .annotationLayer .popup>*{font-size:9px}[_nghost-%COMP%]     .annotationLayer .popup h1{display:inline-block}[_nghost-%COMP%]     .annotationLayer .popupDate{display:inline-block;margin-left:5px}[_nghost-%COMP%]     .annotationLayer .popupContent{border-top:1px solid #333333;margin-top:2px;padding-top:2px}[_nghost-%COMP%]     .annotationLayer .richText>*{white-space:pre-wrap}[_nghost-%COMP%]     .annotationLayer .highlightAnnotation, [_nghost-%COMP%]     .annotationLayer .underlineAnnotation, [_nghost-%COMP%]     .annotationLayer .squigglyAnnotation, [_nghost-%COMP%]     .annotationLayer .strikeoutAnnotation, [_nghost-%COMP%]     .annotationLayer .freeTextAnnotation, [_nghost-%COMP%]     .annotationLayer .lineAnnotation svg line, [_nghost-%COMP%]     .annotationLayer .squareAnnotation svg rect, [_nghost-%COMP%]     .annotationLayer .circleAnnotation svg ellipse, [_nghost-%COMP%]     .annotationLayer .polylineAnnotation svg polyline, [_nghost-%COMP%]     .annotationLayer .polygonAnnotation svg polygon, [_nghost-%COMP%]     .annotationLayer .caretAnnotation, [_nghost-%COMP%]     .annotationLayer .inkAnnotation svg polyline, [_nghost-%COMP%]     .annotationLayer .stampAnnotation, [_nghost-%COMP%]     .annotationLayer .fileAttachmentAnnotation{cursor:pointer}[_nghost-%COMP%]     .xfaLayer .highlight{margin:-1px;padding:1px;background-color:#efcbed;border-radius:4px}[_nghost-%COMP%]     .xfaLayer .highlight.appended{position:initial}[_nghost-%COMP%]     .xfaLayer .highlight.begin{border-radius:4px 0 0 4px}[_nghost-%COMP%]     .xfaLayer .highlight.end{border-radius:0 4px 4px 0}[_nghost-%COMP%]     .xfaLayer .highlight.middle{border-radius:0}[_nghost-%COMP%]     .xfaLayer .highlight.selected{background-color:#cbdfcb}[_nghost-%COMP%]     .xfaLayer ::selection{background:blue}[_nghost-%COMP%]     .xfaPage{overflow:hidden;position:relative}[_nghost-%COMP%]     .xfaContentarea{position:absolute}[_nghost-%COMP%]     .xfaPrintOnly{display:none}[_nghost-%COMP%]     .xfaLayer{position:absolute;text-align:initial;top:0;left:0;transform-origin:0 0;line-height:1.2}[_nghost-%COMP%]     .xfaLayer *{color:inherit;font:inherit;font-style:inherit;font-weight:inherit;font-feature-settings:inherit;font-kerning:inherit;letter-spacing:-.01px;text-align:inherit;text-decoration:inherit;box-sizing:border-box;background-color:transparent;padding:0;margin:0;pointer-events:auto;line-height:inherit}[_nghost-%COMP%]     .xfaLayer div{pointer-events:none}[_nghost-%COMP%]     .xfaLayer svg{pointer-events:none}[_nghost-%COMP%]     .xfaLayer svg *{pointer-events:none}[_nghost-%COMP%]     .xfaLayer a{color:#00f}[_nghost-%COMP%]     .xfaRich li{margin-left:3em}[_nghost-%COMP%]     .xfaFont{color:#000;font-weight:400;font-feature-settings:\\\"kern\\\" off;font-kerning:none;font-size:10px;font-style:normal;letter-spacing:0;text-decoration:none;vertical-align:0}[_nghost-%COMP%]     .xfaCaption{overflow:hidden;flex:0 0 auto}[_nghost-%COMP%]     .xfaCaptionForCheckButton{overflow:hidden;flex:1 1 auto}[_nghost-%COMP%]     .xfaLabel{height:100%;width:100%}[_nghost-%COMP%]     .xfaLeft{display:flex;flex-direction:row;align-items:center}[_nghost-%COMP%]     .xfaRight{display:flex;flex-direction:row-reverse;align-items:center}[_nghost-%COMP%]     .xfaLeft>.xfaCaption, [_nghost-%COMP%]     .xfaLeft>.xfaCaptionForCheckButton, [_nghost-%COMP%]     .xfaRight>.xfaCaption, [_nghost-%COMP%]     .xfaRight>.xfaCaptionForCheckButton{max-height:100%}[_nghost-%COMP%]     .xfaTop{display:flex;flex-direction:column;align-items:flex-start}[_nghost-%COMP%]     .xfaBottom{display:flex;flex-direction:column-reverse;align-items:flex-start}[_nghost-%COMP%]     .xfaTop>.xfaCaption, [_nghost-%COMP%]     .xfaTop>.xfaCaptionForCheckButton, [_nghost-%COMP%]     .xfaBottom>.xfaCaption, [_nghost-%COMP%]     .xfaBottom>.xfaCaptionForCheckButton{width:100%}[_nghost-%COMP%]     .xfaBorder{background-color:transparent;position:absolute;pointer-events:none}[_nghost-%COMP%]     .xfaWrapped{width:100%;height:100%}[_nghost-%COMP%]     .xfaTextfield:focus, [_nghost-%COMP%]     .xfaSelect:focus{background-image:none;background-color:transparent;outline:auto;outline-offset:-1px}[_nghost-%COMP%]     .xfaCheckbox:focus, [_nghost-%COMP%]     .xfaRadio:focus{outline:auto}[_nghost-%COMP%]     .xfaTextfield, [_nghost-%COMP%]     .xfaSelect{height:100%;width:100%;flex:1 1 auto;border:none;resize:none;background-image:var(--xfa-unfocused-field-background)}[_nghost-%COMP%]     .xfaTop>.xfaTextfield, [_nghost-%COMP%]     .xfaTop>.xfaSelect, [_nghost-%COMP%]     .xfaBottom>.xfaTextfield, [_nghost-%COMP%]     .xfaBottom>.xfaSelect{flex:0 1 auto}[_nghost-%COMP%]     .xfaButton{cursor:pointer;width:100%;height:100%;border:none;text-align:center}[_nghost-%COMP%]     .xfaLink{width:100%;height:100%;position:absolute;top:0;left:0}[_nghost-%COMP%]     .xfaCheckbox, [_nghost-%COMP%]     .xfaRadio{width:100%;height:100%;flex:0 0 auto;border:none}[_nghost-%COMP%]     .xfaRich{white-space:pre-wrap;width:100%;height:100%}[_nghost-%COMP%]     .xfaImage{object-position:left top;object-fit:contain;width:100%;height:100%}[_nghost-%COMP%]     .xfaLrTb, [_nghost-%COMP%]     .xfaRlTb, [_nghost-%COMP%]     .xfaTb{display:flex;flex-direction:column;align-items:stretch}[_nghost-%COMP%]     .xfaLr{display:flex;flex-direction:row;align-items:stretch}[_nghost-%COMP%]     .xfaRl{display:flex;flex-direction:row-reverse;align-items:stretch}[_nghost-%COMP%]     .xfaTb>div{justify-content:left}[_nghost-%COMP%]     .xfaPosition{position:relative}[_nghost-%COMP%]     .xfaArea{position:relative}[_nghost-%COMP%]     .xfaValignMiddle{display:flex;align-items:center}[_nghost-%COMP%]     .xfaTable{display:flex;flex-direction:column;align-items:stretch}[_nghost-%COMP%]     .xfaTable .xfaRow{display:flex;flex-direction:row;align-items:stretch}[_nghost-%COMP%]     .xfaTable .xfaRlRow{display:flex;flex-direction:row-reverse;align-items:stretch;flex:1}[_nghost-%COMP%]     .xfaTable .xfaRlRow>div{flex:1}[_nghost-%COMP%]     .xfaNonInteractive input, [_nghost-%COMP%]     .xfaNonInteractive textarea, [_nghost-%COMP%]     .xfaDisabled input, [_nghost-%COMP%]     .xfaDisabled textarea, [_nghost-%COMP%]     .xfaReadOnly input, [_nghost-%COMP%]     .xfaReadOnly textarea{background:initial}@media print{[_nghost-%COMP%]     .xfaTextfield, [_nghost-%COMP%]     .xfaSelect{background:transparent}[_nghost-%COMP%]     .xfaSelect{-webkit-appearance:none;-moz-appearance:none;appearance:none;text-indent:1px;text-overflow:\\\"\\\"}}[_nghost-%COMP%]     .pdfViewer{padding-bottom:var(--pdfViewer-padding-bottom)}[_nghost-%COMP%]     .pdfViewer .canvasWrapper{overflow:hidden}[_nghost-%COMP%]     .pdfViewer .page{direction:ltr;width:816px;height:1056px;margin:var(--page-margin);position:relative;overflow:visible;border:var(--page-border);background-clip:content-box;border-image:var(--shadow) 9 9 repeat;background-color:#fff}[_nghost-%COMP%]     .pdfViewer .dummyPage{position:relative;width:0;height:var(--viewer-container-height)}[_nghost-%COMP%]     .pdfViewer.removePageBorders .page{margin:0 auto 10px;border:none}[_nghost-%COMP%]     .pdfViewer.singlePageView{display:inline-block}[_nghost-%COMP%]     .pdfViewer.singlePageView .page{margin:0;border:none}[_nghost-%COMP%]     .pdfViewer.scrollHorizontal, [_nghost-%COMP%]     .pdfViewer.scrollWrapped, [_nghost-%COMP%]     .spread{margin-left:3.5px;margin-right:3.5px;text-align:center}[_nghost-%COMP%]     .pdfViewer.scrollHorizontal, [_nghost-%COMP%]     .spread{white-space:nowrap}[_nghost-%COMP%]     .pdfViewer.removePageBorders, [_nghost-%COMP%]     .pdfViewer.scrollHorizontal .spread, [_nghost-%COMP%]     .pdfViewer.scrollWrapped .spread{margin-left:0;margin-right:0}[_nghost-%COMP%]     .spread .page, [_nghost-%COMP%]     .spread .dummyPage, [_nghost-%COMP%]     .pdfViewer.scrollHorizontal .page, [_nghost-%COMP%]     .pdfViewer.scrollWrapped .page, [_nghost-%COMP%]     .pdfViewer.scrollHorizontal .spread, [_nghost-%COMP%]     .pdfViewer.scrollWrapped .spread{display:inline-block;vertical-align:middle}[_nghost-%COMP%]     .spread .page, [_nghost-%COMP%]     .pdfViewer.scrollHorizontal .page, [_nghost-%COMP%]     .pdfViewer.scrollWrapped .page{margin-left:var(--spreadHorizontalWrapped-margin-LR);margin-right:var(--spreadHorizontalWrapped-margin-LR)}[_nghost-%COMP%]     .pdfViewer.removePageBorders .spread .page, [_nghost-%COMP%]     .pdfViewer.removePageBorders.scrollHorizontal .page, [_nghost-%COMP%]     .pdfViewer.removePageBorders.scrollWrapped .page{margin-left:5px;margin-right:5px}[_nghost-%COMP%]     .pdfViewer .page canvas{margin:0;display:block}[_nghost-%COMP%]     .pdfViewer .page canvas[hidden]{display:none}[_nghost-%COMP%]     .pdfViewer .page .loadingIcon{position:absolute;display:block;left:0;top:0;right:0;bottom:0;background:url(data:image/gif;base64,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) center no-repeat}[_nghost-%COMP%]     .pdfViewer .page .loadingIcon.notVisible{background:none}[_nghost-%COMP%]     .pdfViewer.enablePermissions .textLayer span{-webkit-user-select:none!important;user-select:none!important;cursor:not-allowed}[_nghost-%COMP%]     .pdfPresentationMode .pdfViewer{padding-bottom:0}[_nghost-%COMP%]     .pdfPresentationMode .spread{margin:0}[_nghost-%COMP%]     .pdfPresentationMode .pdfViewer .page{margin:0 auto;border:2px solid transparent}\"]\n  });\n  return PdfViewerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Created by vadimdez on 01/11/2016.\n */\nlet PdfViewerModule = /*#__PURE__*/(() => {\n  class PdfViewerModule {}\n  PdfViewerModule.ɵfac = function PdfViewerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PdfViewerModule)();\n  };\n  PdfViewerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PdfViewerModule\n  });\n  PdfViewerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return PdfViewerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PdfViewerComponent, PdfViewerModule, RenderTextMode };\n//# sourceMappingURL=ng2-pdf-viewer.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}