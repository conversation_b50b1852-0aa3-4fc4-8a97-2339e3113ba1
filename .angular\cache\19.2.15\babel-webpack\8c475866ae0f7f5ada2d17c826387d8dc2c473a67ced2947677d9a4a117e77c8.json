{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { extend, retrieve3 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport labelLayout from './labelLayout.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getBasicPieLayout, getSeriesLayoutData } from './pieLayout.js';\n/**\r\n * Piece of pie including Sector, Label, LabelLine\r\n */\nvar PiePiece = /** @class */function (_super) {\n  __extends(PiePiece, _super);\n  function PiePiece(data, idx, startAngle) {\n    var _this = _super.call(this) || this;\n    _this.z2 = 2;\n    var text = new graphic.Text();\n    _this.setTextContent(text);\n    _this.updateData(data, idx, startAngle, true);\n    return _this;\n  }\n  PiePiece.prototype.updateData = function (data, idx, startAngle, firstCreate) {\n    var sector = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var layout = data.getItemLayout(idx);\n    // cornerRadius & innerCornerRadius doesn't exist in the item layout. Use `0` if null value is specified.\n    // see `setItemLayout` in `pieLayout.ts`.\n    var sectorShape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    // Ignore NaN data.\n    if (isNaN(sectorShape.startAngle)) {\n      // Use NaN shape to avoid drawing shape.\n      sector.setShape(sectorShape);\n      return;\n    }\n    if (firstCreate) {\n      sector.setShape(sectorShape);\n      var animationType = seriesModel.getShallow('animationType');\n      if (seriesModel.ecModel.ssr) {\n        // Use scale animation in SSR mode(opacity?)\n        // Because CSS SVG animation doesn't support very customized shape animation.\n        graphic.initProps(sector, {\n          scaleX: 0,\n          scaleY: 0\n        }, seriesModel, {\n          dataIndex: idx,\n          isFrom: true\n        });\n        sector.originX = sectorShape.cx;\n        sector.originY = sectorShape.cy;\n      } else if (animationType === 'scale') {\n        sector.shape.r = layout.r0;\n        graphic.initProps(sector, {\n          shape: {\n            r: layout.r\n          }\n        }, seriesModel, idx);\n      }\n      // Expansion\n      else {\n        if (startAngle != null) {\n          sector.setShape({\n            startAngle: startAngle,\n            endAngle: startAngle\n          });\n          graphic.initProps(sector, {\n            shape: {\n              startAngle: layout.startAngle,\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        } else {\n          sector.shape.endAngle = layout.startAngle;\n          graphic.updateProps(sector, {\n            shape: {\n              endAngle: layout.endAngle\n            }\n          }, seriesModel, idx);\n        }\n      }\n    } else {\n      saveOldStyle(sector);\n      // Transition animation from the old shape\n      graphic.updateProps(sector, {\n        shape: sectorShape\n      }, seriesModel, idx);\n    }\n    sector.useStyle(data.getItemVisual(idx, 'style'));\n    setStatesStylesFromModel(sector, itemModel);\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var offset = seriesModel.get('selectedOffset');\n    var dx = Math.cos(midAngle) * offset;\n    var dy = Math.sin(midAngle) * offset;\n    var cursorStyle = itemModel.getShallow('cursor');\n    cursorStyle && sector.attr('cursor', cursorStyle);\n    this._updateLabel(seriesModel, data, idx);\n    sector.ensureState('emphasis').shape = extend({\n      r: layout.r + (emphasisModel.get('scale') ? emphasisModel.get('scaleSize') || 0 : 0)\n    }, getSectorCornerRadius(emphasisModel.getModel('itemStyle'), layout));\n    extend(sector.ensureState('select'), {\n      x: dx,\n      y: dy,\n      shape: getSectorCornerRadius(itemModel.getModel(['select', 'itemStyle']), layout)\n    });\n    extend(sector.ensureState('blur'), {\n      shape: getSectorCornerRadius(itemModel.getModel(['blur', 'itemStyle']), layout)\n    });\n    var labelLine = sector.getTextGuideLine();\n    var labelText = sector.getTextContent();\n    labelLine && extend(labelLine.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    // TODO: needs dx, dy in zrender?\n    extend(labelText.ensureState('select'), {\n      x: dx,\n      y: dy\n    });\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  PiePiece.prototype._updateLabel = function (seriesModel, data, idx) {\n    var sector = this;\n    var itemModel = data.getItemModel(idx);\n    var labelLineModel = itemModel.getModel('labelLine');\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style && style.fill;\n    var visualOpacity = style && style.opacity;\n    setLabelStyle(sector, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      defaultText: seriesModel.getFormattedLabel(idx, 'normal') || data.getName(idx)\n    });\n    var labelText = sector.getTextContent();\n    // Set textConfig on sector.\n    sector.setTextConfig({\n      // reset position, rotation\n      position: null,\n      rotation: null\n    });\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    labelText.attr({\n      z2: 10\n    });\n    var labelPosition = seriesModel.get(['label', 'position']);\n    if (labelPosition !== 'outside' && labelPosition !== 'outer') {\n      sector.removeTextGuideLine();\n    } else {\n      var polyline = this.getTextGuideLine();\n      if (!polyline) {\n        polyline = new graphic.Polyline();\n        this.setTextGuideLine(polyline);\n      }\n      // Default use item visual color\n      setLabelLineStyle(this, getLabelLineStatesModels(itemModel), {\n        stroke: visualColor,\n        opacity: retrieve3(labelLineModel.get(['lineStyle', 'opacity']), visualOpacity, 1)\n      });\n    }\n  };\n  return PiePiece;\n}(graphic.Sector);\n// Pie view\nvar PieView = /** @class */function (_super) {\n  __extends(PieView, _super);\n  function PieView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  PieView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    var startAngle;\n    // First render\n    if (!oldData && data.count() > 0) {\n      var shape = data.getItemLayout(0);\n      for (var s = 1; isNaN(shape && shape.startAngle) && s < data.count(); ++s) {\n        shape = data.getItemLayout(s);\n      }\n      if (shape) {\n        startAngle = shape.startAngle;\n      }\n    }\n    // remove empty-circle if it exists\n    if (this._emptyCircleSector) {\n      group.remove(this._emptyCircleSector);\n    }\n    // when all data are filtered, show lightgray empty circle\n    if (data.count() === 0 && seriesModel.get('showEmptyCircle')) {\n      var layoutData = getSeriesLayoutData(seriesModel);\n      var sector = new graphic.Sector({\n        shape: extend(getBasicPieLayout(seriesModel, api), layoutData)\n      });\n      sector.useStyle(seriesModel.getModel('emptyCircleStyle').getItemStyle());\n      this._emptyCircleSector = sector;\n      group.add(sector);\n    }\n    data.diff(oldData).add(function (idx) {\n      var piePiece = new PiePiece(data, idx, startAngle);\n      data.setItemGraphicEl(idx, piePiece);\n      group.add(piePiece);\n    }).update(function (newIdx, oldIdx) {\n      var piePiece = oldData.getItemGraphicEl(oldIdx);\n      piePiece.updateData(data, newIdx, startAngle);\n      piePiece.off('click');\n      group.add(piePiece);\n      data.setItemGraphicEl(newIdx, piePiece);\n    }).remove(function (idx) {\n      var piePiece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piePiece, seriesModel, idx);\n    }).execute();\n    labelLayout(seriesModel);\n    // Always use initial animation.\n    if (seriesModel.get('animationTypeUpdate') !== 'expansion') {\n      this._data = data;\n    }\n  };\n  PieView.prototype.dispose = function () {};\n  PieView.prototype.containPoint = function (point, seriesModel) {\n    var data = seriesModel.getData();\n    var itemLayout = data.getItemLayout(0);\n    if (itemLayout) {\n      var dx = point[0] - itemLayout.cx;\n      var dy = point[1] - itemLayout.cy;\n      var radius = Math.sqrt(dx * dx + dy * dy);\n      return radius <= itemLayout.r && radius >= itemLayout.r0;\n    }\n  };\n  PieView.type = 'pie';\n  return PieView;\n}(ChartView);\nexport default PieView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}