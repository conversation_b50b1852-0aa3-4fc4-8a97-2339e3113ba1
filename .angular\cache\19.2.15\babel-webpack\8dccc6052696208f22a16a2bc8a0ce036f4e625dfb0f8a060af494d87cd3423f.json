{"ast": null, "code": "import { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nimport { getClassId } from './cssClassId.js';\nexport var EASING_MAP = {\n  cubicIn: '0.32,0,0.67,0',\n  cubicOut: '0.33,1,0.68,1',\n  cubicInOut: '0.65,0,0.35,1',\n  quadraticIn: '0.11,0,0.5,0',\n  quadraticOut: '0.5,1,0.89,1',\n  quadraticInOut: '0.45,0,0.55,1',\n  quarticIn: '0.5,0,0.75,0',\n  quarticOut: '0.25,1,0.5,1',\n  quarticInOut: '0.76,0,0.24,1',\n  quinticIn: '0.64,0,0.78,0',\n  quinticOut: '0.22,1,0.36,1',\n  quinticInOut: '0.83,0,0.17,1',\n  sinusoidalIn: '0.12,0,0.39,0',\n  sinusoidalOut: '0.61,1,0.88,1',\n  sinusoidalInOut: '0.37,0,0.63,1',\n  exponentialIn: '0.7,0,0.84,0',\n  exponentialOut: '0.16,1,0.3,1',\n  exponentialInOut: '0.87,0,0.13,1',\n  circularIn: '0.55,0,1,0.45',\n  circularOut: '0,0.55,0.45,1',\n  circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n  var shape = extend({}, el.shape);\n  extend(shape, kfShape);\n  el.buildPath(path, shape);\n  var svgPathBuilder = new SVGPathRebuilder();\n  svgPathBuilder.reset(getPathPrecision(el));\n  path.rebuildPath(svgPathBuilder, 1);\n  svgPathBuilder.generateStr();\n  return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n  var originX = transform.originX,\n    originY = transform.originY;\n  if (originX || originY) {\n    target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n  }\n}\nexport var ANIMATE_STYLE_MAP = {\n  fill: 'fill',\n  opacity: 'opacity',\n  lineWidth: 'stroke-width',\n  lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n  var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n  scope.cssAnims[animationName] = cssAnim;\n  return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n  var paths = el.shape.paths;\n  var composedAnim = {};\n  var cssAnimationCfg;\n  var cssAnimationName;\n  each(paths, function (path) {\n    var subScope = createBrushScope(scope.zrId);\n    subScope.animation = true;\n    createCSSAnimation(path, {}, subScope, true);\n    var cssAnims = subScope.cssAnims;\n    var cssNodes = subScope.cssNodes;\n    var animNames = keys(cssAnims);\n    var len = animNames.length;\n    if (!len) {\n      return;\n    }\n    cssAnimationName = animNames[len - 1];\n    var lastAnim = cssAnims[cssAnimationName];\n    for (var percent in lastAnim) {\n      var kf = lastAnim[percent];\n      composedAnim[percent] = composedAnim[percent] || {\n        d: ''\n      };\n      composedAnim[percent].d += kf.d || '';\n    }\n    for (var className in cssNodes) {\n      var val = cssNodes[className].animation;\n      if (val.indexOf(cssAnimationName) >= 0) {\n        cssAnimationCfg = val;\n      }\n    }\n  });\n  if (!cssAnimationCfg) {\n    return;\n  }\n  attrs.d = false;\n  var animationName = addAnimation(composedAnim, scope);\n  return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n  return isString(easing) ? EASING_MAP[easing] ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\" : createCubicEasingFunc(easing) ? easing : '' : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n  var animators = el.animators;\n  var len = animators.length;\n  var cssAnimations = [];\n  if (el instanceof CompoundPath) {\n    var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    } else if (!len) {\n      return;\n    }\n  } else if (!len) {\n    return;\n  }\n  var groupAnimators = {};\n  for (var i = 0; i < len; i++) {\n    var animator = animators[i];\n    var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n    var easing = getEasingFunc(animator.getClip().easing);\n    var delay = animator.getDelay();\n    if (easing) {\n      cfgArr.push(easing);\n    } else {\n      cfgArr.push('linear');\n    }\n    if (delay) {\n      cfgArr.push(delay / 1000 + 's');\n    }\n    if (animator.getLoop()) {\n      cfgArr.push('infinite');\n    }\n    var cfg = cfgArr.join(' ');\n    groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n    groupAnimators[cfg][1].push(animator);\n  }\n  function createSingleCSSAnimation(groupAnimator) {\n    var animators = groupAnimator[1];\n    var len = animators.length;\n    var transformKfs = {};\n    var shapeKfs = {};\n    var finalKfs = {};\n    var animationTimingFunctionAttrName = 'animation-timing-function';\n    function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n      var tracks = animator.getTracks();\n      var maxTime = animator.getMaxTime();\n      for (var k = 0; k < tracks.length; k++) {\n        var track = tracks[k];\n        if (track.needsAnimate()) {\n          var kfs = track.keyframes;\n          var attrName = track.propName;\n          toCssAttrName && (attrName = toCssAttrName(attrName));\n          if (attrName) {\n            for (var i = 0; i < kfs.length; i++) {\n              var kf = kfs[i];\n              var percent = Math.round(kf.time / maxTime * 100) + '%';\n              var kfEasing = getEasingFunc(kf.easing);\n              var rawValue = kf.rawValue;\n              if (isString(rawValue) || isNumber(rawValue)) {\n                cssKfs[percent] = cssKfs[percent] || {};\n                cssKfs[percent][attrName] = kf.rawValue;\n                if (kfEasing) {\n                  cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      var targetProp = animator.targetName;\n      if (!targetProp) {\n        !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n      } else if (targetProp === 'shape') {\n        saveAnimatorTrackToCssKfs(animator, shapeKfs);\n      }\n    }\n    for (var percent in transformKfs) {\n      var transform = {};\n      copyTransform(transform, el);\n      extend(transform, transformKfs[percent]);\n      var str = getSRTTransformString(transform);\n      var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n      finalKfs[percent] = str ? {\n        transform: str\n      } : {};\n      setTransformOrigin(finalKfs[percent], transform);\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    var path;\n    var canAnimateShape = true;\n    for (var percent in shapeKfs) {\n      finalKfs[percent] = finalKfs[percent] || {};\n      var isFirst = !path;\n      var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n      if (isFirst) {\n        path = new PathProxy();\n      }\n      var len_1 = path.len();\n      path.reset();\n      finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n      var newLen = path.len();\n      if (!isFirst && len_1 !== newLen) {\n        canAnimateShape = false;\n        break;\n      }\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    if (!canAnimateShape) {\n      for (var percent in finalKfs) {\n        delete finalKfs[percent].d;\n      }\n    }\n    if (!onlyShape) {\n      for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var targetProp = animator.targetName;\n        if (targetProp === 'style') {\n          saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) {\n            return ANIMATE_STYLE_MAP[propName];\n          });\n        }\n      }\n    }\n    var percents = keys(finalKfs);\n    var allTransformOriginSame = true;\n    var transformOrigin;\n    for (var i = 1; i < percents.length; i++) {\n      var p0 = percents[i - 1];\n      var p1 = percents[i];\n      if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n        allTransformOriginSame = false;\n        break;\n      }\n      transformOrigin = finalKfs[p0][transformOriginKey];\n    }\n    if (allTransformOriginSame && transformOrigin) {\n      for (var percent in finalKfs) {\n        if (finalKfs[percent][transformOriginKey]) {\n          delete finalKfs[percent][transformOriginKey];\n        }\n      }\n      attrs[transformOriginKey] = transformOrigin;\n    }\n    if (filter(percents, function (percent) {\n      return keys(finalKfs[percent]).length > 0;\n    }).length) {\n      var animationName = addAnimation(finalKfs, scope);\n      return animationName + \" \" + groupAnimator[0] + \" both\";\n    }\n  }\n  for (var key in groupAnimators) {\n    var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    }\n  }\n  if (cssAnimations.length) {\n    var className = scope.zrId + '-cls-' + getClassId();\n    scope.cssNodes['.' + className] = {\n      animation: cssAnimations.join(',')\n    };\n    attrs[\"class\"] = className;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}