{"ast": null, "code": "import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';\nimport { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/language-services/language.service\";\nimport * as i2 from \"src/app/core/services/program-services/program-day-tasks.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"src/app/core/services/calls-services/ProgramRecitationTasks/program-recitation-tasks.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i9 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"ng2-pdf-viewer\";\nfunction CallDetailsComponent_ng_container_3_ng_template_5_a_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r5.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CallDetailsComponent_ng_container_3_ng_template_5_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵtemplate(1, CallDetailsComponent_ng_container_3_ng_template_5_a_4_ng_container_1_Template, 2, 1, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", item_r5.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.fileName.split(\".\").pop() !== \"pdf\");\n  }\n}\nfunction CallDetailsComponent_ng_container_3_ng_template_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"pdf-viewer\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r5.url)(\"render-text\", true)(\"original-size\", false)(\"fit-to-page\", true)(\"show-all\", true)(\"zoom-scale\", \"page-width\");\n  }\n}\nfunction CallDetailsComponent_ng_container_3_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"h4\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 13);\n    i0.ɵɵtemplate(4, CallDetailsComponent_ng_container_3_ng_template_5_a_4_Template, 2, 2, \"a\", 14)(5, CallDetailsComponent_ng_container_3_ng_template_5_ng_container_5_Template, 2, 6, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16)(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CallDetailsComponent_ng_container_3_ng_template_5_Template_button_click_7_listener() {\n      const modal_r6 = i0.ɵɵrestoreView(_r4).$implicit;\n      return i0.ɵɵresetView(modal_r6.close(\"Close click\"));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.fileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r5.fileName.split(\".\").pop() !== \"pdf\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.fileName.split(\".\").pop() === \"pdf\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 4, \"GENERAL.CLOSE\"), \"\");\n  }\n}\nfunction CallDetailsComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function CallDetailsComponent_ng_container_3_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const content_r2 = i0.ɵɵreference(6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openVerticallyCentered(content_r2));\n    });\n    i0.ɵɵelement(2, \"img\", 9);\n    i0.ɵɵelementStart(3, \"p\", 10);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, CallDetailsComponent_ng_container_3_ng_template_5_Template, 10, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.book_sound, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", item_r5.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.fileName);\n  }\n}\nfunction CallDetailsComponent_app_jitsi_call_integ_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-jitsi-call-integ\", 21);\n    i0.ɵɵlistener(\"endCallEvent\", function CallDetailsComponent_app_jitsi_call_integ_6_Template_app_jitsi_call_integ_endCallEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.endCall($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"callJoinModel\", ctx_r2.callJoinModel)(\"jitsiSettingOptions\", ctx_r2.jitsiSettingOptions);\n  }\n}\nexport let CallDetailsComponent = /*#__PURE__*/(() => {\n  class CallDetailsComponent {\n    languageService;\n    programDayTasksService;\n    translate;\n    route;\n    modalService;\n    router;\n    callService;\n    dialog;\n    aletifyService;\n    imagesPathesService;\n    dayId = \"\";\n    tskId;\n    callId;\n    callUserId;\n    callModuleTypehuffazId;\n    modId;\n    isVideoMute;\n    batId;\n    fileList = [];\n    callJoinModel;\n    jitsiSettingOptions;\n    constructor(languageService, programDayTasksService, translate, route, modalService, router, callService, dialog, aletifyService, imagesPathesService) {\n      this.languageService = languageService;\n      this.programDayTasksService = programDayTasksService;\n      this.translate = translate;\n      this.route = route;\n      this.modalService = modalService;\n      this.router = router;\n      this.callService = callService;\n      this.dialog = dialog;\n      this.aletifyService = aletifyService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.dayId = this.route.snapshot.params.dayId;\n      this.tskId = this.route.snapshot.params.tskId;\n      this.callId = this.route.snapshot.params.callId;\n      this.callUserId = this.route.snapshot.params.user;\n      this.callModuleTypehuffazId = this.route.snapshot.params.CallType;\n      this.modId = this.route.snapshot.params.modId;\n      this.isVideoMute = this.route.snapshot.params.isVideoMute;\n      this.batId = this.route.snapshot.params.batId;\n      this.getAllMemorize();\n      this.initCall();\n    }\n    getAllMemorize() {\n      this.fileList = [];\n      this.programDayTasksService.getProgramMemorizeAtDay(this.dayId || '').subscribe(res => {\n        res.data;\n        Array.from(res.data).forEach(elm => {\n          let lstBookAttatchments = JSON.parse(elm.detailsTask).bookAttatchments;\n          if (lstBookAttatchments.length > 0) {\n            Array.from(lstBookAttatchments).forEach(item => {\n              this.fileList?.push(item);\n            });\n          } else {\n            this.fileList?.push(elm);\n          }\n        });\n      }, error => {\n        //logging\n      });\n    }\n    openVerticallyCentered(content) {\n      this.modalService.open(content, {\n        size: 'lg'\n      });\n    }\n    initCall() {\n      this.callJoinModel = {\n        callId: this.callId,\n        usrId: this.callUserId,\n        //JSON.parse(localStorage.getItem(\"user\") || '{}').id,\n        callModuleTypehuffazId: this.callModuleTypehuffazId,\n        isMod: false\n      };\n      if (this.callModuleTypehuffazId == CallTypesEnum.Recitation) {\n        this.jitsiSettingOptions = {\n          isVideoMute: this.isVideoMute,\n          startCallType: StartCallTypesEnum.JoinMode,\n          endCallType: EndCallTypesEnum.TasmeeaTskTechUsrEndCall\n        };\n      } else if (this.callModuleTypehuffazId == CallTypesEnum.StudentRecitation) {\n        this.jitsiSettingOptions = {\n          isVideoMute: this.isVideoMute,\n          startCallType: StartCallTypesEnum.JoinMode,\n          endCallType: EndCallTypesEnum.SardTolabTskStudUsrEndCall\n        };\n      }\n    }\n    endCall(callId) {\n      if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.TasmeeaTskTechUsrEndCall) {\n        this.progTskTechUsrEndCall(callId);\n      } else if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.SardTolabTskStudUsrEndCall) {\n        this.confirmSardTolabTask();\n      }\n    }\n    confirmSardTolabTask() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Has the student recitation task been completed?\" : \"هل تم الانتهاء من مهمة سرد طلاب\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Student Recitation Task' : 'مهمة سرد طلاب', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.progTskTechUsrEndCall();\n        } else {\n          this.router.navigateByUrl('/student-programs');\n        }\n      });\n    }\n    progTskTechUsrEndCall(callId) {\n      let submitTask;\n      if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.TasmeeaTskTechUsrEndCall) {\n        submitTask = {\n          taskId: this.tskId,\n          usrId: this.modId,\n          //JSON.parse(localStorage.getItem(\"user\") || '{}').id,\n          callId: callId,\n          batId: this.batId,\n          dayId: this.dayId\n        };\n      } else if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.SardTolabTskStudUsrEndCall) {\n        submitTask = {\n          callId: this.callId,\n          taskId: this.tskId,\n          usrId: this.callUserId,\n          //JSON.parse(localStorage.getItem(\"user\") || '{}').id,\n          batId: this.batId,\n          dayId: this.dayId\n        };\n      }\n      this.callService.submitTask(submitTask || {}).subscribe(res => {\n        if (res.isSuccess) {\n          let stuTaskId = res.data;\n          if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.TasmeeaTskTechUsrEndCall) {\n            // setTimeout(() => {\n            this.router.navigateByUrl('recitation-task-call/add-degree/' + stuTaskId + '/' + this.dayId + '/' + callId + '');\n            // }, 1000);\n          } else if (this.jitsiSettingOptions?.endCallType == EndCallTypesEnum.SardTolabTskStudUsrEndCall) {\n            this.router.navigateByUrl('/student-programs');\n          }\n        } else {\n          this.aletifyService.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function CallDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CallDetailsComponent)(i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i2.ProgramDayTasksService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i6.ProgramRecitationTasksService), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.AlertifyService), i0.ɵɵdirectiveInject(i9.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CallDetailsComponent,\n      selectors: [[\"app-call-details\"]],\n      decls: 7,\n      vars: 2,\n      consts: [[\"content\", \"\"], [1, \"container-fluid\", \"callDetails\"], [1, \"row\", \"mx-0\"], [1, \"col-3\", \"cus_info\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-9\"], [1, \"call-integ\"], [3, \"callJoinModel\", \"jitsiSettingOptions\", \"endCallEvent\", 4, \"ngIf\"], [1, \"cardRecourd\", \"d-flex\", 3, \"click\"], [1, \"book\", 3, \"src\"], [1, \"ellipsis\", \"mb-0\", 3, \"matTooltip\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"modal-body\"], [3, \"href\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [3, \"href\"], [1, \"size_img_model\", 3, \"src\"], [3, \"src\", \"render-text\", \"original-size\", \"fit-to-page\", \"show-all\", \"zoom-scale\"], [3, \"endCallEvent\", \"callJoinModel\", \"jitsiSettingOptions\"]],\n      template: function CallDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n          i0.ɵɵtemplate(3, CallDetailsComponent_ng_container_3_Template, 7, 3, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6);\n          i0.ɵɵtemplate(6, CallDetailsComponent_app_jitsi_call_integ_6_Template, 1, 2, \"app-jitsi-call-integ\", 7);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fileList);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.callJoinModel);\n        }\n      },\n      dependencies: [i10.NgForOf, i10.NgIf, i11.PdfViewerComponent, i3.TranslatePipe],\n      styles: [\".callDetails[_ngcontent-%COMP%]   .groupInfo[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;display:flex;justify-content:flex-start;margin-top:1rem}.callDetails[_ngcontent-%COMP%]   .call-integ[_ngcontent-%COMP%]{display:flex;justify-content:center;background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;margin-top:1rem}.callDetails[_ngcontent-%COMP%]   .cus_info[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;margin-top:1rem}.callDetails[_ngcontent-%COMP%]   .cus_info[_ngcontent-%COMP%]   .cardRecourd[_ngcontent-%COMP%]{background:#fbfbfb;padding:1rem;border:.063rem solid #fbfbfb;border-radius:.625rem}.callDetails[_ngcontent-%COMP%]   .book[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}\"]\n    });\n  }\n  return CallDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}