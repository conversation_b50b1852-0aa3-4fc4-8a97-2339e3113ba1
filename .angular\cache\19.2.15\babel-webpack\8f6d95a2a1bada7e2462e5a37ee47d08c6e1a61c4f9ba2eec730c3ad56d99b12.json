{"ast": null, "code": "import { StuTabRequestComponent } from './stu-tab-request/stu-tab-request.component';\nimport { StudentProgramSubscriptionStatusEnum } from 'src/app/core/enums/subscriptionStatusEnum/student-program-subscription-status-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction StuJoinRequestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-stu-rejected\", 6);\n    i0.ɵɵlistener(\"closePopup\", function StuJoinRequestComponent_div_4_Template_app_stu_rejected_closePopup_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeForms());\n    })(\"closeRejectedRequest\", function StuJoinRequestComponent_div_4_Template_app_stu_rejected_closeRejectedRequest_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeRejectedRequest($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"itemStuReq\", ctx_r1.itemStuReq);\n  }\n}\nfunction StuJoinRequestComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-advanced-search\", 7);\n    i0.ɵɵlistener(\"closeAdvancedSearch\", function StuJoinRequestComponent_div_5_Template_app_advanced_search_closeAdvancedSearch_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStuAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"filter\", ctx_r1.filter);\n  }\n}\nexport let StuJoinRequestComponent = /*#__PURE__*/(() => {\n  class StuJoinRequestComponent {\n    stuRejectReq;\n    advancedSearch;\n    advancedSearchObjectpopup = {\n      statusNum: StudentProgramSubscriptionStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    filter = {\n      statusNum: StudentProgramSubscriptionStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    showTap = 'Pending';\n    // roleEnum: RoleEnum = RoleEnum.Teacher;\n    itemStuReq = {};\n    openStuRejectOverlay = false;\n    openStuAdvancedSearch = false;\n    constructor() {}\n    ngOnInit() {}\n    openRejectRequest(event) {\n      this.itemStuReq = event;\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n    }\n    closeRejectedRequest($event) {\n      this.openStuRejectOverlay = !this.openStuRejectOverlay;\n      if ($event == true) {\n        this.stuRejectReq?.getStudentProgramSubscriptionsFilter();\n      }\n    }\n    closeOverlay() {\n      this.openStuRejectOverlay = false;\n      this.openStuAdvancedSearch = false;\n      this.advancedSearch?.getStudentProgramSubscriptionsFilter();\n    }\n    closeStuAdvancedSearch(event) {\n      this.openStuAdvancedSearch = false;\n      this.filter = event;\n      this.advancedSearch?.getStudentProgramSubscriptionsFilter();\n    }\n    openStuAdvancedSearchPopup(event) {\n      this.openStuAdvancedSearch = true;\n      this.filter = event;\n    }\n    advancedSearchObject(event) {\n      this.filter = event;\n    }\n    // as per issue number 3250\n    closeForms() {\n      this.openStuAdvancedSearch = false;\n      this.openStuRejectOverlay = false;\n    }\n    static ɵfac = function StuJoinRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StuJoinRequestComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StuJoinRequestComponent,\n      selectors: [[\"app-stu-join-request\"]],\n      viewQuery: function StuJoinRequestComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StuTabRequestComponent, 5);\n          i0.ɵɵviewQuery(StuTabRequestComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stuRejectReq = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.advancedSearch = _t.first);\n        }\n      },\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [3, \"itemStuReq\", \"closePopup\", \"openAdvancedSearch\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closePopup\", \"closeRejectedRequest\", \"itemStuReq\"], [3, \"closeAdvancedSearch\", \"filter\"]],\n      template: function StuJoinRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-stu-tab-request\", 3);\n          i0.ɵɵlistener(\"itemStuReq\", function StuJoinRequestComponent_Template_app_stu_tab_request_itemStuReq_3_listener($event) {\n            return ctx.openRejectRequest($event);\n          })(\"closePopup\", function StuJoinRequestComponent_Template_app_stu_tab_request_closePopup_3_listener() {\n            return ctx.closeForms();\n          })(\"openAdvancedSearch\", function StuJoinRequestComponent_Template_app_stu_tab_request_openAdvancedSearch_3_listener($event) {\n            return ctx.openStuAdvancedSearchPopup($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, StuJoinRequestComponent_div_4_Template, 2, 1, \"div\", 4)(5, StuJoinRequestComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.openStuRejectOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openStuAdvancedSearch);\n        }\n      },\n      dependencies: [i1.NgIf],\n      styles: [\".container-fluid[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:0;margin-top:1rem;margin-bottom:0;width:100%;height:auto}.disableBlock[_ngcontent-%COMP%]{display:block!important}.showoverlay[_ngcontent-%COMP%]{position:fixed;width:100%;height:100%;inset:0;background-color:#00000080;z-index:2;cursor:pointer;display:none}\"]\n    });\n  }\n  return StuJoinRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}