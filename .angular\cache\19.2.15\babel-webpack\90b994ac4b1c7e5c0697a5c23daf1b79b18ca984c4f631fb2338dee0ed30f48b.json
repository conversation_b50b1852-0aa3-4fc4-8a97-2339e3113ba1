{"ast": null, "code": "import { ChatDetailsComponent } from './chat-details/chat-details.component';\nimport { GroupDetailsComponent } from './group-details/group-details.component';\nimport { GroupViewComponent } from './group-view/group-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i2 from \"src/app/core/services/loader-services/loader.service\";\nimport * as i3 from \"@angular/common\";\nfunction ChatViewComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"app-chat-details\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"groupData\", ctx_r1.groupModel);\n  }\n}\nfunction ChatViewComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"app-group-details\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"groupModel\", ctx_r1.groupModel);\n  }\n}\nfunction ChatViewComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-add-group\", 10);\n    i0.ɵɵlistener(\"closeCreateGroupOverlay\", function ChatViewComponent_div_0_div_6_Template_app_add_group_closeCreateGroupOverlay_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeCreateGroupOverlay($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"createGroupChat\", ctx_r1.createGroupChat);\n  }\n}\nfunction ChatViewComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-add-group\", 11);\n    i0.ɵɵlistener(\"closeEditGroupOverlay\", function ChatViewComponent_div_0_div_7_Template_app_add_group_closeEditGroupOverlay_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeEditGroupOverlay($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"editGroupChat\", ctx_r1.editGroupChat);\n  }\n}\nfunction ChatViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"app-group-view\", 4);\n    i0.ɵɵlistener(\"createGroupOverlayEvent\", function ChatViewComponent_div_0_Template_app_group_view_createGroupOverlayEvent_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openCreateGroupPopup($event));\n    })(\"groupDetailsForEditEvent\", function ChatViewComponent_div_0_Template_app_group_view_groupDetailsForEditEvent_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.groupDetailsForEditEvent($event));\n    })(\"groupDetailsEvent\", function ChatViewComponent_div_0_Template_app_group_view_groupDetailsEvent_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.groupDetailsEvent($event));\n    })(\"groupDetailsForDeleteEvent\", function ChatViewComponent_div_0_Template_app_group_view_groupDetailsForDeleteEvent_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.groupDetailsForDeleteEvent());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, ChatViewComponent_div_0_div_4_Template, 2, 1, \"div\", 5)(5, ChatViewComponent_div_0_div_5_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatViewComponent_div_0_div_6_Template, 2, 1, \"div\", 6)(7, ChatViewComponent_div_0_div_7_Template, 2, 1, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isGroupSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isGroupSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.openCreateGroupOverlay);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.openEditGroupOverlay);\n  }\n}\nexport let ChatViewComponent = /*#__PURE__*/(() => {\n  class ChatViewComponent {\n    chatService;\n    loaderService;\n    groupViewComponent;\n    groupDetailsComponent;\n    chatDetailsComponent;\n    openCreateGroupOverlay = false;\n    openEditGroupOverlay = false;\n    createGroupChat = {};\n    editGroupChat = {};\n    isGroupSelected = false;\n    groupModel;\n    constructor(chatService, loaderService) {\n      this.chatService = chatService;\n      this.loaderService = loaderService;\n    }\n    ngOnInit() {\n      /*starting loader service till groups fetched from firebase db.\n        still this is work around as it's connection to realtime db.\n        we expecting other solution based on await and async till db call back retrieve information\n             loader will stopped with first component initilized in this module \"group-view.component.ts\"\n      */\n      this.loaderService.isLoading.next(true);\n      this.chatService.getAllChatGroups();\n    }\n    closeCreateGroupOverlay(event) {\n      this.openCreateGroupOverlay = false;\n      this.createGroupChat = event;\n      this.groupViewComponent?.getGroupDetails(event);\n    }\n    closeEditGroupOverlay(event) {\n      this.openEditGroupOverlay = false;\n      this.editGroupChat = event;\n      this.groupViewComponent?.getGroupDetails(event);\n    }\n    openCreateGroupPopup(event) {\n      this.openCreateGroupOverlay = true;\n    }\n    // openEditGroupPopup(model: IGroupChat) {\n    //   this.openEditGroupOverlay = true;\n    //   this.editGroupChat = model;\n    // }  \n    groupDetailsForEditEvent(event) {\n      this.editGroupChat = event;\n      this.openEditGroupOverlay = true;\n    }\n    groupDetailsEvent(event) {\n      this.isGroupSelected = true;\n      this.groupModel = event;\n      if (this.groupDetailsComponent) {\n        this.groupDetailsComponent.groupModel = this.groupModel;\n        this.groupDetailsComponent.allowed = event.allowed;\n        this.groupDetailsComponent.listOfParticipants = event.participants || [];\n      }\n      if (this.chatDetailsComponent) {\n        this.chatDetailsComponent.groupData = this.groupModel;\n        this.chatDetailsComponent.getGroupMessages();\n      }\n    }\n    groupDetailsForDeleteEvent() {\n      if (this.chatDetailsComponent) {\n        this.chatDetailsComponent.getGroupMessages();\n      }\n    }\n    static ɵfac = function ChatViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ChatViewComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.LoaderService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatViewComponent,\n      selectors: [[\"app-chat-view\"]],\n      viewQuery: function ChatViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(GroupViewComponent, 5);\n          i0.ɵɵviewQuery(GroupDetailsComponent, 5);\n          i0.ɵɵviewQuery(ChatDetailsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.groupViewComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.groupDetailsComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatDetailsComponent = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"container-fluid\", 4, \"ngIf\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-lg-4\", \"col-md-4\", \"col-sm-12\", \"pl-0\"], [3, \"createGroupOverlayEvent\", \"groupDetailsForEditEvent\", \"groupDetailsEvent\", \"groupDetailsForDeleteEvent\"], [\"class\", \"col-lg-4 col-md-4 col-sm-12 pl-0\", 4, \"ngIf\"], [\"class\", \"overlay\", 4, \"ngIf\"], [3, \"groupData\"], [3, \"groupModel\"], [1, \"overlay\"], [3, \"closeCreateGroupOverlay\", \"createGroupChat\"], [3, \"closeEditGroupOverlay\", \"editGroupChat\"]],\n      template: function ChatViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ChatViewComponent_div_0_Template, 8, 4, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.chatService.allChatGroupsList && ctx.chatService.allChatGroupsList.length > 0);\n        }\n      },\n      dependencies: [i3.NgIf],\n      encapsulation: 2\n    });\n  }\n  return ChatViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}