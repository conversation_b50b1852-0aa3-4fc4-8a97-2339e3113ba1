{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nlet _IdGenerator = /*#__PURE__*/(() => {\n  class _IdGenerator {\n    _appId = inject(APP_ID);\n    /**\n     * Generates a unique ID with a specific prefix.\n     * @param prefix Prefix to add to the ID.\n     */\n    getId(prefix) {\n      // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n      // Angular app on them, we can reduce the amount of breakages by not adding it.\n      if (this._appId !== 'ng') {\n        prefix += this._appId;\n      }\n      if (!counters.hasOwnProperty(prefix)) {\n        counters[prefix] = 0;\n      }\n      return `${prefix}${counters[prefix]++}`;\n    }\n    static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _IdGenerator)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _IdGenerator,\n      factory: _IdGenerator.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return _IdGenerator;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _IdGenerator as _ };\n//# sourceMappingURL=id-generator-Dw_9dSDu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}