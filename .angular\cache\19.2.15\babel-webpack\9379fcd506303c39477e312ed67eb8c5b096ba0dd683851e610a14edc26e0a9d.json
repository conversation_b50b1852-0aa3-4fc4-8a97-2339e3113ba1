{"ast": null, "code": "import { _Id<PERSON><PERSON><PERSON>, FocusMonitor } from '@angular/cdk/a11y';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, inject, ChangeDetectorRef, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, ElementRef, NgZone, Renderer2, Injector, ANIMATION_MODULE_TYPE, HostAttributeToken, numberAttribute, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { _ as _MatInternalFormField } from './internal-form-field-grv62mCZ.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Change event object emitted by radio button and radio group. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"formField\"];\nconst _c2 = [\"*\"];\nclass MatRadioChange {\n  source;\n  value;\n  constructor(/** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = /*#__PURE__*/new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    disabledInteractive: false\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nlet MatRadioGroup = /*#__PURE__*/(() => {\n  class MatRadioGroup {\n    _changeDetector = inject(ChangeDetectorRef);\n    /** Selected value for the radio group. */\n    _value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    _name = inject(_IdGenerator).getId('mat-radio-group-');\n    /** The currently selected radio button. Should match value. */\n    _selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    _labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    _disabled = false;\n    /** Whether the radio group is required. */\n    _required = false;\n    /** Subscription to changes in amount of radio buttons. */\n    _buttonChanges;\n    /** The method to be called in order to update ngModel */\n    _controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** Child radio buttons. */\n    _radios;\n    /**\n     * Theme color of the radio buttons in the group. This API is supported in M2\n     * themes only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n      return this._name;\n    }\n    set name(value) {\n      this._name = value;\n      this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition;\n    }\n    set labelPosition(v) {\n      this._labelPosition = v === 'before' ? 'before' : 'after';\n      this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      if (this._value !== newValue) {\n        // Set this before proceeding to ensure no circular loop occurs with selection.\n        this._value = newValue;\n        this._updateSelectedRadioFromValue();\n        this._checkSelectedRadioButton();\n      }\n    }\n    _checkSelectedRadioButton() {\n      if (this._selected && !this._selected.checked) {\n        this._selected.checked = true;\n      }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n      return this._selected;\n    }\n    set selected(selected) {\n      this._selected = selected;\n      this.value = selected ? selected.value : null;\n      this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n      return this._required;\n    }\n    set required(value) {\n      this._required = value;\n      this._markRadiosForCheck();\n    }\n    /** Whether buttons in the group should be interactive while they're disabled. */\n    get disabledInteractive() {\n      return this._disabledInteractive;\n    }\n    set disabledInteractive(value) {\n      this._disabledInteractive = value;\n      this._markRadiosForCheck();\n    }\n    _disabledInteractive = false;\n    constructor() {}\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n      // Mark this component as initialized in AfterContentInit because the initial value can\n      // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n      // NgModel occurs *after* the OnInit of the MatRadioGroup.\n      this._isInitialized = true;\n      // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n      // buttons depends on it. Note that we don't clear the `value`, because the radio button\n      // may be swapped out with a similar one and there are some internal apps that depend on\n      // that behavior.\n      this._buttonChanges = this._radios.changes.subscribe(() => {\n        if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n          this._selected = null;\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n      if (this.onTouched) {\n        this.onTouched();\n      }\n    }\n    _updateRadioButtonNames() {\n      if (this._radios) {\n        this._radios.forEach(radio => {\n          radio.name = this.name;\n          radio._markForCheck();\n        });\n      }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n      // If the value already matches the selected radio, do nothing.\n      const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n      if (this._radios && !isAlreadySelected) {\n        this._selected = null;\n        this._radios.forEach(radio => {\n          radio.checked = this.value === radio.value;\n          if (radio.checked) {\n            this._selected = radio;\n          }\n        });\n      }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n      if (this._isInitialized) {\n        this.change.emit(new MatRadioChange(this._selected, this._value));\n      }\n    }\n    _markRadiosForCheck() {\n      if (this._radios) {\n        this._radios.forEach(radio => radio._markForCheck());\n      }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n      this.value = value;\n      this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n      this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetector.markForCheck();\n    }\n    static ɵfac = function MatRadioGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioGroup)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioGroup\"],\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }])]\n    });\n  }\n  return MatRadioGroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioButton = /*#__PURE__*/(() => {\n  class MatRadioButton {\n    _elementRef = inject(ElementRef);\n    _changeDetector = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _radioDispatcher = inject(UniqueSelectionDispatcher);\n    _defaultOptions = inject(MAT_RADIO_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _uniqueId = inject(_IdGenerator).getId('mat-radio-');\n    _cleanupClick;\n    /** The unique ID for the radio button. */\n    id = this._uniqueId;\n    /** Analog to HTML 'name' attribute used to group radios for unique selection. */\n    name;\n    /** Used to set the 'aria-label' attribute on the underlying input element. */\n    ariaLabel;\n    /** The 'aria-labelledby' attribute takes precedence as the element's text alternative. */\n    ariaLabelledby;\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    ariaDescribedby;\n    /** Whether ripples are disabled inside the radio button */\n    disableRipple = false;\n    /** Tabindex of the radio button. */\n    tabIndex = 0;\n    /** Whether this radio button is checked. */\n    get checked() {\n      return this._checked;\n    }\n    set checked(value) {\n      if (this._checked !== value) {\n        this._checked = value;\n        if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n          this.radioGroup.selected = this;\n        } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n          // When unchecking the selected radio button, update the selected radio\n          // property on the group.\n          this.radioGroup.selected = null;\n        }\n        if (value) {\n          // Notify all radio buttons with the same name to un-check.\n          this._radioDispatcher.notify(this.id, this.name);\n        }\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** The value of this radio button. */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      if (this._value !== value) {\n        this._value = value;\n        if (this.radioGroup !== null) {\n          if (!this.checked) {\n            // Update checked when the value changed to match the radio group's value\n            this.checked = this.radioGroup.value === value;\n          }\n          if (this.checked) {\n            this.radioGroup.selected = this;\n          }\n        }\n      }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n    }\n    set labelPosition(value) {\n      this._labelPosition = value;\n    }\n    _labelPosition;\n    /** Whether the radio button is disabled. */\n    get disabled() {\n      return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n    }\n    set disabled(value) {\n      this._setDisabled(value);\n    }\n    /** Whether the radio button is required. */\n    get required() {\n      return this._required || this.radioGroup && this.radioGroup.required;\n    }\n    set required(value) {\n      if (value !== this._required) {\n        this._changeDetector.markForCheck();\n      }\n      this._required = value;\n    }\n    /**\n     * Theme color of the radio button. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      // As per M2 design specifications the selection control radio should use the accent color\n      // palette by default. https://m2.material.io/components/radio-buttons#specs\n      return this._color || this.radioGroup && this.radioGroup.color || this._defaultOptions && this._defaultOptions.color || 'accent';\n    }\n    set color(newValue) {\n      this._color = newValue;\n    }\n    _color;\n    /** Whether the radio button should remain interactive when it is disabled. */\n    get disabledInteractive() {\n      return this._disabledInteractive || this.radioGroup !== null && this.radioGroup.disabledInteractive;\n    }\n    set disabledInteractive(value) {\n      this._disabledInteractive = value;\n    }\n    _disabledInteractive;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** The parent radio group. May or may not be present. */\n    radioGroup;\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether this radio is checked. */\n    _checked = false;\n    /** Whether this radio is disabled. */\n    _disabled;\n    /** Whether this radio is required. */\n    _required;\n    /** Value assigned to this radio. */\n    _value = null;\n    /** Unregister function for _radioDispatcher */\n    _removeUniqueSelectionListener = () => {};\n    /** Previous value of the input's tabindex. */\n    _previousTabIndex;\n    /** The native `<input type=radio>` element */\n    _inputElement;\n    /** Trigger elements for the ripple events. */\n    _rippleTrigger;\n    /** Whether animations are disabled. */\n    _noopAnimations;\n    _injector = inject(Injector);\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const radioGroup = inject(MAT_RADIO_GROUP, {\n        optional: true\n      });\n      const animationMode = inject(ANIMATION_MODULE_TYPE, {\n        optional: true\n      });\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      // Assertions. Ideally these should be stripped out by the compiler.\n      // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n      this.radioGroup = radioGroup;\n      this._noopAnimations = animationMode === 'NoopAnimations';\n      this._disabledInteractive = this._defaultOptions?.disabledInteractive ?? false;\n      if (tabIndex) {\n        this.tabIndex = numberAttribute(tabIndex, 0);\n      }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._inputElement, origin, options);\n      } else {\n        this._inputElement.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n      // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n      // update radio button's status\n      this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n      if (this.radioGroup) {\n        // If the radio is inside a radio group, determine if it should be checked\n        this.checked = this.radioGroup.value === this._value;\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n        // Copy name from parent radio group\n        this.name = this.radioGroup.name;\n      }\n      this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n        if (id !== this.id && name === this.name) {\n          this.checked = false;\n        }\n      });\n    }\n    ngDoCheck() {\n      this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n      this._updateTabIndex();\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n        if (!focusOrigin && this.radioGroup) {\n          this.radioGroup._touch();\n        }\n      });\n      // We bind this outside of the zone, because:\n      // 1. Its logic is completely DOM-related so we can avoid some change detections.\n      // 2. There appear to be some internal tests that break when this triggers a change detection.\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupClick = this._renderer.listen(this._inputElement.nativeElement, 'click', this._onInputClick);\n      });\n    }\n    ngOnDestroy() {\n      this._cleanupClick?.();\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n      this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n      return this.disableRipple || this.disabled;\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n      // We always have to stop propagation on the change event.\n      // Otherwise the change event, from the input element, will bubble up and\n      // emit its event object to the `change` output.\n      event.stopPropagation();\n      if (!this.checked && !this.disabled) {\n        const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n        this.checked = true;\n        this._emitChangeEvent();\n        if (this.radioGroup) {\n          this.radioGroup._controlValueAccessorChangeFn(this.value);\n          if (groupValueChanged) {\n            this.radioGroup._emitChangeEvent();\n          }\n        }\n      }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n      this._onInputInteraction(event);\n      if (!this.disabled || this.disabledInteractive) {\n        // Normally the input should be focused already, but if the click\n        // comes from the touch target, then we might have to focus it ourselves.\n        this._inputElement?.nativeElement.focus();\n      }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n      if (this._disabled !== value) {\n        this._disabled = value;\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** Called when the input is clicked. */\n    _onInputClick = event => {\n      // If the input is disabled while interactive, we need to prevent the\n      // selection from happening in this event handler. Note that even though\n      // this happens on `click` events, the logic applies when the user is\n      // navigating with the keyboard as well. An alternative way of doing\n      // this is by resetting the `checked` state in the `change` callback but\n      // it isn't optimal, because it can allow a pre-checked disabled button\n      // to be un-checked. This approach seems to cover everything.\n      if (this.disabled && this.disabledInteractive) {\n        event.preventDefault();\n      }\n    };\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n      const group = this.radioGroup;\n      let value;\n      // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n      // necessary, because the browser handles the tab order for inputs inside a group automatically,\n      // but we need an explicitly higher tabindex for the selected button in order for things like\n      // the focus trap to pick it up correctly.\n      if (!group || !group.selected || this.disabled) {\n        value = this.tabIndex;\n      } else {\n        value = group.selected === this ? this.tabIndex : -1;\n      }\n      if (value !== this._previousTabIndex) {\n        // We have to set the tabindex directly on the DOM node, because it depends on\n        // the selected state which is prone to \"changed after checked errors\".\n        const input = this._inputElement?.nativeElement;\n        if (input) {\n          input.setAttribute('tabindex', value + '');\n          this._previousTabIndex = value;\n          // Wait for any pending tabindex changes to be applied\n          afterNextRender(() => {\n            queueMicrotask(() => {\n              // The radio group uses a \"selection follows focus\" pattern for tab management, so if this\n              // radio button is currently focused and another radio button in the group becomes\n              // selected, we should move focus to the newly selected radio button to maintain\n              // consistency between the focused and selected states.\n              if (group && group.selected && group.selected !== this && document.activeElement === input) {\n                group.selected?._inputElement.nativeElement.focus();\n                // If this radio button still has focus, the selected one must be disabled. In this\n                // case the radio group as a whole should lose focus.\n                if (document.activeElement === input) {\n                  this._inputElement.nativeElement.blur();\n                }\n              }\n            });\n          }, {\n            injector: this._injector\n          });\n        }\n      }\n    }\n    static ɵfac = function MatRadioButton_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioButton)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      viewQuery: function MatRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rippleTrigger = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 19,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"mat-mdc-radio-disabled\", ctx.disabled)(\"mat-mdc-radio-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [2, \"checked\", \"checked\", booleanAttribute],\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        color: \"color\",\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioButton\"],\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 17,\n      consts: [[\"formField\", \"\"], [\"input\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", \"aria-invalid\", \"false\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 5, 1);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputInteraction($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._rippleTrigger.nativeElement)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mdc-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled])~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mdc-radio-state-layer-size, 40px);height:var(--mdc-radio-state-layer-size, 40px);top:calc(-1*(var(--mdc-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mdc-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mdc-radio-state-layer-size, 40px);height:var(--mdc-radio-state-layer-size, 40px)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-sys-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple>.mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio>.mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display, block)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatRadioButton;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioModule = /*#__PURE__*/(() => {\n  class MatRadioModule {\n    static ɵfac = function MatRadioModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule, MatRadioButton, MatCommonModule]\n    });\n  }\n  return MatRadioModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n//# sourceMappingURL=radio.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}