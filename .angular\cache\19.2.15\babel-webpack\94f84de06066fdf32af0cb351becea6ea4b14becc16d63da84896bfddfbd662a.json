{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { TeacherAppointmentRequestsEnum } from '../../../../../../../core/enums/teacher-appointment-requests-enums/teacher-appointment-requests-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"../../../../../../../core/services/teacher-appointment-services/teacher-appointment.service\";\nimport * as i3 from \"../../../../../../../core/services/alertify-services/alertify.service\";\nimport * as i4 from \"../../../../../../../core/services/lookup-services/lookup.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-teacher-appointment-requests-grid\", 14);\n    i0.ɵɵlistener(\"rejectTeacherAppointmentRequest\", function AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_rejectTeacherAppointmentRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectTeacherAppointmentRequestMethod($event));\n    })(\"teacherAppointmentRequest\", function AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptTeacherAppointmentRequest($event));\n    })(\"acceptAllTeacherAppointmentRequestsChecked\", function AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_acceptAllTeacherAppointmentRequestsChecked_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllTeacherAppointmentRequestsChecked());\n    })(\"teacherAppointmentFilterEvent\", function AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherAppointmentPendingChangePage($event));\n    })(\"userIdInput\", function AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_userIdInput_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherAppointmentRequestsItems\", ctx_r1.teacherAppointmentRequestsList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"teacherAppointmentRequestsEnum\", ctx_r1.statusEnum.Pending)(\"totalCount\", ctx_r1.totalCount)(\"teacherAppointmentFilterRequestModel\", ctx_r1.teacherAppointmentFilterRequestModel);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentRequestsTabComponent_div_0_div_20_app_teacher_appointment_requests_grid_1_Template, 1, 5, \"app-teacher-appointment-requests-grid\", 13)(2, AppointmentRequestsTabComponent_div_0_div_20_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length === 0);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-teacher-appointment-requests-grid\", 17);\n    i0.ɵɵlistener(\"teacherAppointmentRequest\", function AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptTeacherAppointmentRequest($event));\n    })(\"acceptAllTeacherAppointmentRequestsChecked\", function AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_acceptAllTeacherAppointmentRequestsChecked_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllTeacherAppointmentRequestsChecked());\n    })(\"teacherAppointmentFilterEvent\", function AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherAppointmentAcceptChangePage($event));\n    })(\"userIdInput\", function AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_userIdInput_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherAppointmentRequestsItems\", ctx_r1.teacherAppointmentRequestsList)(\"teacherAppointmentRequestsEnum\", ctx_r1.statusEnum.Accept)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"totalCount\", ctx_r1.totalCount)(\"teacherAppointmentFilterRequestModel\", ctx_r1.teacherAppointmentFilterRequestModel);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentRequestsTabComponent_div_0_div_21_app_teacher_appointment_requests_grid_1_Template, 1, 5, \"app-teacher-appointment-requests-grid\", 16)(2, AppointmentRequestsTabComponent_div_0_div_21_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length === 0);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_22_app_teacher_appointment_requests_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-teacher-appointment-requests-grid\", 19);\n    i0.ɵɵlistener(\"teacherAppointmentRequest\", function AppointmentRequestsTabComponent_div_0_div_22_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentRequest_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectTeacherAppointmentRequestMethod($event));\n    })(\"teacherAppointmentFilterEvent\", function AppointmentRequestsTabComponent_div_0_div_22_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_teacherAppointmentFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherAppointmentRejectedChangePage($event));\n    })(\"userIdInput\", function AppointmentRequestsTabComponent_div_0_div_22_app_teacher_appointment_requests_grid_1_Template_app_teacher_appointment_requests_grid_userIdInput_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendUserIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherAppointmentRequestsItems\", ctx_r1.teacherAppointmentRequestsList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"teacherAppointmentRequestsEnum\", ctx_r1.statusEnum.Rejected)(\"totalCount\", ctx_r1.totalCount)(\"teacherAppointmentFilterRequestModel\", ctx_r1.teacherAppointmentFilterRequestModel);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentRequestsTabComponent_div_0_div_22_app_teacher_appointment_requests_grid_1_Template, 1, 5, \"app-teacher-appointment-requests-grid\", 18)(2, AppointmentRequestsTabComponent_div_0_div_22_div_2_Template, 4, 3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherAppointmentRequestsList.length === 0);\n  }\n}\nfunction AppointmentRequestsTabComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"app-search-input\", 5);\n    i0.ɵɵlistener(\"searchTerm\", function AppointmentRequestsTabComponent_div_0_Template_app_search_input_searchTerm_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function AppointmentRequestsTabComponent_div_0_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAdvancedSearch());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function AppointmentRequestsTabComponent_div_0_Template_div_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPendingChange());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function AppointmentRequestsTabComponent_div_0_Template_div_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptChange());\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function AppointmentRequestsTabComponent_div_0_Template_div_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRejectedChange());\n    });\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11);\n    i0.ɵɵtemplate(20, AppointmentRequestsTabComponent_div_0_div_20_Template, 3, 2, \"div\", 12)(21, AppointmentRequestsTabComponent_div_0_div_21_Template, 3, 2, \"div\", 12)(22, AppointmentRequestsTabComponent_div_0_div_22_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"searchKey\", ctx_r1.teacherAppointmentFilterRequestModel.usrName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 11, \"TEACHER_APPOINTMENT.ADVANCED_SEARCH\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Pending));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 13, \"STUDENT_VACATION.NEW_VACATION_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Accept));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 15, \"STUDENT_VACATION.ACCEPTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Rejected));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 17, \"STUDENT_VACATION.REJECTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Accept);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Rejected);\n  }\n}\nfunction AppointmentRequestsTabComponent_app_teacher_details_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-teacher-details-view\", 20);\n    i0.ɵɵlistener(\"hideUserDetails\", function AppointmentRequestsTabComponent_app_teacher_details_view_1_Template_app_teacher_details_view_hideUserDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideUserDetailsView($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resiveUserId\", ctx_r1.sendUserID);\n  }\n}\nexport let AppointmentRequestsTabComponent = /*#__PURE__*/(() => {\n  class AppointmentRequestsTabComponent {\n    translate;\n    teacherAppointmentService;\n    alertify;\n    lookupService;\n    advancedSearchEvent = new EventEmitter();\n    itemTeacherAppointmentReq = new EventEmitter();\n    closePopup = new EventEmitter();\n    collectionOfLookup = {};\n    listOfLookup = ['DAYS'];\n    sendUserID;\n    teacherAppointmentRequestsList = [];\n    teacherAppointmentFilterRequestModel = {\n      statusNum: TeacherAppointmentRequestsEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: 'requestdate',\n      sortOrder: 1,\n      page: 1\n    };\n    totalCount = 0;\n    numberItemsPerRow = 3;\n    ids = [];\n    typeEnum = TeacherAppointmentRequestsEnum.Pending;\n    showTap = TeacherAppointmentRequestsEnum.Pending;\n    statusEnum = TeacherAppointmentRequestsEnum;\n    showUserDetailsView = false;\n    constructor(translate, teacherAppointmentService, alertify, lookupService) {\n      this.translate = translate;\n      this.teacherAppointmentService = teacherAppointmentService;\n      this.alertify = alertify;\n      this.lookupService = lookupService;\n    }\n    ngOnInit() {\n      this.teacherAppointmentFilterRequestModel.sortField = 'requestdate';\n      this.getTeacherAppointmentRequests();\n      this.getLookupByKey();\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookup).subscribe(res => {\n        this.collectionOfLookup = res.data;\n      });\n    }\n    searchByText(searchKey) {\n      this.teacherAppointmentFilterRequestModel.usrName = searchKey;\n      this.getTeacherAppointmentRequests();\n    }\n    getTeacherAppointmentRequests() {\n      this.teacherAppointmentService.getTeachersAppointmentRequestsFilterAdminViewWithAdvancedSearch(this.teacherAppointmentFilterRequestModel || {}).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.teacherAppointmentRequestsList = res.data;\n          this.totalCount = res.count ? res.count : 0;\n          if (this.teacherAppointmentFilterRequestModel.skip > 0 && (!this.teacherAppointmentRequestsList || this.teacherAppointmentRequestsList.length === 0)) {\n            this.teacherAppointmentFilterRequestModel.page -= 1;\n            this.teacherAppointmentFilterRequestModel.skip = (this.teacherAppointmentFilterRequestModel.page - 1) * this.teacherAppointmentFilterRequestModel.take;\n            this.getTeacherAppointmentRequests();\n          }\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onPendingChange() {\n      this.teacherAppointmentFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacherAppointmentRequestsEnum.Pending,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: 1,\n        page: 1\n      };\n      this.showTap = TeacherAppointmentRequestsEnum.Pending;\n      this.closeAvancedSearch();\n      this.getTeacherAppointmentRequests();\n    }\n    onAcceptChange() {\n      this.teacherAppointmentFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacherAppointmentRequestsEnum.Accept,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: 1,\n        page: 1\n      };\n      this.showTap = TeacherAppointmentRequestsEnum.Accept;\n      this.closeAvancedSearch();\n      this.getTeacherAppointmentRequests();\n    }\n    onRejectedChange() {\n      this.teacherAppointmentFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacherAppointmentRequestsEnum.Rejected,\n        skip: 0,\n        take: 9,\n        sortField: 'requestdate',\n        sortOrder: 1,\n        page: 1\n      };\n      this.showTap = TeacherAppointmentRequestsEnum.Rejected;\n      this.closeAvancedSearch();\n      this.getTeacherAppointmentRequests();\n    }\n    rejectTeacherAppointmentRequestMethod(event) {\n      this.itemTeacherAppointmentReq.emit(event);\n    }\n    acceptTeacherAppointmentRequest(studentProgramVacationModel) {\n      this.ids?.push(studentProgramVacationModel.reqId || '');\n      this.teacherAppointmentService.teacherAvailableTimeRequestAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getTeacherAppointmentRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    closeAvancedSearch() {\n      this.teacherAppointmentFilterRequestModel.usrName = '';\n      this.teacherAppointmentFilterRequestModel.numberRequest = undefined;\n      this.teacherAppointmentFilterRequestModel.fromDate = undefined;\n      this.teacherAppointmentFilterRequestModel.toDate = undefined;\n      this.teacherAppointmentFilterRequestModel.skip = 0;\n      this.teacherAppointmentFilterRequestModel.take = 9;\n      this.teacherAppointmentFilterRequestModel.sortField = 'requestdate';\n      this.teacherAppointmentFilterRequestModel.sortOrder = 1;\n      this.teacherAppointmentFilterRequestModel.page = 1;\n      this.closePopup.emit(); // as per issue number 3250\n    }\n    acceptAllTeacherAppointmentRequestsChecked() {\n      this.ids = this.teacherAppointmentRequestsList?.filter(i => i.checked).map(a => a.reqId || '');\n      this.teacherAppointmentService.teacherAvailableTimeRequestAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getTeacherAppointmentRequests();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    teacherAppointmentPendingChangePage(event) {\n      this.teacherAppointmentFilterRequestModel.statusNum = TeacherAppointmentRequestsEnum.Pending;\n      this.teacherAppointmentFilterRequestModel = event;\n      this.getTeacherAppointmentRequests();\n    }\n    teacherAppointmentAcceptChangePage(event) {\n      this.teacherAppointmentFilterRequestModel.statusNum = TeacherAppointmentRequestsEnum.Accept;\n      this.teacherAppointmentFilterRequestModel = event;\n      this.getTeacherAppointmentRequests();\n    }\n    teacherAppointmentRejectedChangePage(event) {\n      this.teacherAppointmentFilterRequestModel.statusNum = TeacherAppointmentRequestsEnum.Rejected;\n      this.teacherAppointmentFilterRequestModel = event;\n      this.getTeacherAppointmentRequests();\n    }\n    openAdvancedSearch() {\n      this.advancedSearchEvent.emit(this.teacherAppointmentFilterRequestModel);\n    }\n    advancedSearch(model) {\n      this.teacherAppointmentFilterRequestModel = model || {\n        skip: 0,\n        take: 9,\n        sortField: '',\n        sortOrder: 1,\n        page: 1\n      };\n      this.getTeacherAppointmentRequests();\n    }\n    hideUserDetailsView(event) {\n      this.showUserDetailsView = event;\n    }\n    sendUserIDEvent(event) {\n      this.sendUserID = event;\n      this.showUserDetailsView = true;\n    }\n    static ɵfac = function AppointmentRequestsTabComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppointmentRequestsTabComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.TeacherAppointmentService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.LookupService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppointmentRequestsTabComponent,\n      selectors: [[\"app-appointment-requests-tab\"]],\n      outputs: {\n        advancedSearchEvent: \"advancedSearchEvent\",\n        itemTeacherAppointmentReq: \"itemTeacherAppointmentReq\",\n        closePopup: \"closePopup\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid reuest_container\", 4, \"ngIf\"], [3, \"resiveUserId\", \"hideUserDetails\", 4, \"ngIf\"], [1, \"container-fluid\", \"reuest_container\"], [1, \"row\"], [1, \"col-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [1, \"mr-3\", \"ml-3\", 3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", 3, \"click\"], [1, \"col-12\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [4, \"ngIf\"], [3, \"teacherAppointmentRequestsItems\", \"numberPerRow\", \"teacherAppointmentRequestsEnum\", \"totalCount\", \"teacherAppointmentFilterRequestModel\", \"rejectTeacherAppointmentRequest\", \"teacherAppointmentRequest\", \"acceptAllTeacherAppointmentRequestsChecked\", \"teacherAppointmentFilterEvent\", \"userIdInput\", 4, \"ngIf\"], [3, \"rejectTeacherAppointmentRequest\", \"teacherAppointmentRequest\", \"acceptAllTeacherAppointmentRequestsChecked\", \"teacherAppointmentFilterEvent\", \"userIdInput\", \"teacherAppointmentRequestsItems\", \"numberPerRow\", \"teacherAppointmentRequestsEnum\", \"totalCount\", \"teacherAppointmentFilterRequestModel\"], [1, \"No_data\"], [3, \"teacherAppointmentRequestsItems\", \"teacherAppointmentRequestsEnum\", \"numberPerRow\", \"totalCount\", \"teacherAppointmentFilterRequestModel\", \"teacherAppointmentRequest\", \"acceptAllTeacherAppointmentRequestsChecked\", \"teacherAppointmentFilterEvent\", \"userIdInput\", 4, \"ngIf\"], [3, \"teacherAppointmentRequest\", \"acceptAllTeacherAppointmentRequestsChecked\", \"teacherAppointmentFilterEvent\", \"userIdInput\", \"teacherAppointmentRequestsItems\", \"teacherAppointmentRequestsEnum\", \"numberPerRow\", \"totalCount\", \"teacherAppointmentFilterRequestModel\"], [3, \"teacherAppointmentRequestsItems\", \"numberPerRow\", \"teacherAppointmentRequestsEnum\", \"totalCount\", \"teacherAppointmentFilterRequestModel\", \"teacherAppointmentRequest\", \"teacherAppointmentFilterEvent\", \"userIdInput\", 4, \"ngIf\"], [3, \"teacherAppointmentRequest\", \"teacherAppointmentFilterEvent\", \"userIdInput\", \"teacherAppointmentRequestsItems\", \"numberPerRow\", \"teacherAppointmentRequestsEnum\", \"totalCount\", \"teacherAppointmentFilterRequestModel\"], [3, \"hideUserDetails\", \"resiveUserId\"]],\n      template: function AppointmentRequestsTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppointmentRequestsTabComponent_div_0_Template, 23, 25, \"div\", 0)(1, AppointmentRequestsTabComponent_app_teacher_details_view_1_Template, 1, 1, \"app-teacher-details-view\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.showUserDetailsView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserDetailsView);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i1.TranslatePipe],\n      styles: [\".reuest_container[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:74vh;margin-top:1rem}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.reuest_container[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.reuest_container[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:55vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}.reuest_container[_ngcontent-%COMP%]   .w-60[_ngcontent-%COMP%]{width:60%}.reuest_container[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(en){margin-left:66.666667%}.reuest_container[_ngcontent-%COMP%]   .offset-8[_ngcontent-%COMP%]:lang(ar){margin-right:66.666667%}\"]\n    });\n  }\n  return AppointmentRequestsTabComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}