{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { keys, filter, each, isArray, indexOf } from 'zrender/lib/core/util.js';\nimport { ELEMENT_ANIMATABLE_PROPS } from './customGraphicTransition.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { warn } from '../util/log.js';\nimport { makeInner } from '../util/model.js';\nvar getStateToRestore = makeInner();\nvar KEYFRAME_EXCLUDE_KEYS = ['percent', 'easing', 'shape', 'style', 'extra'];\n/**\r\n * Stop previous keyframe animation and restore the attributes.\r\n * Avoid new keyframe animation starts with wrong internal state when the percent: 0 is not set.\r\n */\nexport function stopPreviousKeyframeAnimationAndRestore(el) {\n  // Stop previous keyframe animation.\n  el.stopAnimation('keyframe');\n  // Restore\n  el.attr(getStateToRestore(el));\n}\nexport function applyKeyframeAnimation(el, animationOpts, animatableModel) {\n  if (!animatableModel.isAnimationEnabled() || !animationOpts) {\n    return;\n  }\n  if (isArray(animationOpts)) {\n    each(animationOpts, function (singleAnimationOpts) {\n      applyKeyframeAnimation(el, singleAnimationOpts, animatableModel);\n    });\n    return;\n  }\n  var keyframes = animationOpts.keyframes;\n  var duration = animationOpts.duration;\n  if (animatableModel && duration == null) {\n    // Default to use duration of config.\n    // NOTE: animation config from payload will be ignored because they are mainly for transitions.\n    var config = getAnimationConfig('enter', animatableModel, 0);\n    duration = config && config.duration;\n  }\n  if (!keyframes || !duration) {\n    return;\n  }\n  var stateToRestore = getStateToRestore(el);\n  each(ELEMENT_ANIMATABLE_PROPS, function (targetPropName) {\n    if (targetPropName && !el[targetPropName]) {\n      return;\n    }\n    var animator;\n    var endFrameIsSet = false;\n    // Sort keyframes by percent.\n    keyframes.sort(function (a, b) {\n      return a.percent - b.percent;\n    });\n    each(keyframes, function (kf) {\n      // Stop current animation.\n      var animators = el.animators;\n      var kfValues = targetPropName ? kf[targetPropName] : kf;\n      if (process.env.NODE_ENV !== 'production') {\n        if (kf.percent >= 1) {\n          endFrameIsSet = true;\n        }\n      }\n      if (!kfValues) {\n        return;\n      }\n      var propKeys = keys(kfValues);\n      if (!targetPropName) {\n        // PENDING performance?\n        propKeys = filter(propKeys, function (key) {\n          return indexOf(KEYFRAME_EXCLUDE_KEYS, key) < 0;\n        });\n      }\n      if (!propKeys.length) {\n        return;\n      }\n      if (!animator) {\n        animator = el.animate(targetPropName, animationOpts.loop, true);\n        animator.scope = 'keyframe';\n      }\n      for (var i = 0; i < animators.length; i++) {\n        // Stop all other animation that is not keyframe.\n        if (animators[i] !== animator && animators[i].targetName === animator.targetName) {\n          animators[i].stopTracks(propKeys);\n        }\n      }\n      targetPropName && (stateToRestore[targetPropName] = stateToRestore[targetPropName] || {});\n      var savedTarget = targetPropName ? stateToRestore[targetPropName] : stateToRestore;\n      each(propKeys, function (key) {\n        // Save original value.\n        savedTarget[key] = ((targetPropName ? el[targetPropName] : el) || {})[key];\n      });\n      animator.whenWithKeys(duration * kf.percent, kfValues, propKeys, kf.easing);\n    });\n    if (!animator) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!endFrameIsSet) {\n        warn('End frame with percent: 1 is missing in the keyframeAnimation.', true);\n      }\n    }\n    animator.delay(animationOpts.delay || 0).duration(duration).start(animationOpts.easing);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}