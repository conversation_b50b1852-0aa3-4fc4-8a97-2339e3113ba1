{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport * as layout from '../../util/layout.js';\nimport * as helper from './helper.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar PiecewiseVisualMapView = /** @class */function (_super) {\n  __extends(PiecewiseVisualMapView, _super);\n  function PiecewiseVisualMapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseVisualMapView.type;\n    return _this;\n  }\n  PiecewiseVisualMapView.prototype.doRender = function () {\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var textStyleModel = visualMapModel.textStyleModel;\n    var textFont = textStyleModel.getFont();\n    var textFill = textStyleModel.getTextColor();\n    var itemAlign = this._getItemAlign();\n    var itemSize = visualMapModel.itemSize;\n    var viewData = this._getViewData();\n    var endsText = viewData.endsText;\n    var showLabel = zrUtil.retrieve(visualMapModel.get('showLabel', true), !endsText);\n    var silent = !visualMapModel.get('selectedMode');\n    endsText && this._renderEndsText(thisGroup, endsText[0], itemSize, showLabel, itemAlign);\n    zrUtil.each(viewData.viewPieceList, function (item) {\n      var piece = item.piece;\n      var itemGroup = new graphic.Group();\n      itemGroup.onclick = zrUtil.bind(this._onItemClick, this, piece);\n      this._enableHoverLink(itemGroup, item.indexInModelPieceList);\n      // TODO Category\n      var representValue = visualMapModel.getRepresentValue(piece);\n      this._createItemSymbol(itemGroup, representValue, [0, 0, itemSize[0], itemSize[1]], silent);\n      if (showLabel) {\n        var visualState = this.visualMapModel.getValueState(representValue);\n        itemGroup.add(new graphic.Text({\n          style: {\n            x: itemAlign === 'right' ? -textGap : itemSize[0] + textGap,\n            y: itemSize[1] / 2,\n            text: piece.text,\n            verticalAlign: 'middle',\n            align: itemAlign,\n            font: textFont,\n            fill: textFill,\n            opacity: visualState === 'outOfRange' ? 0.5 : 1\n          },\n          silent: silent\n        }));\n      }\n      thisGroup.add(itemGroup);\n    }, this);\n    endsText && this._renderEndsText(thisGroup, endsText[1], itemSize, showLabel, itemAlign);\n    layout.box(visualMapModel.get('orient'), thisGroup, visualMapModel.get('itemGap'));\n    this.renderBackground(thisGroup);\n    this.positionGroup(thisGroup);\n  };\n  PiecewiseVisualMapView.prototype._enableHoverLink = function (itemGroup, pieceIndex) {\n    var _this = this;\n    itemGroup.on('mouseover', function () {\n      return onHoverLink('highlight');\n    }).on('mouseout', function () {\n      return onHoverLink('downplay');\n    });\n    var onHoverLink = function (method) {\n      var visualMapModel = _this.visualMapModel;\n      // TODO: TYPE More detailed action types\n      visualMapModel.option.hoverLink && _this.api.dispatchAction({\n        type: method,\n        batch: helper.makeHighDownBatch(visualMapModel.findTargetDataIndices(pieceIndex), visualMapModel)\n      });\n    };\n  };\n  PiecewiseVisualMapView.prototype._getItemAlign = function () {\n    var visualMapModel = this.visualMapModel;\n    var modelOption = visualMapModel.option;\n    if (modelOption.orient === 'vertical') {\n      return helper.getItemAlign(visualMapModel, this.api, visualMapModel.itemSize);\n    } else {\n      // horizontal, most case left unless specifying right.\n      var align = modelOption.align;\n      if (!align || align === 'auto') {\n        align = 'left';\n      }\n      return align;\n    }\n  };\n  PiecewiseVisualMapView.prototype._renderEndsText = function (group, text, itemSize, showLabel, itemAlign) {\n    if (!text) {\n      return;\n    }\n    var itemGroup = new graphic.Group();\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: showLabel ? itemAlign === 'right' ? itemSize[0] : 0 : itemSize[0] / 2,\n        y: itemSize[1] / 2,\n        verticalAlign: 'middle',\n        align: showLabel ? itemAlign : 'center',\n        text: text\n      })\n    }));\n    group.add(itemGroup);\n  };\n  /**\r\n   * @private\r\n   * @return {Object} {peiceList, endsText} The order is the same as screen pixel order.\r\n   */\n  PiecewiseVisualMapView.prototype._getViewData = function () {\n    var visualMapModel = this.visualMapModel;\n    var viewPieceList = zrUtil.map(visualMapModel.getPieceList(), function (piece, index) {\n      return {\n        piece: piece,\n        indexInModelPieceList: index\n      };\n    });\n    var endsText = visualMapModel.get('text');\n    // Consider orient and inverse.\n    var orient = visualMapModel.get('orient');\n    var inverse = visualMapModel.get('inverse');\n    // Order of model pieceList is always [low, ..., high]\n    if (orient === 'horizontal' ? inverse : !inverse) {\n      viewPieceList.reverse();\n    }\n    // Origin order of endsText is [high, low]\n    else if (endsText) {\n      endsText = endsText.slice().reverse();\n    }\n    return {\n      viewPieceList: viewPieceList,\n      endsText: endsText\n    };\n  };\n  PiecewiseVisualMapView.prototype._createItemSymbol = function (group, representValue, shapeParam, silent) {\n    var itemSymbol = createSymbol(\n    // symbol will be string\n    this.getControllerVisual(representValue, 'symbol'), shapeParam[0], shapeParam[1], shapeParam[2], shapeParam[3],\n    // color will be string\n    this.getControllerVisual(representValue, 'color'));\n    itemSymbol.silent = silent;\n    group.add(itemSymbol);\n  };\n  PiecewiseVisualMapView.prototype._onItemClick = function (piece) {\n    var visualMapModel = this.visualMapModel;\n    var option = visualMapModel.option;\n    var selectedMode = option.selectedMode;\n    if (!selectedMode) {\n      return;\n    }\n    var selected = zrUtil.clone(option.selected);\n    var newKey = visualMapModel.getSelectedMapKey(piece);\n    if (selectedMode === 'single' || selectedMode === true) {\n      selected[newKey] = true;\n      zrUtil.each(selected, function (o, key) {\n        selected[key] = key === newKey;\n      });\n    } else {\n      selected[newKey] = !selected[newKey];\n    }\n    this.api.dispatchAction({\n      type: 'selectDataRange',\n      from: this.uid,\n      visualMapId: this.visualMapModel.id,\n      selected: selected\n    });\n  };\n  PiecewiseVisualMapView.type = 'visualMap.piecewise';\n  return PiecewiseVisualMapView;\n}(VisualMapView);\nexport default PiecewiseVisualMapView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}