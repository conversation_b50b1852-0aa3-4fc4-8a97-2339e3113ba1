{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO depends on DataZoom and Brush\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../../helper/BrushController.js';\nimport BrushTargetManager from '../../helper/BrushTargetManager.js';\nimport * as history from '../../dataZoom/history.js';\nimport sliderMove from '../../helper/sliderMove.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { makeInternalComponentId, parseFinder } from '../../../util/model.js';\nimport { registerInternalOptionCreator } from '../../../model/internalComponentCreator.js';\nvar each = zrUtil.each;\nvar DATA_ZOOM_ID_BASE = makeInternalComponentId('toolbox-dataZoom_');\nvar ICON_TYPES = ['zoom', 'back'];\nvar DataZoomFeature = /** @class */function (_super) {\n  __extends(DataZoomFeature, _super);\n  function DataZoomFeature() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DataZoomFeature.prototype.render = function (featureModel, ecModel, api, payload) {\n    if (!this._brushController) {\n      this._brushController = new BrushController(api.getZr());\n      this._brushController.on('brush', zrUtil.bind(this._onBrush, this)).mount();\n    }\n    updateZoomBtnStatus(featureModel, ecModel, this, payload, api);\n    updateBackBtnStatus(featureModel, ecModel);\n  };\n  DataZoomFeature.prototype.onclick = function (ecModel, api, type) {\n    handlers[type].call(this);\n  };\n  DataZoomFeature.prototype.remove = function (ecModel, api) {\n    this._brushController && this._brushController.unmount();\n  };\n  DataZoomFeature.prototype.dispose = function (ecModel, api) {\n    this._brushController && this._brushController.dispose();\n  };\n  DataZoomFeature.prototype._onBrush = function (eventParam) {\n    var areas = eventParam.areas;\n    if (!eventParam.isEnd || !areas.length) {\n      return;\n    }\n    var snapshot = {};\n    var ecModel = this.ecModel;\n    this._brushController.updateCovers([]); // remove cover\n    var brushTargetManager = new BrushTargetManager(makeAxisFinder(this.model), ecModel, {\n      include: ['grid']\n    });\n    brushTargetManager.matchOutputRanges(areas, ecModel, function (area, coordRange, coordSys) {\n      if (coordSys.type !== 'cartesian2d') {\n        return;\n      }\n      var brushType = area.brushType;\n      if (brushType === 'rect') {\n        setBatch('x', coordSys, coordRange[0]);\n        setBatch('y', coordSys, coordRange[1]);\n      } else {\n        setBatch({\n          lineX: 'x',\n          lineY: 'y'\n        }[brushType], coordSys, coordRange);\n      }\n    });\n    history.push(ecModel, snapshot);\n    this._dispatchZoomAction(snapshot);\n    function setBatch(dimName, coordSys, minMax) {\n      var axis = coordSys.getAxis(dimName);\n      var axisModel = axis.model;\n      var dataZoomModel = findDataZoom(dimName, axisModel, ecModel);\n      // Restrict range.\n      var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy(axisModel).getMinMaxSpan();\n      if (minMaxSpan.minValueSpan != null || minMaxSpan.maxValueSpan != null) {\n        minMax = sliderMove(0, minMax.slice(), axis.scale.getExtent(), 0, minMaxSpan.minValueSpan, minMaxSpan.maxValueSpan);\n      }\n      dataZoomModel && (snapshot[dataZoomModel.id] = {\n        dataZoomId: dataZoomModel.id,\n        startValue: minMax[0],\n        endValue: minMax[1]\n      });\n    }\n    function findDataZoom(dimName, axisModel, ecModel) {\n      var found;\n      ecModel.eachComponent({\n        mainType: 'dataZoom',\n        subType: 'select'\n      }, function (dzModel) {\n        var has = dzModel.getAxisModel(dimName, axisModel.componentIndex);\n        has && (found = dzModel);\n      });\n      return found;\n    }\n  };\n  ;\n  DataZoomFeature.prototype._dispatchZoomAction = function (snapshot) {\n    var batch = [];\n    // Convert from hash map to array.\n    each(snapshot, function (batchItem, dataZoomId) {\n      batch.push(zrUtil.clone(batchItem));\n    });\n    batch.length && this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      batch: batch\n    });\n  };\n  DataZoomFeature.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      filterMode: 'filter',\n      // Icon group\n      icon: {\n        zoom: 'M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1',\n        back: 'M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26'\n      },\n      // `zoom`, `back`\n      title: ecModel.getLocaleModel().get(['toolbox', 'dataZoom', 'title']),\n      brushStyle: {\n        borderWidth: 0,\n        color: 'rgba(210,219,238,0.2)'\n      }\n    };\n    return defaultOption;\n  };\n  return DataZoomFeature;\n}(ToolboxFeature);\nvar handlers = {\n  zoom: function () {\n    var nextActive = !this._isZoomActive;\n    this.api.dispatchAction({\n      type: 'takeGlobalCursor',\n      key: 'dataZoomSelect',\n      dataZoomSelectActive: nextActive\n    });\n  },\n  back: function () {\n    this._dispatchZoomAction(history.pop(this.ecModel));\n  }\n};\nfunction makeAxisFinder(dzFeatureModel) {\n  var setting = {\n    xAxisIndex: dzFeatureModel.get('xAxisIndex', true),\n    yAxisIndex: dzFeatureModel.get('yAxisIndex', true),\n    xAxisId: dzFeatureModel.get('xAxisId', true),\n    yAxisId: dzFeatureModel.get('yAxisId', true)\n  };\n  // If both `xAxisIndex` `xAxisId` not set, it means 'all'.\n  // If both `yAxisIndex` `yAxisId` not set, it means 'all'.\n  // Some old cases set like this below to close yAxis control but leave xAxis control:\n  // `{ feature: { dataZoom: { yAxisIndex: false } }`.\n  if (setting.xAxisIndex == null && setting.xAxisId == null) {\n    setting.xAxisIndex = 'all';\n  }\n  if (setting.yAxisIndex == null && setting.yAxisId == null) {\n    setting.yAxisIndex = 'all';\n  }\n  return setting;\n}\nfunction updateBackBtnStatus(featureModel, ecModel) {\n  featureModel.setIconStatus('back', history.count(ecModel) > 1 ? 'emphasis' : 'normal');\n}\nfunction updateZoomBtnStatus(featureModel, ecModel, view, payload, api) {\n  var zoomActive = view._isZoomActive;\n  if (payload && payload.type === 'takeGlobalCursor') {\n    zoomActive = payload.key === 'dataZoomSelect' ? payload.dataZoomSelectActive : false;\n  }\n  view._isZoomActive = zoomActive;\n  featureModel.setIconStatus('zoom', zoomActive ? 'emphasis' : 'normal');\n  var brushTargetManager = new BrushTargetManager(makeAxisFinder(featureModel), ecModel, {\n    include: ['grid']\n  });\n  var panels = brushTargetManager.makePanelOpts(api, function (targetInfo) {\n    return targetInfo.xAxisDeclared && !targetInfo.yAxisDeclared ? 'lineX' : !targetInfo.xAxisDeclared && targetInfo.yAxisDeclared ? 'lineY' : 'rect';\n  });\n  view._brushController.setPanels(panels).enableBrush(zoomActive && panels.length ? {\n    brushType: 'auto',\n    brushStyle: featureModel.getModel('brushStyle').getItemStyle()\n  } : false);\n}\nregisterInternalOptionCreator('dataZoom', function (ecModel) {\n  var toolboxModel = ecModel.getComponent('toolbox', 0);\n  var featureDataZoomPath = ['feature', 'dataZoom'];\n  if (!toolboxModel || toolboxModel.get(featureDataZoomPath) == null) {\n    return;\n  }\n  var dzFeatureModel = toolboxModel.getModel(featureDataZoomPath);\n  var dzOptions = [];\n  var finder = makeAxisFinder(dzFeatureModel);\n  var finderResult = parseFinder(ecModel, finder);\n  each(finderResult.xAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'xAxis', 'xAxisIndex');\n  });\n  each(finderResult.yAxisModels, function (axisModel) {\n    return buildInternalOptions(axisModel, 'yAxis', 'yAxisIndex');\n  });\n  function buildInternalOptions(axisModel, axisMainType, axisIndexPropName) {\n    var axisIndex = axisModel.componentIndex;\n    var newOpt = {\n      type: 'select',\n      $fromToolbox: true,\n      // Default to be filter\n      filterMode: dzFeatureModel.get('filterMode', true) || 'filter',\n      // Id for merge mapping.\n      id: DATA_ZOOM_ID_BASE + axisMainType + axisIndex\n    };\n    newOpt[axisIndexPropName] = axisIndex;\n    dzOptions.push(newOpt);\n  }\n  return dzOptions;\n});\nexport default DataZoomFeature;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}