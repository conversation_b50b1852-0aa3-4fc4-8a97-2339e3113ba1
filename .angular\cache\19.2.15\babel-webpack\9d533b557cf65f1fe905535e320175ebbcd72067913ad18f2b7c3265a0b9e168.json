{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction getCenterCoord(view, point) {\n  // Use projected coord as center because it's linear.\n  return view.pointToProjected ? view.pointToProjected(point) : view.pointToData(point);\n}\nexport function updateCenterAndZoom(view, payload, zoomLimit, api) {\n  var previousZoom = view.getZoom();\n  var center = view.getCenter();\n  var zoom = payload.zoom;\n  var point = view.projectedToPoint ? view.projectedToPoint(center) : view.dataToPoint(center);\n  if (payload.dx != null && payload.dy != null) {\n    point[0] -= payload.dx;\n    point[1] -= payload.dy;\n    view.setCenter(getCenterCoord(view, point), api);\n  }\n  if (zoom != null) {\n    if (zoomLimit) {\n      var zoomMin = zoomLimit.min || 0;\n      var zoomMax = zoomLimit.max || Infinity;\n      zoom = Math.max(Math.min(previousZoom * zoom, zoomMax), zoomMin) / previousZoom;\n    }\n    // Zoom on given point(originX, originY)\n    view.scaleX *= zoom;\n    view.scaleY *= zoom;\n    var fixX = (payload.originX - view.x) * (zoom - 1);\n    var fixY = (payload.originY - view.y) * (zoom - 1);\n    view.x -= fixX;\n    view.y -= fixY;\n    view.updateTransform();\n    // Get the new center\n    view.setCenter(getCenterCoord(view, point), api);\n    view.setZoom(zoom * previousZoom);\n  }\n  return {\n    center: view.getCenter(),\n    zoom: view.getZoom()\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}