{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Yoruba Nigeria [yo]\n//! author : Atolagbe Abisoye : https://github.com/andela-batolagbe\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var yo = moment.defineLocale('yo', {\n    months: 'Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀'.split('_'),\n    monthsShort: 'Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀'.split('_'),\n    weekdays: 'Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta'.split('_'),\n    weekdaysShort: 'Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá'.split('_'),\n    weekdaysMin: 'Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb'.split('_'),\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY h:mm A',\n      LLLL: 'dddd, D MMMM YYYY h:mm A'\n    },\n    calendar: {\n      sameDay: '[Ònì ni] LT',\n      nextDay: '[Ọ̀la ni] LT',\n      nextWeek: \"dddd [Ọsẹ̀ tón'bọ] [ni] LT\",\n      lastDay: '[Àna ni] LT',\n      lastWeek: 'dddd [Ọsẹ̀ tólọ́] [ni] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ní %s',\n      past: '%s kọjá',\n      s: 'ìsẹjú aayá die',\n      ss: 'aayá %d',\n      m: 'ìsẹjú kan',\n      mm: 'ìsẹjú %d',\n      h: 'wákati kan',\n      hh: 'wákati %d',\n      d: 'ọjọ́ kan',\n      dd: 'ọjọ́ %d',\n      M: 'osù kan',\n      MM: 'osù %d',\n      y: 'ọdún kan',\n      yy: 'ọdún %d'\n    },\n    dayOfMonthOrdinalParse: /ọjọ́\\s\\d{1,2}/,\n    ordinal: 'ọjọ́ %d',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return yo;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}