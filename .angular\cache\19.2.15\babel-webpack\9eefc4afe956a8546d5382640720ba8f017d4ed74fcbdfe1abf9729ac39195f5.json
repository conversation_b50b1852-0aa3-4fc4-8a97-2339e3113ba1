{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport RoamController from './RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enableComponentHighDownFeatures, setDefaultStateProxy } from '../../util/states.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { getUID } from '../../util/component.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner } from '../../util/model.js';\n/**\r\n * Only these tags enable use `itemStyle` if they are named in SVG.\r\n * Other tags like <text> <tspan> <image> might not suitable for `itemStyle`.\r\n * They will not be considered to be styled until some requirements come.\r\n */\nvar OPTION_STYLE_ENABLED_TAGS = ['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path'];\nvar OPTION_STYLE_ENABLED_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS);\nvar STATE_TRIGGER_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar LABEL_HOST_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar mapLabelRaw = makeInner();\nfunction getFixedItemStyle(model) {\n  var itemStyle = model.getItemStyle();\n  var areaColor = model.get('areaColor');\n  // If user want the color not to be changed when hover,\n  // they should both set areaColor and color to be null.\n  if (areaColor != null) {\n    itemStyle.fill = areaColor;\n  }\n  return itemStyle;\n}\n// Only stroke can be used for line.\n// Using fill in style if stroke not exits.\n// TODO Not sure yet. Perhaps a separate `lineStyle`?\nfunction fixLineStyle(styleHost) {\n  var style = styleHost.style;\n  if (style) {\n    style.stroke = style.stroke || style.fill;\n    style.fill = null;\n  }\n}\nvar MapDraw = /** @class */function () {\n  function MapDraw(api) {\n    var group = new graphic.Group();\n    this.uid = getUID('ec_map_draw');\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    this.group = group;\n    group.add(this._regionsGroup = new graphic.Group());\n    group.add(this._svgGroup = new graphic.Group());\n  }\n  MapDraw.prototype.draw = function (mapOrGeoModel, ecModel, api, fromView, payload) {\n    var isGeo = mapOrGeoModel.mainType === 'geo';\n    // Map series has data. GEO model that controlled by map series\n    // will be assigned with map data. Other GEO model has no data.\n    var data = mapOrGeoModel.getData && mapOrGeoModel.getData();\n    isGeo && ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'map'\n    }, function (mapSeries) {\n      if (!data && mapSeries.getHostGeoModel() === mapOrGeoModel) {\n        data = mapSeries.getData();\n      }\n    });\n    var geo = mapOrGeoModel.coordinateSystem;\n    var regionsGroup = this._regionsGroup;\n    var group = this.group;\n    var transformInfo = geo.getTransformInfo();\n    var transformInfoRaw = transformInfo.raw;\n    var transformInfoRoam = transformInfo.roam;\n    // No animation when first draw or in action\n    var isFirstDraw = !regionsGroup.childAt(0) || payload;\n    if (isFirstDraw) {\n      group.x = transformInfoRoam.x;\n      group.y = transformInfoRoam.y;\n      group.scaleX = transformInfoRoam.scaleX;\n      group.scaleY = transformInfoRoam.scaleY;\n      group.dirty();\n    } else {\n      graphic.updateProps(group, transformInfoRoam, mapOrGeoModel);\n    }\n    var isVisualEncodedByVisualMap = data && data.getVisual('visualMeta') && data.getVisual('visualMeta').length > 0;\n    var viewBuildCtx = {\n      api: api,\n      geo: geo,\n      mapOrGeoModel: mapOrGeoModel,\n      data: data,\n      isVisualEncodedByVisualMap: isVisualEncodedByVisualMap,\n      isGeo: isGeo,\n      transformInfoRaw: transformInfoRaw\n    };\n    if (geo.resourceType === 'geoJSON') {\n      this._buildGeoJSON(viewBuildCtx);\n    } else if (geo.resourceType === 'geoSVG') {\n      this._buildSVG(viewBuildCtx);\n    }\n    this._updateController(mapOrGeoModel, ecModel, api);\n    this._updateMapSelectHandler(mapOrGeoModel, regionsGroup, api, fromView);\n  };\n  MapDraw.prototype._buildGeoJSON = function (viewBuildCtx) {\n    var regionsGroupByName = this._regionsGroupByName = zrUtil.createHashMap();\n    var regionsInfoByName = zrUtil.createHashMap();\n    var regionsGroup = this._regionsGroup;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n    var data = viewBuildCtx.data;\n    var projection = viewBuildCtx.geo.projection;\n    var projectionStream = projection && projection.stream;\n    function transformPoint(point, project) {\n      if (project) {\n        // projection may return null point.\n        point = project(point);\n      }\n      return point && [point[0] * transformInfoRaw.scaleX + transformInfoRaw.x, point[1] * transformInfoRaw.scaleY + transformInfoRaw.y];\n    }\n    ;\n    function transformPolygonPoints(inPoints) {\n      var outPoints = [];\n      // If projectionStream is provided. Use it instead of single point project.\n      var project = !projectionStream && projection && projection.project;\n      for (var i = 0; i < inPoints.length; ++i) {\n        var newPt = transformPoint(inPoints[i], project);\n        newPt && outPoints.push(newPt);\n      }\n      return outPoints;\n    }\n    function getPolyShape(points) {\n      return {\n        shape: {\n          points: transformPolygonPoints(points)\n        }\n      };\n    }\n    regionsGroup.removeAll();\n    // Only when the resource is GeoJSON, there is `geo.regions`.\n    zrUtil.each(viewBuildCtx.geo.regions, function (region) {\n      var regionName = region.name;\n      // Consider in GeoJson properties.name may be duplicated, for example,\n      // there is multiple region named \"United Kindom\" or \"France\" (so many\n      // colonies). And it is not appropriate to merge them in geo, which\n      // will make them share the same label and bring trouble in label\n      // location calculation.\n      var regionGroup = regionsGroupByName.get(regionName);\n      var _a = regionsInfoByName.get(regionName) || {},\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      if (!regionGroup) {\n        regionGroup = regionsGroupByName.set(regionName, new graphic.Group());\n        regionsGroup.add(regionGroup);\n        dataIdx = data ? data.indexOfName(regionName) : null;\n        regionModel = viewBuildCtx.isGeo ? mapOrGeoModel.getRegionModel(regionName) : data ? data.getItemModel(dataIdx) : null;\n        var silent = regionModel.get('silent', true);\n        silent != null && (regionGroup.silent = silent);\n        regionsInfoByName.set(regionName, {\n          dataIdx: dataIdx,\n          regionModel: regionModel\n        });\n      }\n      var polygonSubpaths = [];\n      var polylineSubpaths = [];\n      zrUtil.each(region.geometries, function (geometry) {\n        // Polygon and MultiPolygon\n        if (geometry.type === 'polygon') {\n          var polys = [geometry.exterior].concat(geometry.interiors || []);\n          if (projectionStream) {\n            polys = projectPolys(polys, projectionStream);\n          }\n          zrUtil.each(polys, function (poly) {\n            polygonSubpaths.push(new graphic.Polygon(getPolyShape(poly)));\n          });\n        }\n        // LineString and MultiLineString\n        else {\n          var points = geometry.points;\n          if (projectionStream) {\n            points = projectPolys(points, projectionStream, true);\n          }\n          zrUtil.each(points, function (points) {\n            polylineSubpaths.push(new graphic.Polyline(getPolyShape(points)));\n          });\n        }\n      });\n      var centerPt = transformPoint(region.getCenter(), projection && projection.project);\n      function createCompoundPath(subpaths, isLine) {\n        if (!subpaths.length) {\n          return;\n        }\n        var compoundPath = new graphic.CompoundPath({\n          culling: true,\n          segmentIgnoreThreshold: 1,\n          shape: {\n            paths: subpaths\n          }\n        });\n        regionGroup.add(compoundPath);\n        applyOptionStyleForRegion(viewBuildCtx, compoundPath, dataIdx, regionModel);\n        resetLabelForRegion(viewBuildCtx, compoundPath, regionName, regionModel, mapOrGeoModel, dataIdx, centerPt);\n        if (isLine) {\n          fixLineStyle(compoundPath);\n          zrUtil.each(compoundPath.states, fixLineStyle);\n        }\n      }\n      createCompoundPath(polygonSubpaths);\n      createCompoundPath(polylineSubpaths, true);\n    });\n    // Ensure children have been added to `regionGroup` before calling them.\n    regionsGroupByName.each(function (regionGroup, regionName) {\n      var _a = regionsInfoByName.get(regionName),\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      resetEventTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel, dataIdx);\n      resetTooltipForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n      resetStateTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n    }, this);\n  };\n  MapDraw.prototype._buildSVG = function (viewBuildCtx) {\n    var mapName = viewBuildCtx.geo.map;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    this._svgGroup.x = transformInfoRaw.x;\n    this._svgGroup.y = transformInfoRaw.y;\n    this._svgGroup.scaleX = transformInfoRaw.scaleX;\n    this._svgGroup.scaleY = transformInfoRaw.scaleY;\n    if (this._svgResourceChanged(mapName)) {\n      this._freeSVG();\n      this._useSVG(mapName);\n    }\n    var svgDispatcherMap = this._svgDispatcherMap = zrUtil.createHashMap();\n    var focusSelf = false;\n    zrUtil.each(this._svgGraphicRecord.named, function (namedItem) {\n      // Note that we also allow different elements have the same name.\n      // For example, a glyph of a city and the label of the city have\n      // the same name and their tooltip info can be defined in a single\n      // region option.\n      var regionName = namedItem.name;\n      var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n      var data = viewBuildCtx.data;\n      var svgNodeTagLower = namedItem.svgNodeTagLower;\n      var el = namedItem.el;\n      var dataIdx = data ? data.indexOfName(regionName) : null;\n      var regionModel = mapOrGeoModel.getRegionModel(regionName);\n      if (OPTION_STYLE_ENABLED_TAG_MAP.get(svgNodeTagLower) != null && el instanceof Displayable) {\n        applyOptionStyleForRegion(viewBuildCtx, el, dataIdx, regionModel);\n      }\n      if (el instanceof Displayable) {\n        el.culling = true;\n      }\n      var silent = regionModel.get('silent', true);\n      silent != null && (el.silent = silent);\n      // We do not know how the SVG like so we'd better not to change z2.\n      // Otherwise it might bring some unexpected result. For example,\n      // an area hovered that make some inner city can not be clicked.\n      el.z2EmphasisLift = 0;\n      // If self named:\n      if (!namedItem.namedFrom) {\n        // label should batter to be displayed based on the center of <g>\n        // if it is named rather than displayed on each child.\n        if (LABEL_HOST_MAP.get(svgNodeTagLower) != null) {\n          resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx, null);\n        }\n        resetEventTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx);\n        resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n        if (STATE_TRIGGER_TAG_MAP.get(svgNodeTagLower) != null) {\n          var focus_1 = resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n          if (focus_1 === 'self') {\n            focusSelf = true;\n          }\n          var els = svgDispatcherMap.get(regionName) || svgDispatcherMap.set(regionName, []);\n          els.push(el);\n        }\n      }\n    }, this);\n    this._enableBlurEntireSVG(focusSelf, viewBuildCtx);\n  };\n  MapDraw.prototype._enableBlurEntireSVG = function (focusSelf, viewBuildCtx) {\n    // It's a little complicated to support blurring the entire geoSVG in series-map.\n    // So do not support it until some requirements come.\n    // At present, in series-map, only regions can be blurred.\n    if (focusSelf && viewBuildCtx.isGeo) {\n      var blurStyle = viewBuildCtx.mapOrGeoModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      // Only support `opacity` here. Because not sure that other props are suitable for\n      // all of the elements generated by SVG (especially for Text/TSpan/Image/... ).\n      var opacity_1 = blurStyle.opacity;\n      this._svgGraphicRecord.root.traverse(function (el) {\n        if (!el.isGroup) {\n          // PENDING: clear those settings to SVG elements when `_freeSVG`.\n          // (Currently it happen not to be needed.)\n          setDefaultStateProxy(el);\n          var style = el.ensureState('blur').style || {};\n          // Do not overwrite the region style that already set from region option.\n          if (style.opacity == null && opacity_1 != null) {\n            style.opacity = opacity_1;\n          }\n          // If `ensureState('blur').style = {}`, there will be default opacity.\n          // Enable `stateTransition` (animation).\n          el.ensureState('emphasis');\n        }\n      });\n    }\n  };\n  MapDraw.prototype.remove = function () {\n    this._regionsGroup.removeAll();\n    this._regionsGroupByName = null;\n    this._svgGroup.removeAll();\n    this._freeSVG();\n    this._controller.dispose();\n    this._controllerHost = null;\n  };\n  MapDraw.prototype.findHighDownDispatchers = function (name, geoModel) {\n    if (name == null) {\n      return [];\n    }\n    var geo = geoModel.coordinateSystem;\n    if (geo.resourceType === 'geoJSON') {\n      var regionsGroupByName = this._regionsGroupByName;\n      if (regionsGroupByName) {\n        var regionGroup = regionsGroupByName.get(name);\n        return regionGroup ? [regionGroup] : [];\n      }\n    } else if (geo.resourceType === 'geoSVG') {\n      return this._svgDispatcherMap && this._svgDispatcherMap.get(name) || [];\n    }\n  };\n  MapDraw.prototype._svgResourceChanged = function (mapName) {\n    return this._svgMapName !== mapName;\n  };\n  MapDraw.prototype._useSVG = function (mapName) {\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      var svgGraphic = resource.useGraphic(this.uid);\n      this._svgGroup.add(svgGraphic.root);\n      this._svgGraphicRecord = svgGraphic;\n      this._svgMapName = mapName;\n    }\n  };\n  MapDraw.prototype._freeSVG = function () {\n    var mapName = this._svgMapName;\n    if (mapName == null) {\n      return;\n    }\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      resource.freeGraphic(this.uid);\n    }\n    this._svgGraphicRecord = null;\n    this._svgDispatcherMap = null;\n    this._svgGroup.removeAll();\n    this._svgMapName = null;\n  };\n  MapDraw.prototype._updateController = function (mapOrGeoModel, ecModel, api) {\n    var geo = mapOrGeoModel.coordinateSystem;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    // @ts-ignore FIXME:TS\n    controllerHost.zoomLimit = mapOrGeoModel.get('scaleLimit');\n    controllerHost.zoom = geo.getZoom();\n    // roamType is will be set default true if it is null\n    // @ts-ignore FIXME:TS\n    controller.enable(mapOrGeoModel.get('roam') || false);\n    var mainType = mapOrGeoModel.mainType;\n    function makeActionBase() {\n      var action = {\n        type: 'geoRoam',\n        componentType: mainType\n      };\n      action[mainType + 'Id'] = mapOrGeoModel.id;\n      return action;\n    }\n    controller.off('pan').on('pan', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        dx: e.dx,\n        dy: e.dy,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.off('zoom').on('zoom', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        totalZoom: controllerHost.zoom,\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.setPointerChecker(function (e, x, y) {\n      return geo.containPoint([x, y]) && !onIrrelevantElement(e, api, mapOrGeoModel);\n    });\n  };\n  /**\r\n   * FIXME: this is a temporarily workaround.\r\n   * When `geoRoam` the elements need to be reset in `MapView['render']`, because the props like\r\n   * `ignore` might have been modified by `LabelManager`, and `LabelManager#addLabelsOfSeries`\r\n   * will subsequently cache `defaultAttr` like `ignore`. If do not do this reset, the modified\r\n   * props will have no chance to be restored.\r\n   * Note: This reset should be after `clearStates` in `renderSeries` because `useStates` in\r\n   * `renderSeries` will cache the modified `ignore` to `el._normalState`.\r\n   * TODO:\r\n   * Use clone/immutable in `LabelManager`?\r\n   */\n  MapDraw.prototype.resetForLabelLayout = function () {\n    this.group.traverse(function (el) {\n      var label = el.getTextContent();\n      if (label) {\n        label.ignore = mapLabelRaw(label).ignore;\n      }\n    });\n  };\n  MapDraw.prototype._updateMapSelectHandler = function (mapOrGeoModel, regionsGroup, api, fromView) {\n    var mapDraw = this;\n    regionsGroup.off('mousedown');\n    regionsGroup.off('click');\n    // @ts-ignore FIXME:TS resolve type conflict\n    if (mapOrGeoModel.get('selectedMode')) {\n      regionsGroup.on('mousedown', function () {\n        mapDraw._mouseDownFlag = true;\n      });\n      regionsGroup.on('click', function (e) {\n        if (!mapDraw._mouseDownFlag) {\n          return;\n        }\n        mapDraw._mouseDownFlag = false;\n      });\n    }\n  };\n  return MapDraw;\n}();\n;\nfunction applyOptionStyleForRegion(viewBuildCtx, el, dataIndex, regionModel) {\n  // All of the path are using `itemStyle`, because\n  // (1) Some SVG also use fill on polyline (The different between\n  // polyline and polygon is \"open\" or \"close\" but not fill or not).\n  // (2) For the common props like opacity, if some use itemStyle\n  // and some use `lineStyle`, it might confuse users.\n  // (3) Most SVG use <path>, where can not detect whether to draw a \"line\"\n  // or a filled shape, so use `itemStyle` for <path>.\n  var normalStyleModel = regionModel.getModel('itemStyle');\n  var emphasisStyleModel = regionModel.getModel(['emphasis', 'itemStyle']);\n  var blurStyleModel = regionModel.getModel(['blur', 'itemStyle']);\n  var selectStyleModel = regionModel.getModel(['select', 'itemStyle']);\n  // NOTE: DON'T use 'style' in visual when drawing map.\n  // This component is used for drawing underlying map for both geo component and map series.\n  var normalStyle = getFixedItemStyle(normalStyleModel);\n  var emphasisStyle = getFixedItemStyle(emphasisStyleModel);\n  var selectStyle = getFixedItemStyle(selectStyleModel);\n  var blurStyle = getFixedItemStyle(blurStyleModel);\n  // Update the itemStyle if has data visual\n  var data = viewBuildCtx.data;\n  if (data) {\n    // Only visual color of each item will be used. It can be encoded by visualMap\n    // But visual color of series is used in symbol drawing\n    // Visual color for each series is for the symbol draw\n    var style = data.getItemVisual(dataIndex, 'style');\n    var decal = data.getItemVisual(dataIndex, 'decal');\n    if (viewBuildCtx.isVisualEncodedByVisualMap && style.fill) {\n      normalStyle.fill = style.fill;\n    }\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, viewBuildCtx.api);\n    }\n  }\n  // SVG text, tspan and image can be named but not supporeted\n  // to be styled by region option yet.\n  el.setStyle(normalStyle);\n  el.style.strokeNoScale = true;\n  el.ensureState('emphasis').style = emphasisStyle;\n  el.ensureState('select').style = selectStyle;\n  el.ensureState('blur').style = blurStyle;\n  // Enable blur\n  setDefaultStateProxy(el);\n}\nfunction resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx,\n// If labelXY not provided, use `textConfig.position: 'inside'`\nlabelXY) {\n  var data = viewBuildCtx.data;\n  var isGeo = viewBuildCtx.isGeo;\n  var isDataNaN = data && isNaN(data.get(data.mapDimension('value'), dataIdx));\n  var itemLayout = data && data.getItemLayout(dataIdx);\n  // In the following cases label will be drawn\n  // 1. In map series and data value is NaN\n  // 2. In geo component\n  // 3. Region has no series legendIcon, which will be add a showLabel flag in mapSymbolLayout\n  if (isGeo || isDataNaN || itemLayout && itemLayout.showLabel) {\n    var query = !isGeo ? dataIdx : regionName;\n    var labelFetcher = void 0;\n    // Consider dataIdx not found.\n    if (!data || dataIdx >= 0) {\n      labelFetcher = mapOrGeoModel;\n    }\n    var specifiedTextOpt = labelXY ? {\n      normal: {\n        align: 'center',\n        verticalAlign: 'middle'\n      }\n    } : null;\n    // Caveat: must be called after `setDefaultStateProxy(el);` called.\n    // because textContent will be assign with `el.stateProxy` inside.\n    setLabelStyle(el, getLabelStatesModels(regionModel), {\n      labelFetcher: labelFetcher,\n      labelDataIndex: query,\n      defaultText: regionName\n    }, specifiedTextOpt);\n    var textEl = el.getTextContent();\n    if (textEl) {\n      mapLabelRaw(textEl).ignore = textEl.ignore;\n      if (el.textConfig && labelXY) {\n        // Compute a relative offset based on the el bounding rect.\n        var rect = el.getBoundingRect().clone();\n        // Need to make sure the percent position base on the same rect in normal and\n        // emphasis state. Otherwise if using boundingRect of el, but the emphasis state\n        // has borderWidth (even 0.5px), the text position will be changed obviously\n        // if the position is very big like ['1234%', '1345%'].\n        el.textConfig.layoutRect = rect;\n        el.textConfig.position = [(labelXY[0] - rect.x) / rect.width * 100 + '%', (labelXY[1] - rect.y) / rect.height * 100 + '%'];\n      }\n    }\n    // PENDING:\n    // If labelLayout is enabled (test/label-layout.html), el.dataIndex should be specified.\n    // But el.dataIndex is also used to determine whether user event should be triggered,\n    // where el.seriesIndex or el.dataModel must be specified. At present for a single el\n    // there is not case that \"only label layout enabled but user event disabled\", so here\n    // we depends `resetEventTriggerForRegion` to do the job of setting `el.dataIndex`.\n    el.disableLabelAnimation = true;\n  } else {\n    el.removeTextContent();\n    el.removeTextConfig();\n    el.disableLabelAnimation = null;\n  }\n}\nfunction resetEventTriggerForRegion(viewBuildCtx, eventTrigger, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx) {\n  // setItemGraphicEl, setHoverStyle after all polygons and labels\n  // are added to the regionGroup\n  if (viewBuildCtx.data) {\n    // FIXME: when series-map use a SVG map, and there are duplicated name specified\n    // on different SVG elements, after `data.setItemGraphicEl(...)`:\n    // (1) all of them will be mounted with `dataIndex`, `seriesIndex`, so that tooltip\n    // can be triggered only mouse hover. That's correct.\n    // (2) only the last element will be kept in `data`, so that if trigger tooltip\n    // by `dispatchAction`, only the last one can be found and triggered. That might be\n    // not correct. We will fix it in future if anyone demanding that.\n    viewBuildCtx.data.setItemGraphicEl(dataIdx, eventTrigger);\n  }\n  // series-map will not trigger \"geoselectchange\" no matter it is\n  // based on a declared geo component. Because series-map will\n  // trigger \"selectchange\". If it trigger both the two events,\n  // If users call `chart.dispatchAction({type: 'toggleSelect'})`,\n  // it not easy to also fire event \"geoselectchanged\".\n  else {\n    // Package custom mouse event for geo component\n    getECData(eventTrigger).eventData = {\n      componentType: 'geo',\n      componentIndex: mapOrGeoModel.componentIndex,\n      geoIndex: mapOrGeoModel.componentIndex,\n      name: regionName,\n      region: regionModel && regionModel.option || {}\n    };\n  }\n}\nfunction resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  if (!viewBuildCtx.data) {\n    graphic.setTooltipConfig({\n      el: el,\n      componentModel: mapOrGeoModel,\n      itemName: regionName,\n      // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n      itemTooltipOption: regionModel.get('tooltip')\n    });\n  }\n}\nfunction resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  el.highDownSilentOnTouch = !!mapOrGeoModel.get('selectedMode');\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  var emphasisModel = regionModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  toggleHoverEmphasis(el, focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  if (viewBuildCtx.isGeo) {\n    enableComponentHighDownFeatures(el, mapOrGeoModel, regionName);\n  }\n  return focus;\n}\nfunction projectPolys(rings,\n// Polygons include exterior and interiors. Or polylines.\ncreateStream, isLine) {\n  var polygons = [];\n  var curPoly;\n  function startPolygon() {\n    curPoly = [];\n  }\n  function endPolygon() {\n    if (curPoly.length) {\n      polygons.push(curPoly);\n      curPoly = [];\n    }\n  }\n  var stream = createStream({\n    polygonStart: startPolygon,\n    polygonEnd: endPolygon,\n    lineStart: startPolygon,\n    lineEnd: endPolygon,\n    point: function (x, y) {\n      // May have NaN values from stream.\n      if (isFinite(x) && isFinite(y)) {\n        curPoly.push([x, y]);\n      }\n    },\n    sphere: function () {}\n  });\n  !isLine && stream.polygonStart();\n  zrUtil.each(rings, function (ring) {\n    stream.lineStart();\n    for (var i = 0; i < ring.length; i++) {\n      stream.point(ring[i][0], ring[i][1]);\n    }\n    stream.lineEnd();\n  });\n  !isLine && stream.polygonEnd();\n  return polygons;\n}\nexport default MapDraw;\n// @ts-ignore FIXME:TS fix the \"compatible with each other\"?", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}