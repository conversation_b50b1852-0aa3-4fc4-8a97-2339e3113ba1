{"ast": null, "code": "import { CorruptedFilesRequestsComponent } from './corrupted-files-requests/corrupted-files-requests.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction CorruptedFilesRequestsViewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"app-corrupted-files-requests-advanced-search\", 6);\n    i0.ɵɵlistener(\"closeAdvancedSearch\", function CorruptedFilesRequestsViewComponent_div_4_Template_app_corrupted_files_requests_advanced_search_closeAdvancedSearch_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"filter\", ctx_r1.filter);\n  }\n}\nexport let CorruptedFilesRequestsViewComponent = /*#__PURE__*/(() => {\n  class CorruptedFilesRequestsViewComponent {\n    corruptedFilesChild;\n    openAdvancedSearch = false;\n    filter = {\n      skip: 0,\n      take: 12,\n      sortField: 'requestDate',\n      sortOrder: 1,\n      page: 1\n    };\n    constructor() {}\n    ngOnInit() {}\n    closeAdvancedSearch(event) {\n      this.openAdvancedSearch = false;\n      this.filter = event;\n      this.corruptedFilesChild?.getAllCorruptedFilesRequests();\n    }\n    openAdvancedSearchPopup(event) {\n      this.openAdvancedSearch = true;\n      this.filter = event;\n    }\n    static ɵfac = function CorruptedFilesRequestsViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CorruptedFilesRequestsViewComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CorruptedFilesRequestsViewComponent,\n      selectors: [[\"app-corrupted-files-requests-view\"]],\n      viewQuery: function CorruptedFilesRequestsViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CorruptedFilesRequestsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.corruptedFilesChild = _t.first);\n        }\n      },\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [3, \"openAdvancedSearchOverLay\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"overlay\"], [3, \"closeAdvancedSearch\", \"filter\"]],\n      template: function CorruptedFilesRequestsViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-corrupted-files-requests\", 3);\n          i0.ɵɵlistener(\"openAdvancedSearchOverLay\", function CorruptedFilesRequestsViewComponent_Template_app_corrupted_files_requests_openAdvancedSearchOverLay_3_listener($event) {\n            return ctx.openAdvancedSearchPopup($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, CorruptedFilesRequestsViewComponent_div_4_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.openAdvancedSearch);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return CorruptedFilesRequestsViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}