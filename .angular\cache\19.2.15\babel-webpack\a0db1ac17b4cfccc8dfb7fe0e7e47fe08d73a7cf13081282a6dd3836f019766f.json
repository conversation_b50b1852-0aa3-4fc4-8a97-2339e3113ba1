{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, Ng<PERSON><PERSON>, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, defer, of } from 'rxjs';\nimport { B as BasePortalOutlet, f as CdkPortalOutlet, C as ComponentPortal, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nexport { d as ɵɵCdkPortal, g as ɵɵPortalHostDirective, e as ɵɵTemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-BYox5gpI.mjs';\nimport { c as OverlayRef, a as Overlay, O as OverlayContainer, f as OverlayConfig, m as OverlayModule } from './overlay-module-BUj0D19H.mjs';\nimport { F as FocusMonitor } from './focus-monitor-e2l_RpN3.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith, take } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass = '';\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose = false;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Dialog label applied via `aria-label` */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus = true;\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation = true;\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy = true;\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments = true;\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * @deprecated No longer used. Will be removed.\n   * @breaking-change 20.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers;\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container;\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext;\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nlet CdkDialogContainer = /*#__PURE__*/(() => {\n  class CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _overlayRef = inject(OverlayRef);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, {\n      optional: true\n    });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    _focusTrapped = new Subject();\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _isDestroyed = false;\n    constructor() {\n      super();\n      // Callback is primarily for some internal tests\n      // that were instantiating the dialog container manually.\n      this._config = inject(DialogConfig, {\n        optional: true\n      }) || new DialogConfig();\n      if (this._config.ariaLabelledBy) {\n        this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n      }\n    }\n    _addAriaLabelledBy(id) {\n      this._ariaLabelledByQueue.push(id);\n      this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n      const index = this._ariaLabelledByQueue.indexOf(id);\n      if (index > -1) {\n        this._ariaLabelledByQueue.splice(index, 1);\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    _contentAttached() {\n      this._initializeFocusTrap();\n      this._handleBackdropClicks();\n      this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n      this._trapFocus();\n    }\n    ngOnDestroy() {\n      this._focusTrapped.complete();\n      this._isDestroyed = true;\n      this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachComponentPortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n      if (!this._containsFocus()) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1;\n        // The tabindex attribute should be removed to avoid navigating to that element again\n        this._ngZone.runOutsideAngular(() => {\n          const callback = () => {\n            deregisterBlur();\n            deregisterMousedown();\n            element.removeAttribute('tabindex');\n          };\n          const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n          const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n        });\n      }\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n      if (this._isDestroyed) {\n        return;\n      }\n      // If were to attempt to focus immediately, then the content of the dialog would not yet be\n      // ready in instances where change detection has to run first. To deal with this, we simply\n      // wait until after the next render.\n      afterNextRender(() => {\n        const element = this._elementRef.nativeElement;\n        switch (this._config.autoFocus) {\n          case false:\n          case 'dialog':\n            // Ensure that focus is on the dialog container. It's possible that a different\n            // component tried to move focus while the open animation was running. See:\n            // https://github.com/angular/components/issues/16215. Note that we only want to do this\n            // if the focus isn't inside the dialog already, because it's possible that the consumer\n            // turned off `autoFocus` in order to move focus themselves.\n            if (!this._containsFocus()) {\n              element.focus(options);\n            }\n            break;\n          case true:\n          case 'first-tabbable':\n            const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n            // If we weren't able to find a focusable element in the dialog, then focus the dialog\n            // container instead.\n            if (!focusedSuccessfully) {\n              this._focusDialogContainer(options);\n            }\n            break;\n          case 'first-heading':\n            this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n            break;\n          default:\n            this._focusByCssSelector(this._config.autoFocus, options);\n            break;\n        }\n        this._focusTrapped.next();\n      }, {\n        injector: this._injector\n      });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n      const focusConfig = this._config.restoreFocus;\n      let focusTargetElement = null;\n      if (typeof focusConfig === 'string') {\n        focusTargetElement = this._document.querySelector(focusConfig);\n      } else if (typeof focusConfig === 'boolean') {\n        focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n      } else if (focusConfig) {\n        focusTargetElement = focusConfig;\n      }\n      // We need the extra check, because IE can set the `activeElement` to null in some cases.\n      if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n        const activeElement = _getFocusedElementPierceShadowDom();\n        const element = this._elementRef.nativeElement;\n        // Make sure that focus is still inside the dialog or is on the body (usually because a\n        // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n        // the consumer moved it themselves before the animation was done, in which case we shouldn't\n        // do anything.\n        if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n          if (this._focusMonitor) {\n            this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n            this._closeInteractionType = null;\n          } else {\n            focusTargetElement.focus();\n          }\n        }\n      }\n      if (this._focusTrap) {\n        this._focusTrap.destroy();\n      }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n      // Note that there is no focus method when rendering on the server.\n      if (this._elementRef.nativeElement.focus) {\n        this._elementRef.nativeElement.focus(options);\n      }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n      const element = this._elementRef.nativeElement;\n      const activeElement = _getFocusedElementPierceShadowDom();\n      return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n      if (this._platform.isBrowser) {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        // Save the previously focused element. This element will be re-focused\n        // when the dialog closes.\n        if (this._document) {\n          this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n        }\n      }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n      // Clicking on the backdrop will move focus out of dialog.\n      // Recapture it if closing via the backdrop is disabled.\n      this._overlayRef.backdropClick().subscribe(() => {\n        if (this._config.disableClose) {\n          this._recaptureFocus();\n        }\n      });\n    }\n    static ɵfac = function CdkDialogContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDialogContainer)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkDialogContainer,\n      selectors: [[\"cdk-dialog-container\"]],\n      viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n      hostVars: 6,\n      hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkPortalOutlet\", \"\"]],\n      template: function CdkDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return CdkDialogContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  overlayRef;\n  config;\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Instance of the container that is rendering out the dialog content. */\n  containerInstance;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Emits when the dialog has been closed. */\n  closed = /*#__PURE__*/new Subject();\n  /** Emits when the backdrop of the dialog is clicked. */\n  backdropClick;\n  /** Emits when on keyboard events within the dialog. */\n  keydownEvents;\n  /** Emits on pointer events that happen outside of the dialog. */\n  outsidePointerEvents;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subscription to external detachments of the dialog. */\n  _detachSubscription;\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = /*#__PURE__*/new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = /*#__PURE__*/new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nlet Dialog = /*#__PURE__*/(() => {\n  class Dialog {\n    _overlay = inject(Overlay);\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, {\n      optional: true\n    });\n    _parentDialog = inject(Dialog, {\n      optional: true,\n      skipSelf: true\n    });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() {}\n    open(componentOrTemplateRef, config) {\n      const defaults = this._defaultOptions || new DialogConfig();\n      config = {\n        ...defaults,\n        ...config\n      };\n      config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n      if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n      }\n      const overlayConfig = this._getOverlayConfig(config);\n      const overlayRef = this._overlay.create(overlayConfig);\n      const dialogRef = new DialogRef(overlayRef, config);\n      const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n      dialogRef.containerInstance = dialogContainer;\n      // If this is the first dialog that we're opening, hide all the non-overlay content.\n      if (!this.openDialogs.length) {\n        // Resolve this ahead of time, because some internal apps\n        // mock it out and depend on it being synchronous.\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        if (dialogContainer._focusTrapped) {\n          dialogContainer._focusTrapped.pipe(take(1)).subscribe(() => {\n            this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n          });\n        } else {\n          this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n        }\n      }\n      this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n      this.openDialogs.push(dialogRef);\n      dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n      this.afterOpened.next(dialogRef);\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n      reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n      // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n      // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n      // determines when `aria-hidden` is removed from elements outside the dialog.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => {\n        // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n        if (dialog.config.closeOnDestroy === false) {\n          this._removeOpenDialog(dialog, false);\n        }\n      });\n      // Make a second pass and close the remaining dialogs. We do this second pass in order to\n      // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n      // that should be closed and dialogs that should not.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n      this._afterAllClosedAtThisLevel.complete();\n      this._afterOpenedAtThisLevel.complete();\n      this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n      const state = new OverlayConfig({\n        positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n        scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n        panelClass: config.panelClass,\n        hasBackdrop: config.hasBackdrop,\n        direction: config.direction,\n        minWidth: config.minWidth,\n        minHeight: config.minHeight,\n        maxWidth: config.maxWidth,\n        maxHeight: config.maxHeight,\n        width: config.width,\n        height: config.height,\n        disposeOnNavigation: config.closeOnNavigation\n      });\n      if (config.backdropClass) {\n        state.backdropClass = config.backdropClass;\n      }\n      return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DialogConfig,\n        useValue: config\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }, {\n        provide: OverlayRef,\n        useValue: overlay\n      }];\n      let containerType;\n      if (config.container) {\n        if (typeof config.container === 'function') {\n          containerType = config.container;\n        } else {\n          containerType = config.container.type;\n          providers.push(...config.container.providers(config));\n        }\n      } else {\n        containerType = CdkDialogContainer;\n      }\n      const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n        parent: userInjector || this._injector,\n        providers\n      }));\n      const containerRef = overlay.attach(containerPortal);\n      return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n      if (componentOrTemplateRef instanceof TemplateRef) {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n        let context = {\n          $implicit: config.data,\n          dialogRef\n        };\n        if (config.templateContext) {\n          context = {\n            ...context,\n            ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n          };\n        }\n        dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n      } else {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n        const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n        dialogRef.componentRef = contentRef;\n        dialogRef.componentInstance = contentRef.instance;\n      }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DIALOG_DATA,\n        useValue: config.data\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }];\n      if (config.providers) {\n        if (typeof config.providers === 'function') {\n          providers.push(...config.providers(dialogRef, config, dialogContainer));\n        } else {\n          providers.push(...config.providers);\n        }\n      }\n      if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n        optional: true\n      }))) {\n        providers.push({\n          provide: Directionality,\n          useValue: {\n            value: config.direction,\n            change: of()\n          }\n        });\n      }\n      return Injector.create({\n        parent: userInjector || fallbackInjector,\n        providers\n      });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        // If all the dialogs were closed, remove/restore the `aria-hidden`\n        // to a the siblings and emit to the `afterAllClosed` stream.\n        if (!this.openDialogs.length) {\n          this._ariaHiddenElements.forEach((previousValue, element) => {\n            if (previousValue) {\n              element.setAttribute('aria-hidden', previousValue);\n            } else {\n              element.removeAttribute('aria-hidden');\n            }\n          });\n          this._ariaHiddenElements.clear();\n          if (emitEvent) {\n            this._getAfterAllClosed().next();\n          }\n        }\n      }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology(overlayContainer) {\n      // Ensure that the overlay container is attached to the DOM.\n      if (overlayContainer.parentElement) {\n        const siblings = overlayContainer.parentElement.children;\n        for (let i = siblings.length - 1; i > -1; i--) {\n          const sibling = siblings[i];\n          if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n            this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n            sibling.setAttribute('aria-hidden', 'true');\n          }\n        }\n      }\n    }\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dialog)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Dialog,\n      factory: Dialog.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return Dialog;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nlet DialogModule = /*#__PURE__*/(() => {\n  class DialogModule {\n    static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Dialog],\n      imports: [OverlayModule, PortalModule, A11yModule,\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule]\n    });\n  }\n  return DialogModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n//# sourceMappingURL=dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}