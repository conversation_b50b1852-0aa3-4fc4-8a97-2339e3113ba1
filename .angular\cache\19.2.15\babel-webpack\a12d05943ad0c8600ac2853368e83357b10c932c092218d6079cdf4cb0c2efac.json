{"ast": null, "code": "import { brush, setClipPath, setGradient, setPattern } from './graphic.js';\nimport { createElement, createVNode, vNodeToString, getCssString, createBrushScope, createSVGVNode } from './core.js';\nimport { normalizeColor, encodeBase64, isGradient, isPattern } from './helper.js';\nimport { extend, keys, logError, map, noop, retrieve2 } from '../core/util.js';\nimport patch, { updateAttrs } from './patch.js';\nimport { getSize } from '../canvas/helper.js';\nvar svgId = 0;\nvar SVGPainter = function () {\n  function SVGPainter(root, storage, opts) {\n    this.type = 'svg';\n    this.refreshHover = createMethodNotSupport('refreshHover');\n    this.configLayer = createMethodNotSupport('configLayer');\n    this.storage = storage;\n    this._opts = opts = extend({}, opts);\n    this.root = root;\n    this._id = 'zr' + svgId++;\n    this._oldVNode = createSVGVNode(opts.width, opts.height);\n    if (root && !opts.ssr) {\n      var viewport = this._viewport = document.createElement('div');\n      viewport.style.cssText = 'position:relative;overflow:hidden';\n      var svgDom = this._svgDom = this._oldVNode.elm = createElement('svg');\n      updateAttrs(null, this._oldVNode);\n      viewport.appendChild(svgDom);\n      root.appendChild(viewport);\n    }\n    this.resize(opts.width, opts.height);\n  }\n  SVGPainter.prototype.getType = function () {\n    return this.type;\n  };\n  SVGPainter.prototype.getViewportRoot = function () {\n    return this._viewport;\n  };\n  SVGPainter.prototype.getViewportRootOffset = function () {\n    var viewportRoot = this.getViewportRoot();\n    if (viewportRoot) {\n      return {\n        offsetLeft: viewportRoot.offsetLeft || 0,\n        offsetTop: viewportRoot.offsetTop || 0\n      };\n    }\n  };\n  SVGPainter.prototype.getSvgDom = function () {\n    return this._svgDom;\n  };\n  SVGPainter.prototype.refresh = function () {\n    if (this.root) {\n      var vnode = this.renderToVNode({\n        willUpdate: true\n      });\n      vnode.attrs.style = 'position:absolute;left:0;top:0;user-select:none';\n      patch(this._oldVNode, vnode);\n      this._oldVNode = vnode;\n    }\n  };\n  SVGPainter.prototype.renderOneToVNode = function (el) {\n    return brush(el, createBrushScope(this._id));\n  };\n  SVGPainter.prototype.renderToVNode = function (opts) {\n    opts = opts || {};\n    var list = this.storage.getDisplayList(true);\n    var width = this._width;\n    var height = this._height;\n    var scope = createBrushScope(this._id);\n    scope.animation = opts.animation;\n    scope.willUpdate = opts.willUpdate;\n    scope.compress = opts.compress;\n    scope.emphasis = opts.emphasis;\n    scope.ssr = this._opts.ssr;\n    var children = [];\n    var bgVNode = this._bgVNode = createBackgroundVNode(width, height, this._backgroundColor, scope);\n    bgVNode && children.push(bgVNode);\n    var mainVNode = !opts.compress ? this._mainVNode = createVNode('g', 'main', {}, []) : null;\n    this._paintList(list, scope, mainVNode ? mainVNode.children : children);\n    mainVNode && children.push(mainVNode);\n    var defs = map(keys(scope.defs), function (id) {\n      return scope.defs[id];\n    });\n    if (defs.length) {\n      children.push(createVNode('defs', 'defs', {}, defs));\n    }\n    if (opts.animation) {\n      var animationCssStr = getCssString(scope.cssNodes, scope.cssAnims, {\n        newline: true\n      });\n      if (animationCssStr) {\n        var styleNode = createVNode('style', 'stl', {}, [], animationCssStr);\n        children.push(styleNode);\n      }\n    }\n    return createSVGVNode(width, height, children, opts.useViewBox);\n  };\n  SVGPainter.prototype.renderToString = function (opts) {\n    opts = opts || {};\n    return vNodeToString(this.renderToVNode({\n      animation: retrieve2(opts.cssAnimation, true),\n      emphasis: retrieve2(opts.cssEmphasis, true),\n      willUpdate: false,\n      compress: true,\n      useViewBox: retrieve2(opts.useViewBox, true)\n    }), {\n      newline: true\n    });\n  };\n  SVGPainter.prototype.setBackgroundColor = function (backgroundColor) {\n    this._backgroundColor = backgroundColor;\n  };\n  SVGPainter.prototype.getSvgRoot = function () {\n    return this._mainVNode && this._mainVNode.elm;\n  };\n  SVGPainter.prototype._paintList = function (list, scope, out) {\n    var listLen = list.length;\n    var clipPathsGroupsStack = [];\n    var clipPathsGroupsStackDepth = 0;\n    var currentClipPathGroup;\n    var prevClipPaths;\n    var clipGroupNodeIdx = 0;\n    for (var i = 0; i < listLen; i++) {\n      var displayable = list[i];\n      if (!displayable.invisible) {\n        var clipPaths = displayable.__clipPaths;\n        var len = clipPaths && clipPaths.length || 0;\n        var prevLen = prevClipPaths && prevClipPaths.length || 0;\n        var lca = void 0;\n        for (lca = Math.max(len - 1, prevLen - 1); lca >= 0; lca--) {\n          if (clipPaths && prevClipPaths && clipPaths[lca] === prevClipPaths[lca]) {\n            break;\n          }\n        }\n        for (var i_1 = prevLen - 1; i_1 > lca; i_1--) {\n          clipPathsGroupsStackDepth--;\n          currentClipPathGroup = clipPathsGroupsStack[clipPathsGroupsStackDepth - 1];\n        }\n        for (var i_2 = lca + 1; i_2 < len; i_2++) {\n          var groupAttrs = {};\n          setClipPath(clipPaths[i_2], groupAttrs, scope);\n          var g = createVNode('g', 'clip-g-' + clipGroupNodeIdx++, groupAttrs, []);\n          (currentClipPathGroup ? currentClipPathGroup.children : out).push(g);\n          clipPathsGroupsStack[clipPathsGroupsStackDepth++] = g;\n          currentClipPathGroup = g;\n        }\n        prevClipPaths = clipPaths;\n        var ret = brush(displayable, scope);\n        if (ret) {\n          (currentClipPathGroup ? currentClipPathGroup.children : out).push(ret);\n        }\n      }\n    }\n  };\n  SVGPainter.prototype.resize = function (width, height) {\n    var opts = this._opts;\n    var root = this.root;\n    var viewport = this._viewport;\n    width != null && (opts.width = width);\n    height != null && (opts.height = height);\n    if (root && viewport) {\n      viewport.style.display = 'none';\n      width = getSize(root, 0, opts);\n      height = getSize(root, 1, opts);\n      viewport.style.display = '';\n    }\n    if (this._width !== width || this._height !== height) {\n      this._width = width;\n      this._height = height;\n      if (viewport) {\n        var viewportStyle = viewport.style;\n        viewportStyle.width = width + 'px';\n        viewportStyle.height = height + 'px';\n      }\n      if (!isPattern(this._backgroundColor)) {\n        var svgDom = this._svgDom;\n        if (svgDom) {\n          svgDom.setAttribute('width', width);\n          svgDom.setAttribute('height', height);\n        }\n        var bgEl = this._bgVNode && this._bgVNode.elm;\n        if (bgEl) {\n          bgEl.setAttribute('width', width);\n          bgEl.setAttribute('height', height);\n        }\n      } else {\n        this.refresh();\n      }\n    }\n  };\n  SVGPainter.prototype.getWidth = function () {\n    return this._width;\n  };\n  SVGPainter.prototype.getHeight = function () {\n    return this._height;\n  };\n  SVGPainter.prototype.dispose = function () {\n    if (this.root) {\n      this.root.innerHTML = '';\n    }\n    this._svgDom = this._viewport = this.storage = this._oldVNode = this._bgVNode = this._mainVNode = null;\n  };\n  SVGPainter.prototype.clear = function () {\n    if (this._svgDom) {\n      this._svgDom.innerHTML = null;\n    }\n    this._oldVNode = null;\n  };\n  SVGPainter.prototype.toDataURL = function (base64) {\n    var str = this.renderToString();\n    var prefix = 'data:image/svg+xml;';\n    if (base64) {\n      str = encodeBase64(str);\n      return str && prefix + 'base64,' + str;\n    }\n    return prefix + 'charset=UTF-8,' + encodeURIComponent(str);\n  };\n  return SVGPainter;\n}();\nfunction createMethodNotSupport(method) {\n  return function () {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('In SVG mode painter not support method \"' + method + '\"');\n    }\n  };\n}\nfunction createBackgroundVNode(width, height, backgroundColor, scope) {\n  var bgVNode;\n  if (backgroundColor && backgroundColor !== 'none') {\n    bgVNode = createVNode('rect', 'bg', {\n      width: width,\n      height: height,\n      x: '0',\n      y: '0'\n    });\n    if (isGradient(backgroundColor)) {\n      setGradient({\n        fill: backgroundColor\n      }, bgVNode.attrs, 'fill', scope);\n    } else if (isPattern(backgroundColor)) {\n      setPattern({\n        style: {\n          fill: backgroundColor\n        },\n        dirty: noop,\n        getBoundingRect: function () {\n          return {\n            width: width,\n            height: height\n          };\n        }\n      }, bgVNode.attrs, 'fill', scope);\n    } else {\n      var _a = normalizeColor(backgroundColor),\n        color = _a.color,\n        opacity = _a.opacity;\n      bgVNode.attrs.fill = color;\n      opacity < 1 && (bgVNode.attrs['fill-opacity'] = opacity);\n    }\n  }\n  return bgVNode;\n}\nexport default SVGPainter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}