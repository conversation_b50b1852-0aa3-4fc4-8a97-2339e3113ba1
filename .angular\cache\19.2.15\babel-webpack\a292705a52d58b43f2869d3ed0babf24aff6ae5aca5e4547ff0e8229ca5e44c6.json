{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { parseRichText, parsePlainText } from './helper/parseText.js';\nimport TSpan from './TSpan.js';\nimport { retrieve2, each, normalizeCssArray, trim, retrieve3, extend, keys, defaults } from '../core/util.js';\nimport { adjustTextX, adjustTextY } from '../contain/text.js';\nimport ZRImage from './Image.js';\nimport Rect from './shape/Rect.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Displayable, { DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_SIZE } from '../core/platform.js';\nvar DEFAULT_RICH_TEXT_COLOR = {\n  fill: '#000'\n};\nvar DEFAULT_STROKE_LINE_WIDTH = 2;\nexport var DEFAULT_TEXT_ANIMATION_PROPS = {\n  style: defaults({\n    fill: true,\n    stroke: true,\n    fillOpacity: true,\n    strokeOpacity: true,\n    lineWidth: true,\n    fontSize: true,\n    lineHeight: true,\n    width: true,\n    height: true,\n    textShadowColor: true,\n    textShadowBlur: true,\n    textShadowOffsetX: true,\n    textShadowOffsetY: true,\n    backgroundColor: true,\n    padding: true,\n    borderColor: true,\n    borderWidth: true,\n    borderRadius: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar ZRText = function (_super) {\n  __extends(ZRText, _super);\n  function ZRText(opts) {\n    var _this = _super.call(this) || this;\n    _this.type = 'text';\n    _this._children = [];\n    _this._defaultStyle = DEFAULT_RICH_TEXT_COLOR;\n    _this.attr(opts);\n    return _this;\n  }\n  ZRText.prototype.childrenRef = function () {\n    return this._children;\n  };\n  ZRText.prototype.update = function () {\n    _super.prototype.update.call(this);\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.zlevel = this.zlevel;\n      child.z = this.z;\n      child.z2 = this.z2;\n      child.culling = this.culling;\n      child.cursor = this.cursor;\n      child.invisible = this.invisible;\n    }\n  };\n  ZRText.prototype.updateTransform = function () {\n    var innerTransformable = this.innerTransformable;\n    if (innerTransformable) {\n      innerTransformable.updateTransform();\n      if (innerTransformable.transform) {\n        this.transform = innerTransformable.transform;\n      }\n    } else {\n      _super.prototype.updateTransform.call(this);\n    }\n  };\n  ZRText.prototype.getLocalTransform = function (m) {\n    var innerTransformable = this.innerTransformable;\n    return innerTransformable ? innerTransformable.getLocalTransform(m) : _super.prototype.getLocalTransform.call(this, m);\n  };\n  ZRText.prototype.getComputedTransform = function () {\n    if (this.__hostTarget) {\n      this.__hostTarget.getComputedTransform();\n      this.__hostTarget.updateInnerText(true);\n    }\n    return _super.prototype.getComputedTransform.call(this);\n  };\n  ZRText.prototype._updateSubTexts = function () {\n    this._childCursor = 0;\n    normalizeTextStyle(this.style);\n    this.style.rich ? this._updateRichTexts() : this._updatePlainTexts();\n    this._children.length = this._childCursor;\n    this.styleUpdated();\n  };\n  ZRText.prototype.addSelfToZr = function (zr) {\n    _super.prototype.addSelfToZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = zr;\n    }\n  };\n  ZRText.prototype.removeSelfFromZr = function (zr) {\n    _super.prototype.removeSelfFromZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = null;\n    }\n  };\n  ZRText.prototype.getBoundingRect = function () {\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    if (!this._rect) {\n      var tmpRect = new BoundingRect(0, 0, 0, 0);\n      var children = this._children;\n      var tmpMat = [];\n      var rect = null;\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        var childRect = child.getBoundingRect();\n        var transform = child.getLocalTransform(tmpMat);\n        if (transform) {\n          tmpRect.copy(childRect);\n          tmpRect.applyTransform(transform);\n          rect = rect || tmpRect.clone();\n          rect.union(tmpRect);\n        } else {\n          rect = rect || childRect.clone();\n          rect.union(childRect);\n        }\n      }\n      this._rect = rect || tmpRect;\n    }\n    return this._rect;\n  };\n  ZRText.prototype.setDefaultTextStyle = function (defaultTextStyle) {\n    this._defaultStyle = defaultTextStyle || DEFAULT_RICH_TEXT_COLOR;\n  };\n  ZRText.prototype.setTextContent = function (textContent) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error('Can\\'t attach text on another text');\n    }\n  };\n  ZRText.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n    if (!sourceStyle) {\n      return targetStyle;\n    }\n    var sourceRich = sourceStyle.rich;\n    var targetRich = targetStyle.rich || sourceRich && {};\n    extend(targetStyle, sourceStyle);\n    if (sourceRich && targetRich) {\n      this._mergeRich(targetRich, sourceRich);\n      targetStyle.rich = targetRich;\n    } else if (targetRich) {\n      targetStyle.rich = targetRich;\n    }\n    return targetStyle;\n  };\n  ZRText.prototype._mergeRich = function (targetRich, sourceRich) {\n    var richNames = keys(sourceRich);\n    for (var i = 0; i < richNames.length; i++) {\n      var richName = richNames[i];\n      targetRich[richName] = targetRich[richName] || {};\n      extend(targetRich[richName], sourceRich[richName]);\n    }\n  };\n  ZRText.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_TEXT_ANIMATION_PROPS;\n  };\n  ZRText.prototype._getOrCreateChild = function (Ctor) {\n    var child = this._children[this._childCursor];\n    if (!child || !(child instanceof Ctor)) {\n      child = new Ctor();\n    }\n    this._children[this._childCursor++] = child;\n    child.__zr = this.__zr;\n    child.parent = this;\n    return child;\n  };\n  ZRText.prototype._updatePlainTexts = function () {\n    var style = this.style;\n    var textFont = style.font || DEFAULT_FONT;\n    var textPadding = style.padding;\n    var text = getStyleText(style);\n    var contentBlock = parsePlainText(text, style);\n    var needDrawBg = needDrawBackground(style);\n    var bgColorDrawn = !!style.backgroundColor;\n    var outerHeight = contentBlock.outerHeight;\n    var outerWidth = contentBlock.outerWidth;\n    var contentWidth = contentBlock.contentWidth;\n    var textLines = contentBlock.lines;\n    var lineHeight = contentBlock.lineHeight;\n    var defaultStyle = this._defaultStyle;\n    this.isTruncated = !!contentBlock.isTruncated;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var textAlign = style.align || defaultStyle.align || 'left';\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign || 'top';\n    var textX = baseX;\n    var textY = adjustTextY(baseY, contentBlock.contentHeight, verticalAlign);\n    if (needDrawBg || textPadding) {\n      var boxX = adjustTextX(baseX, outerWidth, textAlign);\n      var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n      needDrawBg && this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    textY += lineHeight / 2;\n    if (textPadding) {\n      textX = getTextXForPadding(baseX, textAlign, textPadding);\n      if (verticalAlign === 'top') {\n        textY += textPadding[0];\n      } else if (verticalAlign === 'bottom') {\n        textY -= textPadding[2];\n      }\n    }\n    var defaultLineWidth = 0;\n    var useDefaultFill = false;\n    var textFill = getFill('fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in style ? style.stroke : !bgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = style.textShadowBlur > 0;\n    var fixedBoundingRect = style.width != null && (style.overflow === 'truncate' || style.overflow === 'break' || style.overflow === 'breakAll');\n    var calculatedLineHeight = contentBlock.calculatedLineHeight;\n    for (var i = 0; i < textLines.length; i++) {\n      var el = this._getOrCreateChild(TSpan);\n      var subElStyle = el.createStyle();\n      el.useStyle(subElStyle);\n      subElStyle.text = textLines[i];\n      subElStyle.x = textX;\n      subElStyle.y = textY;\n      if (textAlign) {\n        subElStyle.textAlign = textAlign;\n      }\n      subElStyle.textBaseline = 'middle';\n      subElStyle.opacity = style.opacity;\n      subElStyle.strokeFirst = true;\n      if (hasShadow) {\n        subElStyle.shadowBlur = style.textShadowBlur || 0;\n        subElStyle.shadowColor = style.textShadowColor || 'transparent';\n        subElStyle.shadowOffsetX = style.textShadowOffsetX || 0;\n        subElStyle.shadowOffsetY = style.textShadowOffsetY || 0;\n      }\n      subElStyle.stroke = textStroke;\n      subElStyle.fill = textFill;\n      if (textStroke) {\n        subElStyle.lineWidth = style.lineWidth || defaultLineWidth;\n        subElStyle.lineDash = style.lineDash;\n        subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      }\n      subElStyle.font = textFont;\n      setSeparateFont(subElStyle, style);\n      textY += lineHeight;\n      if (fixedBoundingRect) {\n        el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, contentWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, calculatedLineHeight, subElStyle.textBaseline), contentWidth, calculatedLineHeight));\n      }\n    }\n  };\n  ZRText.prototype._updateRichTexts = function () {\n    var style = this.style;\n    var text = getStyleText(style);\n    var contentBlock = parseRichText(text, style);\n    var contentWidth = contentBlock.width;\n    var outerWidth = contentBlock.outerWidth;\n    var outerHeight = contentBlock.outerHeight;\n    var textPadding = style.padding;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var defaultStyle = this._defaultStyle;\n    var textAlign = style.align || defaultStyle.align;\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign;\n    this.isTruncated = !!contentBlock.isTruncated;\n    var boxX = adjustTextX(baseX, outerWidth, textAlign);\n    var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n    var xLeft = boxX;\n    var lineTop = boxY;\n    if (textPadding) {\n      xLeft += textPadding[3];\n      lineTop += textPadding[0];\n    }\n    var xRight = xLeft + contentWidth;\n    if (needDrawBackground(style)) {\n      this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    var bgColorDrawn = !!style.backgroundColor;\n    for (var i = 0; i < contentBlock.lines.length; i++) {\n      var line = contentBlock.lines[i];\n      var tokens = line.tokens;\n      var tokenCount = tokens.length;\n      var lineHeight = line.lineHeight;\n      var remainedWidth = line.width;\n      var leftIndex = 0;\n      var lineXLeft = xLeft;\n      var lineXRight = xRight;\n      var rightIndex = tokenCount - 1;\n      var token = void 0;\n      while (leftIndex < tokenCount && (token = tokens[leftIndex], !token.align || token.align === 'left')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft, 'left', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      while (rightIndex >= 0 && (token = tokens[rightIndex], token.align === 'right')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXRight, 'right', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXRight -= token.width;\n        rightIndex--;\n      }\n      lineXLeft += (contentWidth - (lineXLeft - xLeft) - (xRight - lineXRight) - remainedWidth) / 2;\n      while (leftIndex <= rightIndex) {\n        token = tokens[leftIndex];\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft + token.width / 2, 'center', bgColorDrawn);\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      lineTop += lineHeight;\n    }\n  };\n  ZRText.prototype._placeToken = function (token, style, lineHeight, lineTop, x, textAlign, parentBgColorDrawn) {\n    var tokenStyle = style.rich[token.styleName] || {};\n    tokenStyle.text = token.text;\n    var verticalAlign = token.verticalAlign;\n    var y = lineTop + lineHeight / 2;\n    if (verticalAlign === 'top') {\n      y = lineTop + token.height / 2;\n    } else if (verticalAlign === 'bottom') {\n      y = lineTop + lineHeight - token.height / 2;\n    }\n    var needDrawBg = !token.isLineHolder && needDrawBackground(tokenStyle);\n    needDrawBg && this._renderBackground(tokenStyle, style, textAlign === 'right' ? x - token.width : textAlign === 'center' ? x - token.width / 2 : x, y - token.height / 2, token.width, token.height);\n    var bgColorDrawn = !!tokenStyle.backgroundColor;\n    var textPadding = token.textPadding;\n    if (textPadding) {\n      x = getTextXForPadding(x, textAlign, textPadding);\n      y -= token.height / 2 - textPadding[0] - token.innerHeight / 2;\n    }\n    var el = this._getOrCreateChild(TSpan);\n    var subElStyle = el.createStyle();\n    el.useStyle(subElStyle);\n    var defaultStyle = this._defaultStyle;\n    var useDefaultFill = false;\n    var defaultLineWidth = 0;\n    var textFill = getFill('fill' in tokenStyle ? tokenStyle.fill : 'fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in tokenStyle ? tokenStyle.stroke : 'stroke' in style ? style.stroke : !bgColorDrawn && !parentBgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = tokenStyle.textShadowBlur > 0 || style.textShadowBlur > 0;\n    subElStyle.text = token.text;\n    subElStyle.x = x;\n    subElStyle.y = y;\n    if (hasShadow) {\n      subElStyle.shadowBlur = tokenStyle.textShadowBlur || style.textShadowBlur || 0;\n      subElStyle.shadowColor = tokenStyle.textShadowColor || style.textShadowColor || 'transparent';\n      subElStyle.shadowOffsetX = tokenStyle.textShadowOffsetX || style.textShadowOffsetX || 0;\n      subElStyle.shadowOffsetY = tokenStyle.textShadowOffsetY || style.textShadowOffsetY || 0;\n    }\n    subElStyle.textAlign = textAlign;\n    subElStyle.textBaseline = 'middle';\n    subElStyle.font = token.font || DEFAULT_FONT;\n    subElStyle.opacity = retrieve3(tokenStyle.opacity, style.opacity, 1);\n    setSeparateFont(subElStyle, tokenStyle);\n    if (textStroke) {\n      subElStyle.lineWidth = retrieve3(tokenStyle.lineWidth, style.lineWidth, defaultLineWidth);\n      subElStyle.lineDash = retrieve2(tokenStyle.lineDash, style.lineDash);\n      subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      subElStyle.stroke = textStroke;\n    }\n    if (textFill) {\n      subElStyle.fill = textFill;\n    }\n    var textWidth = token.contentWidth;\n    var textHeight = token.contentHeight;\n    el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, textWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, textHeight, subElStyle.textBaseline), textWidth, textHeight));\n  };\n  ZRText.prototype._renderBackground = function (style, topStyle, x, y, width, height) {\n    var textBackgroundColor = style.backgroundColor;\n    var textBorderWidth = style.borderWidth;\n    var textBorderColor = style.borderColor;\n    var isImageBg = textBackgroundColor && textBackgroundColor.image;\n    var isPlainOrGradientBg = textBackgroundColor && !isImageBg;\n    var textBorderRadius = style.borderRadius;\n    var self = this;\n    var rectEl;\n    var imgEl;\n    if (isPlainOrGradientBg || style.lineHeight || textBorderWidth && textBorderColor) {\n      rectEl = this._getOrCreateChild(Rect);\n      rectEl.useStyle(rectEl.createStyle());\n      rectEl.style.fill = null;\n      var rectShape = rectEl.shape;\n      rectShape.x = x;\n      rectShape.y = y;\n      rectShape.width = width;\n      rectShape.height = height;\n      rectShape.r = textBorderRadius;\n      rectEl.dirtyShape();\n    }\n    if (isPlainOrGradientBg) {\n      var rectStyle = rectEl.style;\n      rectStyle.fill = textBackgroundColor || null;\n      rectStyle.fillOpacity = retrieve2(style.fillOpacity, 1);\n    } else if (isImageBg) {\n      imgEl = this._getOrCreateChild(ZRImage);\n      imgEl.onload = function () {\n        self.dirtyStyle();\n      };\n      var imgStyle = imgEl.style;\n      imgStyle.image = textBackgroundColor.image;\n      imgStyle.x = x;\n      imgStyle.y = y;\n      imgStyle.width = width;\n      imgStyle.height = height;\n    }\n    if (textBorderWidth && textBorderColor) {\n      var rectStyle = rectEl.style;\n      rectStyle.lineWidth = textBorderWidth;\n      rectStyle.stroke = textBorderColor;\n      rectStyle.strokeOpacity = retrieve2(style.strokeOpacity, 1);\n      rectStyle.lineDash = style.borderDash;\n      rectStyle.lineDashOffset = style.borderDashOffset || 0;\n      rectEl.strokeContainThreshold = 0;\n      if (rectEl.hasFill() && rectEl.hasStroke()) {\n        rectStyle.strokeFirst = true;\n        rectStyle.lineWidth *= 2;\n      }\n    }\n    var commonStyle = (rectEl || imgEl).style;\n    commonStyle.shadowBlur = style.shadowBlur || 0;\n    commonStyle.shadowColor = style.shadowColor || 'transparent';\n    commonStyle.shadowOffsetX = style.shadowOffsetX || 0;\n    commonStyle.shadowOffsetY = style.shadowOffsetY || 0;\n    commonStyle.opacity = retrieve3(style.opacity, topStyle.opacity, 1);\n  };\n  ZRText.makeFont = function (style) {\n    var font = '';\n    if (hasSeparateFont(style)) {\n      font = [style.fontStyle, style.fontWeight, parseFontSize(style.fontSize), style.fontFamily || 'sans-serif'].join(' ');\n    }\n    return font && trim(font) || style.textFont || style.font;\n  };\n  return ZRText;\n}(Displayable);\nvar VALID_TEXT_ALIGN = {\n  left: true,\n  right: 1,\n  center: 1\n};\nvar VALID_TEXT_VERTICAL_ALIGN = {\n  top: 1,\n  bottom: 1,\n  middle: 1\n};\nvar FONT_PARTS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily'];\nexport function parseFontSize(fontSize) {\n  if (typeof fontSize === 'string' && (fontSize.indexOf('px') !== -1 || fontSize.indexOf('rem') !== -1 || fontSize.indexOf('em') !== -1)) {\n    return fontSize;\n  } else if (!isNaN(+fontSize)) {\n    return fontSize + 'px';\n  } else {\n    return DEFAULT_FONT_SIZE + 'px';\n  }\n}\nfunction setSeparateFont(targetStyle, sourceStyle) {\n  for (var i = 0; i < FONT_PARTS.length; i++) {\n    var fontProp = FONT_PARTS[i];\n    var val = sourceStyle[fontProp];\n    if (val != null) {\n      targetStyle[fontProp] = val;\n    }\n  }\n}\nexport function hasSeparateFont(style) {\n  return style.fontSize != null || style.fontFamily || style.fontWeight;\n}\nexport function normalizeTextStyle(style) {\n  normalizeStyle(style);\n  each(style.rich, normalizeStyle);\n  return style;\n}\nfunction normalizeStyle(style) {\n  if (style) {\n    style.font = ZRText.makeFont(style);\n    var textAlign = style.align;\n    textAlign === 'middle' && (textAlign = 'center');\n    style.align = textAlign == null || VALID_TEXT_ALIGN[textAlign] ? textAlign : 'left';\n    var verticalAlign = style.verticalAlign;\n    verticalAlign === 'center' && (verticalAlign = 'middle');\n    style.verticalAlign = verticalAlign == null || VALID_TEXT_VERTICAL_ALIGN[verticalAlign] ? verticalAlign : 'top';\n    var textPadding = style.padding;\n    if (textPadding) {\n      style.padding = normalizeCssArray(style.padding);\n    }\n  }\n}\nfunction getStroke(stroke, lineWidth) {\n  return stroke == null || lineWidth <= 0 || stroke === 'transparent' || stroke === 'none' ? null : stroke.image || stroke.colorStops ? '#000' : stroke;\n}\nfunction getFill(fill) {\n  return fill == null || fill === 'none' ? null : fill.image || fill.colorStops ? '#000' : fill;\n}\nfunction getTextXForPadding(x, textAlign, textPadding) {\n  return textAlign === 'right' ? x - textPadding[1] : textAlign === 'center' ? x + textPadding[3] / 2 - textPadding[1] / 2 : x + textPadding[3];\n}\nfunction getStyleText(style) {\n  var text = style.text;\n  text != null && (text += '');\n  return text;\n}\nfunction needDrawBackground(style) {\n  return !!(style.backgroundColor || style.lineHeight || style.borderWidth && style.borderColor);\n}\nexport default ZRText;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}