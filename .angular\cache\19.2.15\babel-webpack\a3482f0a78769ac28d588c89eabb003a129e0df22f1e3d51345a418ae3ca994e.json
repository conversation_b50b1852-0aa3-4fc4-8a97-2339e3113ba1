{"ast": null, "code": "var round = Math.round;\nexport function subPixelOptimizeLine(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var x1 = inputShape.x1;\n  var x2 = inputShape.x2;\n  var y1 = inputShape.y1;\n  var y2 = inputShape.y2;\n  outputShape.x1 = x1;\n  outputShape.x2 = x2;\n  outputShape.y1 = y1;\n  outputShape.y2 = y2;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  if (round(x1 * 2) === round(x2 * 2)) {\n    outputShape.x1 = outputShape.x2 = subPixelOptimize(x1, lineWidth, true);\n  }\n  if (round(y1 * 2) === round(y2 * 2)) {\n    outputShape.y1 = outputShape.y2 = subPixelOptimize(y1, lineWidth, true);\n  }\n  return outputShape;\n}\nexport function subPixelOptimizeRect(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var originX = inputShape.x;\n  var originY = inputShape.y;\n  var originWidth = inputShape.width;\n  var originHeight = inputShape.height;\n  outputShape.x = originX;\n  outputShape.y = originY;\n  outputShape.width = originWidth;\n  outputShape.height = originHeight;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  outputShape.x = subPixelOptimize(originX, lineWidth, true);\n  outputShape.y = subPixelOptimize(originY, lineWidth, true);\n  outputShape.width = Math.max(subPixelOptimize(originX + originWidth, lineWidth, false) - outputShape.x, originWidth === 0 ? 0 : 1);\n  outputShape.height = Math.max(subPixelOptimize(originY + originHeight, lineWidth, false) - outputShape.y, originHeight === 0 ? 0 : 1);\n  return outputShape;\n}\nexport function subPixelOptimize(position, lineWidth, positiveOrNegative) {\n  if (!lineWidth) {\n    return position;\n  }\n  var doubledPosition = round(position * 2);\n  return (doubledPosition + round(lineWidth)) % 2 === 0 ? doubledPosition / 2 : (doubledPosition + (positiveOrNegative ? 1 : -1)) / 2;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}