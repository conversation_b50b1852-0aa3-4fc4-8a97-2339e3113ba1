{"ast": null, "code": "import { BehaviorSubject } from '../BehaviorSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishBehavior(initialValue) {\n  return source => {\n    const subject = new BehaviorSubject(initialValue);\n    return new ConnectableObservable(source, () => subject);\n  };\n}\n//# sourceMappingURL=publishBehavior.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}