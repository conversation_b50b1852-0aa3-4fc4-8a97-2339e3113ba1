{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"src/app/core/services/program-categories-services/program-categories.service\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i6 from \"@angular/common\";\nfunction ViewProgramCategoriesComponent_ng_container_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"mat-card\")(3, \"mat-card-header\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-actions\")(6, \"img\", 9);\n    i0.ɵɵlistener(\"click\", function ViewProgramCategoriesComponent_ng_container_7_ng_container_3_Template_img_click_6_listener() {\n      const PrgoramCategrory_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.editPrgoramCategrory(PrgoramCategrory_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"img\", 9);\n    i0.ɵɵlistener(\"click\", function ViewProgramCategoriesComponent_ng_container_7_ng_container_3_Template_img_click_7_listener() {\n      const PrgoramCategrory_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deletePrgoramCategrory(PrgoramCategrory_r2.id));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const PrgoramCategrory_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.translate.currentLang === ctx_r2.langEnum.en ? PrgoramCategrory_r2.enCatName : PrgoramCategrory_r2.arCatName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.edit, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.imagesPathesService.trash, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewProgramCategoriesComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 2);\n    i0.ɵɵtemplate(3, ViewProgramCategoriesComponent_ng_container_7_ng_container_3_Template, 8, 3, \"ng-container\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.allPrgoramCategrorylist);\n  }\n}\nexport let ViewProgramCategoriesComponent = /*#__PURE__*/(() => {\n  class ViewProgramCategoriesComponent {\n    translate;\n    dialog;\n    programCategoriesService;\n    alertify;\n    imagesPathesService;\n    addEditProgramCategories = new EventEmitter();\n    allPrgoramCategrorylist = [];\n    resMessage = {};\n    langEnum = LanguageEnum;\n    programsCategoryFilterRequestModel = {};\n    constructor(translate, dialog, programCategoriesService, alertify, imagesPathesService) {\n      this.translate = translate;\n      this.dialog = dialog;\n      this.programCategoriesService = programCategoriesService;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getAllCategories();\n    }\n    AddCategories() {\n      this.addEditProgramCategories.emit();\n    }\n    getAllCategories() {\n      this.programCategoriesService.getProgramCategories(this.programsCategoryFilterRequestModel).subscribe(res => {\n        if (res.isSuccess) {\n          this.allPrgoramCategrorylist = res.data;\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    editPrgoramCategrory(event) {\n      this.addEditProgramCategories.emit(event);\n    }\n    deletePrgoramCategrory(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this program category\" : \"هل متأكد من حذف هذا القسم \";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete program category' : 'حذف  القسم', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.programCategoriesService.deleteProgramCatiegories(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getAllCategories();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    static ɵfac = function ViewProgramCategoriesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewProgramCategoriesComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.ProgramCategoriesService), i0.ɵɵdirectiveInject(i4.AlertifyService), i0.ɵɵdirectiveInject(i5.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewProgramCategoriesComponent,\n      selectors: [[\"app-view-program-categories\"]],\n      outputs: {\n        addEditProgramCategories: \"addEditProgramCategories\"\n      },\n      decls: 8,\n      vars: 4,\n      consts: [[1, \"tab_page\", \"notifacation_page\", \"condition_details\"], [1, \"pt-3\", \"mb-3\"], [1, \"row\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"container_haeder\", \"mb-2\"], [1, \"cancel-btn\", 3, \"click\"], [4, \"ngIf\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"max_h\", \"mt-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-3\", \"mb-3\", \"setting_program_category\"], [1, \"mx-2\", \"card_actions_icon\", 3, \"click\", \"src\"]],\n      template: function ViewProgramCategoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ViewProgramCategoriesComponent_Template_button_click_4_listener() {\n            return ctx.AddCategories();\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(7, ViewProgramCategoriesComponent_ng_container_7_Template, 4, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 2, \"PROGRAM_CATEGORY.ADD_CATEGORY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.allPrgoramCategrorylist);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, TranslateModule, i1.TranslatePipe],\n      styles: [\".condition_details.tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;height:85vh;margin-top:1rem;border-radius:.313rem;padding:1rem}.condition_details[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:68vh;max-height:68vh;overflow-y:auto}.condition_details[_ngcontent-%COMP%]   .container_haeder[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center}.condition_details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:8.4375rem;padding:.5rem;border:none;color:#fff;display:block}.condition_details[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{border-bottom:.063rem solid rgba(0,0,0,.1)}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]{color:var(--main_color);font-size:1.25rem;font-weight:700}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]:lang(en){text-align:left}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]:lang(ar){text-align:right}@media (max-width: 48rem){.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:3rem}}\"]\n    });\n  }\n  return ViewProgramCategoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}