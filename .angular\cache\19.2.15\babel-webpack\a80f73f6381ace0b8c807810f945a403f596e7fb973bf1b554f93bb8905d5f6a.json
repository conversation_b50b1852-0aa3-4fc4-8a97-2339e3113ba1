{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { UserScientificProblemComponent } from './components/user-requests-view/user-scientific-problem/user-scientific-problem.component';\nimport { UserRequestsCategoriesViewComponent } from './components/user-requests-view/user-requests-categories-view/user-requests-categories-view.component';\nimport { UserRequestsRoutingModule } from './user-requests-routing.module';\nimport { UserWithdrawalRequestsComponent } from './components/user-requests-view/user-withdrawal-requests/user-withdrawal-requests.component';\nimport { UserJoinRequestsComponent } from './components/user-requests-view/user-join-requests/user-join-requests.component';\nimport { UserRequestsViewComponent } from './components/user-requests-view/user-requests-view.component';\nimport { UserScientificProblemListViewComponent } from './components/user-requests-view/user-scientific-problem/user-scientific-problem-list-view/user-scientific-problem-list-view.component';\nimport { AddScientificProblemComponent } from './components/user-requests-view/user-scientific-problem/add-scientific-problem/add-scientific-problem.component';\nimport { AddStudentDropOutRequestComponent } from './components/user-requests-view/user-withdrawal-requests/add-student-drop-out-request/add-student-drop-out-request.component';\nimport { StudentDropOutRequestComponent } from './components/user-requests-view/user-withdrawal-requests/student-drop-out-request/student-drop-out-request.component';\nlet UserRequestsModule = class UserRequestsModule {};\nUserRequestsModule = __decorate([NgModule({\n  declarations: [UserRequestsViewComponent, UserRequestsCategoriesViewComponent, UserWithdrawalRequestsComponent, UserJoinRequestsComponent, UserScientificProblemComponent, AddScientificProblemComponent, StudentDropOutRequestComponent, AddStudentDropOutRequestComponent, UserScientificProblemListViewComponent],\n  imports: [SharedModule, FormsModule, ReactiveFormsModule, CommonModule, UserRequestsRoutingModule, TranslateModule, MatTooltipModule],\n  providers: [MatTooltipModule, DatePipe]\n})], UserRequestsModule);\nexport { UserRequestsModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}