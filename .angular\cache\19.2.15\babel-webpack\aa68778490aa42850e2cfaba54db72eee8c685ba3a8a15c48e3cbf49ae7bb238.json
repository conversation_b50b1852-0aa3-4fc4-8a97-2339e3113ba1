{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport Model from '../../model/Model.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar SunburstSeriesModel = /** @class */function (_super) {\n  __extends(SunburstSeriesModel, _super);\n  function SunburstSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SunburstSeriesModel.type;\n    _this.ignoreStyleOnData = true;\n    return _this;\n  }\n  SunburstSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levelModels = this._levelModels = zrUtil.map(option.levels || [], function (levelDefine) {\n      return new Model(levelDefine, this, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = levelModels[node.depth];\n        levelModel && (model.parentModel = levelModel);\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  SunburstSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /*\r\n   * @override\r\n   */\n  SunburstSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treePathInfo = wrapTreePathInfo(node, this);\n    return params;\n  };\n  SunburstSeriesModel.prototype.getLevelModel = function (node) {\n    return this._levelModels && this._levelModels[node.depth];\n  };\n  SunburstSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  SunburstSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  SunburstSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  SunburstSeriesModel.type = 'series.sunburst';\n  SunburstSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    // 最小角度改为0\n    minAngle: 0,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // 'rootToNode', 'link', or false\n    nodeClick: 'rootToNode',\n    renderLabelForZeroData: false,\n    label: {\n      // could be: 'radial', 'tangential', or 'none'\n      rotate: 'radial',\n      show: true,\n      opacity: 1,\n      // 'left' is for inner side of inside, and 'right' is for outer\n      // side for inside\n      align: 'center',\n      position: 'inside',\n      distance: 5,\n      silent: true\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderColor: 'white',\n      borderType: 'solid',\n      shadowBlur: 0,\n      shadowColor: 'rgba(0, 0, 0, 0.2)',\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    emphasis: {\n      focus: 'descendant'\n    },\n    blur: {\n      itemStyle: {\n        opacity: 0.2\n      },\n      label: {\n        opacity: 0.1\n      }\n    },\n    // Animation type can be expansion, scale.\n    animationType: 'expansion',\n    animationDuration: 1000,\n    animationDurationUpdate: 500,\n    data: [],\n    /**\r\n     * Sort order.\r\n     *\r\n     * Valid values: 'desc', 'asc', null, or callback function.\r\n     * 'desc' and 'asc' for descend and ascendant order;\r\n     * null for not sorting;\r\n     * example of callback function:\r\n     * function(nodeA, nodeB) {\r\n     *     return nodeA.getValue() - nodeB.getValue();\r\n     * }\r\n     */\n    sort: 'desc'\n  };\n  return SunburstSeriesModel;\n}(SeriesModel);\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    // TODO First value of array must be a number\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\nexport default SunburstSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}