{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { getPercentSeats } from '../../util/number.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar innerData = modelUtil.makeInner();\nvar PieSeriesModel = /** @class */function (_super) {\n  __extends(PieSeriesModel, _super);\n  function PieSeriesModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @overwrite\r\n   */\n  PieSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    this._defaultLabelLine(option);\n  };\n  /**\r\n   * @overwrite\r\n   */\n  PieSeriesModel.prototype.mergeOption = function () {\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n  /**\r\n   * @overwrite\r\n   */\n  PieSeriesModel.prototype.getInitialData = function () {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  /**\r\n   * @overwrite\r\n   */\n  PieSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    // update seats when data is changed\n    var dataInner = innerData(data);\n    var seats = dataInner.seats;\n    if (!seats) {\n      var valueList_1 = [];\n      data.each(data.mapDimension('value'), function (value) {\n        valueList_1.push(value);\n      });\n      seats = dataInner.seats = getPercentSeats(valueList_1, data.hostModel.get('percentPrecision'));\n    }\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    // seats may be empty when sum is 0\n    params.percent = seats[dataIndex] || 0;\n    params.$vars.push('percent');\n    return params;\n  };\n  PieSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    modelUtil.defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  PieSeriesModel.type = 'series.pie';\n  PieSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    endAngle: 'auto',\n    padAngle: 0,\n    // 最小角度改为0\n    minAngle: 0,\n    // If the angle of a sector less than `minShowLabelAngle`,\n    // the label will not be displayed.\n    minShowLabelAngle: 0,\n    // 选中时扇区偏移量\n    selectedOffset: 10,\n    // 选择模式，默认关闭，可选single，multiple\n    // selectedMode: false,\n    // 南丁格尔玫瑰图模式，'radius'（半径） | 'area'（面积）\n    // roseType: null,\n    percentPrecision: 2,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // cursor: null,\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    width: null,\n    height: null,\n    label: {\n      // color: 'inherit',\n      // If rotate around circle\n      rotate: 0,\n      show: true,\n      overflow: 'truncate',\n      // 'outer', 'inside', 'center'\n      position: 'outer',\n      // 'none', 'labelLine', 'edge'. Works only when position is 'outer'\n      alignTo: 'none',\n      // Closest distance between label and chart edge.\n      // Works only position is 'outer' and alignTo is 'edge'.\n      edgeDistance: '25%',\n      // Works only position is 'outer' and alignTo is not 'edge'.\n      bleedMargin: 10,\n      // Distance between text and label line.\n      distanceToLabelLine: 5\n      // formatter: 标签文本格式器，同 tooltip.formatter，不支持异步回调\n      // 默认使用全局文本样式，详见 textStyle\n      // distance: 当position为inner时有效，为label位置到圆心的距离与圆半径(环状图为内外半径和)的比例系数\n    },\n    // Enabled when label.normal.position is 'outer'\n    labelLine: {\n      show: true,\n      // 引导线两段中的第一段长度\n      length: 15,\n      // 引导线两段中的第二段长度\n      length2: 15,\n      smooth: false,\n      minTurnAngle: 90,\n      maxSurfaceAngle: 90,\n      lineStyle: {\n        // color: 各异,\n        width: 1,\n        type: 'solid'\n      }\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderJoin: 'round'\n    },\n    showEmptyCircle: true,\n    emptyCircleStyle: {\n      color: 'lightgray',\n      opacity: 1\n    },\n    labelLayout: {\n      // Hide the overlapped label.\n      hideOverlap: true\n    },\n    emphasis: {\n      scale: true,\n      scaleSize: 5\n    },\n    // If use strategy to avoid label overlapping\n    avoidLabelOverlap: true,\n    // Animation type. Valid values: expansion, scale\n    animationType: 'expansion',\n    animationDuration: 1000,\n    // Animation type when update. Valid values: transition, expansion\n    animationTypeUpdate: 'transition',\n    animationEasingUpdate: 'cubicInOut',\n    animationDurationUpdate: 500,\n    animationEasing: 'cubicInOut'\n  };\n  return PieSeriesModel;\n}(SeriesModel);\nexport default PieSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}