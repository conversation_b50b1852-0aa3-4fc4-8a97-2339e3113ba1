{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport GeoModel from '../../coord/geo/GeoModel.js';\nimport geoCreator from '../../coord/geo/geoCreator.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nimport GeoView from './GeoView.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nfunction registerMap(mapName, geoJson, specialAreas) {\n  geoSourceManager.registerMap(mapName, geoJson, specialAreas);\n}\nexport function install(registers) {\n  registers.registerCoordinateSystem('geo', geoCreator);\n  registers.registerComponentModel(GeoModel);\n  registers.registerComponentView(GeoView);\n  registers.registerImpl('registerMap', registerMap);\n  registers.registerImpl('getMap', function (mapName) {\n    return geoSourceManager.getMapForUser(mapName);\n  });\n  function makeAction(method, actionInfo) {\n    actionInfo.update = 'geo:updateSelectStatus';\n    registers.registerAction(actionInfo, function (payload, ecModel) {\n      var selected = {};\n      var allSelected = [];\n      ecModel.eachComponent({\n        mainType: 'geo',\n        query: payload\n      }, function (geoModel) {\n        geoModel[method](payload.name);\n        var geo = geoModel.coordinateSystem;\n        each(geo.regions, function (region) {\n          selected[region.name] = geoModel.isSelected(region.name) || false;\n        });\n        // Notice: there might be duplicated name in different regions.\n        var names = [];\n        each(selected, function (v, name) {\n          selected[name] && names.push(name);\n        });\n        allSelected.push({\n          geoIndex: geoModel.componentIndex,\n          // Use singular, the same naming convention as the event `selectchanged`.\n          name: names\n        });\n      });\n      return {\n        selected: selected,\n        allSelected: allSelected,\n        name: payload.name\n      };\n    });\n  }\n  makeAction('toggleSelected', {\n    type: 'geoToggleSelect',\n    event: 'geoselectchanged'\n  });\n  makeAction('select', {\n    type: 'geoSelect',\n    event: 'geoselected'\n  });\n  makeAction('unSelect', {\n    type: 'geoUnSelect',\n    event: 'geounselected'\n  });\n  /**\r\n   * @payload\r\n   * @property {string} [componentType=series]\r\n   * @property {number} [dx]\r\n   * @property {number} [dy]\r\n   * @property {number} [zoom]\r\n   * @property {number} [originX]\r\n   * @property {number} [originY]\r\n   */\n  registers.registerAction({\n    type: 'geoRoam',\n    event: 'geoRoam',\n    update: 'updateTransform'\n  }, function (payload, ecModel, api) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      var geo = componentModel.coordinateSystem;\n      if (geo.type !== 'geo') {\n        return;\n      }\n      var res = updateCenterAndZoom(geo, payload, componentModel.get('scaleLimit'), api);\n      componentModel.setCenter && componentModel.setCenter(res.center);\n      componentModel.setZoom && componentModel.setZoom(res.zoom);\n      // All map series with same `map` use the same geo coordinate system\n      // So the center and zoom must be in sync. Include the series not selected by legend\n      if (componentType === 'series') {\n        each(componentModel.seriesGroup, function (seriesModel) {\n          seriesModel.setCenter(res.center);\n          seriesModel.setZoom(res.zoom);\n        });\n      }\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}