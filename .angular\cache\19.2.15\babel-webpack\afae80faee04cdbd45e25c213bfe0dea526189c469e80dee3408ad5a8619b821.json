{"ast": null, "code": "import Point from './Point.js';\nvar extent = [0, 0];\nvar extent2 = [0, 0];\nvar minTv = new Point();\nvar maxTv = new Point();\nvar OrientedBoundingRect = function () {\n  function OrientedBoundingRect(rect, transform) {\n    this._corners = [];\n    this._axes = [];\n    this._origin = [0, 0];\n    for (var i = 0; i < 4; i++) {\n      this._corners[i] = new Point();\n    }\n    for (var i = 0; i < 2; i++) {\n      this._axes[i] = new Point();\n    }\n    if (rect) {\n      this.fromBoundingRect(rect, transform);\n    }\n  }\n  OrientedBoundingRect.prototype.fromBoundingRect = function (rect, transform) {\n    var corners = this._corners;\n    var axes = this._axes;\n    var x = rect.x;\n    var y = rect.y;\n    var x2 = x + rect.width;\n    var y2 = y + rect.height;\n    corners[0].set(x, y);\n    corners[1].set(x2, y);\n    corners[2].set(x2, y2);\n    corners[3].set(x, y2);\n    if (transform) {\n      for (var i = 0; i < 4; i++) {\n        corners[i].transform(transform);\n      }\n    }\n    Point.sub(axes[0], corners[1], corners[0]);\n    Point.sub(axes[1], corners[3], corners[0]);\n    axes[0].normalize();\n    axes[1].normalize();\n    for (var i = 0; i < 2; i++) {\n      this._origin[i] = axes[i].dot(corners[0]);\n    }\n  };\n  OrientedBoundingRect.prototype.intersect = function (other, mtv) {\n    var overlapped = true;\n    var noMtv = !mtv;\n    minTv.set(Infinity, Infinity);\n    maxTv.set(0, 0);\n    if (!this._intersectCheckOneSide(this, other, minTv, maxTv, noMtv, 1)) {\n      overlapped = false;\n      if (noMtv) {\n        return overlapped;\n      }\n    }\n    if (!this._intersectCheckOneSide(other, this, minTv, maxTv, noMtv, -1)) {\n      overlapped = false;\n      if (noMtv) {\n        return overlapped;\n      }\n    }\n    if (!noMtv) {\n      Point.copy(mtv, overlapped ? minTv : maxTv);\n    }\n    return overlapped;\n  };\n  OrientedBoundingRect.prototype._intersectCheckOneSide = function (self, other, minTv, maxTv, noMtv, inverse) {\n    var overlapped = true;\n    for (var i = 0; i < 2; i++) {\n      var axis = this._axes[i];\n      this._getProjMinMaxOnAxis(i, self._corners, extent);\n      this._getProjMinMaxOnAxis(i, other._corners, extent2);\n      if (extent[1] < extent2[0] || extent[0] > extent2[1]) {\n        overlapped = false;\n        if (noMtv) {\n          return overlapped;\n        }\n        var dist0 = Math.abs(extent2[0] - extent[1]);\n        var dist1 = Math.abs(extent[0] - extent2[1]);\n        if (Math.min(dist0, dist1) > maxTv.len()) {\n          if (dist0 < dist1) {\n            Point.scale(maxTv, axis, -dist0 * inverse);\n          } else {\n            Point.scale(maxTv, axis, dist1 * inverse);\n          }\n        }\n      } else if (minTv) {\n        var dist0 = Math.abs(extent2[0] - extent[1]);\n        var dist1 = Math.abs(extent[0] - extent2[1]);\n        if (Math.min(dist0, dist1) < minTv.len()) {\n          if (dist0 < dist1) {\n            Point.scale(minTv, axis, dist0 * inverse);\n          } else {\n            Point.scale(minTv, axis, -dist1 * inverse);\n          }\n        }\n      }\n    }\n    return overlapped;\n  };\n  OrientedBoundingRect.prototype._getProjMinMaxOnAxis = function (dim, corners, out) {\n    var axis = this._axes[dim];\n    var origin = this._origin;\n    var proj = corners[0].dot(axis) + origin[dim];\n    var min = proj;\n    var max = proj;\n    for (var i = 1; i < corners.length; i++) {\n      var proj_1 = corners[i].dot(axis) + origin[dim];\n      min = Math.min(proj_1, min);\n      max = Math.max(proj_1, max);\n    }\n    out[0] = min;\n    out[1] = max;\n  };\n  return OrientedBoundingRect;\n}();\nexport default OrientedBoundingRect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}