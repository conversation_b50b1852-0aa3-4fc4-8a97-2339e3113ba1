{"ast": null, "code": "var Point = function () {\n  function Point(x, y) {\n    this.x = x || 0;\n    this.y = y || 0;\n  }\n  Point.prototype.copy = function (other) {\n    this.x = other.x;\n    this.y = other.y;\n    return this;\n  };\n  Point.prototype.clone = function () {\n    return new Point(this.x, this.y);\n  };\n  Point.prototype.set = function (x, y) {\n    this.x = x;\n    this.y = y;\n    return this;\n  };\n  Point.prototype.equal = function (other) {\n    return other.x === this.x && other.y === this.y;\n  };\n  Point.prototype.add = function (other) {\n    this.x += other.x;\n    this.y += other.y;\n    return this;\n  };\n  Point.prototype.scale = function (scalar) {\n    this.x *= scalar;\n    this.y *= scalar;\n  };\n  Point.prototype.scaleAndAdd = function (other, scalar) {\n    this.x += other.x * scalar;\n    this.y += other.y * scalar;\n  };\n  Point.prototype.sub = function (other) {\n    this.x -= other.x;\n    this.y -= other.y;\n    return this;\n  };\n  Point.prototype.dot = function (other) {\n    return this.x * other.x + this.y * other.y;\n  };\n  Point.prototype.len = function () {\n    return Math.sqrt(this.x * this.x + this.y * this.y);\n  };\n  Point.prototype.lenSquare = function () {\n    return this.x * this.x + this.y * this.y;\n  };\n  Point.prototype.normalize = function () {\n    var len = this.len();\n    this.x /= len;\n    this.y /= len;\n    return this;\n  };\n  Point.prototype.distance = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return Math.sqrt(dx * dx + dy * dy);\n  };\n  Point.prototype.distanceSquare = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return dx * dx + dy * dy;\n  };\n  Point.prototype.negate = function () {\n    this.x = -this.x;\n    this.y = -this.y;\n    return this;\n  };\n  Point.prototype.transform = function (m) {\n    if (!m) {\n      return;\n    }\n    var x = this.x;\n    var y = this.y;\n    this.x = m[0] * x + m[2] * y + m[4];\n    this.y = m[1] * x + m[3] * y + m[5];\n    return this;\n  };\n  Point.prototype.toArray = function (out) {\n    out[0] = this.x;\n    out[1] = this.y;\n    return out;\n  };\n  Point.prototype.fromArray = function (input) {\n    this.x = input[0];\n    this.y = input[1];\n  };\n  Point.set = function (p, x, y) {\n    p.x = x;\n    p.y = y;\n  };\n  Point.copy = function (p, p2) {\n    p.x = p2.x;\n    p.y = p2.y;\n  };\n  Point.len = function (p) {\n    return Math.sqrt(p.x * p.x + p.y * p.y);\n  };\n  Point.lenSquare = function (p) {\n    return p.x * p.x + p.y * p.y;\n  };\n  Point.dot = function (p0, p1) {\n    return p0.x * p1.x + p0.y * p1.y;\n  };\n  Point.add = function (out, p0, p1) {\n    out.x = p0.x + p1.x;\n    out.y = p0.y + p1.y;\n  };\n  Point.sub = function (out, p0, p1) {\n    out.x = p0.x - p1.x;\n    out.y = p0.y - p1.y;\n  };\n  Point.scale = function (out, p0, scalar) {\n    out.x = p0.x * scalar;\n    out.y = p0.y * scalar;\n  };\n  Point.scaleAndAdd = function (out, p0, p1, scalar) {\n    out.x = p0.x + p1.x * scalar;\n    out.y = p0.y + p1.y * scalar;\n  };\n  Point.lerp = function (out, p0, p1, t) {\n    var onet = 1 - t;\n    out.x = onet * p0.x + t * p1.x;\n    out.y = onet * p0.y + t * p1.y;\n  };\n  return Point;\n}();\nexport default Point;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}