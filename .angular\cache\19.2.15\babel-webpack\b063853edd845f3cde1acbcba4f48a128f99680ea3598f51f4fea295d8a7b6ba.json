{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nvar ScatterSeriesModel = /** @class */function (_super) {\n  __extends(ScatterSeriesModel, _super);\n  function ScatterSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScatterSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  ScatterSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  ScatterSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n    if (progressive == null) {\n      // PENDING\n      return this.option.large ? 5e3 : this.get('progressive');\n    }\n    return progressive;\n  };\n  ScatterSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n    if (progressiveThreshold == null) {\n      // PENDING\n      return this.option.large ? 1e4 : this.get('progressiveThreshold');\n    }\n    return progressiveThreshold;\n  };\n  ScatterSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.point(data.getItemLayout(dataIndex));\n  };\n  ScatterSeriesModel.prototype.getZLevelKey = function () {\n    // Each progressive series has individual key.\n    return this.getData().count() > this.getProgressiveThreshold() ? this.id : '';\n  };\n  ScatterSeriesModel.type = 'series.scatter';\n  ScatterSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  ScatterSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    symbolSize: 10,\n    // symbolRotate: null,  // 图形旋转控制\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    // cursor: null,\n    itemStyle: {\n      opacity: 0.8\n      // color: 各异\n    },\n    emphasis: {\n      scale: true\n    },\n    // If clip the overflow graphics\n    // Works on cartesian / polar series\n    clip: true,\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    universalTransition: {\n      divideShape: 'clone'\n    }\n    // progressive: null\n  };\n  return ScatterSeriesModel;\n}(SeriesModel);\nexport default ScatterSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}