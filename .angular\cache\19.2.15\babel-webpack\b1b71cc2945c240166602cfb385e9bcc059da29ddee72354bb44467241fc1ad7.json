{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as vec2 from '../../core/vector.js';\nimport { quadraticSubdivide, cubicSubdivide, quadraticAt, cubicAt, quadraticDerivativeAt, cubicDerivativeAt } from '../../core/curve.js';\nvar out = [];\nvar BezierCurveShape = function () {\n  function BezierCurveShape() {\n    this.x1 = 0;\n    this.y1 = 0;\n    this.x2 = 0;\n    this.y2 = 0;\n    this.cpx1 = 0;\n    this.cpy1 = 0;\n    this.percent = 1;\n  }\n  return BezierCurveShape;\n}();\nexport { BezierCurveShape };\nfunction someVectorAt(shape, t, isTangent) {\n  var cpx2 = shape.cpx2;\n  var cpy2 = shape.cpy2;\n  if (cpx2 != null || cpy2 != null) {\n    return [(isTangent ? cubicDerivativeAt : cubicAt)(shape.x1, shape.cpx1, shape.cpx2, shape.x2, t), (isTangent ? cubicDerivativeAt : cubicAt)(shape.y1, shape.cpy1, shape.cpy2, shape.y2, t)];\n  } else {\n    return [(isTangent ? quadraticDerivativeAt : quadraticAt)(shape.x1, shape.cpx1, shape.x2, t), (isTangent ? quadraticDerivativeAt : quadraticAt)(shape.y1, shape.cpy1, shape.y2, t)];\n  }\n}\nvar BezierCurve = function (_super) {\n  __extends(BezierCurve, _super);\n  function BezierCurve(opts) {\n    return _super.call(this, opts) || this;\n  }\n  BezierCurve.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  BezierCurve.prototype.getDefaultShape = function () {\n    return new BezierCurveShape();\n  };\n  BezierCurve.prototype.buildPath = function (ctx, shape) {\n    var x1 = shape.x1;\n    var y1 = shape.y1;\n    var x2 = shape.x2;\n    var y2 = shape.y2;\n    var cpx1 = shape.cpx1;\n    var cpy1 = shape.cpy1;\n    var cpx2 = shape.cpx2;\n    var cpy2 = shape.cpy2;\n    var percent = shape.percent;\n    if (percent === 0) {\n      return;\n    }\n    ctx.moveTo(x1, y1);\n    if (cpx2 == null || cpy2 == null) {\n      if (percent < 1) {\n        quadraticSubdivide(x1, cpx1, x2, percent, out);\n        cpx1 = out[1];\n        x2 = out[2];\n        quadraticSubdivide(y1, cpy1, y2, percent, out);\n        cpy1 = out[1];\n        y2 = out[2];\n      }\n      ctx.quadraticCurveTo(cpx1, cpy1, x2, y2);\n    } else {\n      if (percent < 1) {\n        cubicSubdivide(x1, cpx1, cpx2, x2, percent, out);\n        cpx1 = out[1];\n        cpx2 = out[2];\n        x2 = out[3];\n        cubicSubdivide(y1, cpy1, cpy2, y2, percent, out);\n        cpy1 = out[1];\n        cpy2 = out[2];\n        y2 = out[3];\n      }\n      ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, x2, y2);\n    }\n  };\n  BezierCurve.prototype.pointAt = function (t) {\n    return someVectorAt(this.shape, t, false);\n  };\n  BezierCurve.prototype.tangentAt = function (t) {\n    var p = someVectorAt(this.shape, t, true);\n    return vec2.normalize(p, p);\n  };\n  return BezierCurve;\n}(Path);\n;\nBezierCurve.prototype.type = 'bezier-curve';\nexport default BezierCurve;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}