{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DateType } from 'ngx-hijri-gregorian-datepicker';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseSelectedDateModel } from '../../../core/ng-model/base-selected-date-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/language-services/language.service\";\nimport * as i3 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"hijGeo\"];\nfunction MiladyHijriCalendarComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"hijri-gregorian-datepicker\", 2, 0);\n    i0.ɵɵtwoWayListener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_0_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dateTo, $event) || (ctx_r1.dateTo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_0_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.emitData($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selectedDate\", ctx_r1.dateTo);\n    i0.ɵɵproperty(\"isRequired\", true)(\"maxGreg\", ctx_r1.maxGreg)(\"maxHijri\", ctx_r1.maxHijri)(\"minGreg\", ctx_r1.minGreg)(\"minHijri\", ctx_r1.minHijri)(\"GregLabel\", ctx_r1.GregLabel)(\"hijriLabel\", ctx_r1.hijriLabel)(\"selectedDateType\", ctx_r1.selectedDateType_Melady);\n  }\n}\nfunction MiladyHijriCalendarComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"hijri-gregorian-datepicker\", 3, 0);\n    i0.ɵɵtwoWayListener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_1_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dateTo, $event) || (ctx_r1.dateTo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_1_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.emitData($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selectedDate\", ctx_r1.dateTo);\n    i0.ɵɵproperty(\"isRequired\", true)(\"maxHijri\", ctx_r1.maxHijri)(\"minHijri\", ctx_r1.minHijri)(\"GregLabel\", ctx_r1.GregLabel)(\"hijriLabel\", ctx_r1.hijriLabel)(\"selectedDateType\", ctx_r1.selectedDateType_Hijri);\n  }\n}\nfunction MiladyHijriCalendarComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"hijri-gregorian-datepicker\", 4, 0);\n    i0.ɵɵtwoWayListener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_2_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dateTo, $event) || (ctx_r1.dateTo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedDateChange\", function MiladyHijriCalendarComponent_ng_container_2_Template_hijri_gregorian_datepicker_selectedDateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.emitData($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selectedDate\", ctx_r1.dateTo);\n    i0.ɵɵproperty(\"isRequired\", true)(\"maxGreg\", ctx_r1.maxGreg)(\"maxHijri\", ctx_r1.maxHijri)(\"minGreg\", ctx_r1.minGreg)(\"minHijri\", ctx_r1.minHijri)(\"GregLabel\", ctx_r1.GregLabel)(\"hijriLabel\", ctx_r1.hijriLabel)(\"selectedDateType\", ctx_r1.calenderType);\n  }\n}\nexport let MiladyHijriCalendarComponent = /*#__PURE__*/(() => {\n  class MiladyHijriCalendarComponent {\n    translate;\n    languageService;\n    dateFormatterService;\n    hijGeoChildComp; //any instead ElementReg to can access property\n    // dateFrom!: NgbDateStruct;\n    dateTo;\n    selectedDateType_Melady = DateType.Gregorian; // or DateType.Gregorian\n    selectedDateType_Hijri = DateType.Hijri;\n    calenderType;\n    dateFromString = '';\n    // maxGreg!: NgbDateStruct;\n    // maxHijri!: NgbDateStruct;\n    // minHijri!: NgbDateStruct;\n    isSubmit = false;\n    GregLabel = 'ميلادي';\n    hijriLabel = 'هجري';\n    hijri = false;\n    milady = false;\n    maxHijri;\n    maxGreg;\n    minHijri;\n    minGreg;\n    sendDate = new EventEmitter();\n    dataSend = new BaseSelectedDateModel();\n    //  @Input() item: { title: string, state: boolean };\n    editcalenderType;\n    constructor(translate, languageService, dateFormatterService) {\n      this.translate = translate;\n      this.languageService = languageService;\n      this.dateFormatterService = dateFormatterService;\n    }\n    ngOnInit() {\n      // this.minHijri = Date.now() || 2020;\n      this.setCurrentLang();\n      // this.setHijri();\n      if (this.editcalenderType) {\n        this.calenderType = this.editcalenderType.selectedDateType;\n      }\n    }\n    setHijri() {\n      // this.selectedDateType = DateType.Hijri;\n      // toDayHijriDate.day=toDayHijriDate.day - 4 ;\n      this.maxHijri = this.dateFormatterService.GetTodayHijri();\n      this.minHijri = this.dateFormatterService.GetTodayHijri();\n    }\n    setCurrentLang() {\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.changeHijri();\n      });\n    }\n    changeHijri() {\n      if (this.translate.currentLang === LanguageEnum.en) {\n        this.GregLabel = 'Melady';\n        this.hijriLabel = 'Hijri';\n      } else {\n        this.GregLabel = 'ميلادي';\n        this.hijriLabel = 'هجري';\n      }\n    }\n    emitData(data) {\n      // let DateNow = Date.now();\n      // // let YearDate = DateNow.toString(\"yyyy mm dd\");\n      // if(data > DateNow){\n      //   this.sendDate.emit(data)\n      // }\n      this.dataSend.selectedDateValue = data;\n      this.dataSend.selectedDateType = this.hijGeoChildComp.selectedDateType;\n      // this.dataSend.selectedDateType == 1? this.dataSend.calendarType = this.selectedDateType_Hijri : this.dataSend.calendarType = this.selectedDateType_Melady;\n      this.sendDate.emit(this.dataSend);\n    }\n    static ɵfac = function MiladyHijriCalendarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MiladyHijriCalendarComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.LanguageService), i0.ɵɵdirectiveInject(i3.DateFormatterService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MiladyHijriCalendarComponent,\n      selectors: [[\"app-milady-hijri-calendar\"]],\n      viewQuery: function MiladyHijriCalendarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hijGeoChildComp = _t.first);\n        }\n      },\n      inputs: {\n        dateTo: \"dateTo\",\n        hijri: \"hijri\",\n        milady: \"milady\",\n        maxHijri: \"maxHijri\",\n        maxGreg: \"maxGreg\",\n        minHijri: \"minHijri\",\n        minGreg: \"minGreg\",\n        editcalenderType: \"editcalenderType\"\n      },\n      outputs: {\n        sendDate: \"sendDate\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[\"hijGeo\", \"\", \"datePickerTo\", \"\"], [4, \"ngIf\"], [1, \"HijriGregorianDatePicker\", \"removeHijri\", 3, \"selectedDateChange\", \"selectedDate\", \"isRequired\", \"maxGreg\", \"maxHijri\", \"minGreg\", \"minHijri\", \"GregLabel\", \"hijriLabel\", \"selectedDateType\"], [1, \"HijriGregorianDatePicker\", \"removemilady\", 3, \"selectedDateChange\", \"selectedDate\", \"isRequired\", \"maxHijri\", \"minHijri\", \"GregLabel\", \"hijriLabel\", \"selectedDateType\"], [1, \"HijriGregorianDatePicker\", 3, \"selectedDateChange\", \"selectedDate\", \"isRequired\", \"maxGreg\", \"maxHijri\", \"minGreg\", \"minHijri\", \"GregLabel\", \"hijriLabel\", \"selectedDateType\"]],\n      template: function MiladyHijriCalendarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MiladyHijriCalendarComponent_ng_container_0_Template, 4, 9, \"ng-container\", 1)(1, MiladyHijriCalendarComponent_ng_container_1_Template, 4, 7, \"ng-container\", 1)(2, MiladyHijriCalendarComponent_ng_container_2_Template, 4, 9, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.hijri && !ctx.milady);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hijri && ctx.milady);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hijri && !ctx.milady);\n        }\n      },\n      dependencies: [i4.NgIf],\n      encapsulation: 2\n    });\n  }\n  return MiladyHijriCalendarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}