{"ast": null, "code": "export default function windingLine(x0, y0, x1, y1, x, y) {\n  if (y > y0 && y > y1 || y < y0 && y < y1) {\n    return 0;\n  }\n  if (y1 === y0) {\n    return 0;\n  }\n  var t = (y - y0) / (y1 - y0);\n  var dir = y1 < y0 ? 1 : -1;\n  if (t === 1 || t === 0) {\n    dir = y1 < y0 ? 0.5 : -0.5;\n  }\n  var x_ = t * (x1 - x0) + x0;\n  return x_ === x ? Infinity : x_ > x ? dir : 0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}