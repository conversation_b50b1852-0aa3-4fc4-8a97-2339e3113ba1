{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as echarts from '../../../core/echarts.js';\nimport * as history from '../../dataZoom/history.js';\nimport { ToolboxFeature } from '../featureManager.js';\nvar RestoreOption = /** @class */function (_super) {\n  __extends(RestoreOption, _super);\n  function RestoreOption() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  RestoreOption.prototype.onclick = function (ecModel, api) {\n    history.clear(ecModel);\n    api.dispatchAction({\n      type: 'restore',\n      from: this.uid\n    });\n  };\n  RestoreOption.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      // eslint-disable-next-line\n      icon: 'M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5',\n      title: ecModel.getLocaleModel().get(['toolbox', 'restore', 'title'])\n    };\n    return defaultOption;\n  };\n  return RestoreOption;\n}(ToolboxFeature);\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'restore',\n  event: 'restore',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  ecModel.resetOption('recreate');\n});\nexport default RestoreOption;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}