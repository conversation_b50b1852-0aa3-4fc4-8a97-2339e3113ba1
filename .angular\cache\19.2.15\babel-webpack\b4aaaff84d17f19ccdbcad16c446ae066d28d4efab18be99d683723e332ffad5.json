{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/question-bank-services/question-bank-question.service\";\nimport * as i3 from \"src/app/core/services/question-bank-services/question-bank-category.service\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction AddScientifiProblemToQuestionBankComponent_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const c_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngValue\", c_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? c_r1.engCatgName : c_r1.arabCatgName);\n  }\n}\nfunction AddScientifiProblemToQuestionBankComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \"\");\n  }\n}\nexport let AddScientifiProblemToQuestionBankComponent = /*#__PURE__*/(() => {\n  class AddScientifiProblemToQuestionBankComponent {\n    translate;\n    questionBankService;\n    questionBankCategoryService;\n    alertify;\n    closeAddScProblemToQuestionBankView = new EventEmitter();\n    scProbObjForAddToQuestionBankView = {};\n    resultMessage = {};\n    questionBankCategoryFilter = {\n      skip: 0,\n      take: **********\n    };\n    questionBankCategoryList = [];\n    selectedCategory;\n    langEnum = LanguageEnum;\n    constructor(translate, questionBankService, questionBankCategoryService, alertify) {\n      this.translate = translate;\n      this.questionBankService = questionBankService;\n      this.questionBankCategoryService = questionBankCategoryService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.getQuestionBankCategories();\n    }\n    closeAddScProblemToQuestionBankEvent() {\n      this.closeAddScProblemToQuestionBankView.emit();\n    }\n    getQuestionBankCategories() {\n      this.questionBankCategoryService.getQuestionBankCategoriesFilter(this.questionBankCategoryFilter).subscribe(res => {\n        if (res.isSuccess) {\n          this.questionBankCategoryList = res.data;\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    saveScProblemToQuestionBank() {\n      let model = {\n        id: this.scProbObjForAddToQuestionBankView.id,\n        question: this.scProbObjForAddToQuestionBankView?.questText,\n        reply: this.scProbObjForAddToQuestionBankView?.repText,\n        catId: this.selectedCategory?.id\n      };\n      if (model.catId) {\n        this.questionBankService.moveScProbToQuestionBank(model).subscribe(res => {\n          if (res.isSuccess) {\n            this.alertify.success(res.message || '');\n            this.closeAddScProblemToQuestionBankView.emit();\n          } else {\n            this.resultMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resultMessage = {\n          message: this.translate.instant('SCIENTIFIC_PROBLEM.SELECT_DEPATMENT'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    static ɵfac = function AddScientifiProblemToQuestionBankComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddScientifiProblemToQuestionBankComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.QuestionBankQuestionService), i0.ɵɵdirectiveInject(i3.QuestionBankCategoryService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddScientifiProblemToQuestionBankComponent,\n      selectors: [[\"app-add-scientifi-problem-to-question-bank\"]],\n      inputs: {\n        scProbObjForAddToQuestionBankView: \"scProbObjForAddToQuestionBankView\"\n      },\n      outputs: {\n        closeAddScProblemToQuestionBankView: \"closeAddScProblemToQuestionBankView\"\n      },\n      decls: 30,\n      vars: 25,\n      consts: [[1, \"form-group\", \"DataForm\", \"mt-5\", \"pl-3\", \"pr-3\"], [1, \"text-right\", \"head\", \"bold\"], [1, \"form-group\"], [1, \"label\"], [1, \"data_label\"], [1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [3, \"ngValue\"], [1, \"bold\"]],\n      template: function AddScientifiProblemToQuestionBankComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"label\", 4);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"label\", 3);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"label\", 4);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 2)(16, \"label\", 3);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"select\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddScientifiProblemToQuestionBankComponent_Template_select_ngModelChange_19_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(20, AddScientifiProblemToQuestionBankComponent_option_20_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(21, AddScientifiProblemToQuestionBankComponent_div_21_Template, 3, 4, \"div\", 7);\n          i0.ɵɵelementStart(22, \"section\", 8)(23, \"div\", 9)(24, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AddScientifiProblemToQuestionBankComponent_Template_button_click_24_listener() {\n            return ctx.saveScProblemToQuestionBank();\n          });\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AddScientifiProblemToQuestionBankComponent_Template_button_click_27_listener() {\n            return ctx.closeAddScProblemToQuestionBankEvent();\n          });\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 12, \"SCIENTIFIC_PROBLEM.ADD_SC_PROBLEM_TO_QUESTION_BANK\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 14, \"SCIENTIFIC_PROBLEM.USER_QUESTION\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.scProbObjForAddToQuestionBankView == null ? null : ctx.scProbObjForAddToQuestionBankView.questText, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 16, \"SCIENTIFIC_PROBLEM.SC_PROBLEM_ANSWER\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.scProbObjForAddToQuestionBankView == null ? null : ctx.scProbObjForAddToQuestionBankView.repText, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 18, \"SCIENTIFIC_PROBLEM.DEPARTMENT\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(24, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.questionBankCategoryList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 20, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 22, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.ɵNgNoValidate, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.NgModel, i6.NgForm, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;text-align:right;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;text-align:right;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin:1rem}.DataForm[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto!important}\"]\n    });\n  }\n  return AddScientifiProblemToQuestionBankComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}