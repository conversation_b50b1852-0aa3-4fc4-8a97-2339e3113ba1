{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { numericToNumber } from '../../util/number.js';\nimport { eqNaN } from 'zrender/lib/core/util.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar DEFAULT_SMOOTH = 0.3;\nvar ParallelView = /** @class */function (_super) {\n  __extends(ParallelView, _super);\n  function ParallelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelView.type;\n    _this._dataGroup = new graphic.Group();\n    _this._initialized = false;\n    return _this;\n  }\n  ParallelView.prototype.init = function () {\n    this.group.add(this._dataGroup);\n  };\n  /**\r\n   * @override\r\n   */\n  ParallelView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var dataGroup = this._dataGroup;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    data.diff(oldData).add(add).update(update).remove(remove).execute();\n    function add(newDataIndex) {\n      var line = addEl(data, dataGroup, newDataIndex, dimensions, coordSys);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function update(newDataIndex, oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      var points = createLinePoints(data, newDataIndex, dimensions, coordSys);\n      data.setItemGraphicEl(newDataIndex, line);\n      graphic.updateProps(line, {\n        shape: {\n          points: points\n        }\n      }, seriesModel, newDataIndex);\n      saveOldStyle(line);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function remove(oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      dataGroup.remove(line);\n    }\n    // First create\n    if (!this._initialized) {\n      this._initialized = true;\n      var clipPath = createGridClipShape(coordSys, seriesModel, function () {\n        // Callback will be invoked immediately if there is no animation\n        setTimeout(function () {\n          dataGroup.removeClipPath();\n        });\n      });\n      dataGroup.setClipPath(clipPath);\n    }\n    this._data = data;\n  };\n  ParallelView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this._initialized = true;\n    this._data = null;\n    this._dataGroup.removeAll();\n  };\n  ParallelView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    var progressiveEls = this._progressiveEls = [];\n    for (var dataIndex = taskParams.start; dataIndex < taskParams.end; dataIndex++) {\n      var line = addEl(data, this._dataGroup, dataIndex, dimensions, coordSys);\n      line.incremental = true;\n      updateElCommon(line, data, dataIndex, seriesScope);\n      progressiveEls.push(line);\n    }\n  };\n  ParallelView.prototype.remove = function () {\n    this._dataGroup && this._dataGroup.removeAll();\n    this._data = null;\n  };\n  ParallelView.type = 'parallel';\n  return ParallelView;\n}(ChartView);\nfunction createGridClipShape(coordSys, seriesModel, cb) {\n  var parallelModel = coordSys.model;\n  var rect = coordSys.getRect();\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    }\n  });\n  var dim = parallelModel.get('layout') === 'horizontal' ? 'width' : 'height';\n  rectEl.setShape(dim, 0);\n  graphic.initProps(rectEl, {\n    shape: {\n      width: rect.width,\n      height: rect.height\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nfunction createLinePoints(data, dataIndex, dimensions, coordSys) {\n  var points = [];\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimName = dimensions[i];\n    var value = data.get(data.mapDimension(dimName), dataIndex);\n    if (!isEmptyValue(value, coordSys.getAxis(dimName).type)) {\n      points.push(coordSys.dataToPoint(value, dimName));\n    }\n  }\n  return points;\n}\nfunction addEl(data, dataGroup, dataIndex, dimensions, coordSys) {\n  var points = createLinePoints(data, dataIndex, dimensions, coordSys);\n  var line = new graphic.Polyline({\n    shape: {\n      points: points\n    },\n    // silent: true,\n    z2: 10\n  });\n  dataGroup.add(line);\n  data.setItemGraphicEl(dataIndex, line);\n  return line;\n}\nfunction makeSeriesScope(seriesModel) {\n  var smooth = seriesModel.get('smooth', true);\n  smooth === true && (smooth = DEFAULT_SMOOTH);\n  smooth = numericToNumber(smooth);\n  eqNaN(smooth) && (smooth = 0);\n  return {\n    smooth: smooth\n  };\n}\nfunction updateElCommon(el, data, dataIndex, seriesScope) {\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.fill = null;\n  el.setShape('smooth', seriesScope.smooth);\n  var itemModel = data.getItemModel(dataIndex);\n  var emphasisModel = itemModel.getModel('emphasis');\n  setStatesStylesFromModel(el, itemModel, 'lineStyle');\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\n// function simpleDiff(oldData, newData, dimensions) {\n//     let oldLen;\n//     if (!oldData\n//         || !oldData.__plProgressive\n//         || (oldLen = oldData.count()) !== newData.count()\n//     ) {\n//         return true;\n//     }\n//     let dimLen = dimensions.length;\n//     for (let i = 0; i < oldLen; i++) {\n//         for (let j = 0; j < dimLen; j++) {\n//             if (oldData.get(dimensions[j], i) !== newData.get(dimensions[j], i)) {\n//                 return true;\n//             }\n//         }\n//     }\n//     return false;\n// }\n// FIXME put in common util?\nfunction isEmptyValue(val, axisType) {\n  return axisType === 'category' ? val == null : val == null || isNaN(val); // axisType === 'value'\n}\nexport default ParallelView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}