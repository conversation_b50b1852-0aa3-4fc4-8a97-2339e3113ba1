{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { addSafe } from '../util/number.js';\n// (1) [Caution]: the logic is correct based on the premises:\n//     data processing stage is blocked in stream.\n//     See <module:echarts/stream/Scheduler#performDataProcessorTasks>\n// (2) Only register once when import repeatedly.\n//     Should be executed after series is filtered and before stack calculation.\nexport default function dataStack(ecModel) {\n  var stackInfoMap = createHashMap();\n  ecModel.eachSeries(function (seriesModel) {\n    var stack = seriesModel.get('stack');\n    // Compatible: when `stack` is set as '', do not stack.\n    if (stack) {\n      var stackInfoList = stackInfoMap.get(stack) || stackInfoMap.set(stack, []);\n      var data = seriesModel.getData();\n      var stackInfo = {\n        // Used for calculate axis extent automatically.\n        // TODO: Type getCalculationInfo return more specific type?\n        stackResultDimension: data.getCalculationInfo('stackResultDimension'),\n        stackedOverDimension: data.getCalculationInfo('stackedOverDimension'),\n        stackedDimension: data.getCalculationInfo('stackedDimension'),\n        stackedByDimension: data.getCalculationInfo('stackedByDimension'),\n        isStackedByIndex: data.getCalculationInfo('isStackedByIndex'),\n        data: data,\n        seriesModel: seriesModel\n      };\n      // If stacked on axis that do not support data stack.\n      if (!stackInfo.stackedDimension || !(stackInfo.isStackedByIndex || stackInfo.stackedByDimension)) {\n        return;\n      }\n      stackInfoList.length && data.setCalculationInfo('stackedOnSeries', stackInfoList[stackInfoList.length - 1].seriesModel);\n      stackInfoList.push(stackInfo);\n    }\n  });\n  stackInfoMap.each(calculateStack);\n}\nfunction calculateStack(stackInfoList) {\n  each(stackInfoList, function (targetStackInfo, idxInStack) {\n    var resultVal = [];\n    var resultNaN = [NaN, NaN];\n    var dims = [targetStackInfo.stackResultDimension, targetStackInfo.stackedOverDimension];\n    var targetData = targetStackInfo.data;\n    var isStackedByIndex = targetStackInfo.isStackedByIndex;\n    var stackStrategy = targetStackInfo.seriesModel.get('stackStrategy') || 'samesign';\n    // Should not write on raw data, because stack series model list changes\n    // depending on legend selection.\n    targetData.modify(dims, function (v0, v1, dataIndex) {\n      var sum = targetData.get(targetStackInfo.stackedDimension, dataIndex);\n      // Consider `connectNulls` of line area, if value is NaN, stackedOver\n      // should also be NaN, to draw a appropriate belt area.\n      if (isNaN(sum)) {\n        return resultNaN;\n      }\n      var byValue;\n      var stackedDataRawIndex;\n      if (isStackedByIndex) {\n        stackedDataRawIndex = targetData.getRawIndex(dataIndex);\n      } else {\n        byValue = targetData.get(targetStackInfo.stackedByDimension, dataIndex);\n      }\n      // If stackOver is NaN, chart view will render point on value start.\n      var stackedOver = NaN;\n      for (var j = idxInStack - 1; j >= 0; j--) {\n        var stackInfo = stackInfoList[j];\n        // Has been optimized by inverted indices on `stackedByDimension`.\n        if (!isStackedByIndex) {\n          stackedDataRawIndex = stackInfo.data.rawIndexOf(stackInfo.stackedByDimension, byValue);\n        }\n        if (stackedDataRawIndex >= 0) {\n          var val = stackInfo.data.getByRawIndex(stackInfo.stackResultDimension, stackedDataRawIndex);\n          // Considering positive stack, negative stack and empty data\n          if (stackStrategy === 'all' // single stack group\n          || stackStrategy === 'positive' && val > 0 || stackStrategy === 'negative' && val < 0 || stackStrategy === 'samesign' && sum >= 0 && val > 0 // All positive stack\n          || stackStrategy === 'samesign' && sum <= 0 && val < 0 // All negative stack\n          ) {\n            // The sum has to be very small to be affected by the\n            // floating arithmetic problem. An incorrect result will probably\n            // cause axis min/max to be filtered incorrectly.\n            sum = addSafe(sum, val);\n            stackedOver = val;\n            break;\n          }\n        }\n      }\n      resultVal[0] = sum;\n      resultVal[1] = stackedOver;\n      return resultVal;\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}