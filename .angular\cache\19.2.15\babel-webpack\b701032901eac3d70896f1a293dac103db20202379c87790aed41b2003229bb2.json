{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// FIXME emphasis label position is not same with normal label position\nimport { parsePercent } from '../../util/number.js';\nimport { Point } from '../../util/graphic.js';\nimport { each, isNumber } from 'zrender/lib/core/util.js';\nimport { limitTurnAngle, limitSurfaceAngle } from '../../label/labelGuideHelper.js';\nimport { shiftLayoutOnY } from '../../label/labelLayoutHelper.js';\nvar RADIAN = Math.PI / 180;\nfunction adjustSingleSide(list, cx, cy, r, dir, viewWidth, viewHeight, viewLeft, viewTop, farthestX) {\n  if (list.length < 2) {\n    return;\n  }\n  ;\n  function recalculateXOnSemiToAlignOnEllipseCurve(semi) {\n    var rB = semi.rB;\n    var rB2 = rB * rB;\n    for (var i = 0; i < semi.list.length; i++) {\n      var item = semi.list[i];\n      var dy = Math.abs(item.label.y - cy);\n      // horizontal r is always same with original r because x is not changed.\n      var rA = r + item.len;\n      var rA2 = rA * rA;\n      // Use ellipse implicit function to calculate x\n      var dx = Math.sqrt(Math.abs((1 - dy * dy / rB2) * rA2));\n      var newX = cx + (dx + item.len2) * dir;\n      var deltaX = newX - item.label.x;\n      var newTargetWidth = item.targetTextWidth - deltaX * dir;\n      // text x is changed, so need to recalculate width.\n      constrainTextWidth(item, newTargetWidth, true);\n      item.label.x = newX;\n    }\n  }\n  // Adjust X based on the shifted y. Make tight labels aligned on an ellipse curve.\n  function recalculateX(items) {\n    // Extremes of\n    var topSemi = {\n      list: [],\n      maxY: 0\n    };\n    var bottomSemi = {\n      list: [],\n      maxY: 0\n    };\n    for (var i = 0; i < items.length; i++) {\n      if (items[i].labelAlignTo !== 'none') {\n        continue;\n      }\n      var item = items[i];\n      var semi = item.label.y > cy ? bottomSemi : topSemi;\n      var dy = Math.abs(item.label.y - cy);\n      if (dy >= semi.maxY) {\n        var dx = item.label.x - cx - item.len2 * dir;\n        // horizontal r is always same with original r because x is not changed.\n        var rA = r + item.len;\n        // Canculate rB based on the topest / bottemest label.\n        var rB = Math.abs(dx) < rA ? Math.sqrt(dy * dy / (1 - dx * dx / rA / rA)) : rA;\n        semi.rB = rB;\n        semi.maxY = dy;\n      }\n      semi.list.push(item);\n    }\n    recalculateXOnSemiToAlignOnEllipseCurve(topSemi);\n    recalculateXOnSemiToAlignOnEllipseCurve(bottomSemi);\n  }\n  var len = list.length;\n  for (var i = 0; i < len; i++) {\n    if (list[i].position === 'outer' && list[i].labelAlignTo === 'labelLine') {\n      var dx = list[i].label.x - farthestX;\n      list[i].linePoints[1][0] += dx;\n      list[i].label.x = farthestX;\n    }\n  }\n  if (shiftLayoutOnY(list, viewTop, viewTop + viewHeight)) {\n    recalculateX(list);\n  }\n}\nfunction avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop) {\n  var leftList = [];\n  var rightList = [];\n  var leftmostX = Number.MAX_VALUE;\n  var rightmostX = -Number.MAX_VALUE;\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var label = labelLayoutList[i].label;\n    if (isPositionCenter(labelLayoutList[i])) {\n      continue;\n    }\n    if (label.x < cx) {\n      leftmostX = Math.min(leftmostX, label.x);\n      leftList.push(labelLayoutList[i]);\n    } else {\n      rightmostX = Math.max(rightmostX, label.x);\n      rightList.push(labelLayoutList[i]);\n    }\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      if (layout.labelStyleWidth != null) {\n        continue;\n      }\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var targetTextWidth = void 0;\n      if (layout.labelAlignTo === 'edge') {\n        if (label.x < cx) {\n          targetTextWidth = linePoints[2][0] - layout.labelDistance - viewLeft - layout.edgeDistance;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - layout.edgeDistance - linePoints[2][0] - layout.labelDistance;\n        }\n      } else if (layout.labelAlignTo === 'labelLine') {\n        if (label.x < cx) {\n          targetTextWidth = leftmostX - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - rightmostX - layout.bleedMargin;\n        }\n      } else {\n        if (label.x < cx) {\n          targetTextWidth = label.x - viewLeft - layout.bleedMargin;\n        } else {\n          targetTextWidth = viewLeft + viewWidth - label.x - layout.bleedMargin;\n        }\n      }\n      layout.targetTextWidth = targetTextWidth;\n      constrainTextWidth(layout, targetTextWidth);\n    }\n  }\n  adjustSingleSide(rightList, cx, cy, r, 1, viewWidth, viewHeight, viewLeft, viewTop, rightmostX);\n  adjustSingleSide(leftList, cx, cy, r, -1, viewWidth, viewHeight, viewLeft, viewTop, leftmostX);\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    if (!isPositionCenter(layout) && layout.linePoints) {\n      var label = layout.label;\n      var linePoints = layout.linePoints;\n      var isAlignToEdge = layout.labelAlignTo === 'edge';\n      var padding = label.style.padding;\n      var paddingH = padding ? padding[1] + padding[3] : 0;\n      // textRect.width already contains paddingH if bgColor is set\n      var extraPaddingH = label.style.backgroundColor ? 0 : paddingH;\n      var realTextWidth = layout.rect.width + extraPaddingH;\n      var dist = linePoints[1][0] - linePoints[2][0];\n      if (isAlignToEdge) {\n        if (label.x < cx) {\n          linePoints[2][0] = viewLeft + layout.edgeDistance + realTextWidth + layout.labelDistance;\n        } else {\n          linePoints[2][0] = viewLeft + viewWidth - layout.edgeDistance - realTextWidth - layout.labelDistance;\n        }\n      } else {\n        if (label.x < cx) {\n          linePoints[2][0] = label.x + layout.labelDistance;\n        } else {\n          linePoints[2][0] = label.x - layout.labelDistance;\n        }\n        linePoints[1][0] = linePoints[2][0] + dist;\n      }\n      linePoints[1][1] = linePoints[2][1] = label.y;\n    }\n  }\n}\n/**\r\n * Set max width of each label, and then wrap each label to the max width.\r\n *\r\n * @param layout label layout\r\n * @param availableWidth max width for the label to display\r\n * @param forceRecalculate recaculate the text layout even if the current width\r\n * is smaller than `availableWidth`. This is useful when the text was previously\r\n * wrapped by calling `constrainTextWidth` but now `availableWidth` changed, in\r\n * which case, previous wrapping should be redo.\r\n */\nfunction constrainTextWidth(layout, availableWidth, forceRecalculate) {\n  if (forceRecalculate === void 0) {\n    forceRecalculate = false;\n  }\n  if (layout.labelStyleWidth != null) {\n    // User-defined style.width has the highest priority.\n    return;\n  }\n  var label = layout.label;\n  var style = label.style;\n  var textRect = layout.rect;\n  var bgColor = style.backgroundColor;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var overflow = style.overflow;\n  // textRect.width already contains paddingH if bgColor is set\n  var oldOuterWidth = textRect.width + (bgColor ? 0 : paddingH);\n  if (availableWidth < oldOuterWidth || forceRecalculate) {\n    var oldHeight = textRect.height;\n    if (overflow && overflow.match('break')) {\n      // Temporarily set background to be null to calculate\n      // the bounding box without background.\n      label.setStyle('backgroundColor', null);\n      // Set constraining width\n      label.setStyle('width', availableWidth - paddingH);\n      // This is the real bounding box of the text without padding.\n      var innerRect = label.getBoundingRect();\n      label.setStyle('width', Math.ceil(innerRect.width));\n      label.setStyle('backgroundColor', bgColor);\n    } else {\n      var availableInnerWidth = availableWidth - paddingH;\n      var newWidth = availableWidth < oldOuterWidth\n      // Current text is too wide, use `availableWidth` as max width.\n      ? availableInnerWidth :\n      // Current available width is enough, but the text may have\n      // already been wrapped with a smaller available width.\n      forceRecalculate ? availableInnerWidth > layout.unconstrainedWidth\n      // Current available is larger than text width,\n      // so don't constrain width (otherwise it may have\n      // empty space in the background).\n      ? null\n      // Current available is smaller than text width, so\n      // use the current available width as constraining\n      // width.\n      : availableInnerWidth\n      // Current available width is enough, so no need to\n      // constrain.\n      : null;\n      label.setStyle('width', newWidth);\n    }\n    var newRect = label.getBoundingRect();\n    textRect.width = newRect.width;\n    var margin = (label.style.margin || 0) + 2.1;\n    textRect.height = newRect.height + margin;\n    textRect.y -= (textRect.height - oldHeight) / 2;\n  }\n}\nfunction isPositionCenter(sectorShape) {\n  // Not change x for center label\n  return sectorShape.position === 'center';\n}\nexport default function pieLabelLayout(seriesModel) {\n  var data = seriesModel.getData();\n  var labelLayoutList = [];\n  var cx;\n  var cy;\n  var hasLabelRotate = false;\n  var minShowLabelRadian = (seriesModel.get('minShowLabelAngle') || 0) * RADIAN;\n  var viewRect = data.getLayout('viewRect');\n  var r = data.getLayout('r');\n  var viewWidth = viewRect.width;\n  var viewLeft = viewRect.x;\n  var viewTop = viewRect.y;\n  var viewHeight = viewRect.height;\n  function setNotShow(el) {\n    el.ignore = true;\n  }\n  function isLabelShown(label) {\n    if (!label.ignore) {\n      return true;\n    }\n    for (var key in label.states) {\n      if (label.states[key].ignore === false) {\n        return true;\n      }\n    }\n    return false;\n  }\n  data.each(function (idx) {\n    var sector = data.getItemGraphicEl(idx);\n    var sectorShape = sector.shape;\n    var label = sector.getTextContent();\n    var labelLine = sector.getTextGuideLine();\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    // Use position in normal or emphasis\n    var labelPosition = labelModel.get('position') || itemModel.get(['emphasis', 'label', 'position']);\n    var labelDistance = labelModel.get('distanceToLabelLine');\n    var labelAlignTo = labelModel.get('alignTo');\n    var edgeDistance = parsePercent(labelModel.get('edgeDistance'), viewWidth);\n    var bleedMargin = labelModel.get('bleedMargin');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var labelLineLen = labelLineModel.get('length');\n    labelLineLen = parsePercent(labelLineLen, viewWidth);\n    var labelLineLen2 = labelLineModel.get('length2');\n    labelLineLen2 = parsePercent(labelLineLen2, viewWidth);\n    if (Math.abs(sectorShape.endAngle - sectorShape.startAngle) < minShowLabelRadian) {\n      each(label.states, setNotShow);\n      label.ignore = true;\n      if (labelLine) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      }\n      return;\n    }\n    if (!isLabelShown(label)) {\n      return;\n    }\n    var midAngle = (sectorShape.startAngle + sectorShape.endAngle) / 2;\n    var nx = Math.cos(midAngle);\n    var ny = Math.sin(midAngle);\n    var textX;\n    var textY;\n    var linePoints;\n    var textAlign;\n    cx = sectorShape.cx;\n    cy = sectorShape.cy;\n    var isLabelInside = labelPosition === 'inside' || labelPosition === 'inner';\n    if (labelPosition === 'center') {\n      textX = sectorShape.cx;\n      textY = sectorShape.cy;\n      textAlign = 'center';\n    } else {\n      var x1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * nx : sectorShape.r * nx) + cx;\n      var y1 = (isLabelInside ? (sectorShape.r + sectorShape.r0) / 2 * ny : sectorShape.r * ny) + cy;\n      textX = x1 + nx * 3;\n      textY = y1 + ny * 3;\n      if (!isLabelInside) {\n        // For roseType\n        var x2 = x1 + nx * (labelLineLen + r - sectorShape.r);\n        var y2 = y1 + ny * (labelLineLen + r - sectorShape.r);\n        var x3 = x2 + (nx < 0 ? -1 : 1) * labelLineLen2;\n        var y3 = y2;\n        if (labelAlignTo === 'edge') {\n          // Adjust textX because text align of edge is opposite\n          textX = nx < 0 ? viewLeft + edgeDistance : viewLeft + viewWidth - edgeDistance;\n        } else {\n          textX = x3 + (nx < 0 ? -labelDistance : labelDistance);\n        }\n        textY = y3;\n        linePoints = [[x1, y1], [x2, y2], [x3, y3]];\n      }\n      textAlign = isLabelInside ? 'center' : labelAlignTo === 'edge' ? nx > 0 ? 'right' : 'left' : nx > 0 ? 'left' : 'right';\n    }\n    var PI = Math.PI;\n    var labelRotate = 0;\n    var rotate = labelModel.get('rotate');\n    if (isNumber(rotate)) {\n      labelRotate = rotate * (PI / 180);\n    } else if (labelPosition === 'center') {\n      labelRotate = 0;\n    } else if (rotate === 'radial' || rotate === true) {\n      var radialAngle = nx < 0 ? -midAngle + PI : -midAngle;\n      labelRotate = radialAngle;\n    } else if (rotate === 'tangential' && labelPosition !== 'outside' && labelPosition !== 'outer') {\n      var rad = Math.atan2(nx, ny);\n      if (rad < 0) {\n        rad = PI * 2 + rad;\n      }\n      var isDown = ny > 0;\n      if (isDown) {\n        rad = PI + rad;\n      }\n      labelRotate = rad - PI;\n    }\n    hasLabelRotate = !!labelRotate;\n    label.x = textX;\n    label.y = textY;\n    label.rotation = labelRotate;\n    label.setStyle({\n      verticalAlign: 'middle'\n    });\n    // Not sectorShape the inside label\n    if (!isLabelInside) {\n      var textRect = label.getBoundingRect().clone();\n      textRect.applyTransform(label.getComputedTransform());\n      // Text has a default 1px stroke. Exclude this.\n      var margin = (label.style.margin || 0) + 2.1;\n      textRect.y -= margin / 2;\n      textRect.height += margin;\n      labelLayoutList.push({\n        label: label,\n        labelLine: labelLine,\n        position: labelPosition,\n        len: labelLineLen,\n        len2: labelLineLen2,\n        minTurnAngle: labelLineModel.get('minTurnAngle'),\n        maxSurfaceAngle: labelLineModel.get('maxSurfaceAngle'),\n        surfaceNormal: new Point(nx, ny),\n        linePoints: linePoints,\n        textAlign: textAlign,\n        labelDistance: labelDistance,\n        labelAlignTo: labelAlignTo,\n        edgeDistance: edgeDistance,\n        bleedMargin: bleedMargin,\n        rect: textRect,\n        unconstrainedWidth: textRect.width,\n        labelStyleWidth: label.style.width\n      });\n    } else {\n      label.setStyle({\n        align: textAlign\n      });\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    sector.setTextConfig({\n      inside: isLabelInside\n    });\n  });\n  if (!hasLabelRotate && seriesModel.get('avoidLabelOverlap')) {\n    avoidOverlap(labelLayoutList, cx, cy, r, viewWidth, viewHeight, viewLeft, viewTop);\n  }\n  for (var i = 0; i < labelLayoutList.length; i++) {\n    var layout = labelLayoutList[i];\n    var label = layout.label;\n    var labelLine = layout.labelLine;\n    var notShowLabel = isNaN(label.x) || isNaN(label.y);\n    if (label) {\n      label.setStyle({\n        align: layout.textAlign\n      });\n      if (notShowLabel) {\n        each(label.states, setNotShow);\n        label.ignore = true;\n      }\n      var selectState = label.states.select;\n      if (selectState) {\n        selectState.x += label.x;\n        selectState.y += label.y;\n      }\n    }\n    if (labelLine) {\n      var linePoints = layout.linePoints;\n      if (notShowLabel || !linePoints) {\n        each(labelLine.states, setNotShow);\n        labelLine.ignore = true;\n      } else {\n        limitTurnAngle(linePoints, layout.minTurnAngle);\n        limitSurfaceAngle(linePoints, layout.surfaceNormal, layout.maxSurfaceAngle);\n        labelLine.setShape({\n          points: linePoints\n        });\n        // Set the anchor to the midpoint of sector\n        label.__hostTarget.textGuideLineConfig = {\n          anchor: new Point(linePoints[0][0], linePoints[0][1])\n        };\n      }\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}