{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction UserGroupViewComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function UserGroupViewComponent_ng_container_6_Template_div_click_1_listener() {\n      const groupOfItem_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getGroupDetails(groupOfItem_r2));\n    });\n    i0.ɵɵelementStart(2, \"div\", 6)(3, \"p\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 6)(8, \"p\", 9);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const groupOfItem_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"title\", \" \", groupOfItem_r2.group_name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r2.group_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r2.last_date, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"\", groupOfItem_r2.last_message, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", groupOfItem_r2.last_message, \" \");\n  }\n}\nexport let UserGroupViewComponent = /*#__PURE__*/(() => {\n  class UserGroupViewComponent {\n    chatService;\n    groupDetailsEvent = new EventEmitter();\n    currentUser;\n    listOfGroups = [];\n    selectedIndex = 0;\n    constructor(chatService) {\n      this.chatService = chatService;\n    }\n    ngOnInit() {\n      this.chatService.allGroupsList = [];\n      this.listOfGroups = [];\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.chatService.getAllGroupsByParticipantId(this.currentUser.id || '');\n      this.listOfGroups = this.chatService.allGroupsList;\n    }\n    getGroupDetails(event) {\n      this.groupDetailsEvent.emit(event);\n    }\n    filterByText(searchKey) {\n      if (searchKey.length > 0) {\n        this.listOfGroups = this.listOfGroups.filter(x => x.group_name?.includes(searchKey));\n      } else {\n        this.listOfGroups = this.chatService.allGroupsList;\n      }\n    }\n    static ɵfac = function UserGroupViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserGroupViewComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserGroupViewComponent,\n      selectors: [[\"app-user-group-view\"]],\n      outputs: {\n        groupDetailsEvent: \"groupDetailsEvent\"\n      },\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"list-group\", \"teacher-list-request\"], [1, \"mb-0\", \"head_groupName\"], [3, \"searchTerm\"], [1, \"internal_scroll_list_group\"], [4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"ellipsis\", \"p-0\", \"group_name\", 3, \"title\"], [1, \"last_date\"], [1, \"ellipsis\", \"p-0\", \"last_message\", 3, \"title\"]],\n      template: function UserGroupViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"p\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"app-search-input\", 2);\n          i0.ɵɵlistener(\"searchTerm\", function UserGroupViewComponent_Template_app_search_input_searchTerm_4_listener($event) {\n            return ctx.filterByText($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, UserGroupViewComponent_ng_container_6_Template, 10, 7, \"ng-container\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"CHAT_GROUP.GROUPS\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.chatService.allGroupsList);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.TranslatePipe],\n      styles: [\".teacher-list-request[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:75vh;margin-top:1rem}.teacher-list-request[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]{color:#333;font-size:1.23rem;font-weight:700;padding-bottom:1rem}.teacher-list-request[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]{height:65vh;overflow-y:auto;height:63vh;overflow-x:hidden;padding:.3rem}.teacher-list-request[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.teacher-list-request[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.teacher-list-request__addGroup[_ngcontent-%COMP%]{color:var(--second_color);font-size:.85rem;font-weight:700;justify-content:flex-end;display:flex;align-items:center}.teacher-list-request[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;color:gray;border-radius:.75rem;letter-spacing:0;margin-bottom:1rem;opacity:1;margin-top:1rem;font-weight:700;font-size:1rem;justify-content:space-between;padding:.5rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.937rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .last_date[_ngcontent-%COMP%]{color:#333;font-size:.563rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .last_message[_ngcontent-%COMP%]{color:#333;font-size:.625rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .trash_icon[_ngcontent-%COMP%]{color:var(--main_color);margin-right:.1rem;margin-left:.2rem}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .trash_icon[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .last_message[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .last_date[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .trash_icon[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .last_message[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .last_date[_ngcontent-%COMP%], .teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .group_name[_ngcontent-%COMP%]{color:#fff}.teacher-list-request[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:80%}@media screen and (max-width: 64rem){.teacher-list-request[_ngcontent-%COMP%]{padding:.8rem}}\"]\n    });\n  }\n  return UserGroupViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}