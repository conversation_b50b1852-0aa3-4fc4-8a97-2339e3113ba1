{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseSelectedDateModel } from '../../../../../core/ng-model/base-selected-date-model';\nimport { BaseConstantModel } from '../../../../../core/ng-model/base-constant-model';\nimport { LanguageEnum } from '../../../../../core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../core/services/program-services/program.service\";\nimport * as i2 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"../../../../../core/services/lookup-services/lookup.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/checkbox\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction ScientificProblemAdvancedSearchComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r1.progName, \" \");\n  }\n}\nfunction ScientificProblemAdvancedSearchComponent_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"value\", item_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.translate.currentLang === ctx_r2.langEnum.en ? item_r2.nameEn : item_r2.nameAr, \" \");\n  }\n}\nfunction ScientificProblemAdvancedSearchComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r2.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.resultMessage.message, \"\");\n  }\n}\nexport let ScientificProblemAdvancedSearchComponent = /*#__PURE__*/(() => {\n  class ScientificProblemAdvancedSearchComponent {\n    programService;\n    dateFormatterService;\n    translate;\n    lookupService;\n    closeAdvancedSearch = new EventEmitter();\n    filter = {\n      skip: 0,\n      take: 12,\n      sorField: '',\n      ordType: 1,\n      page: 1\n    };\n    resultMessage = {};\n    calenderType = new BaseSelectedDateModel();\n    selectedDateType;\n    maxGregDate = this.dateFormatterService.GetTodayGregorian();\n    typeDateBinding;\n    datafromBinding;\n    dataToBinding;\n    hijri = false;\n    milady = false;\n    collectionOfLookup = {};\n    listOfLookup = ['TASKS'];\n    filterFromDate;\n    filterToDate;\n    constructor(programService, dateFormatterService, translate, lookupService) {\n      this.programService = programService;\n      this.dateFormatterService = dateFormatterService;\n      this.translate = translate;\n      this.lookupService = lookupService;\n    }\n    programsbyAdvancedFilter = {\n      skip: 0,\n      take: 2147483647\n    };\n    ProgramsList = [];\n    langEnum = LanguageEnum;\n    ngOnInit() {\n      this.getAllProgram();\n      this.getLookupByKey();\n      if (this.filter.scFrom) {\n        let date = new Date(this.filter.scFrom || '');\n        this.filterFromDate = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n      }\n      if (this.filter.scTo) {\n        let date = new Date(this.filter.scTo || '');\n        this.filterToDate = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n      }\n    }\n    getLookupByKey() {\n      this.lookupService.getLookupByKey(this.listOfLookup).subscribe(res => {\n        if (res.isSuccess) {\n          this.collectionOfLookup = res.data;\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    SendDatafrom(data) {\n      this.typeDateBinding = data.selectedDateType;\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.datafromBinding = data.selectedDateValue;\n      this.filter.scFrom = this.datafromBinding;\n      this.selectedDateType = data.selectedDateType;\n    }\n    SendDataTo(data) {\n      this.typeDateBinding = data.selectedDateType;\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.dataToBinding = data.selectedDateValue;\n      this.filter.scTo = this.dataToBinding;\n      this.selectedDateType = data.selectedDateType;\n    }\n    closeScientificAdvancedSearch() {\n      this.filter.filterText = '';\n      this.filter.progId = '';\n      this.filter.scFrom = undefined;\n      this.filter.scTo = undefined;\n      this.filter.skip = 0;\n      this.filter.take = 9;\n      this.filter.page = 1;\n      this.filter.scNo = undefined;\n      this.closeAdvancedSearch.emit(this.filter);\n    }\n    sendAdvancedSearch() {\n      if (this.datafromBinding > this.dataToBinding) {\n        this.resultMessage = {\n          message: this.translate.instant('STUDENT_SUBSCRIBERS.VALIDATIONDATE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else this.closeAdvancedSearch.emit();\n    }\n    getAllProgram() {\n      this.programService.getProgramAdvancedFilter(this.programsbyAdvancedFilter || {}).subscribe(res => {\n        if (res.isSuccess) {\n          this.ProgramsList = res.data;\n        } else {}\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function ScientificProblemAdvancedSearchComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScientificProblemAdvancedSearchComponent)(i0.ɵɵdirectiveInject(i1.ProgramService), i0.ɵɵdirectiveInject(i2.DateFormatterService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LookupService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ScientificProblemAdvancedSearchComponent,\n      selectors: [[\"app-scientific-problem-advanced-search\"]],\n      inputs: {\n        filter: \"filter\"\n      },\n      outputs: {\n        closeAdvancedSearch: \"closeAdvancedSearch\"\n      },\n      decls: 66,\n      vars: 67,\n      consts: [[1, \"form-group\", \"DataForm\", \"pl-3\", \"pr-3\"], [1, \"head\", \"bold\"], [\"for\", \"matrialTitleAr\", 1, \"label\"], [1, \"example-margin\", \"mx-3\"], [1, \"form-group\"], [\"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [\"for\", \"category\", 1, \"label\"], [1, \"d-flex\"], [1, \"input-group\"], [1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"band\", 1, \"label\"], [1, \"label\"], [\"type\", \"number\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [1, \"form-group\", \"subscribtion-date\"], [1, \"col-12\", \"p-0\"], [3, \"sendDate\", \"keypress\", \"editcalenderType\", \"hijri\", \"milady\", \"maxGreg\", \"dateTo\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-2\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-2\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [3, \"value\"], [1, \"bold\"]],\n      template: function ScientificProblemAdvancedSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerStart(4);\n          i0.ɵɵelementStart(5, \"label\", 2);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-checkbox\", 3);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-checkbox\", 3);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(14, \"div\", 4)(15, \"label\", 2);\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ScientificProblemAdvancedSearchComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filter.filterText, $event) || (ctx.filter.filterText = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 4)(20, \"label\", 6);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 7)(24, \"div\", 8)(25, \"select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ScientificProblemAdvancedSearchComponent_Template_select_ngModelChange_25_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filter.progId, $event) || (ctx.filter.progId = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(26, \"option\", 10);\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, ScientificProblemAdvancedSearchComponent_option_29_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"div\", 4)(31, \"label\", 12);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 7)(35, \"div\", 8)(36, \"select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ScientificProblemAdvancedSearchComponent_Template_select_ngModelChange_36_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filter.progTask, $event) || (ctx.filter.progTask = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(37, \"option\", 10);\n          i0.ɵɵtext(38);\n          i0.ɵɵpipe(39, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, ScientificProblemAdvancedSearchComponent_option_40_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 4)(42, \"label\", 13);\n          i0.ɵɵtext(43);\n          i0.ɵɵpipe(44, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ScientificProblemAdvancedSearchComponent_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.filter.scNo, $event) || (ctx.filter.scNo = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 15)(47, \"div\", 16)(48, \"label\", 13);\n          i0.ɵɵtext(49);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"app-milady-hijri-calendar\", 17);\n          i0.ɵɵlistener(\"sendDate\", function ScientificProblemAdvancedSearchComponent_Template_app_milady_hijri_calendar_sendDate_51_listener($event) {\n            return ctx.SendDatafrom($event);\n          })(\"keypress\", function ScientificProblemAdvancedSearchComponent_Template_app_milady_hijri_calendar_keypress_51_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 16)(53, \"label\", 13);\n          i0.ɵɵtext(54);\n          i0.ɵɵpipe(55, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"app-milady-hijri-calendar\", 17);\n          i0.ɵɵlistener(\"sendDate\", function ScientificProblemAdvancedSearchComponent_Template_app_milady_hijri_calendar_sendDate_56_listener($event) {\n            return ctx.SendDataTo($event);\n          })(\"keypress\", function ScientificProblemAdvancedSearchComponent_Template_app_milady_hijri_calendar_keypress_56_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(57, ScientificProblemAdvancedSearchComponent_div_57_Template, 3, 4, \"div\", 18);\n          i0.ɵɵelementStart(58, \"section\", 19)(59, \"div\", 20)(60, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ScientificProblemAdvancedSearchComponent_Template_button_click_60_listener() {\n            return ctx.sendAdvancedSearch();\n          });\n          i0.ɵɵtext(61);\n          i0.ɵɵpipe(62, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ScientificProblemAdvancedSearchComponent_Template_button_click_63_listener() {\n            return ctx.closeScientificAdvancedSearch();\n          });\n          i0.ɵɵtext(64);\n          i0.ɵɵpipe(65, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 35, \"SCIENTIFIC_PROBLEM.ADVANCED_SEARCH\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 37, \"SCIENTIFIC_PROBLEM.QUESTION_TYPE\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 39, \"SCIENTIFIC_PROBLEM.RELATED_TO_PROGRAM\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 41, \"SCIENTIFIC_PROBLEM.GENERAL\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 43, \"SCIENTIFIC_PROBLEM.QUESTION_OWNER_NAME\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filter.filterText);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(63, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 45, \"SCIENTIFIC_PROBLEM.PROGRAM_NAME_SEARCH\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filter.progId);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(64, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 47, \"SCIENTIFIC_MATERIAL.SELECTOPTION\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ProgramsList);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 49, \"SCIENTIFIC_PROBLEM.BAND_NAME\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filter.progTask);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(65, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(39, 51, \"SCIENTIFIC_MATERIAL.SELECTOPTION\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.collectionOfLookup.TASKS);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(44, 53, \"SCIENTIFIC_PROBLEM.QUESTION_NUMBER\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filter.scNo);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(66, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(50, 55, \"SCIENTIFIC_PROBLEM.FROM\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"hijri\", !ctx.hijri)(\"milady\", ctx.milady)(\"maxGreg\", ctx.maxGregDate)(\"dateTo\", ctx.filterFromDate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(55, 57, \"SCIENTIFIC_PROBLEM.TO\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"hijri\", !ctx.hijri)(\"milady\", ctx.milady)(\"maxGreg\", ctx.maxGregDate)(\"dateTo\", ctx.filterToDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(62, 59, \"SCIENTIFIC_PROBLEM.SEARCH\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(65, 61, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.NumberValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, i7.MatCheckbox, i3.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right!important}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left!important}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(ar), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(en), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{margin:1rem}.DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-file[_ngcontent-%COMP%]:not(:last-child)   .custom-file-label[_ngcontent-%COMP%]:after, .DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-select[_ngcontent-%COMP%]:not(:last-child), .DataForm[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]:not(.has-validation) > .form-control[_ngcontent-%COMP%]:not(:last-child){border:.313rem}.DataForm[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto;-moz-appearance:auto;-webkit-appearance:auto;position:relative}\"]\n    });\n  }\n  return ScientificProblemAdvancedSearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}