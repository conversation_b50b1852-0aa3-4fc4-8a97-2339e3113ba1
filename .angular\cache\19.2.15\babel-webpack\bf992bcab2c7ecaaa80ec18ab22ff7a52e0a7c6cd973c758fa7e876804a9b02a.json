{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Link lists and struct (graph or tree)\r\n */\nimport { curry, each, assert, extend, map, keys } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nfunction linkSeriesData(opt) {\n  var mainData = opt.mainData;\n  var datas = opt.datas;\n  if (!datas) {\n    datas = {\n      main: mainData\n    };\n    opt.datasAttr = {\n      main: 'data'\n    };\n  }\n  opt.datas = opt.mainData = null;\n  linkAll(mainData, datas, opt);\n  // Porxy data original methods.\n  each(datas, function (data) {\n    each(mainData.TRANSFERABLE_METHODS, function (methodName) {\n      data.wrapMethod(methodName, curry(transferInjection, opt));\n    });\n  });\n  // Beyond transfer, additional features should be added to `cloneShallow`.\n  mainData.wrapMethod('cloneShallow', curry(cloneShallowInjection, opt));\n  // Only mainData trigger change, because struct.update may trigger\n  // another changable methods, which may bring about dead lock.\n  each(mainData.CHANGABLE_METHODS, function (methodName) {\n    mainData.wrapMethod(methodName, curry(changeInjection, opt));\n  });\n  // Make sure datas contains mainData.\n  assert(datas[mainData.dataType] === mainData);\n}\nfunction transferInjection(opt, res) {\n  if (isMainData(this)) {\n    // Transfer datas to new main data.\n    var datas = extend({}, inner(this).datas);\n    datas[this.dataType] = res;\n    linkAll(res, datas, opt);\n  } else {\n    // Modify the reference in main data to point newData.\n    linkSingle(res, this.dataType, inner(this).mainData, opt);\n  }\n  return res;\n}\nfunction changeInjection(opt, res) {\n  opt.struct && opt.struct.update();\n  return res;\n}\nfunction cloneShallowInjection(opt, res) {\n  // cloneShallow, which brings about some fragilities, may be inappropriate\n  // to be exposed as an API. So for implementation simplicity we can make\n  // the restriction that cloneShallow of not-mainData should not be invoked\n  // outside, but only be invoked here.\n  each(inner(res).datas, function (data, dataType) {\n    data !== res && linkSingle(data.cloneShallow(), dataType, res, opt);\n  });\n  return res;\n}\n/**\r\n * Supplement method to List.\r\n *\r\n * @public\r\n * @param [dataType] If not specified, return mainData.\r\n */\nfunction getLinkedData(dataType) {\n  var mainData = inner(this).mainData;\n  return dataType == null || mainData == null ? mainData : inner(mainData).datas[dataType];\n}\n/**\r\n * Get list of all linked data\r\n */\nfunction getLinkedDataAll() {\n  var mainData = inner(this).mainData;\n  return mainData == null ? [{\n    data: mainData\n  }] : map(keys(inner(mainData).datas), function (type) {\n    return {\n      type: type,\n      data: inner(mainData).datas[type]\n    };\n  });\n}\nfunction isMainData(data) {\n  return inner(data).mainData === data;\n}\nfunction linkAll(mainData, datas, opt) {\n  inner(mainData).datas = {};\n  each(datas, function (data, dataType) {\n    linkSingle(data, dataType, mainData, opt);\n  });\n}\nfunction linkSingle(data, dataType, mainData, opt) {\n  inner(mainData).datas[dataType] = data;\n  inner(data).mainData = mainData;\n  data.dataType = dataType;\n  if (opt.struct) {\n    data[opt.structAttr] = opt.struct;\n    opt.struct[opt.datasAttr[dataType]] = data;\n  }\n  // Supplement method.\n  data.getLinkedData = getLinkedData;\n  data.getLinkedDataAll = getLinkedDataAll;\n}\nexport default linkSeriesData;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}