{"ast": null, "code": "var Entry = function () {\n  function Entry(val) {\n    this.value = val;\n  }\n  return Entry;\n}();\nexport { Entry };\nvar LinkedList = function () {\n  function LinkedList() {\n    this._len = 0;\n  }\n  LinkedList.prototype.insert = function (val) {\n    var entry = new Entry(val);\n    this.insertEntry(entry);\n    return entry;\n  };\n  LinkedList.prototype.insertEntry = function (entry) {\n    if (!this.head) {\n      this.head = this.tail = entry;\n    } else {\n      this.tail.next = entry;\n      entry.prev = this.tail;\n      entry.next = null;\n      this.tail = entry;\n    }\n    this._len++;\n  };\n  LinkedList.prototype.remove = function (entry) {\n    var prev = entry.prev;\n    var next = entry.next;\n    if (prev) {\n      prev.next = next;\n    } else {\n      this.head = next;\n    }\n    if (next) {\n      next.prev = prev;\n    } else {\n      this.tail = prev;\n    }\n    entry.next = entry.prev = null;\n    this._len--;\n  };\n  LinkedList.prototype.len = function () {\n    return this._len;\n  };\n  LinkedList.prototype.clear = function () {\n    this.head = this.tail = null;\n    this._len = 0;\n  };\n  return LinkedList;\n}();\nexport { LinkedList };\nvar LRU = function () {\n  function LRU(maxSize) {\n    this._list = new LinkedList();\n    this._maxSize = 10;\n    this._map = {};\n    this._maxSize = maxSize;\n  }\n  LRU.prototype.put = function (key, value) {\n    var list = this._list;\n    var map = this._map;\n    var removed = null;\n    if (map[key] == null) {\n      var len = list.len();\n      var entry = this._lastRemovedEntry;\n      if (len >= this._maxSize && len > 0) {\n        var leastUsedEntry = list.head;\n        list.remove(leastUsedEntry);\n        delete map[leastUsedEntry.key];\n        removed = leastUsedEntry.value;\n        this._lastRemovedEntry = leastUsedEntry;\n      }\n      if (entry) {\n        entry.value = value;\n      } else {\n        entry = new Entry(value);\n      }\n      entry.key = key;\n      list.insertEntry(entry);\n      map[key] = entry;\n    }\n    return removed;\n  };\n  LRU.prototype.get = function (key) {\n    var entry = this._map[key];\n    var list = this._list;\n    if (entry != null) {\n      if (entry !== list.tail) {\n        list.remove(entry);\n        list.insertEntry(entry);\n      }\n      return entry.value;\n    }\n  };\n  LRU.prototype.clear = function () {\n    this._list.clear();\n    this._map = {};\n  };\n  LRU.prototype.len = function () {\n    return this._list.len();\n  };\n  return LRU;\n}();\nexport default LRU;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}