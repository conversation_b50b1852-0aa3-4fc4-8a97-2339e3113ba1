{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as curveTool from 'zrender/lib/core/curve.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport { getSymbolSize } from './graphHelper.js';\nvar v1 = [];\nvar v2 = [];\nvar v3 = [];\nvar quadraticAt = curveTool.quadraticAt;\nvar v2DistSquare = vec2.distSquare;\nvar mathAbs = Math.abs;\nfunction intersectCurveCircle(curvePoints, center, radius) {\n  var p0 = curvePoints[0];\n  var p1 = curvePoints[1];\n  var p2 = curvePoints[2];\n  var d = Infinity;\n  var t;\n  var radiusSquare = radius * radius;\n  var interval = 0.1;\n  for (var _t = 0.1; _t <= 0.9; _t += 0.1) {\n    v1[0] = quadraticAt(p0[0], p1[0], p2[0], _t);\n    v1[1] = quadraticAt(p0[1], p1[1], p2[1], _t);\n    var diff = mathAbs(v2DistSquare(v1, center) - radiusSquare);\n    if (diff < d) {\n      d = diff;\n      t = _t;\n    }\n  }\n  // Assume the segment is monotone，Find root through Bisection method\n  // At most 32 iteration\n  for (var i = 0; i < 32; i++) {\n    // let prev = t - interval;\n    var next = t + interval;\n    // v1[0] = quadraticAt(p0[0], p1[0], p2[0], prev);\n    // v1[1] = quadraticAt(p0[1], p1[1], p2[1], prev);\n    v2[0] = quadraticAt(p0[0], p1[0], p2[0], t);\n    v2[1] = quadraticAt(p0[1], p1[1], p2[1], t);\n    v3[0] = quadraticAt(p0[0], p1[0], p2[0], next);\n    v3[1] = quadraticAt(p0[1], p1[1], p2[1], next);\n    var diff = v2DistSquare(v2, center) - radiusSquare;\n    if (mathAbs(diff) < 1e-2) {\n      break;\n    }\n    // let prevDiff = v2DistSquare(v1, center) - radiusSquare;\n    var nextDiff = v2DistSquare(v3, center) - radiusSquare;\n    interval /= 2;\n    if (diff < 0) {\n      if (nextDiff >= 0) {\n        t = t + interval;\n      } else {\n        t = t - interval;\n      }\n    } else {\n      if (nextDiff >= 0) {\n        t = t - interval;\n      } else {\n        t = t + interval;\n      }\n    }\n  }\n  return t;\n}\n// Adjust edge to avoid\nexport default function adjustEdge(graph, scale) {\n  var tmp0 = [];\n  var quadraticSubdivide = curveTool.quadraticSubdivide;\n  var pts = [[], [], []];\n  var pts2 = [[], []];\n  var v = [];\n  scale /= 2;\n  graph.eachEdge(function (edge, idx) {\n    var linePoints = edge.getLayout();\n    var fromSymbol = edge.getVisual('fromSymbol');\n    var toSymbol = edge.getVisual('toSymbol');\n    if (!linePoints.__original) {\n      linePoints.__original = [vec2.clone(linePoints[0]), vec2.clone(linePoints[1])];\n      if (linePoints[2]) {\n        linePoints.__original.push(vec2.clone(linePoints[2]));\n      }\n    }\n    var originalPoints = linePoints.__original;\n    // Quadratic curve\n    if (linePoints[2] != null) {\n      vec2.copy(pts[0], originalPoints[0]);\n      vec2.copy(pts[1], originalPoints[2]);\n      vec2.copy(pts[2], originalPoints[1]);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        var t = intersectCurveCircle(pts, originalPoints[0], symbolSize * scale);\n        // Subdivide and get the second\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[0][0] = tmp0[3];\n        pts[1][0] = tmp0[4];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[0][1] = tmp0[3];\n        pts[1][1] = tmp0[4];\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        var t = intersectCurveCircle(pts, originalPoints[1], symbolSize * scale);\n        // Subdivide and get the first\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[1][0] = tmp0[1];\n        pts[2][0] = tmp0[2];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[1][1] = tmp0[1];\n        pts[2][1] = tmp0[2];\n      }\n      // Copy back to layout\n      vec2.copy(linePoints[0], pts[0]);\n      vec2.copy(linePoints[1], pts[2]);\n      vec2.copy(linePoints[2], pts[1]);\n    }\n    // Line\n    else {\n      vec2.copy(pts2[0], originalPoints[0]);\n      vec2.copy(pts2[1], originalPoints[1]);\n      vec2.sub(v, pts2[1], pts2[0]);\n      vec2.normalize(v, v);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        vec2.scaleAndAdd(pts2[0], pts2[0], v, symbolSize * scale);\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        vec2.scaleAndAdd(pts2[1], pts2[1], v, -symbolSize * scale);\n      }\n      vec2.copy(linePoints[0], pts2[0]);\n      vec2.copy(linePoints[1], pts2[1]);\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}