{"ast": null, "code": "export function buildPath(ctx, shape) {\n  var x = shape.x;\n  var y = shape.y;\n  var width = shape.width;\n  var height = shape.height;\n  var r = shape.r;\n  var r1;\n  var r2;\n  var r3;\n  var r4;\n  if (width < 0) {\n    x = x + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y = y + height;\n    height = -height;\n  }\n  if (typeof r === 'number') {\n    r1 = r2 = r3 = r4 = r;\n  } else if (r instanceof Array) {\n    if (r.length === 1) {\n      r1 = r2 = r3 = r4 = r[0];\n    } else if (r.length === 2) {\n      r1 = r3 = r[0];\n      r2 = r4 = r[1];\n    } else if (r.length === 3) {\n      r1 = r[0];\n      r2 = r4 = r[1];\n      r3 = r[2];\n    } else {\n      r1 = r[0];\n      r2 = r[1];\n      r3 = r[2];\n      r4 = r[3];\n    }\n  } else {\n    r1 = r2 = r3 = r4 = 0;\n  }\n  var total;\n  if (r1 + r2 > width) {\n    total = r1 + r2;\n    r1 *= width / total;\n    r2 *= width / total;\n  }\n  if (r3 + r4 > width) {\n    total = r3 + r4;\n    r3 *= width / total;\n    r4 *= width / total;\n  }\n  if (r2 + r3 > height) {\n    total = r2 + r3;\n    r2 *= height / total;\n    r3 *= height / total;\n  }\n  if (r1 + r4 > height) {\n    total = r1 + r4;\n    r1 *= height / total;\n    r4 *= height / total;\n  }\n  ctx.moveTo(x + r1, y);\n  ctx.lineTo(x + width - r2, y);\n  r2 !== 0 && ctx.arc(x + width - r2, y + r2, r2, -Math.PI / 2, 0);\n  ctx.lineTo(x + width, y + height - r3);\n  r3 !== 0 && ctx.arc(x + width - r3, y + height - r3, r3, 0, Math.PI / 2);\n  ctx.lineTo(x + r4, y + height);\n  r4 !== 0 && ctx.arc(x + r4, y + height - r4, r4, Math.PI / 2, Math.PI);\n  ctx.lineTo(x, y + r1);\n  r1 !== 0 && ctx.arc(x + r1, y + r1, r1, Math.PI, Math.PI * 1.5);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}