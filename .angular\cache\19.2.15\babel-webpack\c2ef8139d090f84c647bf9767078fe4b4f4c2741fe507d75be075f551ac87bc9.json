{"ast": null, "code": "import { ScientificProblemsComponent } from './scientific-problems/scientific-problems.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"disableBlock\": a0\n});\nfunction ScientificProblemsViewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"app-add-scientifi-problem-reply\", 7);\n    i0.ɵɵlistener(\"closeAddReplyToScProblem\", function ScientificProblemsViewComponent_div_4_Template_app_add_scientifi_problem_reply_closeAddReplyToScProblem_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddReplyToScProblemView($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scProbObjForAddReplyView\", ctx_r1.scProbObjForAddReplyView);\n  }\n}\nfunction ScientificProblemsViewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"app-add-scientifi-problem-to-question-bank\", 8);\n    i0.ɵɵlistener(\"closeAddScProblemToQuestionBankView\", function ScientificProblemsViewComponent_div_5_Template_app_add_scientifi_problem_to_question_bank_closeAddScProblemToQuestionBankView_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddScProbToQuestionBankView($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scProbObjForAddToQuestionBankView\", ctx_r1.scProbObjForAddToQuestionBankView);\n  }\n}\nfunction ScientificProblemsViewComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"app-scientific-problem-advanced-search\", 9);\n    i0.ɵɵlistener(\"closeAdvancedSearch\", function ScientificProblemsViewComponent_div_7_Template_app_scientific_problem_advanced_search_closeAdvancedSearch_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAdvancedSearch($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"filter\", ctx_r1.filter);\n  }\n}\nexport let ScientificProblemsViewComponent = /*#__PURE__*/(() => {\n  class ScientificProblemsViewComponent {\n    scientificProblmChild;\n    showAddReplyOverlay = false;\n    showAddScProbToQuestionBankOverlay = false;\n    scProbObjForAddReplyView = {};\n    scProbObjForAddToQuestionBankView = {};\n    openAdvancedSearch = false;\n    filter = {\n      skip: 0,\n      take: 12,\n      sorField: '',\n      ordType: 1,\n      page: 1\n    };\n    constructor() {}\n    ngOnInit() {}\n    showAddReplyToScProblemView(event) {\n      this.scProbObjForAddReplyView = event;\n      this.showAddReplyOverlay = !this.showAddReplyOverlay;\n      if (!this.showAddReplyOverlay) {\n        this.scientificProblmChild?.getScientificProblems();\n      }\n    }\n    showAddScProbToQuestionBankView(event) {\n      this.scProbObjForAddToQuestionBankView = event;\n      this.showAddScProbToQuestionBankOverlay = !this.showAddScProbToQuestionBankOverlay;\n      if (!this.showAddScProbToQuestionBankOverlay) {\n        this.scientificProblmChild?.getScientificProblems();\n      }\n    }\n    closeOverlay() {\n      this.showAddReplyOverlay = false;\n      this.showAddScProbToQuestionBankOverlay = false;\n      this.scientificProblmChild?.getScientificProblems();\n    }\n    closeAdvancedSearch(event) {\n      this.openAdvancedSearch = false;\n      this.filter = event;\n      this.scientificProblmChild?.getScientificProblems();\n    }\n    openAdvancedSearchPopup(event) {\n      this.openAdvancedSearch = true;\n      this.filter = event;\n    }\n    static ɵfac = function ScientificProblemsViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScientificProblemsViewComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ScientificProblemsViewComponent,\n      selectors: [[\"app-scientific-problems-view\"]],\n      viewQuery: function ScientificProblemsViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ScientificProblemsComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scientificProblmChild = _t.first);\n        }\n      },\n      decls: 8,\n      vars: 6,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [3, \"showAddReplyToScProblem\", \"showAddScProblemToQuestionBank\", \"openAdvancedSearchOverLay\"], [\"class\", \"overlay\", 4, \"ngIf\"], [1, \"showoverlay\", 3, \"click\", \"ngClass\"], [1, \"overlay\"], [3, \"closeAddReplyToScProblem\", \"scProbObjForAddReplyView\"], [3, \"closeAddScProblemToQuestionBankView\", \"scProbObjForAddToQuestionBankView\"], [3, \"closeAdvancedSearch\", \"filter\"]],\n      template: function ScientificProblemsViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-scientific-problems\", 3);\n          i0.ɵɵlistener(\"showAddReplyToScProblem\", function ScientificProblemsViewComponent_Template_app_scientific_problems_showAddReplyToScProblem_3_listener($event) {\n            return ctx.showAddReplyToScProblemView($event);\n          })(\"showAddScProblemToQuestionBank\", function ScientificProblemsViewComponent_Template_app_scientific_problems_showAddScProblemToQuestionBank_3_listener($event) {\n            return ctx.showAddScProbToQuestionBankView($event);\n          })(\"openAdvancedSearchOverLay\", function ScientificProblemsViewComponent_Template_app_scientific_problems_openAdvancedSearchOverLay_3_listener($event) {\n            return ctx.openAdvancedSearchPopup($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(4, ScientificProblemsViewComponent_div_4_Template, 2, 1, \"div\", 4)(5, ScientificProblemsViewComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ScientificProblemsViewComponent_Template_div_click_6_listener() {\n            return ctx.closeOverlay();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, ScientificProblemsViewComponent_div_7_Template, 2, 1, \"div\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddReplyOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddScProbToQuestionBankOverlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx.showAddReplyOverlay || ctx.showAddScProbToQuestionBankOverlay));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.openAdvancedSearch);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf],\n      styles: [\".container-fluid[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:0;margin-top:1rem;margin-bottom:0;width:100%;height:auto}.disableBlock[_ngcontent-%COMP%]{display:block!important}.showoverlay[_ngcontent-%COMP%]{position:fixed;width:100%;height:100%;inset:0;background-color:#********;z-index:2;cursor:pointer;display:none}\"]\n    });\n  }\n  return ScientificProblemsViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}