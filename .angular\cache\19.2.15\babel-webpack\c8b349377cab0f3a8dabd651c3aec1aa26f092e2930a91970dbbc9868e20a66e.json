{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Float32Array */\n// TODO Batch by color\nimport * as graphic from '../../util/graphic.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { getECData } from '../../util/innerStore.js';\nvar BOOST_SIZE_THRESHOLD = 4;\nvar LargeSymbolPathShape = /** @class */function () {\n  function LargeSymbolPathShape() {}\n  return LargeSymbolPathShape;\n}();\nvar LargeSymbolPath = /** @class */function (_super) {\n  __extends(LargeSymbolPath, _super);\n  function LargeSymbolPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this._off = 0;\n    _this.hoverDataIdx = -1;\n    return _this;\n  }\n  LargeSymbolPath.prototype.getDefaultShape = function () {\n    return new LargeSymbolPathShape();\n  };\n  LargeSymbolPath.prototype.reset = function () {\n    this.notClear = false;\n    this._off = 0;\n  };\n  LargeSymbolPath.prototype.buildPath = function (path, shape) {\n    var points = shape.points;\n    var size = shape.size;\n    var symbolProxy = this.symbolProxy;\n    var symbolProxyShape = symbolProxy.shape;\n    var ctx = path.getContext ? path.getContext() : path;\n    var canBoost = ctx && size[0] < BOOST_SIZE_THRESHOLD;\n    var softClipShape = this.softClipShape;\n    var i;\n    // Do draw in afterBrush.\n    if (canBoost) {\n      this._ctx = ctx;\n      return;\n    }\n    this._ctx = null;\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      symbolProxyShape.x = x - size[0] / 2;\n      symbolProxyShape.y = y - size[1] / 2;\n      symbolProxyShape.width = size[0];\n      symbolProxyShape.height = size[1];\n      symbolProxy.buildPath(path, symbolProxyShape, true);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.afterBrush = function () {\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var ctx = this._ctx;\n    var softClipShape = this.softClipShape;\n    var i;\n    if (!ctx) {\n      return;\n    }\n    // PENDING If style or other canvas status changed?\n    for (i = this._off; i < points.length;) {\n      var x = points[i++];\n      var y = points[i++];\n      if (isNaN(x) || isNaN(y)) {\n        continue;\n      }\n      if (softClipShape && !softClipShape.contain(x, y)) {\n        continue;\n      }\n      // fillRect is faster than building a rect path and draw.\n      // And it support light globalCompositeOperation.\n      ctx.fillRect(x - size[0] / 2, y - size[1] / 2, size[0], size[1]);\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeSymbolPath.prototype.findDataIndex = function (x, y) {\n    // TODO ???\n    // Consider transform\n    var shape = this.shape;\n    var points = shape.points;\n    var size = shape.size;\n    var w = Math.max(size[0], 4);\n    var h = Math.max(size[1], 4);\n    // Not consider transform\n    // Treat each element as a rect\n    // top down traverse\n    for (var idx = points.length / 2 - 1; idx >= 0; idx--) {\n      var i = idx * 2;\n      var x0 = points[i] - w / 2;\n      var y0 = points[i + 1] - h / 2;\n      if (x >= x0 && y >= y0 && x <= x0 + w && y <= y0 + h) {\n        return idx;\n      }\n    }\n    return -1;\n  };\n  LargeSymbolPath.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      // Cache found data index.\n      var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);\n      return dataIdx >= 0;\n    }\n    this.hoverDataIdx = -1;\n    return false;\n  };\n  LargeSymbolPath.prototype.getBoundingRect = function () {\n    // Ignore stroke for large symbol draw.\n    var rect = this._rect;\n    if (!rect) {\n      var shape = this.shape;\n      var points = shape.points;\n      var size = shape.size;\n      var w = size[0];\n      var h = size[1];\n      var minX = Infinity;\n      var minY = Infinity;\n      var maxX = -Infinity;\n      var maxY = -Infinity;\n      for (var i = 0; i < points.length;) {\n        var x = points[i++];\n        var y = points[i++];\n        minX = Math.min(x, minX);\n        maxX = Math.max(x, maxX);\n        minY = Math.min(y, minY);\n        maxY = Math.max(y, maxY);\n      }\n      rect = this._rect = new graphic.BoundingRect(minX - w / 2, minY - h / 2, maxX - minX + w, maxY - minY + h);\n    }\n    return rect;\n  };\n  return LargeSymbolPath;\n}(graphic.Path);\nvar LargeSymbolDraw = /** @class */function () {\n  function LargeSymbolDraw() {\n    this.group = new graphic.Group();\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  LargeSymbolDraw.prototype.updateData = function (data, opt) {\n    this._clear();\n    var symbolEl = this._create();\n    symbolEl.setShape({\n      points: data.getLayout('points')\n    });\n    this._setCommon(symbolEl, data, opt);\n  };\n  LargeSymbolDraw.prototype.updateLayout = function (data) {\n    var points = data.getLayout('points');\n    this.group.eachChild(function (child) {\n      if (child.startIndex != null) {\n        var len = (child.endIndex - child.startIndex) * 2;\n        var byteOffset = child.startIndex * 4 * 2;\n        points = new Float32Array(points.buffer, byteOffset, len);\n      }\n      child.setShape('points', points);\n      // Reset draw cursor.\n      child.reset();\n    });\n  };\n  LargeSymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    var lastAdded = this._newAdded[0];\n    var points = data.getLayout('points');\n    var oldPoints = lastAdded && lastAdded.shape.points;\n    // Merging the exists. Each element has 1e4 points.\n    // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)\n    if (oldPoints && oldPoints.length < 2e4) {\n      var oldLen = oldPoints.length;\n      var newPoints = new Float32Array(oldLen + points.length);\n      // Concat two array\n      newPoints.set(oldPoints);\n      newPoints.set(points, oldLen);\n      // Update endIndex\n      lastAdded.endIndex = taskParams.end;\n      lastAdded.setShape({\n        points: newPoints\n      });\n    } else {\n      // Clear\n      this._newAdded = [];\n      var symbolEl = this._create();\n      symbolEl.startIndex = taskParams.start;\n      symbolEl.endIndex = taskParams.end;\n      symbolEl.incremental = true;\n      symbolEl.setShape({\n        points: points\n      });\n      this._setCommon(symbolEl, data, opt);\n    }\n  };\n  LargeSymbolDraw.prototype.eachRendered = function (cb) {\n    this._newAdded[0] && cb(this._newAdded[0]);\n  };\n  LargeSymbolDraw.prototype._create = function () {\n    var symbolEl = new LargeSymbolPath({\n      cursor: 'default'\n    });\n    symbolEl.ignoreCoarsePointer = true;\n    this.group.add(symbolEl);\n    this._newAdded.push(symbolEl);\n    return symbolEl;\n  };\n  LargeSymbolDraw.prototype._setCommon = function (symbolEl, data, opt) {\n    var hostModel = data.hostModel;\n    opt = opt || {};\n    var size = data.getVisual('symbolSize');\n    symbolEl.setShape('size', size instanceof Array ? size : [size, size]);\n    symbolEl.softClipShape = opt.clipShape || null;\n    // Create symbolProxy to build path for each data\n    symbolEl.symbolProxy = createSymbol(data.getVisual('symbol'), 0, 0, 0, 0);\n    // Use symbolProxy setColor method\n    symbolEl.setColor = symbolEl.symbolProxy.setColor;\n    var extrudeShadow = symbolEl.shape.size[0] < BOOST_SIZE_THRESHOLD;\n    symbolEl.useStyle(\n    // Draw shadow when doing fillRect is extremely slow.\n    hostModel.getModel('itemStyle').getItemStyle(extrudeShadow ? ['color', 'shadowBlur', 'shadowColor'] : ['color']));\n    var globalStyle = data.getVisual('style');\n    var visualColor = globalStyle && globalStyle.fill;\n    if (visualColor) {\n      symbolEl.setColor(visualColor);\n    }\n    var ecData = getECData(symbolEl);\n    // Enable tooltip\n    // PENDING May have performance issue when path is extremely large\n    ecData.seriesIndex = hostModel.seriesIndex;\n    symbolEl.on('mousemove', function (e) {\n      ecData.dataIndex = null;\n      var dataIndex = symbolEl.hoverDataIdx;\n      if (dataIndex >= 0) {\n        // Provide dataIndex for tooltip\n        ecData.dataIndex = dataIndex + (symbolEl.startIndex || 0);\n      }\n    });\n  };\n  LargeSymbolDraw.prototype.remove = function () {\n    this._clear();\n  };\n  LargeSymbolDraw.prototype._clear = function () {\n    this._newAdded = [];\n    this.group.removeAll();\n  };\n  return LargeSymbolDraw;\n}();\nexport default LargeSymbolDraw;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}