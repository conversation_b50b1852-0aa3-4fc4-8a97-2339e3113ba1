{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/teacher-drop-out-request-services/teacher-drop-out-request.service\";\nimport * as i2 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction TeacherDropOutRequestRejectedComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r0.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.resultMessage.message, \" \");\n  }\n}\nexport let TeacherDropOutRequestRejectedComponent = /*#__PURE__*/(() => {\n  class TeacherDropOutRequestRejectedComponent {\n    teacherDropOutRequestService;\n    alertify;\n    translate;\n    closeRejectedRequest = new EventEmitter();\n    itemTeacherDropOutRequestForReject = {};\n    rejectRequest = {};\n    resultMessage = {};\n    langEnum = LanguageEnum;\n    constructor(teacherDropOutRequestService, alertify, translate) {\n      this.teacherDropOutRequestService = teacherDropOutRequestService;\n      this.alertify = alertify;\n      this.translate = translate;\n    }\n    ngOnInit() {}\n    closeRejectRequest() {\n      this.closeRejectedRequest.emit();\n    }\n    saveRejectRequest() {\n      this.resultMessage = {};\n      if (this.itemTeacherDropOutRequestForReject.reasonReject && this.itemTeacherDropOutRequestForReject.reasonReject?.length > 256) {\n        this.resultMessage = {\n          message: this.translate.instant('GENERAL.REQUIRED'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      let model = {\n        teacherDropOutRequest: this.itemTeacherDropOutRequestForReject.id,\n        reasonReject: this.itemTeacherDropOutRequestForReject.reasonReject\n      };\n      if (model.reasonReject) {\n        this.teacherDropOutRequestService.teacherDropOutRequestsRejection(model).subscribe(res => {\n          if (res.isSuccess) {\n            this.closeRejectRequest();\n            this.alertify.success(res.message || '');\n          } else {\n            this.alertify.error(res.message || '');\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resultMessage = {\n          message: this.translate.instant('GENERAL.REQUIRED'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    static ɵfac = function TeacherDropOutRequestRejectedComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherDropOutRequestRejectedComponent)(i0.ɵɵdirectiveInject(i1.TeacherDropOutRequestService), i0.ɵɵdirectiveInject(i2.AlertifyService), i0.ɵɵdirectiveInject(i3.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherDropOutRequestRejectedComponent,\n      selectors: [[\"app-teacher-drop-out-request-rejected\"]],\n      inputs: {\n        itemTeacherDropOutRequestForReject: \"itemTeacherDropOutRequestForReject\"\n      },\n      outputs: {\n        closeRejectedRequest: \"closeRejectedRequest\"\n      },\n      decls: 26,\n      vars: 23,\n      consts: [[1, \"form-group\", \"DataForm\", \"mt-5\", \"pl-3\", \"pr-3\"], [1, \"head\", \"bold\"], [1, \"form-group\"], [1, \"label\"], [1, \"data_input\"], [1, \"label\", \"mt-4\"], [\"cols\", \"1\", \"rows\", \"5\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"]],\n      template: function TeacherDropOutRequestRejectedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementStart(8, \"span\", 4);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"label\", 5);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"textarea\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TeacherDropOutRequestRejectedComponent_Template_textarea_ngModelChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.itemTeacherDropOutRequestForReject.reasonReject, $event) || (ctx.itemTeacherDropOutRequestForReject.reasonReject = $event);\n            return $event;\n          });\n          i0.ɵɵtext(16, \"        \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, TeacherDropOutRequestRejectedComponent_div_17_Template, 2, 4, \"div\", 7);\n          i0.ɵɵelementStart(18, \"section\", 8)(19, \"div\", 9)(20, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TeacherDropOutRequestRejectedComponent_Template_button_click_20_listener() {\n            return ctx.saveRejectRequest();\n          });\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function TeacherDropOutRequestRejectedComponent_Template_button_click_23_listener() {\n            return ctx.closeRejectRequest();\n          });\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 10, \"STUDENT_SUBSCRIBERS.REJECTION_APPLICATION\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 12, \"STUDENT_SUBSCRIBERS.TEACHER_REJECTED\"), \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang == ctx.langEnum.en ? ctx.itemTeacherDropOutRequestForReject == null ? null : ctx.itemTeacherDropOutRequestForReject.teacherNameEn : ctx.itemTeacherDropOutRequestForReject == null ? null : ctx.itemTeacherDropOutRequestForReject.teacherNameAr, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 14, \"GENERAL_DROP_OUT_REQUEST.REMOVE_FROM_DROP_OUT\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 16, \"STUDENT_SUBSCRIBERS.REJECTED_REASON\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemTeacherDropOutRequestForReject.reasonReject);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage.message);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 18, \"GENERAL.SAVE\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 20, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.NgModel, i5.NgForm, i3.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:2.31rem;margin:1rem}\"]\n    });\n  }\n  return TeacherDropOutRequestRejectedComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}