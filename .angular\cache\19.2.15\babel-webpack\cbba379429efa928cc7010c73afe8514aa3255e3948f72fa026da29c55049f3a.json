{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { BaseSelectedDateModel } from '../../../../../core/ng-model/base-selected-date-model';\nimport { BaseConstantModel } from '../../../../../core/ng-model/base-constant-model';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../../core/services/student-program-vacation-services/student-program-vacation-services.service\";\nimport * as i4 from \"../../../../../core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction AddStudentProgramVacationRequestComponent_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"SCIENTIFIC_MATERIAL.REQUIRED\"));\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddStudentProgramVacationRequestComponent_div_18_div_1_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.addStudentVacationRequestModel.vacationStartDate == undefined || ctx_r1.isSubmit);\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"SCIENTIFIC_MATERIAL.REQUIRED\"));\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddStudentProgramVacationRequestComponent_div_24_div_1_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.addStudentVacationRequestModel.vacationEndDate == undefined || ctx_r1.isSubmit);\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"SCIENTIFIC_MATERIAL.REQUIRED\"));\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddStudentProgramVacationRequestComponent_div_32_div_1_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const vacationReason_r3 = i0.ɵɵreference(30);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", vacationReason_r3.errors == null ? null : vacationReason_r3.errors.required);\n  }\n}\nfunction AddStudentProgramVacationRequestComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resultMessage.type, \" my-4 py-2 text-center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resultMessage.message, \"\");\n  }\n}\nexport let AddStudentProgramVacationRequestComponent = /*#__PURE__*/(() => {\n  class AddStudentProgramVacationRequestComponent {\n    dateFormatterService;\n    translate;\n    studentProgramVacationService;\n    alertfyService;\n    resultMessage = {};\n    calenderType = new BaseSelectedDateModel();\n    selectedDateType;\n    typeDateBinding;\n    datafromBinding;\n    dataToBinding;\n    hijri = false;\n    milady = false;\n    addStudentVacationRequestModel = {};\n    selectedIndex = -1;\n    currentUser;\n    programFilter = {\n      take: 2147483647\n    };\n    programs;\n    closeAddVacationRequest = new EventEmitter();\n    maxGregDate;\n    vacationStartDate;\n    vacationEndDate;\n    langEnum = LanguageEnum;\n    isSubmit = false;\n    disabledButton = false;\n    selectedProgramBatchFromParent;\n    constructor(dateFormatterService, translate, studentProgramVacationService, alertfyService) {\n      this.dateFormatterService = dateFormatterService;\n      this.translate = translate;\n      this.studentProgramVacationService = studentProgramVacationService;\n      this.alertfyService = alertfyService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.addStudentVacationRequestModel.userId = this.currentUser.id;\n      this.addStudentVacationRequestModel.batchId = this.selectedProgramBatchFromParent?.batId;\n      this.programFilter.usrId = this.currentUser.id;\n      this.maxGregDate = this.dateFormatterService.GetTodayGregorian();\n      this.vacationStartDate = this.dateFormatterService.GetTodayGregorian(); //Vacation Start Date ToDay\n      this.getAllProgram();\n    }\n    SendDatafrom(data) {\n      this.typeDateBinding = data.selectedDateType;\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.datafromBinding = data.selectedDateValue;\n      this.addStudentVacationRequestModel.vacationStartDate = this.datafromBinding;\n      this.selectedDateType = data.selectedDateType;\n      //Vacation End Date start from Vacation Start Date To Future Date\n      this.vacationEndDate = this.dateFormatterService.ToGregorianDateStruct(this.datafromBinding, '');\n    }\n    SendDataTo(data) {\n      this.typeDateBinding = data.selectedDateType;\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.dataToBinding = data.selectedDateValue;\n      this.addStudentVacationRequestModel.vacationEndDate = this.dataToBinding;\n      this.selectedDateType = data.selectedDateType;\n    }\n    closeAddStudentVacation() {\n      this.addStudentVacationRequestModel.vacationStartDate = ' ';\n      this.addStudentVacationRequestModel.vacationEndDate = '';\n      this.addStudentVacationRequestModel.vacationReason = '';\n      this.addStudentVacationRequestModel.batchId = '';\n      this.addStudentVacationRequestModel.userId = '';\n      this.closeAddVacationRequest.emit(this.addStudentVacationRequestModel);\n    }\n    AddStudentVacationRequest() {\n      this.isSubmit = true;\n      if (this.addStudentVacationRequestModel.vacationReason == null) {\n        return;\n      }\n      this.addStudentVacationRequestModel ? this.studentProgramVacationService.addStudentProgramVacation(this.addStudentVacationRequestModel).subscribe(res => {\n        if (res.isSuccess) {\n          this.closeAddStudentVacation();\n          this.alertfyService.success(res.message || '');\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      }) : '';\n    }\n    getAllProgram() {\n      this.programFilter.skip = 0;\n      this.programFilter.take = 2147483647;\n      if (this.currentUser?.id) this.studentProgramVacationService.getStudentAvailablePrograms(this.currentUser?.id).subscribe(res => {\n        this.programs = res.data;\n        this.selectedIndex = -1;\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function AddStudentProgramVacationRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddStudentProgramVacationRequestComponent)(i0.ɵɵdirectiveInject(i1.DateFormatterService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.StudentProgramVacationServicesService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddStudentProgramVacationRequestComponent,\n      selectors: [[\"app-add-student-program-vacation-request\"]],\n      inputs: {\n        selectedProgramBatchFromParent: \"selectedProgramBatchFromParent\"\n      },\n      outputs: {\n        closeAddVacationRequest: \"closeAddVacationRequest\"\n      },\n      decls: 42,\n      vars: 38,\n      consts: [[\"vacationReason\", \"ngModel\"], [1, \"form-group\", \"DataForm\", \"mt-5\", \"pl-3\", \"pr-3\"], [1, \"head\", \"bold\"], [1, \"form-group\"], [\"for\", \"category\", 1, \"label\"], [1, \"d-flex\"], [1, \"input-group\"], [1, \"col-12\", \"mt-2\", \"mb-2\"], [\"type\", \"email\", \"disabled\", \"\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"form-group\", \"subscribtion-date\"], [1, \"col-12\", \"p-0\"], [1, \"label\"], [\"id\", \"dateFrom\", \"name\", \"dateFrom\", 3, \"sendDate\", \"keypress\", \"editcalenderType\", \"hijri\", \"milady\", \"minGreg\"], [\"class\", \"alert\", 4, \"ngIf\"], [3, \"sendDate\", \"keypress\", \"editcalenderType\", \"hijri\", \"milady\", \"minGreg\"], [1, \"label\", \"mt-4\"], [\"cols\", \"1\", \"rows\", \"5\", \"id\", \"vacationReason\", \"name\", \"vacationReason\", \"required\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [3, \"class\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"row\", \"direction-rtl\", \"buttons-center\", \"mt-5\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"click\", \"disabled\"], [\"type\", \"reset\", 1, \"cancel-btn\", 3, \"click\"], [1, \"alert\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [1, \"bold\"]],\n      template: function AddStudentProgramVacationRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"h3\", 2);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"label\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"div\", 7);\n          i0.ɵɵelement(11, \"input\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"app-milady-hijri-calendar\", 12);\n          i0.ɵɵlistener(\"sendDate\", function AddStudentProgramVacationRequestComponent_Template_app_milady_hijri_calendar_sendDate_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.SendDatafrom($event));\n          })(\"keypress\", function AddStudentProgramVacationRequestComponent_Template_app_milady_hijri_calendar_keypress_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.preventDefault());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(18, AddStudentProgramVacationRequestComponent_div_18_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementStart(19, \"div\", 10)(20, \"label\", 11);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"app-milady-hijri-calendar\", 14);\n          i0.ɵɵlistener(\"sendDate\", function AddStudentProgramVacationRequestComponent_Template_app_milady_hijri_calendar_sendDate_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.SendDataTo($event));\n          })(\"keypress\", function AddStudentProgramVacationRequestComponent_Template_app_milady_hijri_calendar_keypress_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.preventDefault());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, AddStudentProgramVacationRequestComponent_div_24_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\")(26, \"label\", 15);\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"textarea\", 16, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddStudentProgramVacationRequestComponent_Template_textarea_ngModelChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.addStudentVacationRequestModel.vacationReason, $event) || (ctx.addStudentVacationRequestModel.vacationReason = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtext(31, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, AddStudentProgramVacationRequestComponent_div_32_Template, 2, 1, \"div\", 13)(33, AddStudentProgramVacationRequestComponent_div_33_Template, 3, 4, \"div\", 17);\n          i0.ɵɵelementStart(34, \"section\", 18)(35, \"div\", 19)(36, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function AddStudentProgramVacationRequestComponent_Template_button_click_36_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.AddStudentVacationRequest());\n          });\n          i0.ɵɵtext(37);\n          i0.ɵɵpipe(38, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function AddStudentProgramVacationRequestComponent_Template_button_click_39_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeAddStudentVacation());\n          });\n          i0.ɵɵtext(40);\n          i0.ɵɵpipe(41, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const vacationReason_r3 = i0.ɵɵreference(30);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 23, \"STUDENT_VACATION.VACATION_REQUEST\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 25, \"STUDENT_VACATION.PROGRAM\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.translate.currentLang === ctx.langEnum.en ? ctx.selectedProgramBatchFromParent == null ? null : ctx.selectedProgramBatchFromParent.enProgBatchName : ctx.selectedProgramBatchFromParent == null ? null : ctx.selectedProgramBatchFromParent.arProgBatchName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 27, \"STUDENT_VACATION.DATE_FROM\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"hijri\", !ctx.hijri)(\"milady\", ctx.milady)(\"minGreg\", ctx.vacationStartDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.addStudentVacationRequestModel.vacationStartDate == undefined && ctx.isSubmit);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 29, \"STUDENT_VACATION.DATE_TO\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"editcalenderType\", ctx.calenderType)(\"hijri\", !ctx.hijri)(\"milady\", ctx.milady)(\"minGreg\", ctx.vacationEndDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.addStudentVacationRequestModel.vacationEndDate == undefined && ctx.isSubmit);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 31, \"STUDENT_VACATION.VACATION_REASON\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addStudentVacationRequestModel.vacationReason);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(37, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", vacationReason_r3.invalid && (vacationReason_r3.dirty || vacationReason_r3.touched || ctx.isSubmit));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resultMessage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.disabledButton);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 33, \"STUDENT_VACATION.SEND\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(41, 35, \"GENERAL.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i5.NgIf, i6.DefaultValueAccessor, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.DataForm[_ngcontent-%COMP%]:lang(ar){text-align:right!important}.DataForm[_ngcontent-%COMP%]:lang(en){text-align:left!important}.DataForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{width:100%;color:#333;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_input[_ngcontent-%COMP%]{font-weight:700;color:var(--second_color);font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]{width:100%;color:gray;font-weight:700;font-size:.875rem}.DataForm[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0!important;padding:0;font-size:.75rem}.DataForm[_ngcontent-%COMP%]   .title_add[_ngcontent-%COMP%]{font-size:1.25rem;padding-top:10%;padding-bottom:1rem}.DataForm[_ngcontent-%COMP%]   .DataForm[_ngcontent-%COMP%]{height:70vh}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(ar), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(ar){text-align:right}.DataForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]:lang(en), .DataForm[_ngcontent-%COMP%]   .data_label[_ngcontent-%COMP%]:lang(en){text-align:left}.DataForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .DataForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{margin:1rem}.input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-file[_ngcontent-%COMP%]:not(:last-child)   .custom-file-label[_ngcontent-%COMP%]:after, .input-group[_ngcontent-%COMP%]:not(.has-validation) > .custom-select[_ngcontent-%COMP%]:not(:last-child), .input-group[_ngcontent-%COMP%]:not(.has-validation) > .form-control[_ngcontent-%COMP%]:not(:last-child){border:.313rem}\"]\n    });\n  }\n  return AddStudentProgramVacationRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}