{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar CircleShape = function () {\n  function CircleShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r = 0;\n  }\n  return CircleShape;\n}();\nexport { CircleShape };\nvar Circle = function (_super) {\n  __extends(Circle, _super);\n  function Circle(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Circle.prototype.getDefaultShape = function () {\n    return new CircleShape();\n  };\n  Circle.prototype.buildPath = function (ctx, shape) {\n    ctx.moveTo(shape.cx + shape.r, shape.cy);\n    ctx.arc(shape.cx, shape.cy, shape.r, 0, Math.PI * 2);\n  };\n  return Circle;\n}(Path);\n;\nCircle.prototype.type = 'circle';\nexport default Circle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}