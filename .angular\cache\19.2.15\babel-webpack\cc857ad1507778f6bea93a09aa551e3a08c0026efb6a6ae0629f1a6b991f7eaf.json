{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Provide effect for line\r\n */\nimport * as graphic from '../../util/graphic.js';\nimport Line from './Line.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as curveUtil from 'zrender/lib/core/curve.js';\nvar EffectLine = /** @class */function (_super) {\n  __extends(EffectLine, _super);\n  function EffectLine(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this.add(_this.createLine(lineData, idx, seriesScope));\n    _this._updateEffectSymbol(lineData, idx);\n    return _this;\n  }\n  EffectLine.prototype.createLine = function (lineData, idx, seriesScope) {\n    return new Line(lineData, idx, seriesScope);\n  };\n  EffectLine.prototype._updateEffectSymbol = function (lineData, idx) {\n    var itemModel = lineData.getItemModel(idx);\n    var effectModel = itemModel.getModel('effect');\n    var size = effectModel.get('symbolSize');\n    var symbolType = effectModel.get('symbol');\n    if (!zrUtil.isArray(size)) {\n      size = [size, size];\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var color = effectModel.get('color') || lineStyle && lineStyle.stroke;\n    var symbol = this.childAt(1);\n    if (this._symbolType !== symbolType) {\n      // Remove previous\n      this.remove(symbol);\n      symbol = createSymbol(symbolType, -0.5, -0.5, 1, 1, color);\n      symbol.z2 = 100;\n      symbol.culling = true;\n      this.add(symbol);\n    }\n    // Symbol may be removed if loop is false\n    if (!symbol) {\n      return;\n    }\n    // Shadow color is same with color in default\n    symbol.setStyle('shadowColor', color);\n    symbol.setStyle(effectModel.getItemStyle(['color']));\n    symbol.scaleX = size[0];\n    symbol.scaleY = size[1];\n    symbol.setColor(color);\n    this._symbolType = symbolType;\n    this._symbolScale = size;\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  EffectLine.prototype._updateEffectAnimation = function (lineData, effectModel, idx) {\n    var symbol = this.childAt(1);\n    if (!symbol) {\n      return;\n    }\n    var points = lineData.getItemLayout(idx);\n    var period = effectModel.get('period') * 1000;\n    var loop = effectModel.get('loop');\n    var roundTrip = effectModel.get('roundTrip');\n    var constantSpeed = effectModel.get('constantSpeed');\n    var delayExpr = zrUtil.retrieve(effectModel.get('delay'), function (idx) {\n      return idx / lineData.count() * period / 3;\n    });\n    // Ignore when updating\n    symbol.ignore = true;\n    this._updateAnimationPoints(symbol, points);\n    if (constantSpeed > 0) {\n      period = this._getLineLength(symbol) / constantSpeed * 1000;\n    }\n    if (period !== this._period || loop !== this._loop || roundTrip !== this._roundTrip) {\n      symbol.stopAnimation();\n      var delayNum = void 0;\n      if (zrUtil.isFunction(delayExpr)) {\n        delayNum = delayExpr(idx);\n      } else {\n        delayNum = delayExpr;\n      }\n      if (symbol.__t > 0) {\n        delayNum = -period * symbol.__t;\n      }\n      this._animateSymbol(symbol, period, delayNum, loop, roundTrip);\n    }\n    this._period = period;\n    this._loop = loop;\n    this._roundTrip = roundTrip;\n  };\n  EffectLine.prototype._animateSymbol = function (symbol, period, delayNum, loop, roundTrip) {\n    if (period > 0) {\n      symbol.__t = 0;\n      var self_1 = this;\n      var animator = symbol.animate('', loop).when(roundTrip ? period * 2 : period, {\n        __t: roundTrip ? 2 : 1\n      }).delay(delayNum).during(function () {\n        self_1._updateSymbolPosition(symbol);\n      });\n      if (!loop) {\n        animator.done(function () {\n          self_1.remove(symbol);\n        });\n      }\n      animator.start();\n    }\n  };\n  EffectLine.prototype._getLineLength = function (symbol) {\n    // Not so accurate\n    return vec2.dist(symbol.__p1, symbol.__cp1) + vec2.dist(symbol.__cp1, symbol.__p2);\n  };\n  EffectLine.prototype._updateAnimationPoints = function (symbol, points) {\n    symbol.__p1 = points[0];\n    symbol.__p2 = points[1];\n    symbol.__cp1 = points[2] || [(points[0][0] + points[1][0]) / 2, (points[0][1] + points[1][1]) / 2];\n  };\n  EffectLine.prototype.updateData = function (lineData, idx, seriesScope) {\n    this.childAt(0).updateData(lineData, idx, seriesScope);\n    this._updateEffectSymbol(lineData, idx);\n  };\n  EffectLine.prototype._updateSymbolPosition = function (symbol) {\n    var p1 = symbol.__p1;\n    var p2 = symbol.__p2;\n    var cp1 = symbol.__cp1;\n    var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;\n    var pos = [symbol.x, symbol.y];\n    var lastPos = pos.slice();\n    var quadraticAt = curveUtil.quadraticAt;\n    var quadraticDerivativeAt = curveUtil.quadraticDerivativeAt;\n    pos[0] = quadraticAt(p1[0], cp1[0], p2[0], t);\n    pos[1] = quadraticAt(p1[1], cp1[1], p2[1], t);\n    // Tangent\n    var tx = symbol.__t < 1 ? quadraticDerivativeAt(p1[0], cp1[0], p2[0], t) : quadraticDerivativeAt(p2[0], cp1[0], p1[0], 1 - t);\n    var ty = symbol.__t < 1 ? quadraticDerivativeAt(p1[1], cp1[1], p2[1], t) : quadraticDerivativeAt(p2[1], cp1[1], p1[1], 1 - t);\n    symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;\n    // enable continuity trail for 'line', 'rect', 'roundRect' symbolType\n    if (this._symbolType === 'line' || this._symbolType === 'rect' || this._symbolType === 'roundRect') {\n      if (symbol.__lastT !== undefined && symbol.__lastT < symbol.__t) {\n        symbol.scaleY = vec2.dist(lastPos, pos) * 1.05;\n        // make sure the last segment render within endPoint\n        if (t === 1) {\n          pos[0] = lastPos[0] + (pos[0] - lastPos[0]) / 2;\n          pos[1] = lastPos[1] + (pos[1] - lastPos[1]) / 2;\n        }\n      } else if (symbol.__lastT === 1) {\n        // After first loop, symbol.__t does NOT start with 0, so connect p1 to pos directly.\n        symbol.scaleY = 2 * vec2.dist(p1, pos);\n      } else {\n        symbol.scaleY = this._symbolScale[1];\n      }\n    }\n    symbol.__lastT = symbol.__t;\n    symbol.ignore = false;\n    symbol.x = pos[0];\n    symbol.y = pos[1];\n  };\n  EffectLine.prototype.updateLayout = function (lineData, idx) {\n    this.childAt(0).updateLayout(lineData, idx);\n    var effectModel = lineData.getItemModel(idx).getModel('effect');\n    this._updateEffectAnimation(lineData, effectModel, idx);\n  };\n  return EffectLine;\n}(graphic.Group);\nexport default EffectLine;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}