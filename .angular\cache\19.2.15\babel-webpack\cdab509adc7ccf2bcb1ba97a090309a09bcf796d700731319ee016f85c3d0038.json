{"ast": null, "code": "import { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AdminTeacherTabService = /*#__PURE__*/(() => {\n  class AdminTeacherTabService {\n    http;\n    getTeacherManagementUrl = environment.baseUrl + 'TeacherManagement/get-all-teachers/';\n    constructor(http) {\n      this.http = http;\n    }\n    getTeacherManagement(model) {\n      return this.http.post(this.getTeacherManagementUrl, model);\n    }\n    static ɵfac = function AdminTeacherTabService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminTeacherTabService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminTeacherTabService,\n      factory: AdminTeacherTabService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AdminTeacherTabService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}