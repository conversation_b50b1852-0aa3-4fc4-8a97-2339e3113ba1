{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Uyghur (China) [ug-cn]\n//! author: boyaq : https://github.com/boyaq\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ugCn = moment.defineLocale('ug-cn', {\n    months: 'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split('_'),\n    monthsShort: 'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split('_'),\n    weekdays: 'يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە'.split('_'),\n    weekdaysShort: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n    weekdaysMin: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY-يىلىM-ئاينىڭD-كۈنى',\n      LLL: 'YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm',\n      LLLL: 'dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm'\n    },\n    meridiemParse: /يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'يېرىم كېچە' || meridiem === 'سەھەر' || meridiem === 'چۈشتىن بۇرۇن') {\n        return hour;\n      } else if (meridiem === 'چۈشتىن كېيىن' || meridiem === 'كەچ') {\n        return hour + 12;\n      } else {\n        return hour >= 11 ? hour : hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      var hm = hour * 100 + minute;\n      if (hm < 600) {\n        return 'يېرىم كېچە';\n      } else if (hm < 900) {\n        return 'سەھەر';\n      } else if (hm < 1130) {\n        return 'چۈشتىن بۇرۇن';\n      } else if (hm < 1230) {\n        return 'چۈش';\n      } else if (hm < 1800) {\n        return 'چۈشتىن كېيىن';\n      } else {\n        return 'كەچ';\n      }\n    },\n    calendar: {\n      sameDay: '[بۈگۈن سائەت] LT',\n      nextDay: '[ئەتە سائەت] LT',\n      nextWeek: '[كېلەركى] dddd [سائەت] LT',\n      lastDay: '[تۆنۈگۈن] LT',\n      lastWeek: '[ئالدىنقى] dddd [سائەت] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s كېيىن',\n      past: '%s بۇرۇن',\n      s: 'نەچچە سېكونت',\n      ss: '%d سېكونت',\n      m: 'بىر مىنۇت',\n      mm: '%d مىنۇت',\n      h: 'بىر سائەت',\n      hh: '%d سائەت',\n      d: 'بىر كۈن',\n      dd: '%d كۈن',\n      M: 'بىر ئاي',\n      MM: '%d ئاي',\n      y: 'بىر يىل',\n      yy: '%d يىل'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '-كۈنى';\n        case 'w':\n        case 'W':\n          return number + '-ھەپتە';\n        default:\n          return number;\n      }\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1st is the first week of the year.\n    }\n  });\n  return ugCn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}