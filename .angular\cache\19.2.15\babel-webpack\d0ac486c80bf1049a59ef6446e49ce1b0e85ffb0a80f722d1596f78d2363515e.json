{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/bank-account/bank-account.service\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nfunction BankAccountListComponent_ng_container_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"app-bank-account-card\", 9);\n    i0.ɵɵlistener(\"deleteCardNotify\", function BankAccountListComponent_ng_container_8_ng_container_1_Template_app_bank_account_card_deleteCardNotify_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteCard($event));\n    })(\"idBankAccount\", function BankAccountListComponent_ng_container_8_ng_container_1_Template_app_bank_account_card_idBankAccount_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editOrAddCardBankaccount($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"item\", item_r3);\n  }\n}\nfunction BankAccountListComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, BankAccountListComponent_ng_container_8_ng_container_1_Template, 3, 1, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.bankAccountListModel);\n  }\n}\nfunction BankAccountListComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nexport let BankAccountListComponent = /*#__PURE__*/(() => {\n  class BankAccountListComponent {\n    translate;\n    bankAccountService;\n    dialog;\n    alertify;\n    idBankAccount = new EventEmitter();\n    bankAccountListModel = [];\n    constructor(translate, bankAccountService, dialog, alertify) {\n      this.translate = translate;\n      this.bankAccountService = bankAccountService;\n      this.dialog = dialog;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.getListBankAccount();\n    }\n    deleteCard(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this bank account\" : \"هل متأكد من حذف هذا حساب البنكي \";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete  bank account' : 'حذف  الحساب البنكي', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.bankAccountService.deleteBankAccount(id).subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getListBankAccount();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    getListBankAccount() {\n      this.bankAccountService.viewBankAccount().subscribe(res => {\n        if (res.isSuccess) {\n          this.bankAccountListModel = res.data;\n          // this.alertify.success(res.message || '');\n        } else {}\n      }, error => {\n        //logging\n      });\n    }\n    editOrAddCardBankaccount(event) {\n      this.idBankAccount.emit(event);\n    }\n    static ɵfac = function BankAccountListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BankAccountListComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.BankAccountService), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BankAccountListComponent,\n      selectors: [[\"app-bank-account-list\"]],\n      outputs: {\n        idBankAccount: \"idBankAccount\"\n      },\n      decls: 10,\n      vars: 5,\n      consts: [[1, \"tab_page\", \"notifacation_page\"], [1, \"row\", \"pt-3\"], [1, \"col-md-12\", \"container_haeder\", \"mb-3\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"col-md-12\", \"max_h\"], [1, \"row\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-4\", \"mb-3\"], [3, \"deleteCardNotify\", \"idBankAccount\", \"item\"], [1, \"No_data\"]],\n      template: function BankAccountListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function BankAccountListComponent_Template_button_click_3_listener() {\n            return ctx.editOrAddCardBankaccount();\n          });\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5);\n          i0.ɵɵtemplate(8, BankAccountListComponent_ng_container_8_Template, 2, 1, \"ng-container\", 6)(9, BankAccountListComponent_ng_container_9_Template, 4, 3, \"ng-container\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, \"BANKACCOUNT.ADDBANK_ACCOUNT\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.bankAccountListModel && ctx.bankAccountListModel.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.bankAccountListModel || ctx.bankAccountListModel && ctx.bankAccountListModel.length == 0);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, TranslateModule, i1.TranslatePipe],\n      styles: [\".notifacation_page[_ngcontent-%COMP%]{height:85vh}.notifacation_page[_ngcontent-%COMP%]   .pt-3[_ngcontent-%COMP%]:lang(en){float:right}.notifacation_page[_ngcontent-%COMP%]   .pt-3[_ngcontent-%COMP%]:lang(ar){float:left}.notifacation_page.tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;margin-top:1rem;border-radius:.313rem;padding:1rem}.notifacation_page[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:70vh;max-height:70vh;overflow-y:auto}.notifacation_page[_ngcontent-%COMP%]   .container_haeder[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.notifacation_page[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--unnamed-color-8e2520) 0% 0% no-repeat padding-box;background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.notifacation_page[_ngcontent-%COMP%]   .haeder[_ngcontent-%COMP%]{font-size:1.25rem;color:#333;font-weight:700}@media (max-width: 64rem){.notifacation_page[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:3.31rem}}\"]\n    });\n  }\n  return BankAccountListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}