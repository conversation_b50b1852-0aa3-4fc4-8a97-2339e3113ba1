{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport * as numberUtil from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\n// Constant\nvar DEFAULT_BAR_BOUND = [20, 140];\nvar ContinuousModel = /** @class */function (_super) {\n  __extends(ContinuousModel, _super);\n  function ContinuousModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    this.resetVisual(function (mappingOption) {\n      mappingOption.mappingMethod = 'linear';\n      mappingOption.dataExtent = this.getExtent();\n    });\n    this._resetRange();\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  ContinuousModel.prototype.resetItemSize = function () {\n    _super.prototype.resetItemSize.apply(this, arguments);\n    var itemSize = this.itemSize;\n    (itemSize[0] == null || isNaN(itemSize[0])) && (itemSize[0] = DEFAULT_BAR_BOUND[0]);\n    (itemSize[1] == null || isNaN(itemSize[1])) && (itemSize[1] = DEFAULT_BAR_BOUND[1]);\n  };\n  /**\r\n   * @private\r\n   */\n  ContinuousModel.prototype._resetRange = function () {\n    var dataExtent = this.getExtent();\n    var range = this.option.range;\n    if (!range || range.auto) {\n      // `range` should always be array (so we don't use other\n      // value like 'auto') for user-friend. (consider getOption).\n      dataExtent.auto = 1;\n      this.option.range = dataExtent;\n    } else if (zrUtil.isArray(range)) {\n      if (range[0] > range[1]) {\n        range.reverse();\n      }\n      range[0] = Math.max(range[0], dataExtent[0]);\n      range[1] = Math.min(range[1], dataExtent[1]);\n    }\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  ContinuousModel.prototype.completeVisualOption = function () {\n    _super.prototype.completeVisualOption.apply(this, arguments);\n    zrUtil.each(this.stateList, function (state) {\n      var symbolSize = this.option.controller[state].symbolSize;\n      if (symbolSize && symbolSize[0] !== symbolSize[1]) {\n        symbolSize[0] = symbolSize[1] / 3; // For good looking.\n      }\n    }, this);\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.setSelected = function (selected) {\n    this.option.range = selected.slice();\n    this._resetRange();\n  };\n  /**\r\n   * @public\r\n   */\n  ContinuousModel.prototype.getSelected = function () {\n    var dataExtent = this.getExtent();\n    var dataInterval = numberUtil.asc((this.get('range') || []).slice());\n    // Clamp\n    dataInterval[0] > dataExtent[1] && (dataInterval[0] = dataExtent[1]);\n    dataInterval[1] > dataExtent[1] && (dataInterval[1] = dataExtent[1]);\n    dataInterval[0] < dataExtent[0] && (dataInterval[0] = dataExtent[0]);\n    dataInterval[1] < dataExtent[0] && (dataInterval[1] = dataExtent[0]);\n    return dataInterval;\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.getValueState = function (value) {\n    var range = this.option.range;\n    var dataExtent = this.getExtent();\n    // When range[0] === dataExtent[0], any value larger than dataExtent[0] maps to 'inRange'.\n    // range[1] is processed likewise.\n    return (range[0] <= dataExtent[0] || range[0] <= value) && (range[1] >= dataExtent[1] || value <= range[1]) ? 'inRange' : 'outOfRange';\n  };\n  ContinuousModel.prototype.findTargetDataIndices = function (range) {\n    var result = [];\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        range[0] <= value && value <= range[1] && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @implement\r\n   */\n  ContinuousModel.prototype.getVisualMeta = function (getColorVisual) {\n    var oVals = getColorStopValues(this, 'outOfRange', this.getExtent());\n    var iVals = getColorStopValues(this, 'inRange', this.option.range.slice());\n    var stops = [];\n    function setStop(value, valueState) {\n      stops.push({\n        value: value,\n        color: getColorVisual(value, valueState)\n      });\n    }\n    // Format to: outOfRange -- inRange -- outOfRange.\n    var iIdx = 0;\n    var oIdx = 0;\n    var iLen = iVals.length;\n    var oLen = oVals.length;\n    for (; oIdx < oLen && (!iVals.length || oVals[oIdx] <= iVals[0]); oIdx++) {\n      // If oVal[oIdx] === iVals[iIdx], oVal[oIdx] should be ignored.\n      if (oVals[oIdx] < iVals[iIdx]) {\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    for (var first = 1; iIdx < iLen; iIdx++, first = 0) {\n      // If range is full, value beyond min, max will be clamped.\n      // make a singularity\n      first && stops.length && setStop(iVals[iIdx], 'outOfRange');\n      setStop(iVals[iIdx], 'inRange');\n    }\n    for (var first = 1; oIdx < oLen; oIdx++) {\n      if (!iVals.length || iVals[iVals.length - 1] < oVals[oIdx]) {\n        // make a singularity\n        if (first) {\n          stops.length && setStop(stops[stops.length - 1].value, 'outOfRange');\n          first = 0;\n        }\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    var stopsLen = stops.length;\n    return {\n      stops: stops,\n      outerColors: [stopsLen ? stops[0].color : 'transparent', stopsLen ? stops[stopsLen - 1].color : 'transparent']\n    };\n  };\n  ContinuousModel.type = 'visualMap.continuous';\n  ContinuousModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    align: 'auto',\n    calculable: false,\n    hoverLink: true,\n    realtime: true,\n    handleIcon: 'path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z',\n    handleSize: '120%',\n    handleStyle: {\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    indicatorIcon: 'circle',\n    indicatorSize: '50%',\n    indicatorStyle: {\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0,0,0,0.2)'\n    }\n    // emphasis: {\n    //     handleStyle: {\n    //         shadowBlur: 3,\n    //         shadowOffsetX: 1,\n    //         shadowOffsetY: 1,\n    //         shadowColor: 'rgba(0,0,0,0.2)'\n    //     }\n    // }\n  });\n  return ContinuousModel;\n}(VisualMapModel);\nfunction getColorStopValues(visualMapModel, valueState, dataExtent) {\n  if (dataExtent[0] === dataExtent[1]) {\n    return dataExtent.slice();\n  }\n  // When using colorHue mapping, it is not linear color any more.\n  // Moreover, canvas gradient seems not to be accurate linear.\n  // FIXME\n  // Should be arbitrary value 100? or based on pixel size?\n  var count = 200;\n  var step = (dataExtent[1] - dataExtent[0]) / count;\n  var value = dataExtent[0];\n  var stopValues = [];\n  for (var i = 0; i <= count && value < dataExtent[1]; i++) {\n    stopValues.push(value);\n    value += step;\n  }\n  stopValues.push(dataExtent[1]);\n  return stopValues;\n}\nexport default ContinuousModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}