{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nimport AxisView from './AxisView.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine'];\nvar SingleAxisView = /** @class */function (_super) {\n  __extends(SingleAxisView, _super);\n  function SingleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisView.type;\n    _this.axisPointerClass = 'SingleAxisPointer';\n    return _this;\n  }\n  SingleAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    var layout = singleAxisHelper.layout(axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    group.add(this._axisGroup);\n    group.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this.group, this._axisGroup, axisModel);\n      }\n    }, this);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  SingleAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  SingleAxisView.type = 'singleAxis';\n  return SingleAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, group, axisGroup, axisModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var lineWidth = lineStyleModel.get('width');\n    var gridRect = axisModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var splitLines = [];\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    for (var i = 0; i < ticksCoords.length; ++i) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var line = new graphic.Line({\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineWidth);\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(line);\n    }\n    var lineStyle = lineStyleModel.getLineStyle(['color']);\n    for (var i = 0; i < splitLines.length; ++i) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyle),\n        silent: true\n      }));\n    }\n  },\n  splitArea: function (axisView, group, axisGroup, axisModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, axisModel);\n  }\n};\nexport default SingleAxisView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}