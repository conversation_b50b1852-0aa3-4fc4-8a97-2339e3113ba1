{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ScientificProblemUsersEnum } from 'src/app/core/enums/scientific-problem-users-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"src/app/core/services/scientific-problem-services/scientific-problem.service\";\nimport * as i4 from \"src/app/core/services/language-services/language.service\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/question-bank-services/question-bank-question.service\";\nimport * as i7 from \"@angular/common\";\nfunction ScientificProblemsComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction ScientificProblemsComponent_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-scientific-problems-grid\", 11);\n    i0.ɵɵlistener(\"adminFilterEvent\", function ScientificProblemsComponent_div_0_div_12_Template_app_scientific_problems_grid_adminFilterEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.filterRequest($event));\n    })(\"deleteUserScProb\", function ScientificProblemsComponent_div_0_div_12_Template_app_scientific_problems_grid_deleteUserScProb_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteUserSingleScProb($event));\n    })(\"addReplyToScProb\", function ScientificProblemsComponent_div_0_div_12_Template_app_scientific_problems_grid_addReplyToScProb_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addReplyToScProb($event));\n    })(\"saveScProbToQuestionBank\", function ScientificProblemsComponent_div_0_div_12_Template_app_scientific_problems_grid_saveScProbToQuestionBank_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveScProbToQuestionBank($event));\n    })(\"deleteListOfScProblems\", function ScientificProblemsComponent_div_0_div_12_Template_app_scientific_problems_grid_deleteListOfScProblems_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteScProblmes());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"totalCount\", ctx_r1.totalCount)(\"adminItems\", ctx_r1.scientificProblems)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"userMode\", ctx_r1.adminCard)(\"adminFilterRequestModel\", ctx_r1.scientificProblemFilter);\n  }\n}\nfunction ScientificProblemsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"app-search-input\", 6);\n    i0.ɵɵlistener(\"searchTerm\", function ScientificProblemsComponent_div_0_Template_app_search_input_searchTerm_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function ScientificProblemsComponent_div_0_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAdvancedSearch());\n    });\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, ScientificProblemsComponent_div_0_div_11_Template, 3, 3, \"div\", 8)(12, ScientificProblemsComponent_div_0_div_12_Template, 2, 5, \"div\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"ADMIN_MESSAGING.SCIENTIFIC_PROBLEM\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 6, \"SCIENTIFIC_PROBLEM.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scientificProblems || ctx_r1.scientificProblems.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scientificProblems && ctx_r1.scientificProblems.length > 0);\n  }\n}\nexport let ScientificProblemsComponent = /*#__PURE__*/(() => {\n  class ScientificProblemsComponent {\n    translate;\n    dialog;\n    scientificProblemService;\n    languageService;\n    alertify;\n    questionBankService;\n    showAddReplyToScProblem = new EventEmitter();\n    showAddScProblemToQuestionBank = new EventEmitter();\n    scientificProblemFilter = {\n      skip: 0,\n      take: 12,\n      sorField: '',\n      ordType: 1,\n      page: 1\n    };\n    resultMessage = {};\n    scientificProblems;\n    adminCard = ScientificProblemUsersEnum.Admin;\n    numberItemsPerRow = 4;\n    totalCount = 0;\n    openAdvancedSearchOverLay = new EventEmitter();\n    //@Output() openAdvancedSearch = new EventEmitter<IStudentSubscriptionFilterRequestModel>();\n    constructor(translate, dialog, scientificProblemService, languageService, alertify, questionBankService) {\n      this.translate = translate;\n      this.dialog = dialog;\n      this.scientificProblemService = scientificProblemService;\n      this.languageService = languageService;\n      this.alertify = alertify;\n      this.questionBankService = questionBankService;\n    }\n    ngOnInit() {\n      this.scientificProblemFilter.sorField = 'createdon';\n      // this.translate.currentLang === LanguageEnum.ar ? 'studfullnamear' : 'studfullnameen'\n      this.getScientificProblems();\n    }\n    getScientificProblems() {\n      this.resultMessage = {};\n      this.scientificProblemService.getScientificMateriaFilter(this.scientificProblemFilter).subscribe(res => {\n        if (res.isSuccess) {\n          this.scientificProblems = res.data;\n          this.scientificProblems?.forEach(function (item) {\n            item.scCreatedOn = item.scCreatedOn ? new Date(item.scCreatedOn).toDateString() : '';\n          });\n          this.totalCount = res.count ? res.count : 0;\n          if (this.scientificProblemFilter.skip > 0 && (!this.scientificProblems || this.scientificProblems.length === 0)) {\n            this.scientificProblemFilter.page -= 1;\n            this.scientificProblemFilter.skip = (this.scientificProblemFilter.page - 1) * this.scientificProblemFilter.take;\n            this.getScientificProblems();\n          }\n        } else {\n          this.resultMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    filterByText(searchKey) {\n      this.scientificProblemFilter.filterText = searchKey;\n      this.getScientificProblems();\n    }\n    filterRequest(event) {\n      this.scientificProblemFilter = event;\n      this.getScientificProblems();\n    }\n    deleteUserSingleScProb(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this scientific problem\" : \"هل متأكد من حذف هذا الإشكال العلمى\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Scinetific Problem' : 'حذف إشكال علمى', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.scientificProblemService.DeleteScientificProblem(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getScientificProblems();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    addReplyToScProb(event) {\n      this.showAddReplyToScProblem.emit(event);\n    }\n    saveScProbToQuestionBank(event) {\n      this.showAddScProblemToQuestionBank.emit(event);\n    }\n    deleteScProblmes() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this list of scientific problems\" : \"هل متأكد من حذف هذه القائمة من الإشكالات العلمية\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete Scientific Problmes' : 'حذف إشكالات علمية', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          let ids = this.scientificProblems?.filter(i => i.checked).map(a => a.id);\n          this.scientificProblemService.deleteListOfScientificProblems(ids).subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getScientificProblems();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    openAdvancedSearch() {\n      this.openAdvancedSearchOverLay.emit(this.scientificProblemFilter);\n    }\n    static ɵfac = function ScientificProblemsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScientificProblemsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.ScientificProblemService), i0.ɵɵdirectiveInject(i4.LanguageService), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.QuestionBankQuestionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ScientificProblemsComponent,\n      selectors: [[\"app-scientific-problems\"]],\n      outputs: {\n        showAddReplyToScProblem: \"showAddReplyToScProblem\",\n        showAddScProblemToQuestionBank: \"showAddScProblemToQuestionBank\",\n        openAdvancedSearchOverLay: \"openAdvancedSearchOverLay\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"scientific-problem\", 4, \"ngIf\"], [1, \"scientific-problem\"], [1, \"row\"], [1, \"col-xl-8\", \"col-lg-8\", \"col-md-5\", \"col-sm-12\", \"text-left\"], [1, \"bold\", \"header_sc\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-7\", \"col-sm-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [3, \"searchTerm\"], [1, \"advanced_search\", \"mb-3\", \"mx-3\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"col-12 internal_scroll\", 4, \"ngIf\"], [1, \"col-12\", \"internal_scroll\"], [3, \"adminFilterEvent\", \"deleteUserScProb\", \"addReplyToScProb\", \"saveScProbToQuestionBank\", \"deleteListOfScProblems\", \"totalCount\", \"adminItems\", \"numberPerRow\", \"userMode\", \"adminFilterRequestModel\"]],\n      template: function ScientificProblemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ScientificProblemsComponent_div_0_Template, 13, 8, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.scientificProblems);\n        }\n      },\n      dependencies: [i7.NgIf, i1.TranslatePipe],\n      styles: [\".scientific-problem[_ngcontent-%COMP%]{background:#fff;border:.063rem solid #b3b3b3;border-radius:1.188rem;opacity:1;padding:1rem;height:97%;margin-top:1rem;margin-bottom:0}.scientific-problem[_ngcontent-%COMP%]   .internal_scroll[_ngcontent-%COMP%]{height:60vh;overflow-y:auto;padding-left:.5rem;overflow-x:hidden;padding-top:.7rem}.scientific-problem[_ngcontent-%COMP%]   .header_sc[_ngcontent-%COMP%]{font-size:1.25rem;color:#333}.scientific-problem[_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%]:lang(en){text-align:left}.scientific-problem[_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%]:lang(ar){text-align:right}.scientific-problem[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}@media all and (min-width: 19in){.internal_scroll[_ngcontent-%COMP%]{height:66vh}}\"]\n    });\n  }\n  return ScientificProblemsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}