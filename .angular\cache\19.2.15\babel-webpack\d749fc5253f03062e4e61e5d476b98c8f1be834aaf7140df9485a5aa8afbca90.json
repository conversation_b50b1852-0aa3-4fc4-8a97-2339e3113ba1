{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { Group } from '../../util/graphic.js';\nvar LineSeriesModel = /** @class */function (_super) {\n  __extends(LineSeriesModel, _super);\n  function LineSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LineSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  LineSeriesModel.prototype.getInitialData = function (option) {\n    if (process.env.NODE_ENV !== 'production') {\n      var coordSys = option.coordinateSystem;\n      if (coordSys !== 'polar' && coordSys !== 'cartesian2d') {\n        throw new Error('Line not support coordinateSystem besides cartesian and polar');\n      }\n    }\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  LineSeriesModel.prototype.getLegendIcon = function (opt) {\n    var group = new Group();\n    var line = createSymbol('line', 0, opt.itemHeight / 2, opt.itemWidth, 0, opt.lineStyle.stroke, false);\n    group.add(line);\n    line.setStyle(opt.lineStyle);\n    var visualType = this.getData().getVisual('symbol');\n    var visualRotate = this.getData().getVisual('symbolRotate');\n    var symbolType = visualType === 'none' ? 'circle' : visualType;\n    // Symbol size is 80% when there is a line\n    var size = opt.itemHeight * 0.8;\n    var symbol = createSymbol(symbolType, (opt.itemWidth - size) / 2, (opt.itemHeight - size) / 2, size, size, opt.itemStyle.fill);\n    group.add(symbol);\n    symbol.setStyle(opt.itemStyle);\n    var symbolRotate = opt.iconRotate === 'inherit' ? visualRotate : opt.iconRotate || 0;\n    symbol.rotation = symbolRotate * Math.PI / 180;\n    symbol.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n    if (symbolType.indexOf('empty') > -1) {\n      symbol.style.stroke = symbol.style.fill;\n      symbol.style.fill = '#fff';\n      symbol.style.lineWidth = 2;\n    }\n    return group;\n  };\n  LineSeriesModel.type = 'series.line';\n  LineSeriesModel.dependencies = ['grid', 'polar'];\n  LineSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 3,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    clip: true,\n    label: {\n      position: 'top'\n    },\n    // itemStyle: {\n    // },\n    endLabel: {\n      show: false,\n      valueAnimation: true,\n      distance: 8\n    },\n    lineStyle: {\n      width: 2,\n      type: 'solid'\n    },\n    emphasis: {\n      scale: true\n    },\n    // areaStyle: {\n    // origin of areaStyle. Valid values:\n    // `'auto'/null/undefined`: from axisLine to data\n    // `'start'`: from min to data\n    // `'end'`: from data to max\n    // origin: 'auto'\n    // },\n    // false, 'start', 'end', 'middle'\n    step: false,\n    // Disabled if step is true\n    smooth: false,\n    smoothMonotone: null,\n    symbol: 'emptyCircle',\n    symbolSize: 4,\n    symbolRotate: null,\n    showSymbol: true,\n    // `false`: follow the label interval strategy.\n    // `true`: show all symbols.\n    // `'auto'`: If possible, show all symbols, otherwise\n    //           follow the label interval strategy.\n    showAllSymbol: 'auto',\n    // Whether to connect break point.\n    connectNulls: false,\n    // Sampling for large data. Can be: 'average', 'max', 'min', 'sum', 'lttb'.\n    sampling: 'none',\n    animationEasing: 'linear',\n    // Disable progressive\n    progressive: 0,\n    hoverLayerThreshold: Infinity,\n    universalTransition: {\n      divideShape: 'clone'\n    },\n    triggerLineEvent: false\n  };\n  return LineSeriesModel;\n}(SeriesModel);\nexport default LineSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}