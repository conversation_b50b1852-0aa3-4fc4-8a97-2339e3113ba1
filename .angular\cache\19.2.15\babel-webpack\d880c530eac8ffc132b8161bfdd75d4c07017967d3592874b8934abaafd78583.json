{"ast": null, "code": "var _asyncToGenerator = require(\"C:/Users/<USER>/Desktop/data/Projects/Mostaneer/huffadh-white-label-app - Copy/node_modules/@babel/runtime/helpers/asyncToGenerator.js\").default;\n/**\n * @licstart The following is the entire license notice for the\n * JavaScript code in this page\n *\n * Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * @licend The above is the entire license notice for the\n * JavaScript code in this page\n */\n\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define(\"pdfjs-dist/web/pdf_viewer\", [], factory);else if (typeof exports === 'object') exports[\"pdfjs-dist/web/pdf_viewer\"] = factory();else root[\"pdfjs-dist/web/pdf_viewer\"] = root.pdfjsViewer = factory();\n})(this, () => {\n  return /******/(() => {\n    // webpackBootstrap\n    /******/\n    \"use strict\";\n\n    /******/\n    var __webpack_modules__ = [\n      /* 0 */\n    , (/* 1 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.DefaultXfaLayerFactory = exports.DefaultTextLayerFactory = exports.DefaultStructTreeLayerFactory = exports.DefaultAnnotationLayerFactory = void 0;\n      var _annotation_layer_builder = __w_pdfjs_require__(2);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      var _pdf_link_service = __w_pdfjs_require__(5);\n      var _struct_tree_layer_builder = __w_pdfjs_require__(7);\n      var _text_layer_builder = __w_pdfjs_require__(8);\n      var _xfa_layer_builder = __w_pdfjs_require__(9);\n      class DefaultAnnotationLayerFactory {\n        createAnnotationLayerBuilder(pageDiv, pdfPage, annotationStorage = null, imageResourcesPath = \"\", renderForms = true, l10n = _l10n_utils.NullL10n, enableScripting = false, hasJSActionsPromise = null, mouseState = null, fieldObjectsPromise = null, annotationCanvasMap = null) {\n          return new _annotation_layer_builder.AnnotationLayerBuilder({\n            pageDiv,\n            pdfPage,\n            imageResourcesPath,\n            renderForms,\n            linkService: new _pdf_link_service.SimpleLinkService(),\n            l10n,\n            annotationStorage,\n            enableScripting,\n            hasJSActionsPromise,\n            fieldObjectsPromise,\n            mouseState,\n            annotationCanvasMap\n          });\n        }\n      }\n      exports.DefaultAnnotationLayerFactory = DefaultAnnotationLayerFactory;\n      class DefaultStructTreeLayerFactory {\n        createStructTreeLayerBuilder(pdfPage) {\n          return new _struct_tree_layer_builder.StructTreeLayerBuilder({\n            pdfPage\n          });\n        }\n      }\n      exports.DefaultStructTreeLayerFactory = DefaultStructTreeLayerFactory;\n      class DefaultTextLayerFactory {\n        createTextLayerBuilder(textLayerDiv, pageIndex, viewport, enhanceTextSelection = false, eventBus, highlighter) {\n          return new _text_layer_builder.TextLayerBuilder({\n            textLayerDiv,\n            pageIndex,\n            viewport,\n            enhanceTextSelection,\n            eventBus,\n            highlighter\n          });\n        }\n      }\n      exports.DefaultTextLayerFactory = DefaultTextLayerFactory;\n      class DefaultXfaLayerFactory {\n        createXfaLayerBuilder(pageDiv, pdfPage, annotationStorage = null, xfaHtml = null) {\n          return new _xfa_layer_builder.XfaLayerBuilder({\n            pageDiv,\n            pdfPage,\n            annotationStorage,\n            linkService: new _pdf_link_service.SimpleLinkService(),\n            xfaHtml\n          });\n        }\n      }\n      exports.DefaultXfaLayerFactory = DefaultXfaLayerFactory;\n\n      /***/\n    }), (/* 2 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.AnnotationLayerBuilder = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      class AnnotationLayerBuilder {\n        constructor({\n          pageDiv,\n          pdfPage,\n          linkService,\n          downloadManager,\n          annotationStorage = null,\n          imageResourcesPath = \"\",\n          renderForms = true,\n          l10n = _l10n_utils.NullL10n,\n          enableScripting = false,\n          hasJSActionsPromise = null,\n          fieldObjectsPromise = null,\n          mouseState = null,\n          annotationCanvasMap = null\n        }) {\n          this.pageDiv = pageDiv;\n          this.pdfPage = pdfPage;\n          this.linkService = linkService;\n          this.downloadManager = downloadManager;\n          this.imageResourcesPath = imageResourcesPath;\n          this.renderForms = renderForms;\n          this.l10n = l10n;\n          this.annotationStorage = annotationStorage;\n          this.enableScripting = enableScripting;\n          this._hasJSActionsPromise = hasJSActionsPromise;\n          this._fieldObjectsPromise = fieldObjectsPromise;\n          this._mouseState = mouseState;\n          this._annotationCanvasMap = annotationCanvasMap;\n          this.div = null;\n          this._cancelled = false;\n        }\n        render(_x) {\n          var _this = this;\n          return _asyncToGenerator(function* (viewport, intent = \"display\") {\n            const [annotations, hasJSActions = false, fieldObjects = null] = yield Promise.all([_this.pdfPage.getAnnotations({\n              intent\n            }), _this._hasJSActionsPromise, _this._fieldObjectsPromise]);\n            if (_this._cancelled || annotations.length === 0) {\n              return;\n            }\n            const parameters = {\n              viewport: viewport.clone({\n                dontFlip: true\n              }),\n              div: _this.div,\n              annotations,\n              page: _this.pdfPage,\n              imageResourcesPath: _this.imageResourcesPath,\n              renderForms: _this.renderForms,\n              linkService: _this.linkService,\n              downloadManager: _this.downloadManager,\n              annotationStorage: _this.annotationStorage,\n              enableScripting: _this.enableScripting,\n              hasJSActions,\n              fieldObjects,\n              mouseState: _this._mouseState,\n              annotationCanvasMap: _this._annotationCanvasMap\n            };\n            if (_this.div) {\n              _pdfjsLib.AnnotationLayer.update(parameters);\n            } else {\n              _this.div = document.createElement(\"div\");\n              _this.div.className = \"annotationLayer\";\n              _this.pageDiv.appendChild(_this.div);\n              parameters.div = _this.div;\n              _pdfjsLib.AnnotationLayer.render(parameters);\n              _this.l10n.translate(_this.div);\n            }\n          }).apply(this, arguments);\n        }\n        cancel() {\n          this._cancelled = true;\n        }\n        hide() {\n          if (!this.div) {\n            return;\n          }\n          this.div.hidden = true;\n        }\n      }\n      exports.AnnotationLayerBuilder = AnnotationLayerBuilder;\n\n      /***/\n    }), (/* 3 */\n    /***/module => {\n      let pdfjsLib;\n      if (typeof window !== \"undefined\" && window[\"pdfjs-dist/build/pdf\"]) {\n        pdfjsLib = window[\"pdfjs-dist/build/pdf\"];\n      } else {\n        pdfjsLib = require(\"../build/pdf.js\");\n      }\n      module.exports = pdfjsLib;\n\n      /***/\n    }), (/* 4 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.NullL10n = void 0;\n      exports.fixupLangCode = fixupLangCode;\n      exports.getL10nFallback = getL10nFallback;\n      const DEFAULT_L10N_STRINGS = {\n        of_pages: \"of {{pagesCount}}\",\n        page_of_pages: \"({{pageNumber}} of {{pagesCount}})\",\n        document_properties_kb: \"{{size_kb}} KB ({{size_b}} bytes)\",\n        document_properties_mb: \"{{size_mb}} MB ({{size_b}} bytes)\",\n        document_properties_date_string: \"{{date}}, {{time}}\",\n        document_properties_page_size_unit_inches: \"in\",\n        document_properties_page_size_unit_millimeters: \"mm\",\n        document_properties_page_size_orientation_portrait: \"portrait\",\n        document_properties_page_size_orientation_landscape: \"landscape\",\n        document_properties_page_size_name_a3: \"A3\",\n        document_properties_page_size_name_a4: \"A4\",\n        document_properties_page_size_name_letter: \"Letter\",\n        document_properties_page_size_name_legal: \"Legal\",\n        document_properties_page_size_dimension_string: \"{{width}} × {{height}} {{unit}} ({{orientation}})\",\n        document_properties_page_size_dimension_name_string: \"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})\",\n        document_properties_linearized_yes: \"Yes\",\n        document_properties_linearized_no: \"No\",\n        print_progress_percent: \"{{progress}}%\",\n        \"toggle_sidebar.title\": \"Toggle Sidebar\",\n        \"toggle_sidebar_notification2.title\": \"Toggle Sidebar (document contains outline/attachments/layers)\",\n        additional_layers: \"Additional Layers\",\n        page_landmark: \"Page {{page}}\",\n        thumb_page_title: \"Page {{page}}\",\n        thumb_page_canvas: \"Thumbnail of Page {{page}}\",\n        find_reached_top: \"Reached top of document, continued from bottom\",\n        find_reached_bottom: \"Reached end of document, continued from top\",\n        \"find_match_count[one]\": \"{{current}} of {{total}} match\",\n        \"find_match_count[other]\": \"{{current}} of {{total}} matches\",\n        \"find_match_count_limit[one]\": \"More than {{limit}} match\",\n        \"find_match_count_limit[other]\": \"More than {{limit}} matches\",\n        find_not_found: \"Phrase not found\",\n        error_version_info: \"PDF.js v{{version}} (build: {{build}})\",\n        error_message: \"Message: {{message}}\",\n        error_stack: \"Stack: {{stack}}\",\n        error_file: \"File: {{file}}\",\n        error_line: \"Line: {{line}}\",\n        rendering_error: \"An error occurred while rendering the page.\",\n        page_scale_width: \"Page Width\",\n        page_scale_fit: \"Page Fit\",\n        page_scale_auto: \"Automatic Zoom\",\n        page_scale_actual: \"Actual Size\",\n        page_scale_percent: \"{{scale}}%\",\n        loading: \"Loading…\",\n        loading_error: \"An error occurred while loading the PDF.\",\n        invalid_file_error: \"Invalid or corrupted PDF file.\",\n        missing_file_error: \"Missing PDF file.\",\n        unexpected_response_error: \"Unexpected server response.\",\n        printing_not_supported: \"Warning: Printing is not fully supported by this browser.\",\n        printing_not_ready: \"Warning: The PDF is not fully loaded for printing.\",\n        web_fonts_disabled: \"Web fonts are disabled: unable to use embedded PDF fonts.\"\n      };\n      function getL10nFallback(key, args) {\n        switch (key) {\n          case \"find_match_count\":\n            key = `find_match_count[${args.total === 1 ? \"one\" : \"other\"}]`;\n            break;\n          case \"find_match_count_limit\":\n            key = `find_match_count_limit[${args.limit === 1 ? \"one\" : \"other\"}]`;\n            break;\n        }\n        return DEFAULT_L10N_STRINGS[key] || \"\";\n      }\n      const PARTIAL_LANG_CODES = {\n        en: \"en-US\",\n        es: \"es-ES\",\n        fy: \"fy-NL\",\n        ga: \"ga-IE\",\n        gu: \"gu-IN\",\n        hi: \"hi-IN\",\n        hy: \"hy-AM\",\n        nb: \"nb-NO\",\n        ne: \"ne-NP\",\n        nn: \"nn-NO\",\n        pa: \"pa-IN\",\n        pt: \"pt-PT\",\n        sv: \"sv-SE\",\n        zh: \"zh-CN\"\n      };\n      function fixupLangCode(langCode) {\n        return PARTIAL_LANG_CODES[langCode?.toLowerCase()] || langCode;\n      }\n      function formatL10nValue(text, args) {\n        if (!args) {\n          return text;\n        }\n        return text.replace(/\\{\\{\\s*(\\w+)\\s*\\}\\}/g, (all, name) => {\n          return name in args ? args[name] : \"{{\" + name + \"}}\";\n        });\n      }\n      const NullL10n = {\n        getLanguage() {\n          return _asyncToGenerator(function* () {\n            return \"en-us\";\n          })();\n        },\n        getDirection() {\n          return _asyncToGenerator(function* () {\n            return \"ltr\";\n          })();\n        },\n        get(_x2) {\n          return _asyncToGenerator(function* (key, args = null, fallback = getL10nFallback(key, args)) {\n            return formatL10nValue(fallback, args);\n          }).apply(this, arguments);\n        },\n        translate(element) {\n          return _asyncToGenerator(function* () {})();\n        }\n      };\n      exports.NullL10n = NullL10n;\n\n      /***/\n    }), (/* 5 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.SimpleLinkService = exports.PDFLinkService = exports.LinkTarget = void 0;\n      var _ui_utils = __w_pdfjs_require__(6);\n      const DEFAULT_LINK_REL = \"noopener noreferrer nofollow\";\n      const LinkTarget = {\n        NONE: 0,\n        SELF: 1,\n        BLANK: 2,\n        PARENT: 3,\n        TOP: 4\n      };\n      exports.LinkTarget = LinkTarget;\n      function addLinkAttributes(link, {\n        url,\n        target,\n        rel,\n        enabled = true\n      } = {}) {\n        if (!url || typeof url !== \"string\") {\n          throw new Error('A valid \"url\" parameter must provided.');\n        }\n        const urlNullRemoved = (0, _ui_utils.removeNullCharacters)(url);\n        if (enabled) {\n          link.href = link.title = urlNullRemoved;\n        } else {\n          link.href = \"\";\n          link.title = `Disabled: ${urlNullRemoved}`;\n          link.onclick = () => {\n            return false;\n          };\n        }\n        let targetStr = \"\";\n        switch (target) {\n          case LinkTarget.NONE:\n            break;\n          case LinkTarget.SELF:\n            targetStr = \"_self\";\n            break;\n          case LinkTarget.BLANK:\n            targetStr = \"_blank\";\n            break;\n          case LinkTarget.PARENT:\n            targetStr = \"_parent\";\n            break;\n          case LinkTarget.TOP:\n            targetStr = \"_top\";\n            break;\n        }\n        link.target = targetStr;\n        link.rel = typeof rel === \"string\" ? rel : DEFAULT_LINK_REL;\n      }\n      class PDFLinkService {\n        #pagesRefCache = new Map();\n        constructor({\n          eventBus,\n          externalLinkTarget = null,\n          externalLinkRel = null,\n          ignoreDestinationZoom = false\n        } = {}) {\n          this.eventBus = eventBus;\n          this.externalLinkTarget = externalLinkTarget;\n          this.externalLinkRel = externalLinkRel;\n          this.externalLinkEnabled = true;\n          this._ignoreDestinationZoom = ignoreDestinationZoom;\n          this.baseUrl = null;\n          this.pdfDocument = null;\n          this.pdfViewer = null;\n          this.pdfHistory = null;\n        }\n        setDocument(pdfDocument, baseUrl = null) {\n          this.baseUrl = baseUrl;\n          this.pdfDocument = pdfDocument;\n          this.#pagesRefCache.clear();\n        }\n        setViewer(pdfViewer) {\n          this.pdfViewer = pdfViewer;\n        }\n        setHistory(pdfHistory) {\n          this.pdfHistory = pdfHistory;\n        }\n        get pagesCount() {\n          return this.pdfDocument ? this.pdfDocument.numPages : 0;\n        }\n        get page() {\n          return this.pdfViewer.currentPageNumber;\n        }\n        set page(value) {\n          this.pdfViewer.currentPageNumber = value;\n        }\n        get rotation() {\n          return this.pdfViewer.pagesRotation;\n        }\n        set rotation(value) {\n          this.pdfViewer.pagesRotation = value;\n        }\n        #goToDestinationHelper(rawDest, namedDest = null, explicitDest) {\n          const destRef = explicitDest[0];\n          let pageNumber;\n          if (typeof destRef === \"object\" && destRef !== null) {\n            pageNumber = this._cachedPageNumber(destRef);\n            if (!pageNumber) {\n              this.pdfDocument.getPageIndex(destRef).then(pageIndex => {\n                this.cachePageRef(pageIndex + 1, destRef);\n                this.#goToDestinationHelper(rawDest, namedDest, explicitDest);\n              }).catch(() => {\n                console.error(`PDFLinkService.#goToDestinationHelper: \"${destRef}\" is not ` + `a valid page reference, for dest=\"${rawDest}\".`);\n              });\n              return;\n            }\n          } else if (Number.isInteger(destRef)) {\n            pageNumber = destRef + 1;\n          } else {\n            console.error(`PDFLinkService.#goToDestinationHelper: \"${destRef}\" is not ` + `a valid destination reference, for dest=\"${rawDest}\".`);\n            return;\n          }\n          if (!pageNumber || pageNumber < 1 || pageNumber > this.pagesCount) {\n            console.error(`PDFLinkService.#goToDestinationHelper: \"${pageNumber}\" is not ` + `a valid page number, for dest=\"${rawDest}\".`);\n            return;\n          }\n          if (this.pdfHistory) {\n            this.pdfHistory.pushCurrentPosition();\n            this.pdfHistory.push({\n              namedDest,\n              explicitDest,\n              pageNumber\n            });\n          }\n          this.pdfViewer.scrollPageIntoView({\n            pageNumber,\n            destArray: explicitDest,\n            ignoreDestinationZoom: this._ignoreDestinationZoom\n          });\n        }\n        goToDestination(dest) {\n          var _this2 = this;\n          return _asyncToGenerator(function* () {\n            if (!_this2.pdfDocument) {\n              return;\n            }\n            let namedDest, explicitDest;\n            if (typeof dest === \"string\") {\n              namedDest = dest;\n              explicitDest = yield _this2.pdfDocument.getDestination(dest);\n            } else {\n              namedDest = null;\n              explicitDest = yield dest;\n            }\n            if (!Array.isArray(explicitDest)) {\n              console.error(`PDFLinkService.goToDestination: \"${explicitDest}\" is not ` + `a valid destination array, for dest=\"${dest}\".`);\n              return;\n            }\n            _this2.#goToDestinationHelper(dest, namedDest, explicitDest);\n          })();\n        }\n        goToPage(val) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          const pageNumber = typeof val === \"string\" && this.pdfViewer.pageLabelToPageNumber(val) || val | 0;\n          if (!(Number.isInteger(pageNumber) && pageNumber > 0 && pageNumber <= this.pagesCount)) {\n            console.error(`PDFLinkService.goToPage: \"${val}\" is not a valid page.`);\n            return;\n          }\n          if (this.pdfHistory) {\n            this.pdfHistory.pushCurrentPosition();\n            this.pdfHistory.pushPage(pageNumber);\n          }\n          this.pdfViewer.scrollPageIntoView({\n            pageNumber\n          });\n        }\n        addLinkAttributes(link, url, newWindow = false) {\n          addLinkAttributes(link, {\n            url,\n            target: newWindow ? LinkTarget.BLANK : this.externalLinkTarget,\n            rel: this.externalLinkRel,\n            enabled: this.externalLinkEnabled\n          });\n        }\n        getDestinationHash(dest) {\n          if (typeof dest === \"string\") {\n            if (dest.length > 0) {\n              return this.getAnchorUrl(\"#\" + escape(dest));\n            }\n          } else if (Array.isArray(dest)) {\n            const str = JSON.stringify(dest);\n            if (str.length > 0) {\n              return this.getAnchorUrl(\"#\" + escape(str));\n            }\n          }\n          return this.getAnchorUrl(\"\");\n        }\n        getAnchorUrl(anchor) {\n          return (this.baseUrl || \"\") + anchor;\n        }\n        setHash(hash) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          let pageNumber, dest;\n          if (hash.includes(\"=\")) {\n            const params = (0, _ui_utils.parseQueryString)(hash);\n            if (params.has(\"search\")) {\n              this.eventBus.dispatch(\"findfromurlhash\", {\n                source: this,\n                query: params.get(\"search\").replace(/\"/g, \"\"),\n                phraseSearch: params.get(\"phrase\") === \"true\"\n              });\n            }\n            if (params.has(\"page\")) {\n              pageNumber = params.get(\"page\") | 0 || 1;\n            }\n            if (params.has(\"zoom\")) {\n              const zoomArgs = params.get(\"zoom\").split(\",\");\n              const zoomArg = zoomArgs[0];\n              const zoomArgNumber = parseFloat(zoomArg);\n              if (!zoomArg.includes(\"Fit\")) {\n                dest = [null, {\n                  name: \"XYZ\"\n                }, zoomArgs.length > 1 ? zoomArgs[1] | 0 : null, zoomArgs.length > 2 ? zoomArgs[2] | 0 : null, zoomArgNumber ? zoomArgNumber / 100 : zoomArg];\n              } else {\n                if (zoomArg === \"Fit\" || zoomArg === \"FitB\") {\n                  dest = [null, {\n                    name: zoomArg\n                  }];\n                } else if (zoomArg === \"FitH\" || zoomArg === \"FitBH\" || zoomArg === \"FitV\" || zoomArg === \"FitBV\") {\n                  dest = [null, {\n                    name: zoomArg\n                  }, zoomArgs.length > 1 ? zoomArgs[1] | 0 : null];\n                } else if (zoomArg === \"FitR\") {\n                  if (zoomArgs.length !== 5) {\n                    console.error('PDFLinkService.setHash: Not enough parameters for \"FitR\".');\n                  } else {\n                    dest = [null, {\n                      name: zoomArg\n                    }, zoomArgs[1] | 0, zoomArgs[2] | 0, zoomArgs[3] | 0, zoomArgs[4] | 0];\n                  }\n                } else {\n                  console.error(`PDFLinkService.setHash: \"${zoomArg}\" is not a valid zoom value.`);\n                }\n              }\n            }\n            if (dest) {\n              this.pdfViewer.scrollPageIntoView({\n                pageNumber: pageNumber || this.page,\n                destArray: dest,\n                allowNegativeOffset: true\n              });\n            } else if (pageNumber) {\n              this.page = pageNumber;\n            }\n            if (params.has(\"pagemode\")) {\n              this.eventBus.dispatch(\"pagemode\", {\n                source: this,\n                mode: params.get(\"pagemode\")\n              });\n            }\n            if (params.has(\"nameddest\")) {\n              this.goToDestination(params.get(\"nameddest\"));\n            }\n          } else {\n            dest = unescape(hash);\n            try {\n              dest = JSON.parse(dest);\n              if (!Array.isArray(dest)) {\n                dest = dest.toString();\n              }\n            } catch (ex) {}\n            if (typeof dest === \"string\" || PDFLinkService.#isValidExplicitDestination(dest)) {\n              this.goToDestination(dest);\n              return;\n            }\n            console.error(`PDFLinkService.setHash: \"${unescape(hash)}\" is not a valid destination.`);\n          }\n        }\n        executeNamedAction(action) {\n          switch (action) {\n            case \"GoBack\":\n              this.pdfHistory?.back();\n              break;\n            case \"GoForward\":\n              this.pdfHistory?.forward();\n              break;\n            case \"NextPage\":\n              this.pdfViewer.nextPage();\n              break;\n            case \"PrevPage\":\n              this.pdfViewer.previousPage();\n              break;\n            case \"LastPage\":\n              this.page = this.pagesCount;\n              break;\n            case \"FirstPage\":\n              this.page = 1;\n              break;\n            default:\n              break;\n          }\n          this.eventBus.dispatch(\"namedaction\", {\n            source: this,\n            action\n          });\n        }\n        cachePageRef(pageNum, pageRef) {\n          if (!pageRef) {\n            return;\n          }\n          const refStr = pageRef.gen === 0 ? `${pageRef.num}R` : `${pageRef.num}R${pageRef.gen}`;\n          this.#pagesRefCache.set(refStr, pageNum);\n        }\n        _cachedPageNumber(pageRef) {\n          if (!pageRef) {\n            return null;\n          }\n          const refStr = pageRef.gen === 0 ? `${pageRef.num}R` : `${pageRef.num}R${pageRef.gen}`;\n          return this.#pagesRefCache.get(refStr) || null;\n        }\n        isPageVisible(pageNumber) {\n          return this.pdfViewer.isPageVisible(pageNumber);\n        }\n        isPageCached(pageNumber) {\n          return this.pdfViewer.isPageCached(pageNumber);\n        }\n        static #isValidExplicitDestination(dest) {\n          if (!Array.isArray(dest)) {\n            return false;\n          }\n          const destLength = dest.length;\n          if (destLength < 2) {\n            return false;\n          }\n          const page = dest[0];\n          if (!(typeof page === \"object\" && Number.isInteger(page.num) && Number.isInteger(page.gen)) && !(Number.isInteger(page) && page >= 0)) {\n            return false;\n          }\n          const zoom = dest[1];\n          if (!(typeof zoom === \"object\" && typeof zoom.name === \"string\")) {\n            return false;\n          }\n          let allowNull = true;\n          switch (zoom.name) {\n            case \"XYZ\":\n              if (destLength !== 5) {\n                return false;\n              }\n              break;\n            case \"Fit\":\n            case \"FitB\":\n              return destLength === 2;\n            case \"FitH\":\n            case \"FitBH\":\n            case \"FitV\":\n            case \"FitBV\":\n              if (destLength !== 3) {\n                return false;\n              }\n              break;\n            case \"FitR\":\n              if (destLength !== 6) {\n                return false;\n              }\n              allowNull = false;\n              break;\n            default:\n              return false;\n          }\n          for (let i = 2; i < destLength; i++) {\n            const param = dest[i];\n            if (!(typeof param === \"number\" || allowNull && param === null)) {\n              return false;\n            }\n          }\n          return true;\n        }\n      }\n      exports.PDFLinkService = PDFLinkService;\n      class SimpleLinkService {\n        constructor() {\n          this.externalLinkEnabled = true;\n        }\n        get pagesCount() {\n          return 0;\n        }\n        get page() {\n          return 0;\n        }\n        set page(value) {}\n        get rotation() {\n          return 0;\n        }\n        set rotation(value) {}\n        goToDestination(dest) {\n          return _asyncToGenerator(function* () {})();\n        }\n        goToPage(val) {}\n        addLinkAttributes(link, url, newWindow = false) {\n          addLinkAttributes(link, {\n            url,\n            enabled: this.externalLinkEnabled\n          });\n        }\n        getDestinationHash(dest) {\n          return \"#\";\n        }\n        getAnchorUrl(hash) {\n          return \"#\";\n        }\n        setHash(hash) {}\n        executeNamedAction(action) {}\n        cachePageRef(pageNum, pageRef) {}\n        isPageVisible(pageNumber) {\n          return true;\n        }\n        isPageCached(pageNumber) {\n          return true;\n        }\n      }\n      exports.SimpleLinkService = SimpleLinkService;\n\n      /***/\n    }), (/* 6 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.animationStarted = exports.VERTICAL_PADDING = exports.UNKNOWN_SCALE = exports.TextLayerMode = exports.SpreadMode = exports.SidebarView = exports.ScrollMode = exports.SCROLLBAR_PADDING = exports.RenderingStates = exports.RendererType = exports.ProgressBar = exports.PresentationModeState = exports.OutputScale = exports.MIN_SCALE = exports.MAX_SCALE = exports.MAX_AUTO_SCALE = exports.DEFAULT_SCALE_VALUE = exports.DEFAULT_SCALE_DELTA = exports.DEFAULT_SCALE = exports.AutoPrintRegExp = void 0;\n      exports.apiPageLayoutToViewerModes = apiPageLayoutToViewerModes;\n      exports.apiPageModeToSidebarView = apiPageModeToSidebarView;\n      exports.approximateFraction = approximateFraction;\n      exports.backtrackBeforeAllVisibleElements = backtrackBeforeAllVisibleElements;\n      exports.binarySearchFirstItem = binarySearchFirstItem;\n      exports.getActiveOrFocusedElement = getActiveOrFocusedElement;\n      exports.getPageSizeInches = getPageSizeInches;\n      exports.getVisibleElements = getVisibleElements;\n      exports.isPortraitOrientation = isPortraitOrientation;\n      exports.isValidRotation = isValidRotation;\n      exports.isValidScrollMode = isValidScrollMode;\n      exports.isValidSpreadMode = isValidSpreadMode;\n      exports.noContextMenuHandler = noContextMenuHandler;\n      exports.normalizeWheelEventDelta = normalizeWheelEventDelta;\n      exports.normalizeWheelEventDirection = normalizeWheelEventDirection;\n      exports.parseQueryString = parseQueryString;\n      exports.removeNullCharacters = removeNullCharacters;\n      exports.roundToDivide = roundToDivide;\n      exports.scrollIntoView = scrollIntoView;\n      exports.watchScroll = watchScroll;\n      const DEFAULT_SCALE_VALUE = \"auto\";\n      exports.DEFAULT_SCALE_VALUE = DEFAULT_SCALE_VALUE;\n      const DEFAULT_SCALE = 1.0;\n      exports.DEFAULT_SCALE = DEFAULT_SCALE;\n      const DEFAULT_SCALE_DELTA = 1.1;\n      exports.DEFAULT_SCALE_DELTA = DEFAULT_SCALE_DELTA;\n      const MIN_SCALE = 0.1;\n      exports.MIN_SCALE = MIN_SCALE;\n      const MAX_SCALE = 10.0;\n      exports.MAX_SCALE = MAX_SCALE;\n      const UNKNOWN_SCALE = 0;\n      exports.UNKNOWN_SCALE = UNKNOWN_SCALE;\n      const MAX_AUTO_SCALE = 1.25;\n      exports.MAX_AUTO_SCALE = MAX_AUTO_SCALE;\n      const SCROLLBAR_PADDING = 40;\n      exports.SCROLLBAR_PADDING = SCROLLBAR_PADDING;\n      const VERTICAL_PADDING = 5;\n      exports.VERTICAL_PADDING = VERTICAL_PADDING;\n      const RenderingStates = {\n        INITIAL: 0,\n        RUNNING: 1,\n        PAUSED: 2,\n        FINISHED: 3\n      };\n      exports.RenderingStates = RenderingStates;\n      const PresentationModeState = {\n        UNKNOWN: 0,\n        NORMAL: 1,\n        CHANGING: 2,\n        FULLSCREEN: 3\n      };\n      exports.PresentationModeState = PresentationModeState;\n      const SidebarView = {\n        UNKNOWN: -1,\n        NONE: 0,\n        THUMBS: 1,\n        OUTLINE: 2,\n        ATTACHMENTS: 3,\n        LAYERS: 4\n      };\n      exports.SidebarView = SidebarView;\n      const RendererType = {\n        CANVAS: \"canvas\",\n        SVG: \"svg\"\n      };\n      exports.RendererType = RendererType;\n      const TextLayerMode = {\n        DISABLE: 0,\n        ENABLE: 1,\n        ENABLE_ENHANCE: 2\n      };\n      exports.TextLayerMode = TextLayerMode;\n      const ScrollMode = {\n        UNKNOWN: -1,\n        VERTICAL: 0,\n        HORIZONTAL: 1,\n        WRAPPED: 2,\n        PAGE: 3\n      };\n      exports.ScrollMode = ScrollMode;\n      const SpreadMode = {\n        UNKNOWN: -1,\n        NONE: 0,\n        ODD: 1,\n        EVEN: 2\n      };\n      exports.SpreadMode = SpreadMode;\n      const AutoPrintRegExp = /\\bprint\\s*\\(/;\n      exports.AutoPrintRegExp = AutoPrintRegExp;\n      class OutputScale {\n        constructor() {\n          const pixelRatio = window.devicePixelRatio || 1;\n          this.sx = pixelRatio;\n          this.sy = pixelRatio;\n        }\n        get scaled() {\n          return this.sx !== 1 || this.sy !== 1;\n        }\n      }\n      exports.OutputScale = OutputScale;\n      function scrollIntoView(element, spot, scrollMatches = false) {\n        let parent = element.offsetParent;\n        if (!parent) {\n          console.error(\"offsetParent is not set -- cannot scroll\");\n          return;\n        }\n        let offsetY = element.offsetTop + element.clientTop;\n        let offsetX = element.offsetLeft + element.clientLeft;\n        while (parent.clientHeight === parent.scrollHeight && parent.clientWidth === parent.scrollWidth || scrollMatches && (parent.classList.contains(\"markedContent\") || getComputedStyle(parent).overflow === \"hidden\")) {\n          offsetY += parent.offsetTop;\n          offsetX += parent.offsetLeft;\n          parent = parent.offsetParent;\n          if (!parent) {\n            return;\n          }\n        }\n        if (spot) {\n          if (spot.top !== undefined) {\n            offsetY += spot.top;\n          }\n          if (spot.left !== undefined) {\n            offsetX += spot.left;\n            parent.scrollLeft = offsetX;\n          }\n        }\n        parent.scrollTop = offsetY;\n      }\n      function watchScroll(viewAreaElement, callback) {\n        const debounceScroll = function (evt) {\n          if (rAF) {\n            return;\n          }\n          rAF = window.requestAnimationFrame(function viewAreaElementScrolled() {\n            rAF = null;\n            const currentX = viewAreaElement.scrollLeft;\n            const lastX = state.lastX;\n            if (currentX !== lastX) {\n              state.right = currentX > lastX;\n            }\n            state.lastX = currentX;\n            const currentY = viewAreaElement.scrollTop;\n            const lastY = state.lastY;\n            if (currentY !== lastY) {\n              state.down = currentY > lastY;\n            }\n            state.lastY = currentY;\n            callback(state);\n          });\n        };\n        const state = {\n          right: true,\n          down: true,\n          lastX: viewAreaElement.scrollLeft,\n          lastY: viewAreaElement.scrollTop,\n          _eventHandler: debounceScroll\n        };\n        let rAF = null;\n        viewAreaElement.addEventListener(\"scroll\", debounceScroll, true);\n        return state;\n      }\n      function parseQueryString(query) {\n        const params = new Map();\n        for (const [key, value] of new URLSearchParams(query)) {\n          params.set(key.toLowerCase(), value);\n        }\n        return params;\n      }\n      const NullCharactersRegExp = /\\x00/g;\n      const InvisibleCharactersRegExp = /[\\x01-\\x1F]/g;\n      function removeNullCharacters(str, replaceInvisible = false) {\n        if (typeof str !== \"string\") {\n          console.error(`The argument must be a string.`);\n          return str;\n        }\n        if (replaceInvisible) {\n          str = str.replace(InvisibleCharactersRegExp, \" \");\n        }\n        return str.replace(NullCharactersRegExp, \"\");\n      }\n      function binarySearchFirstItem(items, condition, start = 0) {\n        let minIndex = start;\n        let maxIndex = items.length - 1;\n        if (maxIndex < 0 || !condition(items[maxIndex])) {\n          return items.length;\n        }\n        if (condition(items[minIndex])) {\n          return minIndex;\n        }\n        while (minIndex < maxIndex) {\n          const currentIndex = minIndex + maxIndex >> 1;\n          const currentItem = items[currentIndex];\n          if (condition(currentItem)) {\n            maxIndex = currentIndex;\n          } else {\n            minIndex = currentIndex + 1;\n          }\n        }\n        return minIndex;\n      }\n      function approximateFraction(x) {\n        if (Math.floor(x) === x) {\n          return [x, 1];\n        }\n        const xinv = 1 / x;\n        const limit = 8;\n        if (xinv > limit) {\n          return [1, limit];\n        } else if (Math.floor(xinv) === xinv) {\n          return [1, xinv];\n        }\n        const x_ = x > 1 ? xinv : x;\n        let a = 0,\n          b = 1,\n          c = 1,\n          d = 1;\n        while (true) {\n          const p = a + c,\n            q = b + d;\n          if (q > limit) {\n            break;\n          }\n          if (x_ <= p / q) {\n            c = p;\n            d = q;\n          } else {\n            a = p;\n            b = q;\n          }\n        }\n        let result;\n        if (x_ - a / b < c / d - x_) {\n          result = x_ === x ? [a, b] : [b, a];\n        } else {\n          result = x_ === x ? [c, d] : [d, c];\n        }\n        return result;\n      }\n      function roundToDivide(x, div) {\n        const r = x % div;\n        return r === 0 ? x : Math.round(x - r + div);\n      }\n      function getPageSizeInches({\n        view,\n        userUnit,\n        rotate\n      }) {\n        const [x1, y1, x2, y2] = view;\n        const changeOrientation = rotate % 180 !== 0;\n        const width = (x2 - x1) / 72 * userUnit;\n        const height = (y2 - y1) / 72 * userUnit;\n        return {\n          width: changeOrientation ? height : width,\n          height: changeOrientation ? width : height\n        };\n      }\n      function backtrackBeforeAllVisibleElements(index, views, top) {\n        if (index < 2) {\n          return index;\n        }\n        let elt = views[index].div;\n        let pageTop = elt.offsetTop + elt.clientTop;\n        if (pageTop >= top) {\n          elt = views[index - 1].div;\n          pageTop = elt.offsetTop + elt.clientTop;\n        }\n        for (let i = index - 2; i >= 0; --i) {\n          elt = views[i].div;\n          if (elt.offsetTop + elt.clientTop + elt.clientHeight <= pageTop) {\n            break;\n          }\n          index = i;\n        }\n        return index;\n      }\n      function getVisibleElements({\n        scrollEl,\n        views,\n        sortByVisibility = false,\n        horizontal = false,\n        rtl = false\n      }) {\n        const top = scrollEl.scrollTop,\n          bottom = top + scrollEl.clientHeight;\n        const left = scrollEl.scrollLeft,\n          right = left + scrollEl.clientWidth;\n        function isElementBottomAfterViewTop(view) {\n          const element = view.div;\n          const elementBottom = element.offsetTop + element.clientTop + element.clientHeight;\n          return elementBottom > top;\n        }\n        function isElementNextAfterViewHorizontally(view) {\n          const element = view.div;\n          const elementLeft = element.offsetLeft + element.clientLeft;\n          const elementRight = elementLeft + element.clientWidth;\n          return rtl ? elementLeft < right : elementRight > left;\n        }\n        const visible = [],\n          ids = new Set(),\n          numViews = views.length;\n        let firstVisibleElementInd = binarySearchFirstItem(views, horizontal ? isElementNextAfterViewHorizontally : isElementBottomAfterViewTop);\n        if (firstVisibleElementInd > 0 && firstVisibleElementInd < numViews && !horizontal) {\n          firstVisibleElementInd = backtrackBeforeAllVisibleElements(firstVisibleElementInd, views, top);\n        }\n        let lastEdge = horizontal ? right : -1;\n        for (let i = firstVisibleElementInd; i < numViews; i++) {\n          const view = views[i],\n            element = view.div;\n          const currentWidth = element.offsetLeft + element.clientLeft;\n          const currentHeight = element.offsetTop + element.clientTop;\n          const viewWidth = element.clientWidth,\n            viewHeight = element.clientHeight;\n          const viewRight = currentWidth + viewWidth;\n          const viewBottom = currentHeight + viewHeight;\n          if (lastEdge === -1) {\n            if (viewBottom >= bottom) {\n              lastEdge = viewBottom;\n            }\n          } else if ((horizontal ? currentWidth : currentHeight) > lastEdge) {\n            break;\n          }\n          if (viewBottom <= top || currentHeight >= bottom || viewRight <= left || currentWidth >= right) {\n            continue;\n          }\n          const hiddenHeight = Math.max(0, top - currentHeight) + Math.max(0, viewBottom - bottom);\n          const hiddenWidth = Math.max(0, left - currentWidth) + Math.max(0, viewRight - right);\n          const fractionHeight = (viewHeight - hiddenHeight) / viewHeight,\n            fractionWidth = (viewWidth - hiddenWidth) / viewWidth;\n          const percent = fractionHeight * fractionWidth * 100 | 0;\n          visible.push({\n            id: view.id,\n            x: currentWidth,\n            y: currentHeight,\n            view,\n            percent,\n            widthPercent: fractionWidth * 100 | 0\n          });\n          ids.add(view.id);\n        }\n        const first = visible[0],\n          last = visible[visible.length - 1];\n        if (sortByVisibility) {\n          visible.sort(function (a, b) {\n            const pc = a.percent - b.percent;\n            if (Math.abs(pc) > 0.001) {\n              return -pc;\n            }\n            return a.id - b.id;\n          });\n        }\n        return {\n          first,\n          last,\n          views: visible,\n          ids\n        };\n      }\n      function noContextMenuHandler(evt) {\n        evt.preventDefault();\n      }\n      function normalizeWheelEventDirection(evt) {\n        let delta = Math.hypot(evt.deltaX, evt.deltaY);\n        const angle = Math.atan2(evt.deltaY, evt.deltaX);\n        if (-0.25 * Math.PI < angle && angle < 0.75 * Math.PI) {\n          delta = -delta;\n        }\n        return delta;\n      }\n      function normalizeWheelEventDelta(evt) {\n        let delta = normalizeWheelEventDirection(evt);\n        const MOUSE_DOM_DELTA_PIXEL_MODE = 0;\n        const MOUSE_DOM_DELTA_LINE_MODE = 1;\n        const MOUSE_PIXELS_PER_LINE = 30;\n        const MOUSE_LINES_PER_PAGE = 30;\n        if (evt.deltaMode === MOUSE_DOM_DELTA_PIXEL_MODE) {\n          delta /= MOUSE_PIXELS_PER_LINE * MOUSE_LINES_PER_PAGE;\n        } else if (evt.deltaMode === MOUSE_DOM_DELTA_LINE_MODE) {\n          delta /= MOUSE_LINES_PER_PAGE;\n        }\n        return delta;\n      }\n      function isValidRotation(angle) {\n        return Number.isInteger(angle) && angle % 90 === 0;\n      }\n      function isValidScrollMode(mode) {\n        return Number.isInteger(mode) && Object.values(ScrollMode).includes(mode) && mode !== ScrollMode.UNKNOWN;\n      }\n      function isValidSpreadMode(mode) {\n        return Number.isInteger(mode) && Object.values(SpreadMode).includes(mode) && mode !== SpreadMode.UNKNOWN;\n      }\n      function isPortraitOrientation(size) {\n        return size.width <= size.height;\n      }\n      const animationStarted = new Promise(function (resolve) {\n        window.requestAnimationFrame(resolve);\n      });\n      exports.animationStarted = animationStarted;\n      function clamp(v, min, max) {\n        return Math.min(Math.max(v, min), max);\n      }\n      class ProgressBar {\n        constructor(id) {\n          if (arguments.length > 1) {\n            throw new Error(\"ProgressBar no longer accepts any additional options, \" + \"please use CSS rules to modify its appearance instead.\");\n          }\n          this.visible = true;\n          this.div = document.querySelector(id + \" .progress\");\n          this.bar = this.div.parentNode;\n          this.percent = 0;\n        }\n        #updateBar() {\n          if (this._indeterminate) {\n            this.div.classList.add(\"indeterminate\");\n            return;\n          }\n          this.div.classList.remove(\"indeterminate\");\n          const doc = document.documentElement;\n          doc.style.setProperty(\"--progressBar-percent\", `${this._percent}%`);\n        }\n        get percent() {\n          return this._percent;\n        }\n        set percent(val) {\n          this._indeterminate = isNaN(val);\n          this._percent = clamp(val, 0, 100);\n          this.#updateBar();\n        }\n        setWidth(viewer) {\n          if (!viewer) {\n            return;\n          }\n          const container = viewer.parentNode;\n          const scrollbarWidth = container.offsetWidth - viewer.offsetWidth;\n          if (scrollbarWidth > 0) {\n            const doc = document.documentElement;\n            doc.style.setProperty(\"--progressBar-end-offset\", `${scrollbarWidth}px`);\n          }\n        }\n        hide() {\n          if (!this.visible) {\n            return;\n          }\n          this.visible = false;\n          this.bar.classList.add(\"hidden\");\n        }\n        show() {\n          if (this.visible) {\n            return;\n          }\n          this.visible = true;\n          this.bar.classList.remove(\"hidden\");\n        }\n      }\n      exports.ProgressBar = ProgressBar;\n      function getActiveOrFocusedElement() {\n        let curRoot = document;\n        let curActiveOrFocused = curRoot.activeElement || curRoot.querySelector(\":focus\");\n        while (curActiveOrFocused?.shadowRoot) {\n          curRoot = curActiveOrFocused.shadowRoot;\n          curActiveOrFocused = curRoot.activeElement || curRoot.querySelector(\":focus\");\n        }\n        return curActiveOrFocused;\n      }\n      function apiPageLayoutToViewerModes(layout) {\n        let scrollMode = ScrollMode.VERTICAL,\n          spreadMode = SpreadMode.NONE;\n        switch (layout) {\n          case \"SinglePage\":\n            scrollMode = ScrollMode.PAGE;\n            break;\n          case \"OneColumn\":\n            break;\n          case \"TwoPageLeft\":\n            scrollMode = ScrollMode.PAGE;\n          case \"TwoColumnLeft\":\n            spreadMode = SpreadMode.ODD;\n            break;\n          case \"TwoPageRight\":\n            scrollMode = ScrollMode.PAGE;\n          case \"TwoColumnRight\":\n            spreadMode = SpreadMode.EVEN;\n            break;\n        }\n        return {\n          scrollMode,\n          spreadMode\n        };\n      }\n      function apiPageModeToSidebarView(mode) {\n        switch (mode) {\n          case \"UseNone\":\n            return SidebarView.NONE;\n          case \"UseThumbs\":\n            return SidebarView.THUMBS;\n          case \"UseOutlines\":\n            return SidebarView.OUTLINE;\n          case \"UseAttachments\":\n            return SidebarView.ATTACHMENTS;\n          case \"UseOC\":\n            return SidebarView.LAYERS;\n        }\n        return SidebarView.NONE;\n      }\n\n      /***/\n    }), (/* 7 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.StructTreeLayerBuilder = void 0;\n      const PDF_ROLE_TO_HTML_ROLE = {\n        Document: null,\n        DocumentFragment: null,\n        Part: \"group\",\n        Sect: \"group\",\n        Div: \"group\",\n        Aside: \"note\",\n        NonStruct: \"none\",\n        P: null,\n        H: \"heading\",\n        Title: null,\n        FENote: \"note\",\n        Sub: \"group\",\n        Lbl: null,\n        Span: null,\n        Em: null,\n        Strong: null,\n        Link: \"link\",\n        Annot: \"note\",\n        Form: \"form\",\n        Ruby: null,\n        RB: null,\n        RT: null,\n        RP: null,\n        Warichu: null,\n        WT: null,\n        WP: null,\n        L: \"list\",\n        LI: \"listitem\",\n        LBody: null,\n        Table: \"table\",\n        TR: \"row\",\n        TH: \"columnheader\",\n        TD: \"cell\",\n        THead: \"columnheader\",\n        TBody: null,\n        TFoot: null,\n        Caption: null,\n        Figure: \"figure\",\n        Formula: null,\n        Artifact: null\n      };\n      const HEADING_PATTERN = /^H(\\d+)$/;\n      class StructTreeLayerBuilder {\n        constructor({\n          pdfPage\n        }) {\n          this.pdfPage = pdfPage;\n        }\n        render(structTree) {\n          return this._walk(structTree);\n        }\n        _setAttributes(structElement, htmlElement) {\n          if (structElement.alt !== undefined) {\n            htmlElement.setAttribute(\"aria-label\", structElement.alt);\n          }\n          if (structElement.id !== undefined) {\n            htmlElement.setAttribute(\"aria-owns\", structElement.id);\n          }\n          if (structElement.lang !== undefined) {\n            htmlElement.setAttribute(\"lang\", structElement.lang);\n          }\n        }\n        _walk(node) {\n          if (!node) {\n            return null;\n          }\n          const element = document.createElement(\"span\");\n          if (\"role\" in node) {\n            const {\n              role\n            } = node;\n            const match = role.match(HEADING_PATTERN);\n            if (match) {\n              element.setAttribute(\"role\", \"heading\");\n              element.setAttribute(\"aria-level\", match[1]);\n            } else if (PDF_ROLE_TO_HTML_ROLE[role]) {\n              element.setAttribute(\"role\", PDF_ROLE_TO_HTML_ROLE[role]);\n            }\n          }\n          this._setAttributes(node, element);\n          if (node.children) {\n            if (node.children.length === 1 && \"id\" in node.children[0]) {\n              this._setAttributes(node.children[0], element);\n            } else {\n              for (const kid of node.children) {\n                element.appendChild(this._walk(kid));\n              }\n            }\n          }\n          return element;\n        }\n      }\n      exports.StructTreeLayerBuilder = StructTreeLayerBuilder;\n\n      /***/\n    }), (/* 8 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.TextLayerBuilder = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      const EXPAND_DIVS_TIMEOUT = 300;\n      class TextLayerBuilder {\n        constructor({\n          textLayerDiv,\n          eventBus,\n          pageIndex,\n          viewport,\n          highlighter = null,\n          enhanceTextSelection = false\n        }) {\n          this.textLayerDiv = textLayerDiv;\n          this.eventBus = eventBus;\n          this.textContent = null;\n          this.textContentItemsStr = [];\n          this.textContentStream = null;\n          this.renderingDone = false;\n          this.pageNumber = pageIndex + 1;\n          this.viewport = viewport;\n          this.textDivs = [];\n          this.textLayerRenderTask = null;\n          this.highlighter = highlighter;\n          this.enhanceTextSelection = enhanceTextSelection;\n          this._bindMouse();\n        }\n        _finishRendering() {\n          this.renderingDone = true;\n          if (!this.enhanceTextSelection) {\n            const endOfContent = document.createElement(\"div\");\n            endOfContent.className = \"endOfContent\";\n            this.textLayerDiv.appendChild(endOfContent);\n          }\n          this.eventBus.dispatch(\"textlayerrendered\", {\n            source: this,\n            pageNumber: this.pageNumber,\n            numTextDivs: this.textDivs.length\n          });\n        }\n        render(timeout = 0) {\n          if (!(this.textContent || this.textContentStream) || this.renderingDone) {\n            return;\n          }\n          this.cancel();\n          this.textDivs.length = 0;\n          this.highlighter?.setTextMapping(this.textDivs, this.textContentItemsStr);\n          const textLayerFrag = document.createDocumentFragment();\n          this.textLayerRenderTask = (0, _pdfjsLib.renderTextLayer)({\n            textContent: this.textContent,\n            textContentStream: this.textContentStream,\n            container: textLayerFrag,\n            viewport: this.viewport,\n            textDivs: this.textDivs,\n            textContentItemsStr: this.textContentItemsStr,\n            timeout,\n            enhanceTextSelection: this.enhanceTextSelection\n          });\n          this.textLayerRenderTask.promise.then(() => {\n            this.textLayerDiv.appendChild(textLayerFrag);\n            this._finishRendering();\n            this.highlighter?.enable();\n          }, function (reason) {});\n        }\n        cancel() {\n          if (this.textLayerRenderTask) {\n            this.textLayerRenderTask.cancel();\n            this.textLayerRenderTask = null;\n          }\n          this.highlighter?.disable();\n        }\n        setTextContentStream(readableStream) {\n          this.cancel();\n          this.textContentStream = readableStream;\n        }\n        setTextContent(textContent) {\n          this.cancel();\n          this.textContent = textContent;\n        }\n        _bindMouse() {\n          const div = this.textLayerDiv;\n          let expandDivsTimer = null;\n          div.addEventListener(\"mousedown\", evt => {\n            if (this.enhanceTextSelection && this.textLayerRenderTask) {\n              this.textLayerRenderTask.expandTextDivs(true);\n              if (expandDivsTimer) {\n                clearTimeout(expandDivsTimer);\n                expandDivsTimer = null;\n              }\n              return;\n            }\n            const end = div.querySelector(\".endOfContent\");\n            if (!end) {\n              return;\n            }\n            let adjustTop = evt.target !== div;\n            adjustTop = adjustTop && window.getComputedStyle(end).getPropertyValue(\"-moz-user-select\") !== \"none\";\n            if (adjustTop) {\n              const divBounds = div.getBoundingClientRect();\n              const r = Math.max(0, (evt.pageY - divBounds.top) / divBounds.height);\n              end.style.top = (r * 100).toFixed(2) + \"%\";\n            }\n            end.classList.add(\"active\");\n          });\n          div.addEventListener(\"mouseup\", () => {\n            if (this.enhanceTextSelection && this.textLayerRenderTask) {\n              expandDivsTimer = setTimeout(() => {\n                if (this.textLayerRenderTask) {\n                  this.textLayerRenderTask.expandTextDivs(false);\n                }\n                expandDivsTimer = null;\n              }, EXPAND_DIVS_TIMEOUT);\n              return;\n            }\n            const end = div.querySelector(\".endOfContent\");\n            if (!end) {\n              return;\n            }\n            end.style.top = \"\";\n            end.classList.remove(\"active\");\n          });\n        }\n      }\n      exports.TextLayerBuilder = TextLayerBuilder;\n\n      /***/\n    }), (/* 9 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.XfaLayerBuilder = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      class XfaLayerBuilder {\n        constructor({\n          pageDiv,\n          pdfPage,\n          annotationStorage = null,\n          linkService,\n          xfaHtml = null\n        }) {\n          this.pageDiv = pageDiv;\n          this.pdfPage = pdfPage;\n          this.annotationStorage = annotationStorage;\n          this.linkService = linkService;\n          this.xfaHtml = xfaHtml;\n          this.div = null;\n          this._cancelled = false;\n        }\n        render(viewport, intent = \"display\") {\n          if (intent === \"print\") {\n            const parameters = {\n              viewport: viewport.clone({\n                dontFlip: true\n              }),\n              div: this.div,\n              xfaHtml: this.xfaHtml,\n              annotationStorage: this.annotationStorage,\n              linkService: this.linkService,\n              intent\n            };\n            const div = document.createElement(\"div\");\n            this.pageDiv.appendChild(div);\n            parameters.div = div;\n            const result = _pdfjsLib.XfaLayer.render(parameters);\n            return Promise.resolve(result);\n          }\n          return this.pdfPage.getXfa().then(xfaHtml => {\n            if (this._cancelled || !xfaHtml) {\n              return {\n                textDivs: []\n              };\n            }\n            const parameters = {\n              viewport: viewport.clone({\n                dontFlip: true\n              }),\n              div: this.div,\n              xfaHtml,\n              annotationStorage: this.annotationStorage,\n              linkService: this.linkService,\n              intent\n            };\n            if (this.div) {\n              return _pdfjsLib.XfaLayer.update(parameters);\n            }\n            this.div = document.createElement(\"div\");\n            this.pageDiv.appendChild(this.div);\n            parameters.div = this.div;\n            return _pdfjsLib.XfaLayer.render(parameters);\n          }).catch(error => {\n            console.error(error);\n          });\n        }\n        cancel() {\n          this._cancelled = true;\n        }\n        hide() {\n          if (!this.div) {\n            return;\n          }\n          this.div.hidden = true;\n        }\n      }\n      exports.XfaLayerBuilder = XfaLayerBuilder;\n\n      /***/\n    }), (/* 10 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFViewer = exports.PDFSinglePageViewer = void 0;\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _base_viewer = __w_pdfjs_require__(11);\n      class PDFViewer extends _base_viewer.BaseViewer {}\n      exports.PDFViewer = PDFViewer;\n      class PDFSinglePageViewer extends _base_viewer.BaseViewer {\n        _resetView() {\n          super._resetView();\n          this._scrollMode = _ui_utils.ScrollMode.PAGE;\n          this._spreadMode = _ui_utils.SpreadMode.NONE;\n        }\n        set scrollMode(mode) {}\n        _updateScrollMode() {}\n        set spreadMode(mode) {}\n        _updateSpreadMode() {}\n      }\n      exports.PDFSinglePageViewer = PDFSinglePageViewer;\n\n      /***/\n    }), (/* 11 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PagesCountLimit = exports.PDFPageViewBuffer = exports.BaseViewer = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _annotation_layer_builder = __w_pdfjs_require__(2);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      var _pdf_page_view = __w_pdfjs_require__(12);\n      var _pdf_rendering_queue = __w_pdfjs_require__(14);\n      var _pdf_link_service = __w_pdfjs_require__(5);\n      var _struct_tree_layer_builder = __w_pdfjs_require__(7);\n      var _text_highlighter = __w_pdfjs_require__(15);\n      var _text_layer_builder = __w_pdfjs_require__(8);\n      var _xfa_layer_builder = __w_pdfjs_require__(9);\n      const DEFAULT_CACHE_SIZE = 10;\n      const ENABLE_PERMISSIONS_CLASS = \"enablePermissions\";\n      const PagesCountLimit = {\n        FORCE_SCROLL_MODE_PAGE: 15000,\n        FORCE_LAZY_PAGE_INIT: 7500,\n        PAUSE_EAGER_PAGE_INIT: 250\n      };\n      exports.PagesCountLimit = PagesCountLimit;\n      class PDFPageViewBuffer {\n        #buf = new Set();\n        #size = 0;\n        constructor(size) {\n          this.#size = size;\n        }\n        push(view) {\n          const buf = this.#buf;\n          if (buf.has(view)) {\n            buf.delete(view);\n          }\n          buf.add(view);\n          if (buf.size > this.#size) {\n            this.#destroyFirstView();\n          }\n        }\n        resize(newSize, idsToKeep = null) {\n          this.#size = newSize;\n          const buf = this.#buf;\n          if (idsToKeep) {\n            const ii = buf.size;\n            let i = 1;\n            for (const view of buf) {\n              if (idsToKeep.has(view.id)) {\n                buf.delete(view);\n                buf.add(view);\n              }\n              if (++i > ii) {\n                break;\n              }\n            }\n          }\n          while (buf.size > this.#size) {\n            this.#destroyFirstView();\n          }\n        }\n        has(view) {\n          return this.#buf.has(view);\n        }\n        [Symbol.iterator]() {\n          return this.#buf.keys();\n        }\n        #destroyFirstView() {\n          const firstView = this.#buf.keys().next().value;\n          firstView?.destroy();\n          this.#buf.delete(firstView);\n        }\n      }\n      exports.PDFPageViewBuffer = PDFPageViewBuffer;\n      class BaseViewer {\n        #buffer = null;\n        #annotationMode = _pdfjsLib.AnnotationMode.ENABLE_FORMS;\n        #previousAnnotationMode = null;\n        #enablePermissions = false;\n        #previousContainerHeight = 0;\n        #scrollModePageState = null;\n        #onVisibilityChange = null;\n        constructor(options) {\n          if (this.constructor === BaseViewer) {\n            throw new Error(\"Cannot initialize BaseViewer.\");\n          }\n          const viewerVersion = '2.14.305';\n          if (_pdfjsLib.version !== viewerVersion) {\n            throw new Error(`The API version \"${_pdfjsLib.version}\" does not match the Viewer version \"${viewerVersion}\".`);\n          }\n          this.container = options.container;\n          this.viewer = options.viewer || options.container.firstElementChild;\n          if (!(this.container?.tagName.toUpperCase() === \"DIV\" && this.viewer?.tagName.toUpperCase() === \"DIV\")) {\n            throw new Error(\"Invalid `container` and/or `viewer` option.\");\n          }\n          if (this.container.offsetParent && getComputedStyle(this.container).position !== \"absolute\") {\n            throw new Error(\"The `container` must be absolutely positioned.\");\n          }\n          this.eventBus = options.eventBus;\n          this.linkService = options.linkService || new _pdf_link_service.SimpleLinkService();\n          this.downloadManager = options.downloadManager || null;\n          this.findController = options.findController || null;\n          this._scriptingManager = options.scriptingManager || null;\n          this.removePageBorders = options.removePageBorders || false;\n          this.textLayerMode = options.textLayerMode ?? _ui_utils.TextLayerMode.ENABLE;\n          this.#annotationMode = options.annotationMode ?? _pdfjsLib.AnnotationMode.ENABLE_FORMS;\n          this.imageResourcesPath = options.imageResourcesPath || \"\";\n          this.enablePrintAutoRotate = options.enablePrintAutoRotate || false;\n          this.renderer = options.renderer || _ui_utils.RendererType.CANVAS;\n          this.useOnlyCssZoom = options.useOnlyCssZoom || false;\n          this.maxCanvasPixels = options.maxCanvasPixels;\n          this.l10n = options.l10n || _l10n_utils.NullL10n;\n          this.#enablePermissions = options.enablePermissions || false;\n          this.pageColors = options.pageColors || null;\n          if (options.pageColors && (!CSS.supports(\"color\", options.pageColors.background) || !CSS.supports(\"color\", options.pageColors.foreground))) {\n            if (options.pageColors.background || options.pageColors.foreground) {\n              console.warn(\"Ignoring `pageColors`-option, since the browser doesn't support the values used.\");\n            }\n            this.pageColors = null;\n          }\n          this.defaultRenderingQueue = !options.renderingQueue;\n          if (this.defaultRenderingQueue) {\n            this.renderingQueue = new _pdf_rendering_queue.PDFRenderingQueue();\n            this.renderingQueue.setViewer(this);\n          } else {\n            this.renderingQueue = options.renderingQueue;\n          }\n          this._doc = document.documentElement;\n          this.scroll = (0, _ui_utils.watchScroll)(this.container, this._scrollUpdate.bind(this));\n          this.presentationModeState = _ui_utils.PresentationModeState.UNKNOWN;\n          this._onBeforeDraw = this._onAfterDraw = null;\n          this._resetView();\n          if (this.removePageBorders) {\n            this.viewer.classList.add(\"removePageBorders\");\n          }\n          this.updateContainerHeightCss();\n          Promise.resolve().then(() => {\n            this.eventBus.dispatch(\"baseviewerinit\", {\n              source: this\n            });\n          });\n        }\n        get pagesCount() {\n          return this._pages.length;\n        }\n        getPageView(index) {\n          return this._pages[index];\n        }\n        get pageViewsReady() {\n          if (!this._pagesCapability.settled) {\n            return false;\n          }\n          return this._pages.every(function (pageView) {\n            return pageView?.pdfPage;\n          });\n        }\n        get renderForms() {\n          return this.#annotationMode === _pdfjsLib.AnnotationMode.ENABLE_FORMS;\n        }\n        get enableScripting() {\n          return !!this._scriptingManager;\n        }\n        get currentPageNumber() {\n          return this._currentPageNumber;\n        }\n        set currentPageNumber(val) {\n          if (!Number.isInteger(val)) {\n            throw new Error(\"Invalid page number.\");\n          }\n          if (!this.pdfDocument) {\n            return;\n          }\n          if (!this._setCurrentPageNumber(val, true)) {\n            console.error(`currentPageNumber: \"${val}\" is not a valid page.`);\n          }\n        }\n        _setCurrentPageNumber(val, resetCurrentPageView = false) {\n          if (this._currentPageNumber === val) {\n            if (resetCurrentPageView) {\n              this.#resetCurrentPageView();\n            }\n            return true;\n          }\n          if (!(0 < val && val <= this.pagesCount)) {\n            return false;\n          }\n          const previous = this._currentPageNumber;\n          this._currentPageNumber = val;\n          this.eventBus.dispatch(\"pagechanging\", {\n            source: this,\n            pageNumber: val,\n            pageLabel: this._pageLabels?.[val - 1] ?? null,\n            previous\n          });\n          if (resetCurrentPageView) {\n            this.#resetCurrentPageView();\n          }\n          return true;\n        }\n        get currentPageLabel() {\n          return this._pageLabels?.[this._currentPageNumber - 1] ?? null;\n        }\n        set currentPageLabel(val) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          let page = val | 0;\n          if (this._pageLabels) {\n            const i = this._pageLabels.indexOf(val);\n            if (i >= 0) {\n              page = i + 1;\n            }\n          }\n          if (!this._setCurrentPageNumber(page, true)) {\n            console.error(`currentPageLabel: \"${val}\" is not a valid page.`);\n          }\n        }\n        get currentScale() {\n          return this._currentScale !== _ui_utils.UNKNOWN_SCALE ? this._currentScale : _ui_utils.DEFAULT_SCALE;\n        }\n        set currentScale(val) {\n          if (isNaN(val)) {\n            throw new Error(\"Invalid numeric scale.\");\n          }\n          if (!this.pdfDocument) {\n            return;\n          }\n          this._setScale(val, false);\n        }\n        get currentScaleValue() {\n          return this._currentScaleValue;\n        }\n        set currentScaleValue(val) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          this._setScale(val, false);\n        }\n        get pagesRotation() {\n          return this._pagesRotation;\n        }\n        set pagesRotation(rotation) {\n          if (!(0, _ui_utils.isValidRotation)(rotation)) {\n            throw new Error(\"Invalid pages rotation angle.\");\n          }\n          if (!this.pdfDocument) {\n            return;\n          }\n          rotation %= 360;\n          if (rotation < 0) {\n            rotation += 360;\n          }\n          if (this._pagesRotation === rotation) {\n            return;\n          }\n          this._pagesRotation = rotation;\n          const pageNumber = this._currentPageNumber;\n          const updateArgs = {\n            rotation\n          };\n          for (const pageView of this._pages) {\n            pageView.update(updateArgs);\n          }\n          if (this._currentScaleValue) {\n            this._setScale(this._currentScaleValue, true);\n          }\n          this.eventBus.dispatch(\"rotationchanging\", {\n            source: this,\n            pagesRotation: rotation,\n            pageNumber\n          });\n          if (this.defaultRenderingQueue) {\n            this.update();\n          }\n        }\n        get firstPagePromise() {\n          return this.pdfDocument ? this._firstPageCapability.promise : null;\n        }\n        get onePageRendered() {\n          return this.pdfDocument ? this._onePageRenderedCapability.promise : null;\n        }\n        get pagesPromise() {\n          return this.pdfDocument ? this._pagesCapability.promise : null;\n        }\n        #initializePermissions(permissions) {\n          if (!permissions) {\n            return;\n          }\n          if (!permissions.includes(_pdfjsLib.PermissionFlag.COPY)) {\n            this.viewer.classList.add(ENABLE_PERMISSIONS_CLASS);\n          }\n          if (!permissions.includes(_pdfjsLib.PermissionFlag.MODIFY_ANNOTATIONS) && !permissions.includes(_pdfjsLib.PermissionFlag.FILL_INTERACTIVE_FORMS)) {\n            if (this.#annotationMode === _pdfjsLib.AnnotationMode.ENABLE_FORMS) {\n              this.#previousAnnotationMode = this.#annotationMode;\n              this.#annotationMode = _pdfjsLib.AnnotationMode.ENABLE;\n            }\n          }\n        }\n        #onePageRenderedOrForceFetch() {\n          if (document.visibilityState === \"hidden\" || !this.container.offsetParent || this._getVisiblePages().views.length === 0) {\n            return Promise.resolve();\n          }\n          const visibilityChangePromise = new Promise(resolve => {\n            this.#onVisibilityChange = () => {\n              if (document.visibilityState !== \"hidden\") {\n                return;\n              }\n              resolve();\n              document.removeEventListener(\"visibilitychange\", this.#onVisibilityChange);\n              this.#onVisibilityChange = null;\n            };\n            document.addEventListener(\"visibilitychange\", this.#onVisibilityChange);\n          });\n          return Promise.race([this._onePageRenderedCapability.promise, visibilityChangePromise]);\n        }\n        setDocument(pdfDocument) {\n          var _this3 = this;\n          if (this.pdfDocument) {\n            this.eventBus.dispatch(\"pagesdestroy\", {\n              source: this\n            });\n            this._cancelRendering();\n            this._resetView();\n            if (this.findController) {\n              this.findController.setDocument(null);\n            }\n            if (this._scriptingManager) {\n              this._scriptingManager.setDocument(null);\n            }\n          }\n          this.pdfDocument = pdfDocument;\n          if (!pdfDocument) {\n            return;\n          }\n          const isPureXfa = pdfDocument.isPureXfa;\n          const pagesCount = pdfDocument.numPages;\n          const firstPagePromise = pdfDocument.getPage(1);\n          const optionalContentConfigPromise = pdfDocument.getOptionalContentConfig();\n          const permissionsPromise = this.#enablePermissions ? pdfDocument.getPermissions() : Promise.resolve();\n          if (pagesCount > PagesCountLimit.FORCE_SCROLL_MODE_PAGE) {\n            console.warn(\"Forcing PAGE-scrolling for performance reasons, given the length of the document.\");\n            const mode = this._scrollMode = _ui_utils.ScrollMode.PAGE;\n            this.eventBus.dispatch(\"scrollmodechanged\", {\n              source: this,\n              mode\n            });\n          }\n          this._pagesCapability.promise.then(() => {\n            this.eventBus.dispatch(\"pagesloaded\", {\n              source: this,\n              pagesCount\n            });\n          }, () => {});\n          this._onBeforeDraw = evt => {\n            const pageView = this._pages[evt.pageNumber - 1];\n            if (!pageView) {\n              return;\n            }\n            this.#buffer.push(pageView);\n          };\n          this.eventBus._on(\"pagerender\", this._onBeforeDraw);\n          this._onAfterDraw = evt => {\n            if (evt.cssTransform || this._onePageRenderedCapability.settled) {\n              return;\n            }\n            this._onePageRenderedCapability.resolve({\n              timestamp: evt.timestamp\n            });\n            this.eventBus._off(\"pagerendered\", this._onAfterDraw);\n            this._onAfterDraw = null;\n            if (this.#onVisibilityChange) {\n              document.removeEventListener(\"visibilitychange\", this.#onVisibilityChange);\n              this.#onVisibilityChange = null;\n            }\n          };\n          this.eventBus._on(\"pagerendered\", this._onAfterDraw);\n          Promise.all([firstPagePromise, permissionsPromise]).then(([firstPdfPage, permissions]) => {\n            if (pdfDocument !== this.pdfDocument) {\n              return;\n            }\n            this._firstPageCapability.resolve(firstPdfPage);\n            this._optionalContentConfigPromise = optionalContentConfigPromise;\n            this.#initializePermissions(permissions);\n            const viewerElement = this._scrollMode === _ui_utils.ScrollMode.PAGE ? null : this.viewer;\n            const scale = this.currentScale;\n            const viewport = firstPdfPage.getViewport({\n              scale: scale * _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS\n            });\n            const textLayerFactory = this.textLayerMode !== _ui_utils.TextLayerMode.DISABLE && !isPureXfa ? this : null;\n            const annotationLayerFactory = this.#annotationMode !== _pdfjsLib.AnnotationMode.DISABLE ? this : null;\n            const xfaLayerFactory = isPureXfa ? this : null;\n            for (let pageNum = 1; pageNum <= pagesCount; ++pageNum) {\n              const pageView = new _pdf_page_view.PDFPageView({\n                container: viewerElement,\n                eventBus: this.eventBus,\n                id: pageNum,\n                scale,\n                defaultViewport: viewport.clone(),\n                optionalContentConfigPromise,\n                renderingQueue: this.renderingQueue,\n                textLayerFactory,\n                textLayerMode: this.textLayerMode,\n                annotationLayerFactory,\n                annotationMode: this.#annotationMode,\n                xfaLayerFactory,\n                textHighlighterFactory: this,\n                structTreeLayerFactory: this,\n                imageResourcesPath: this.imageResourcesPath,\n                renderer: this.renderer,\n                useOnlyCssZoom: this.useOnlyCssZoom,\n                maxCanvasPixels: this.maxCanvasPixels,\n                pageColors: this.pageColors,\n                l10n: this.l10n\n              });\n              this._pages.push(pageView);\n            }\n            const firstPageView = this._pages[0];\n            if (firstPageView) {\n              firstPageView.setPdfPage(firstPdfPage);\n              this.linkService.cachePageRef(1, firstPdfPage.ref);\n            }\n            if (this._scrollMode === _ui_utils.ScrollMode.PAGE) {\n              this.#ensurePageViewVisible();\n            } else if (this._spreadMode !== _ui_utils.SpreadMode.NONE) {\n              this._updateSpreadMode();\n            }\n            this.#onePageRenderedOrForceFetch().then(/*#__PURE__*/_asyncToGenerator(function* () {\n              if (_this3.findController) {\n                _this3.findController.setDocument(pdfDocument);\n              }\n              if (_this3._scriptingManager) {\n                _this3._scriptingManager.setDocument(pdfDocument);\n              }\n              if (pdfDocument.loadingParams.disableAutoFetch || pagesCount > PagesCountLimit.FORCE_LAZY_PAGE_INIT) {\n                _this3._pagesCapability.resolve();\n                return;\n              }\n              let getPagesLeft = pagesCount - 1;\n              if (getPagesLeft <= 0) {\n                _this3._pagesCapability.resolve();\n                return;\n              }\n              for (let pageNum = 2; pageNum <= pagesCount; ++pageNum) {\n                const promise = pdfDocument.getPage(pageNum).then(pdfPage => {\n                  const pageView = _this3._pages[pageNum - 1];\n                  if (!pageView.pdfPage) {\n                    pageView.setPdfPage(pdfPage);\n                  }\n                  _this3.linkService.cachePageRef(pageNum, pdfPage.ref);\n                  if (--getPagesLeft === 0) {\n                    _this3._pagesCapability.resolve();\n                  }\n                }, reason => {\n                  console.error(`Unable to get page ${pageNum} to initialize viewer`, reason);\n                  if (--getPagesLeft === 0) {\n                    _this3._pagesCapability.resolve();\n                  }\n                });\n                if (pageNum % PagesCountLimit.PAUSE_EAGER_PAGE_INIT === 0) {\n                  yield promise;\n                }\n              }\n            }));\n            this.eventBus.dispatch(\"pagesinit\", {\n              source: this\n            });\n            pdfDocument.getMetadata().then(({\n              info\n            }) => {\n              if (pdfDocument !== this.pdfDocument) {\n                return;\n              }\n              if (info.Language) {\n                this.viewer.lang = info.Language;\n              }\n            });\n            if (this.defaultRenderingQueue) {\n              this.update();\n            }\n          }).catch(reason => {\n            console.error(\"Unable to initialize viewer\", reason);\n            this._pagesCapability.reject(reason);\n          });\n        }\n        setPageLabels(labels) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          if (!labels) {\n            this._pageLabels = null;\n          } else if (!(Array.isArray(labels) && this.pdfDocument.numPages === labels.length)) {\n            this._pageLabels = null;\n            console.error(`setPageLabels: Invalid page labels.`);\n          } else {\n            this._pageLabels = labels;\n          }\n          for (let i = 0, ii = this._pages.length; i < ii; i++) {\n            this._pages[i].setPageLabel(this._pageLabels?.[i] ?? null);\n          }\n        }\n        _resetView() {\n          this._pages = [];\n          this._currentPageNumber = 1;\n          this._currentScale = _ui_utils.UNKNOWN_SCALE;\n          this._currentScaleValue = null;\n          this._pageLabels = null;\n          this.#buffer = new PDFPageViewBuffer(DEFAULT_CACHE_SIZE);\n          this._location = null;\n          this._pagesRotation = 0;\n          this._optionalContentConfigPromise = null;\n          this._firstPageCapability = (0, _pdfjsLib.createPromiseCapability)();\n          this._onePageRenderedCapability = (0, _pdfjsLib.createPromiseCapability)();\n          this._pagesCapability = (0, _pdfjsLib.createPromiseCapability)();\n          this._scrollMode = _ui_utils.ScrollMode.VERTICAL;\n          this._previousScrollMode = _ui_utils.ScrollMode.UNKNOWN;\n          this._spreadMode = _ui_utils.SpreadMode.NONE;\n          this.#scrollModePageState = {\n            previousPageNumber: 1,\n            scrollDown: true,\n            pages: []\n          };\n          if (this._onBeforeDraw) {\n            this.eventBus._off(\"pagerender\", this._onBeforeDraw);\n            this._onBeforeDraw = null;\n          }\n          if (this._onAfterDraw) {\n            this.eventBus._off(\"pagerendered\", this._onAfterDraw);\n            this._onAfterDraw = null;\n          }\n          if (this.#onVisibilityChange) {\n            document.removeEventListener(\"visibilitychange\", this.#onVisibilityChange);\n            this.#onVisibilityChange = null;\n          }\n          this.viewer.textContent = \"\";\n          this._updateScrollMode();\n          this.viewer.removeAttribute(\"lang\");\n          this.viewer.classList.remove(ENABLE_PERMISSIONS_CLASS);\n          if (this.#previousAnnotationMode !== null) {\n            this.#annotationMode = this.#previousAnnotationMode;\n            this.#previousAnnotationMode = null;\n          }\n        }\n        #ensurePageViewVisible() {\n          if (this._scrollMode !== _ui_utils.ScrollMode.PAGE) {\n            throw new Error(\"#ensurePageViewVisible: Invalid scrollMode value.\");\n          }\n          const pageNumber = this._currentPageNumber,\n            state = this.#scrollModePageState,\n            viewer = this.viewer;\n          viewer.textContent = \"\";\n          state.pages.length = 0;\n          if (this._spreadMode === _ui_utils.SpreadMode.NONE && !this.isInPresentationMode) {\n            const pageView = this._pages[pageNumber - 1];\n            viewer.appendChild(pageView.div);\n            state.pages.push(pageView);\n          } else {\n            const pageIndexSet = new Set(),\n              parity = this._spreadMode - 1;\n            if (parity === -1) {\n              pageIndexSet.add(pageNumber - 1);\n            } else if (pageNumber % 2 !== parity) {\n              pageIndexSet.add(pageNumber - 1);\n              pageIndexSet.add(pageNumber);\n            } else {\n              pageIndexSet.add(pageNumber - 2);\n              pageIndexSet.add(pageNumber - 1);\n            }\n            const spread = document.createElement(\"div\");\n            spread.className = \"spread\";\n            if (this.isInPresentationMode) {\n              const dummyPage = document.createElement(\"div\");\n              dummyPage.className = \"dummyPage\";\n              spread.appendChild(dummyPage);\n            }\n            for (const i of pageIndexSet) {\n              const pageView = this._pages[i];\n              if (!pageView) {\n                continue;\n              }\n              spread.appendChild(pageView.div);\n              state.pages.push(pageView);\n            }\n            viewer.appendChild(spread);\n          }\n          state.scrollDown = pageNumber >= state.previousPageNumber;\n          state.previousPageNumber = pageNumber;\n        }\n        _scrollUpdate() {\n          if (this.pagesCount === 0) {\n            return;\n          }\n          this.update();\n        }\n        #scrollIntoView(pageView, pageSpot = null) {\n          const {\n            div,\n            id\n          } = pageView;\n          if (this._scrollMode === _ui_utils.ScrollMode.PAGE) {\n            this._setCurrentPageNumber(id);\n            this.#ensurePageViewVisible();\n            this.update();\n          }\n          if (!pageSpot && !this.isInPresentationMode) {\n            const left = div.offsetLeft + div.clientLeft,\n              right = left + div.clientWidth;\n            const {\n              scrollLeft,\n              clientWidth\n            } = this.container;\n            if (this._scrollMode === _ui_utils.ScrollMode.HORIZONTAL || left < scrollLeft || right > scrollLeft + clientWidth) {\n              pageSpot = {\n                left: 0,\n                top: 0\n              };\n            }\n          }\n          (0, _ui_utils.scrollIntoView)(div, pageSpot);\n        }\n        #isSameScale(newScale) {\n          return newScale === this._currentScale || Math.abs(newScale - this._currentScale) < 1e-15;\n        }\n        _setScaleUpdatePages(newScale, newValue, noScroll = false, preset = false) {\n          this._currentScaleValue = newValue.toString();\n          if (this.#isSameScale(newScale)) {\n            if (preset) {\n              this.eventBus.dispatch(\"scalechanging\", {\n                source: this,\n                scale: newScale,\n                presetValue: newValue\n              });\n            }\n            return;\n          }\n          this._doc.style.setProperty(\"--zoom-factor\", newScale);\n          const updateArgs = {\n            scale: newScale\n          };\n          for (const pageView of this._pages) {\n            pageView.update(updateArgs);\n          }\n          this._currentScale = newScale;\n          if (!noScroll) {\n            let page = this._currentPageNumber,\n              dest;\n            if (this._location && !(this.isInPresentationMode || this.isChangingPresentationMode)) {\n              page = this._location.pageNumber;\n              dest = [null, {\n                name: \"XYZ\"\n              }, this._location.left, this._location.top, null];\n            }\n            this.scrollPageIntoView({\n              pageNumber: page,\n              destArray: dest,\n              allowNegativeOffset: true\n            });\n          }\n          this.eventBus.dispatch(\"scalechanging\", {\n            source: this,\n            scale: newScale,\n            presetValue: preset ? newValue : undefined\n          });\n          if (this.defaultRenderingQueue) {\n            this.update();\n          }\n          this.updateContainerHeightCss();\n        }\n        get _pageWidthScaleFactor() {\n          if (this._spreadMode !== _ui_utils.SpreadMode.NONE && this._scrollMode !== _ui_utils.ScrollMode.HORIZONTAL) {\n            return 2;\n          }\n          return 1;\n        }\n        _setScale(value, noScroll = false) {\n          let scale = parseFloat(value);\n          if (scale > 0) {\n            this._setScaleUpdatePages(scale, value, noScroll, false);\n          } else {\n            const currentPage = this._pages[this._currentPageNumber - 1];\n            if (!currentPage) {\n              return;\n            }\n            let hPadding = _ui_utils.SCROLLBAR_PADDING,\n              vPadding = _ui_utils.VERTICAL_PADDING;\n            if (this.isInPresentationMode) {\n              hPadding = vPadding = 4;\n            } else if (this.removePageBorders) {\n              hPadding = vPadding = 0;\n            } else if (this._scrollMode === _ui_utils.ScrollMode.HORIZONTAL) {\n              [hPadding, vPadding] = [vPadding, hPadding];\n            }\n            const pageWidthScale = (this.container.clientWidth - hPadding) / currentPage.width * currentPage.scale / this._pageWidthScaleFactor;\n            const pageHeightScale = (this.container.clientHeight - vPadding) / currentPage.height * currentPage.scale;\n            switch (value) {\n              case \"page-actual\":\n                scale = 1;\n                break;\n              case \"page-width\":\n                scale = pageWidthScale;\n                break;\n              case \"page-height\":\n                scale = pageHeightScale;\n                break;\n              case \"page-fit\":\n                scale = Math.min(pageWidthScale, pageHeightScale);\n                break;\n              case \"auto\":\n                const horizontalScale = (0, _ui_utils.isPortraitOrientation)(currentPage) ? pageWidthScale : Math.min(pageHeightScale, pageWidthScale);\n                scale = Math.min(_ui_utils.MAX_AUTO_SCALE, horizontalScale);\n                break;\n              default:\n                console.error(`_setScale: \"${value}\" is an unknown zoom value.`);\n                return;\n            }\n            this._setScaleUpdatePages(scale, value, noScroll, true);\n          }\n        }\n        #resetCurrentPageView() {\n          const pageView = this._pages[this._currentPageNumber - 1];\n          if (this.isInPresentationMode) {\n            this._setScale(this._currentScaleValue, true);\n          }\n          this.#scrollIntoView(pageView);\n        }\n        pageLabelToPageNumber(label) {\n          if (!this._pageLabels) {\n            return null;\n          }\n          const i = this._pageLabels.indexOf(label);\n          if (i < 0) {\n            return null;\n          }\n          return i + 1;\n        }\n        scrollPageIntoView({\n          pageNumber,\n          destArray = null,\n          allowNegativeOffset = false,\n          ignoreDestinationZoom = false\n        }) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          const pageView = Number.isInteger(pageNumber) && this._pages[pageNumber - 1];\n          if (!pageView) {\n            console.error(`scrollPageIntoView: \"${pageNumber}\" is not a valid pageNumber parameter.`);\n            return;\n          }\n          if (this.isInPresentationMode || !destArray) {\n            this._setCurrentPageNumber(pageNumber, true);\n            return;\n          }\n          let x = 0,\n            y = 0;\n          let width = 0,\n            height = 0,\n            widthScale,\n            heightScale;\n          const changeOrientation = pageView.rotation % 180 !== 0;\n          const pageWidth = (changeOrientation ? pageView.height : pageView.width) / pageView.scale / _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS;\n          const pageHeight = (changeOrientation ? pageView.width : pageView.height) / pageView.scale / _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS;\n          let scale = 0;\n          switch (destArray[1].name) {\n            case \"XYZ\":\n              x = destArray[2];\n              y = destArray[3];\n              scale = destArray[4];\n              x = x !== null ? x : 0;\n              y = y !== null ? y : pageHeight;\n              break;\n            case \"Fit\":\n            case \"FitB\":\n              scale = \"page-fit\";\n              break;\n            case \"FitH\":\n            case \"FitBH\":\n              y = destArray[2];\n              scale = \"page-width\";\n              if (y === null && this._location) {\n                x = this._location.left;\n                y = this._location.top;\n              } else if (typeof y !== \"number\" || y < 0) {\n                y = pageHeight;\n              }\n              break;\n            case \"FitV\":\n            case \"FitBV\":\n              x = destArray[2];\n              width = pageWidth;\n              height = pageHeight;\n              scale = \"page-height\";\n              break;\n            case \"FitR\":\n              x = destArray[2];\n              y = destArray[3];\n              width = destArray[4] - x;\n              height = destArray[5] - y;\n              const hPadding = this.removePageBorders ? 0 : _ui_utils.SCROLLBAR_PADDING;\n              const vPadding = this.removePageBorders ? 0 : _ui_utils.VERTICAL_PADDING;\n              widthScale = (this.container.clientWidth - hPadding) / width / _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS;\n              heightScale = (this.container.clientHeight - vPadding) / height / _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS;\n              scale = Math.min(Math.abs(widthScale), Math.abs(heightScale));\n              break;\n            default:\n              console.error(`scrollPageIntoView: \"${destArray[1].name}\" is not a valid destination type.`);\n              return;\n          }\n          if (!ignoreDestinationZoom) {\n            if (scale && scale !== this._currentScale) {\n              this.currentScaleValue = scale;\n            } else if (this._currentScale === _ui_utils.UNKNOWN_SCALE) {\n              this.currentScaleValue = _ui_utils.DEFAULT_SCALE_VALUE;\n            }\n          }\n          if (scale === \"page-fit\" && !destArray[4]) {\n            this.#scrollIntoView(pageView);\n            return;\n          }\n          const boundingRect = [pageView.viewport.convertToViewportPoint(x, y), pageView.viewport.convertToViewportPoint(x + width, y + height)];\n          let left = Math.min(boundingRect[0][0], boundingRect[1][0]);\n          let top = Math.min(boundingRect[0][1], boundingRect[1][1]);\n          if (!allowNegativeOffset) {\n            left = Math.max(left, 0);\n            top = Math.max(top, 0);\n          }\n          this.#scrollIntoView(pageView, {\n            left,\n            top\n          });\n        }\n        _updateLocation(firstPage) {\n          const currentScale = this._currentScale;\n          const currentScaleValue = this._currentScaleValue;\n          const normalizedScaleValue = parseFloat(currentScaleValue) === currentScale ? Math.round(currentScale * 10000) / 100 : currentScaleValue;\n          const pageNumber = firstPage.id;\n          const currentPageView = this._pages[pageNumber - 1];\n          const container = this.container;\n          const topLeft = currentPageView.getPagePoint(container.scrollLeft - firstPage.x, container.scrollTop - firstPage.y);\n          const intLeft = Math.round(topLeft[0]);\n          const intTop = Math.round(topLeft[1]);\n          let pdfOpenParams = `#page=${pageNumber}`;\n          if (!this.isInPresentationMode) {\n            pdfOpenParams += `&zoom=${normalizedScaleValue},${intLeft},${intTop}`;\n          }\n          this._location = {\n            pageNumber,\n            scale: normalizedScaleValue,\n            top: intTop,\n            left: intLeft,\n            rotation: this._pagesRotation,\n            pdfOpenParams\n          };\n        }\n        update() {\n          const visible = this._getVisiblePages();\n          const visiblePages = visible.views,\n            numVisiblePages = visiblePages.length;\n          if (numVisiblePages === 0) {\n            return;\n          }\n          const newCacheSize = Math.max(DEFAULT_CACHE_SIZE, 2 * numVisiblePages + 1);\n          this.#buffer.resize(newCacheSize, visible.ids);\n          this.renderingQueue.renderHighestPriority(visible);\n          const isSimpleLayout = this._spreadMode === _ui_utils.SpreadMode.NONE && (this._scrollMode === _ui_utils.ScrollMode.PAGE || this._scrollMode === _ui_utils.ScrollMode.VERTICAL);\n          const currentId = this._currentPageNumber;\n          let stillFullyVisible = false;\n          for (const page of visiblePages) {\n            if (page.percent < 100) {\n              break;\n            }\n            if (page.id === currentId && isSimpleLayout) {\n              stillFullyVisible = true;\n              break;\n            }\n          }\n          this._setCurrentPageNumber(stillFullyVisible ? currentId : visiblePages[0].id);\n          this._updateLocation(visible.first);\n          this.eventBus.dispatch(\"updateviewarea\", {\n            source: this,\n            location: this._location\n          });\n        }\n        containsElement(element) {\n          return this.container.contains(element);\n        }\n        focus() {\n          this.container.focus();\n        }\n        get _isContainerRtl() {\n          return getComputedStyle(this.container).direction === \"rtl\";\n        }\n        get isInPresentationMode() {\n          return this.presentationModeState === _ui_utils.PresentationModeState.FULLSCREEN;\n        }\n        get isChangingPresentationMode() {\n          return this.presentationModeState === _ui_utils.PresentationModeState.CHANGING;\n        }\n        get isHorizontalScrollbarEnabled() {\n          return this.isInPresentationMode ? false : this.container.scrollWidth > this.container.clientWidth;\n        }\n        get isVerticalScrollbarEnabled() {\n          return this.isInPresentationMode ? false : this.container.scrollHeight > this.container.clientHeight;\n        }\n        _getVisiblePages() {\n          const views = this._scrollMode === _ui_utils.ScrollMode.PAGE ? this.#scrollModePageState.pages : this._pages,\n            horizontal = this._scrollMode === _ui_utils.ScrollMode.HORIZONTAL,\n            rtl = horizontal && this._isContainerRtl;\n          return (0, _ui_utils.getVisibleElements)({\n            scrollEl: this.container,\n            views,\n            sortByVisibility: true,\n            horizontal,\n            rtl\n          });\n        }\n        isPageVisible(pageNumber) {\n          if (!this.pdfDocument) {\n            return false;\n          }\n          if (!(Number.isInteger(pageNumber) && pageNumber > 0 && pageNumber <= this.pagesCount)) {\n            console.error(`isPageVisible: \"${pageNumber}\" is not a valid page.`);\n            return false;\n          }\n          return this._getVisiblePages().ids.has(pageNumber);\n        }\n        isPageCached(pageNumber) {\n          if (!this.pdfDocument) {\n            return false;\n          }\n          if (!(Number.isInteger(pageNumber) && pageNumber > 0 && pageNumber <= this.pagesCount)) {\n            console.error(`isPageCached: \"${pageNumber}\" is not a valid page.`);\n            return false;\n          }\n          const pageView = this._pages[pageNumber - 1];\n          return this.#buffer.has(pageView);\n        }\n        cleanup() {\n          for (const pageView of this._pages) {\n            if (pageView.renderingState !== _ui_utils.RenderingStates.FINISHED) {\n              pageView.reset();\n            }\n          }\n        }\n        _cancelRendering() {\n          for (const pageView of this._pages) {\n            pageView.cancelRendering();\n          }\n        }\n        #ensurePdfPageLoaded(pageView) {\n          var _this4 = this;\n          return _asyncToGenerator(function* () {\n            if (pageView.pdfPage) {\n              return pageView.pdfPage;\n            }\n            try {\n              const pdfPage = yield _this4.pdfDocument.getPage(pageView.id);\n              if (!pageView.pdfPage) {\n                pageView.setPdfPage(pdfPage);\n              }\n              if (!_this4.linkService._cachedPageNumber?.(pdfPage.ref)) {\n                _this4.linkService.cachePageRef(pageView.id, pdfPage.ref);\n              }\n              return pdfPage;\n            } catch (reason) {\n              console.error(\"Unable to get page for page view\", reason);\n              return null;\n            }\n          })();\n        }\n        #getScrollAhead(visible) {\n          if (visible.first?.id === 1) {\n            return true;\n          } else if (visible.last?.id === this.pagesCount) {\n            return false;\n          }\n          switch (this._scrollMode) {\n            case _ui_utils.ScrollMode.PAGE:\n              return this.#scrollModePageState.scrollDown;\n            case _ui_utils.ScrollMode.HORIZONTAL:\n              return this.scroll.right;\n          }\n          return this.scroll.down;\n        }\n        #toggleLoadingIconSpinner(visibleIds) {\n          for (const id of visibleIds) {\n            const pageView = this._pages[id - 1];\n            pageView?.toggleLoadingIconSpinner(true);\n          }\n          for (const pageView of this.#buffer) {\n            if (visibleIds.has(pageView.id)) {\n              continue;\n            }\n            pageView.toggleLoadingIconSpinner(false);\n          }\n        }\n        forceRendering(currentlyVisiblePages) {\n          const visiblePages = currentlyVisiblePages || this._getVisiblePages();\n          const scrollAhead = this.#getScrollAhead(visiblePages);\n          const preRenderExtra = this._spreadMode !== _ui_utils.SpreadMode.NONE && this._scrollMode !== _ui_utils.ScrollMode.HORIZONTAL;\n          const pageView = this.renderingQueue.getHighestPriority(visiblePages, this._pages, scrollAhead, preRenderExtra);\n          this.#toggleLoadingIconSpinner(visiblePages.ids);\n          if (pageView) {\n            this.#ensurePdfPageLoaded(pageView).then(() => {\n              this.renderingQueue.renderView(pageView);\n            });\n            return true;\n          }\n          return false;\n        }\n        createTextLayerBuilder(textLayerDiv, pageIndex, viewport, enhanceTextSelection = false, eventBus, highlighter) {\n          return new _text_layer_builder.TextLayerBuilder({\n            textLayerDiv,\n            eventBus,\n            pageIndex,\n            viewport,\n            enhanceTextSelection: this.isInPresentationMode ? false : enhanceTextSelection,\n            highlighter\n          });\n        }\n        createTextHighlighter(pageIndex, eventBus) {\n          return new _text_highlighter.TextHighlighter({\n            eventBus,\n            pageIndex,\n            findController: this.isInPresentationMode ? null : this.findController\n          });\n        }\n        createAnnotationLayerBuilder(pageDiv, pdfPage, annotationStorage = null, imageResourcesPath = \"\", renderForms = true, l10n = _l10n_utils.NullL10n, enableScripting = null, hasJSActionsPromise = null, mouseState = null, fieldObjectsPromise = null, annotationCanvasMap = null) {\n          return new _annotation_layer_builder.AnnotationLayerBuilder({\n            pageDiv,\n            pdfPage,\n            annotationStorage: annotationStorage || this.pdfDocument?.annotationStorage,\n            imageResourcesPath,\n            renderForms,\n            linkService: this.linkService,\n            downloadManager: this.downloadManager,\n            l10n,\n            enableScripting: enableScripting ?? this.enableScripting,\n            hasJSActionsPromise: hasJSActionsPromise || this.pdfDocument?.hasJSActions(),\n            fieldObjectsPromise: fieldObjectsPromise || this.pdfDocument?.getFieldObjects(),\n            mouseState: mouseState || this._scriptingManager?.mouseState,\n            annotationCanvasMap\n          });\n        }\n        createXfaLayerBuilder(pageDiv, pdfPage, annotationStorage = null) {\n          return new _xfa_layer_builder.XfaLayerBuilder({\n            pageDiv,\n            pdfPage,\n            annotationStorage: annotationStorage || this.pdfDocument?.annotationStorage,\n            linkService: this.linkService\n          });\n        }\n        createStructTreeLayerBuilder(pdfPage) {\n          return new _struct_tree_layer_builder.StructTreeLayerBuilder({\n            pdfPage\n          });\n        }\n        get hasEqualPageSizes() {\n          const firstPageView = this._pages[0];\n          for (let i = 1, ii = this._pages.length; i < ii; ++i) {\n            const pageView = this._pages[i];\n            if (pageView.width !== firstPageView.width || pageView.height !== firstPageView.height) {\n              return false;\n            }\n          }\n          return true;\n        }\n        getPagesOverview() {\n          return this._pages.map(pageView => {\n            const viewport = pageView.pdfPage.getViewport({\n              scale: 1\n            });\n            if (!this.enablePrintAutoRotate || (0, _ui_utils.isPortraitOrientation)(viewport)) {\n              return {\n                width: viewport.width,\n                height: viewport.height,\n                rotation: viewport.rotation\n              };\n            }\n            return {\n              width: viewport.height,\n              height: viewport.width,\n              rotation: (viewport.rotation - 90) % 360\n            };\n          });\n        }\n        get optionalContentConfigPromise() {\n          if (!this.pdfDocument) {\n            return Promise.resolve(null);\n          }\n          if (!this._optionalContentConfigPromise) {\n            return this.pdfDocument.getOptionalContentConfig();\n          }\n          return this._optionalContentConfigPromise;\n        }\n        set optionalContentConfigPromise(promise) {\n          if (!(promise instanceof Promise)) {\n            throw new Error(`Invalid optionalContentConfigPromise: ${promise}`);\n          }\n          if (!this.pdfDocument) {\n            return;\n          }\n          if (!this._optionalContentConfigPromise) {\n            return;\n          }\n          this._optionalContentConfigPromise = promise;\n          const updateArgs = {\n            optionalContentConfigPromise: promise\n          };\n          for (const pageView of this._pages) {\n            pageView.update(updateArgs);\n          }\n          this.update();\n          this.eventBus.dispatch(\"optionalcontentconfigchanged\", {\n            source: this,\n            promise\n          });\n        }\n        get scrollMode() {\n          return this._scrollMode;\n        }\n        set scrollMode(mode) {\n          if (this._scrollMode === mode) {\n            return;\n          }\n          if (!(0, _ui_utils.isValidScrollMode)(mode)) {\n            throw new Error(`Invalid scroll mode: ${mode}`);\n          }\n          if (this.pagesCount > PagesCountLimit.FORCE_SCROLL_MODE_PAGE) {\n            return;\n          }\n          this._previousScrollMode = this._scrollMode;\n          this._scrollMode = mode;\n          this.eventBus.dispatch(\"scrollmodechanged\", {\n            source: this,\n            mode\n          });\n          this._updateScrollMode(this._currentPageNumber);\n        }\n        _updateScrollMode(pageNumber = null) {\n          const scrollMode = this._scrollMode,\n            viewer = this.viewer;\n          viewer.classList.toggle(\"scrollHorizontal\", scrollMode === _ui_utils.ScrollMode.HORIZONTAL);\n          viewer.classList.toggle(\"scrollWrapped\", scrollMode === _ui_utils.ScrollMode.WRAPPED);\n          if (!this.pdfDocument || !pageNumber) {\n            return;\n          }\n          if (scrollMode === _ui_utils.ScrollMode.PAGE) {\n            this.#ensurePageViewVisible();\n          } else if (this._previousScrollMode === _ui_utils.ScrollMode.PAGE) {\n            this._updateSpreadMode();\n          }\n          if (this._currentScaleValue && isNaN(this._currentScaleValue)) {\n            this._setScale(this._currentScaleValue, true);\n          }\n          this._setCurrentPageNumber(pageNumber, true);\n          this.update();\n        }\n        get spreadMode() {\n          return this._spreadMode;\n        }\n        set spreadMode(mode) {\n          if (this._spreadMode === mode) {\n            return;\n          }\n          if (!(0, _ui_utils.isValidSpreadMode)(mode)) {\n            throw new Error(`Invalid spread mode: ${mode}`);\n          }\n          this._spreadMode = mode;\n          this.eventBus.dispatch(\"spreadmodechanged\", {\n            source: this,\n            mode\n          });\n          this._updateSpreadMode(this._currentPageNumber);\n        }\n        _updateSpreadMode(pageNumber = null) {\n          if (!this.pdfDocument) {\n            return;\n          }\n          const viewer = this.viewer,\n            pages = this._pages;\n          if (this._scrollMode === _ui_utils.ScrollMode.PAGE) {\n            this.#ensurePageViewVisible();\n          } else {\n            viewer.textContent = \"\";\n            if (this._spreadMode === _ui_utils.SpreadMode.NONE) {\n              for (const pageView of this._pages) {\n                viewer.appendChild(pageView.div);\n              }\n            } else {\n              const parity = this._spreadMode - 1;\n              let spread = null;\n              for (let i = 0, ii = pages.length; i < ii; ++i) {\n                if (spread === null) {\n                  spread = document.createElement(\"div\");\n                  spread.className = \"spread\";\n                  viewer.appendChild(spread);\n                } else if (i % 2 === parity) {\n                  spread = spread.cloneNode(false);\n                  viewer.appendChild(spread);\n                }\n                spread.appendChild(pages[i].div);\n              }\n            }\n          }\n          if (!pageNumber) {\n            return;\n          }\n          if (this._currentScaleValue && isNaN(this._currentScaleValue)) {\n            this._setScale(this._currentScaleValue, true);\n          }\n          this._setCurrentPageNumber(pageNumber, true);\n          this.update();\n        }\n        _getPageAdvance(currentPageNumber, previous = false) {\n          switch (this._scrollMode) {\n            case _ui_utils.ScrollMode.WRAPPED:\n              {\n                const {\n                    views\n                  } = this._getVisiblePages(),\n                  pageLayout = new Map();\n                for (const {\n                  id,\n                  y,\n                  percent,\n                  widthPercent\n                } of views) {\n                  if (percent === 0 || widthPercent < 100) {\n                    continue;\n                  }\n                  let yArray = pageLayout.get(y);\n                  if (!yArray) {\n                    pageLayout.set(y, yArray ||= []);\n                  }\n                  yArray.push(id);\n                }\n                for (const yArray of pageLayout.values()) {\n                  const currentIndex = yArray.indexOf(currentPageNumber);\n                  if (currentIndex === -1) {\n                    continue;\n                  }\n                  const numPages = yArray.length;\n                  if (numPages === 1) {\n                    break;\n                  }\n                  if (previous) {\n                    for (let i = currentIndex - 1, ii = 0; i >= ii; i--) {\n                      const currentId = yArray[i],\n                        expectedId = yArray[i + 1] - 1;\n                      if (currentId < expectedId) {\n                        return currentPageNumber - expectedId;\n                      }\n                    }\n                  } else {\n                    for (let i = currentIndex + 1, ii = numPages; i < ii; i++) {\n                      const currentId = yArray[i],\n                        expectedId = yArray[i - 1] + 1;\n                      if (currentId > expectedId) {\n                        return expectedId - currentPageNumber;\n                      }\n                    }\n                  }\n                  if (previous) {\n                    const firstId = yArray[0];\n                    if (firstId < currentPageNumber) {\n                      return currentPageNumber - firstId + 1;\n                    }\n                  } else {\n                    const lastId = yArray[numPages - 1];\n                    if (lastId > currentPageNumber) {\n                      return lastId - currentPageNumber + 1;\n                    }\n                  }\n                  break;\n                }\n                break;\n              }\n            case _ui_utils.ScrollMode.HORIZONTAL:\n              {\n                break;\n              }\n            case _ui_utils.ScrollMode.PAGE:\n            case _ui_utils.ScrollMode.VERTICAL:\n              {\n                if (this._spreadMode === _ui_utils.SpreadMode.NONE) {\n                  break;\n                }\n                const parity = this._spreadMode - 1;\n                if (previous && currentPageNumber % 2 !== parity) {\n                  break;\n                } else if (!previous && currentPageNumber % 2 === parity) {\n                  break;\n                }\n                const {\n                    views\n                  } = this._getVisiblePages(),\n                  expectedId = previous ? currentPageNumber - 1 : currentPageNumber + 1;\n                for (const {\n                  id,\n                  percent,\n                  widthPercent\n                } of views) {\n                  if (id !== expectedId) {\n                    continue;\n                  }\n                  if (percent > 0 && widthPercent === 100) {\n                    return 2;\n                  }\n                  break;\n                }\n                break;\n              }\n          }\n          return 1;\n        }\n        nextPage() {\n          const currentPageNumber = this._currentPageNumber,\n            pagesCount = this.pagesCount;\n          if (currentPageNumber >= pagesCount) {\n            return false;\n          }\n          const advance = this._getPageAdvance(currentPageNumber, false) || 1;\n          this.currentPageNumber = Math.min(currentPageNumber + advance, pagesCount);\n          return true;\n        }\n        previousPage() {\n          const currentPageNumber = this._currentPageNumber;\n          if (currentPageNumber <= 1) {\n            return false;\n          }\n          const advance = this._getPageAdvance(currentPageNumber, true) || 1;\n          this.currentPageNumber = Math.max(currentPageNumber - advance, 1);\n          return true;\n        }\n        increaseScale(steps = 1) {\n          let newScale = this._currentScale;\n          do {\n            newScale = (newScale * _ui_utils.DEFAULT_SCALE_DELTA).toFixed(2);\n            newScale = Math.ceil(newScale * 10) / 10;\n            newScale = Math.min(_ui_utils.MAX_SCALE, newScale);\n          } while (--steps > 0 && newScale < _ui_utils.MAX_SCALE);\n          this.currentScaleValue = newScale;\n        }\n        decreaseScale(steps = 1) {\n          let newScale = this._currentScale;\n          do {\n            newScale = (newScale / _ui_utils.DEFAULT_SCALE_DELTA).toFixed(2);\n            newScale = Math.floor(newScale * 10) / 10;\n            newScale = Math.max(_ui_utils.MIN_SCALE, newScale);\n          } while (--steps > 0 && newScale > _ui_utils.MIN_SCALE);\n          this.currentScaleValue = newScale;\n        }\n        updateContainerHeightCss() {\n          const height = this.container.clientHeight;\n          if (height !== this.#previousContainerHeight) {\n            this.#previousContainerHeight = height;\n            this._doc.style.setProperty(\"--viewer-container-height\", `${height}px`);\n          }\n        }\n      }\n      exports.BaseViewer = BaseViewer;\n\n      /***/\n    }), (/* 12 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFPageView = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _app_options = __w_pdfjs_require__(13);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      const MAX_CANVAS_PIXELS = _app_options.compatibilityParams.maxCanvasPixels || 16777216;\n      class PDFPageView {\n        #annotationMode = _pdfjsLib.AnnotationMode.ENABLE_FORMS;\n        constructor(options) {\n          const container = options.container;\n          const defaultViewport = options.defaultViewport;\n          this.id = options.id;\n          this.renderingId = \"page\" + this.id;\n          this.pdfPage = null;\n          this.pageLabel = null;\n          this.rotation = 0;\n          this.scale = options.scale || _ui_utils.DEFAULT_SCALE;\n          this.viewport = defaultViewport;\n          this.pdfPageRotate = defaultViewport.rotation;\n          this._optionalContentConfigPromise = options.optionalContentConfigPromise || null;\n          this.hasRestrictedScaling = false;\n          this.textLayerMode = options.textLayerMode ?? _ui_utils.TextLayerMode.ENABLE;\n          this.#annotationMode = options.annotationMode ?? _pdfjsLib.AnnotationMode.ENABLE_FORMS;\n          this.imageResourcesPath = options.imageResourcesPath || \"\";\n          this.useOnlyCssZoom = options.useOnlyCssZoom || false;\n          this.maxCanvasPixels = options.maxCanvasPixels || MAX_CANVAS_PIXELS;\n          this.pageColors = options.pageColors || null;\n          this.eventBus = options.eventBus;\n          this.renderingQueue = options.renderingQueue;\n          this.textLayerFactory = options.textLayerFactory;\n          this.annotationLayerFactory = options.annotationLayerFactory;\n          this.xfaLayerFactory = options.xfaLayerFactory;\n          this.textHighlighter = options.textHighlighterFactory?.createTextHighlighter(this.id - 1, this.eventBus);\n          this.structTreeLayerFactory = options.structTreeLayerFactory;\n          this.renderer = options.renderer || _ui_utils.RendererType.CANVAS;\n          this.l10n = options.l10n || _l10n_utils.NullL10n;\n          this.paintTask = null;\n          this.paintedViewportMap = new WeakMap();\n          this.renderingState = _ui_utils.RenderingStates.INITIAL;\n          this.resume = null;\n          this._renderError = null;\n          this._isStandalone = !this.renderingQueue?.hasViewer();\n          this._annotationCanvasMap = null;\n          this.annotationLayer = null;\n          this.textLayer = null;\n          this.zoomLayer = null;\n          this.xfaLayer = null;\n          this.structTreeLayer = null;\n          const div = document.createElement(\"div\");\n          div.className = \"page\";\n          div.style.width = Math.floor(this.viewport.width) + \"px\";\n          div.style.height = Math.floor(this.viewport.height) + \"px\";\n          div.setAttribute(\"data-page-number\", this.id);\n          div.setAttribute(\"role\", \"region\");\n          this.l10n.get(\"page_landmark\", {\n            page: this.id\n          }).then(msg => {\n            div.setAttribute(\"aria-label\", msg);\n          });\n          this.div = div;\n          container?.appendChild(div);\n        }\n        setPdfPage(pdfPage) {\n          this.pdfPage = pdfPage;\n          this.pdfPageRotate = pdfPage.rotate;\n          const totalRotation = (this.rotation + this.pdfPageRotate) % 360;\n          this.viewport = pdfPage.getViewport({\n            scale: this.scale * _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS,\n            rotation: totalRotation\n          });\n          this.reset();\n        }\n        destroy() {\n          this.reset();\n          if (this.pdfPage) {\n            this.pdfPage.cleanup();\n          }\n        }\n        _renderAnnotationLayer() {\n          var _this5 = this;\n          return _asyncToGenerator(function* () {\n            let error = null;\n            try {\n              yield _this5.annotationLayer.render(_this5.viewport, \"display\");\n            } catch (ex) {\n              error = ex;\n            } finally {\n              _this5.eventBus.dispatch(\"annotationlayerrendered\", {\n                source: _this5,\n                pageNumber: _this5.id,\n                error\n              });\n            }\n          })();\n        }\n        _renderXfaLayer() {\n          var _this6 = this;\n          return _asyncToGenerator(function* () {\n            let error = null;\n            try {\n              const result = yield _this6.xfaLayer.render(_this6.viewport, \"display\");\n              if (_this6.textHighlighter) {\n                _this6._buildXfaTextContentItems(result.textDivs);\n              }\n            } catch (ex) {\n              error = ex;\n            } finally {\n              _this6.eventBus.dispatch(\"xfalayerrendered\", {\n                source: _this6,\n                pageNumber: _this6.id,\n                error\n              });\n            }\n          })();\n        }\n        _buildXfaTextContentItems(textDivs) {\n          var _this7 = this;\n          return _asyncToGenerator(function* () {\n            const text = yield _this7.pdfPage.getTextContent();\n            const items = [];\n            for (const item of text.items) {\n              items.push(item.str);\n            }\n            _this7.textHighlighter.setTextMapping(textDivs, items);\n            _this7.textHighlighter.enable();\n          })();\n        }\n        _resetZoomLayer(removeFromDOM = false) {\n          if (!this.zoomLayer) {\n            return;\n          }\n          const zoomLayerCanvas = this.zoomLayer.firstChild;\n          this.paintedViewportMap.delete(zoomLayerCanvas);\n          zoomLayerCanvas.width = 0;\n          zoomLayerCanvas.height = 0;\n          if (removeFromDOM) {\n            this.zoomLayer.remove();\n          }\n          this.zoomLayer = null;\n        }\n        reset({\n          keepZoomLayer = false,\n          keepAnnotationLayer = false,\n          keepXfaLayer = false\n        } = {}) {\n          this.cancelRendering({\n            keepAnnotationLayer,\n            keepXfaLayer\n          });\n          this.renderingState = _ui_utils.RenderingStates.INITIAL;\n          const div = this.div;\n          div.style.width = Math.floor(this.viewport.width) + \"px\";\n          div.style.height = Math.floor(this.viewport.height) + \"px\";\n          const childNodes = div.childNodes,\n            zoomLayerNode = keepZoomLayer && this.zoomLayer || null,\n            annotationLayerNode = keepAnnotationLayer && this.annotationLayer?.div || null,\n            xfaLayerNode = keepXfaLayer && this.xfaLayer?.div || null;\n          for (let i = childNodes.length - 1; i >= 0; i--) {\n            const node = childNodes[i];\n            switch (node) {\n              case zoomLayerNode:\n              case annotationLayerNode:\n              case xfaLayerNode:\n                continue;\n            }\n            node.remove();\n          }\n          div.removeAttribute(\"data-loaded\");\n          if (annotationLayerNode) {\n            this.annotationLayer.hide();\n          }\n          if (xfaLayerNode) {\n            this.xfaLayer.hide();\n          }\n          if (!zoomLayerNode) {\n            if (this.canvas) {\n              this.paintedViewportMap.delete(this.canvas);\n              this.canvas.width = 0;\n              this.canvas.height = 0;\n              delete this.canvas;\n            }\n            this._resetZoomLayer();\n          }\n          if (this.svg) {\n            this.paintedViewportMap.delete(this.svg);\n            delete this.svg;\n          }\n          this.loadingIconDiv = document.createElement(\"div\");\n          this.loadingIconDiv.className = \"loadingIcon notVisible\";\n          if (this._isStandalone) {\n            this.toggleLoadingIconSpinner(true);\n          }\n          this.loadingIconDiv.setAttribute(\"role\", \"img\");\n          this.l10n.get(\"loading\").then(msg => {\n            this.loadingIconDiv?.setAttribute(\"aria-label\", msg);\n          });\n          div.appendChild(this.loadingIconDiv);\n        }\n        update({\n          scale = 0,\n          rotation = null,\n          optionalContentConfigPromise = null\n        }) {\n          this.scale = scale || this.scale;\n          if (typeof rotation === \"number\") {\n            this.rotation = rotation;\n          }\n          if (optionalContentConfigPromise instanceof Promise) {\n            this._optionalContentConfigPromise = optionalContentConfigPromise;\n          }\n          const totalRotation = (this.rotation + this.pdfPageRotate) % 360;\n          this.viewport = this.viewport.clone({\n            scale: this.scale * _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS,\n            rotation: totalRotation\n          });\n          if (this._isStandalone) {\n            const {\n              style\n            } = document.documentElement;\n            style.setProperty(\"--zoom-factor\", this.scale);\n          }\n          if (this.svg) {\n            this.cssTransform({\n              target: this.svg,\n              redrawAnnotationLayer: true,\n              redrawXfaLayer: true\n            });\n            this.eventBus.dispatch(\"pagerendered\", {\n              source: this,\n              pageNumber: this.id,\n              cssTransform: true,\n              timestamp: performance.now(),\n              error: this._renderError\n            });\n            return;\n          }\n          let isScalingRestricted = false;\n          if (this.canvas && this.maxCanvasPixels > 0) {\n            const outputScale = this.outputScale;\n            if ((Math.floor(this.viewport.width) * outputScale.sx | 0) * (Math.floor(this.viewport.height) * outputScale.sy | 0) > this.maxCanvasPixels) {\n              isScalingRestricted = true;\n            }\n          }\n          if (this.canvas) {\n            if (this.useOnlyCssZoom || this.hasRestrictedScaling && isScalingRestricted) {\n              this.cssTransform({\n                target: this.canvas,\n                redrawAnnotationLayer: true,\n                redrawXfaLayer: true\n              });\n              this.eventBus.dispatch(\"pagerendered\", {\n                source: this,\n                pageNumber: this.id,\n                cssTransform: true,\n                timestamp: performance.now(),\n                error: this._renderError\n              });\n              return;\n            }\n            if (!this.zoomLayer && !this.canvas.hidden) {\n              this.zoomLayer = this.canvas.parentNode;\n              this.zoomLayer.style.position = \"absolute\";\n            }\n          }\n          if (this.zoomLayer) {\n            this.cssTransform({\n              target: this.zoomLayer.firstChild\n            });\n          }\n          this.reset({\n            keepZoomLayer: true,\n            keepAnnotationLayer: true,\n            keepXfaLayer: true\n          });\n        }\n        cancelRendering({\n          keepAnnotationLayer = false,\n          keepXfaLayer = false\n        } = {}) {\n          if (this.paintTask) {\n            this.paintTask.cancel();\n            this.paintTask = null;\n          }\n          this.resume = null;\n          if (this.textLayer) {\n            this.textLayer.cancel();\n            this.textLayer = null;\n          }\n          if (this.annotationLayer && (!keepAnnotationLayer || !this.annotationLayer.div)) {\n            this.annotationLayer.cancel();\n            this.annotationLayer = null;\n            this._annotationCanvasMap = null;\n          }\n          if (this.xfaLayer && (!keepXfaLayer || !this.xfaLayer.div)) {\n            this.xfaLayer.cancel();\n            this.xfaLayer = null;\n            this.textHighlighter?.disable();\n          }\n          if (this._onTextLayerRendered) {\n            this.eventBus._off(\"textlayerrendered\", this._onTextLayerRendered);\n            this._onTextLayerRendered = null;\n          }\n        }\n        cssTransform({\n          target,\n          redrawAnnotationLayer = false,\n          redrawXfaLayer = false\n        }) {\n          const width = this.viewport.width;\n          const height = this.viewport.height;\n          const div = this.div;\n          target.style.width = target.parentNode.style.width = div.style.width = Math.floor(width) + \"px\";\n          target.style.height = target.parentNode.style.height = div.style.height = Math.floor(height) + \"px\";\n          const relativeRotation = this.viewport.rotation - this.paintedViewportMap.get(target).rotation;\n          const absRotation = Math.abs(relativeRotation);\n          let scaleX = 1,\n            scaleY = 1;\n          if (absRotation === 90 || absRotation === 270) {\n            scaleX = height / width;\n            scaleY = width / height;\n          }\n          target.style.transform = `rotate(${relativeRotation}deg) scale(${scaleX}, ${scaleY})`;\n          if (this.textLayer) {\n            const textLayerViewport = this.textLayer.viewport;\n            const textRelativeRotation = this.viewport.rotation - textLayerViewport.rotation;\n            const textAbsRotation = Math.abs(textRelativeRotation);\n            let scale = width / textLayerViewport.width;\n            if (textAbsRotation === 90 || textAbsRotation === 270) {\n              scale = width / textLayerViewport.height;\n            }\n            const textLayerDiv = this.textLayer.textLayerDiv;\n            let transX, transY;\n            switch (textAbsRotation) {\n              case 0:\n                transX = transY = 0;\n                break;\n              case 90:\n                transX = 0;\n                transY = \"-\" + textLayerDiv.style.height;\n                break;\n              case 180:\n                transX = \"-\" + textLayerDiv.style.width;\n                transY = \"-\" + textLayerDiv.style.height;\n                break;\n              case 270:\n                transX = \"-\" + textLayerDiv.style.width;\n                transY = 0;\n                break;\n              default:\n                console.error(\"Bad rotation value.\");\n                break;\n            }\n            textLayerDiv.style.transform = `rotate(${textAbsRotation}deg) ` + `scale(${scale}) ` + `translate(${transX}, ${transY})`;\n            textLayerDiv.style.transformOrigin = \"0% 0%\";\n          }\n          if (redrawAnnotationLayer && this.annotationLayer) {\n            this._renderAnnotationLayer();\n          }\n          if (redrawXfaLayer && this.xfaLayer) {\n            this._renderXfaLayer();\n          }\n        }\n        get width() {\n          return this.viewport.width;\n        }\n        get height() {\n          return this.viewport.height;\n        }\n        getPagePoint(x, y) {\n          return this.viewport.convertToPdfPoint(x, y);\n        }\n        toggleLoadingIconSpinner(viewVisible = false) {\n          this.loadingIconDiv?.classList.toggle(\"notVisible\", !viewVisible);\n        }\n        draw() {\n          var _this8 = this;\n          if (this.renderingState !== _ui_utils.RenderingStates.INITIAL) {\n            console.error(\"Must be in new state before drawing\");\n            this.reset();\n          }\n          const {\n            div,\n            pdfPage\n          } = this;\n          if (!pdfPage) {\n            this.renderingState = _ui_utils.RenderingStates.FINISHED;\n            if (this.loadingIconDiv) {\n              this.loadingIconDiv.remove();\n              delete this.loadingIconDiv;\n            }\n            return Promise.reject(new Error(\"pdfPage is not loaded\"));\n          }\n          this.renderingState = _ui_utils.RenderingStates.RUNNING;\n          const canvasWrapper = document.createElement(\"div\");\n          canvasWrapper.style.width = div.style.width;\n          canvasWrapper.style.height = div.style.height;\n          canvasWrapper.classList.add(\"canvasWrapper\");\n          if (this.annotationLayer?.div) {\n            div.insertBefore(canvasWrapper, this.annotationLayer.div);\n          } else {\n            div.appendChild(canvasWrapper);\n          }\n          let textLayer = null;\n          if (this.textLayerMode !== _ui_utils.TextLayerMode.DISABLE && this.textLayerFactory) {\n            const textLayerDiv = document.createElement(\"div\");\n            textLayerDiv.className = \"textLayer\";\n            textLayerDiv.style.width = canvasWrapper.style.width;\n            textLayerDiv.style.height = canvasWrapper.style.height;\n            if (this.annotationLayer?.div) {\n              div.insertBefore(textLayerDiv, this.annotationLayer.div);\n            } else {\n              div.appendChild(textLayerDiv);\n            }\n            textLayer = this.textLayerFactory.createTextLayerBuilder(textLayerDiv, this.id - 1, this.viewport, this.textLayerMode === _ui_utils.TextLayerMode.ENABLE_ENHANCE, this.eventBus, this.textHighlighter);\n          }\n          this.textLayer = textLayer;\n          if (this.#annotationMode !== _pdfjsLib.AnnotationMode.DISABLE && this.annotationLayerFactory) {\n            this._annotationCanvasMap ||= new Map();\n            this.annotationLayer ||= this.annotationLayerFactory.createAnnotationLayerBuilder(div, pdfPage, null, this.imageResourcesPath, this.#annotationMode === _pdfjsLib.AnnotationMode.ENABLE_FORMS, this.l10n, null, null, null, null, this._annotationCanvasMap);\n          }\n          if (this.xfaLayer?.div) {\n            div.appendChild(this.xfaLayer.div);\n          }\n          let renderContinueCallback = null;\n          if (this.renderingQueue) {\n            renderContinueCallback = cont => {\n              if (!this.renderingQueue.isHighestPriority(this)) {\n                this.renderingState = _ui_utils.RenderingStates.PAUSED;\n                this.resume = () => {\n                  this.renderingState = _ui_utils.RenderingStates.RUNNING;\n                  cont();\n                };\n                return;\n              }\n              cont();\n            };\n          }\n          const finishPaintTask = /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator(function* (error = null) {\n              if (paintTask === _this8.paintTask) {\n                _this8.paintTask = null;\n              }\n              if (error instanceof _pdfjsLib.RenderingCancelledException) {\n                _this8._renderError = null;\n                return;\n              }\n              _this8._renderError = error;\n              _this8.renderingState = _ui_utils.RenderingStates.FINISHED;\n              if (_this8.loadingIconDiv) {\n                _this8.loadingIconDiv.remove();\n                delete _this8.loadingIconDiv;\n              }\n              _this8._resetZoomLayer(true);\n              _this8.eventBus.dispatch(\"pagerendered\", {\n                source: _this8,\n                pageNumber: _this8.id,\n                cssTransform: false,\n                timestamp: performance.now(),\n                error: _this8._renderError\n              });\n              if (error) {\n                throw error;\n              }\n            });\n            return function finishPaintTask() {\n              return _ref2.apply(this, arguments);\n            };\n          }();\n          const paintTask = this.renderer === _ui_utils.RendererType.SVG ? this.paintOnSvg(canvasWrapper) : this.paintOnCanvas(canvasWrapper);\n          paintTask.onRenderContinue = renderContinueCallback;\n          this.paintTask = paintTask;\n          const resultPromise = paintTask.promise.then(() => {\n            return finishPaintTask(null).then(() => {\n              if (textLayer) {\n                const readableStream = pdfPage.streamTextContent({\n                  includeMarkedContent: true\n                });\n                textLayer.setTextContentStream(readableStream);\n                textLayer.render();\n              }\n              if (this.annotationLayer) {\n                this._renderAnnotationLayer();\n              }\n            });\n          }, function (reason) {\n            return finishPaintTask(reason);\n          });\n          if (this.xfaLayerFactory) {\n            if (!this.xfaLayer) {\n              this.xfaLayer = this.xfaLayerFactory.createXfaLayerBuilder(div, pdfPage, null);\n            }\n            this._renderXfaLayer();\n          }\n          if (this.structTreeLayerFactory && this.textLayer && this.canvas) {\n            this._onTextLayerRendered = event => {\n              if (event.pageNumber !== this.id) {\n                return;\n              }\n              this.eventBus._off(\"textlayerrendered\", this._onTextLayerRendered);\n              this._onTextLayerRendered = null;\n              if (!this.canvas) {\n                return;\n              }\n              this.pdfPage.getStructTree().then(tree => {\n                if (!tree) {\n                  return;\n                }\n                if (!this.canvas) {\n                  return;\n                }\n                const treeDom = this.structTreeLayer.render(tree);\n                treeDom.classList.add(\"structTree\");\n                this.canvas.appendChild(treeDom);\n              });\n            };\n            this.eventBus._on(\"textlayerrendered\", this._onTextLayerRendered);\n            this.structTreeLayer = this.structTreeLayerFactory.createStructTreeLayerBuilder(pdfPage);\n          }\n          div.setAttribute(\"data-loaded\", true);\n          this.eventBus.dispatch(\"pagerender\", {\n            source: this,\n            pageNumber: this.id\n          });\n          return resultPromise;\n        }\n        paintOnCanvas(canvasWrapper) {\n          const renderCapability = (0, _pdfjsLib.createPromiseCapability)();\n          const result = {\n            promise: renderCapability.promise,\n            onRenderContinue(cont) {\n              cont();\n            },\n            cancel() {\n              renderTask.cancel();\n            }\n          };\n          const viewport = this.viewport;\n          const canvas = document.createElement(\"canvas\");\n          canvas.hidden = true;\n          let isCanvasHidden = true;\n          const showCanvas = function () {\n            if (isCanvasHidden) {\n              canvas.hidden = false;\n              isCanvasHidden = false;\n            }\n          };\n          canvasWrapper.appendChild(canvas);\n          this.canvas = canvas;\n          const ctx = canvas.getContext(\"2d\", {\n            alpha: false\n          });\n          const outputScale = this.outputScale = new _ui_utils.OutputScale();\n          if (this.useOnlyCssZoom) {\n            const actualSizeViewport = viewport.clone({\n              scale: _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS\n            });\n            outputScale.sx *= actualSizeViewport.width / viewport.width;\n            outputScale.sy *= actualSizeViewport.height / viewport.height;\n          }\n          if (this.maxCanvasPixels > 0) {\n            const pixelsInViewport = viewport.width * viewport.height;\n            const maxScale = Math.sqrt(this.maxCanvasPixels / pixelsInViewport);\n            if (outputScale.sx > maxScale || outputScale.sy > maxScale) {\n              outputScale.sx = maxScale;\n              outputScale.sy = maxScale;\n              this.hasRestrictedScaling = true;\n            } else {\n              this.hasRestrictedScaling = false;\n            }\n          }\n          const sfx = (0, _ui_utils.approximateFraction)(outputScale.sx);\n          const sfy = (0, _ui_utils.approximateFraction)(outputScale.sy);\n          canvas.width = (0, _ui_utils.roundToDivide)(viewport.width * outputScale.sx, sfx[0]);\n          canvas.height = (0, _ui_utils.roundToDivide)(viewport.height * outputScale.sy, sfy[0]);\n          canvas.style.width = (0, _ui_utils.roundToDivide)(viewport.width, sfx[1]) + \"px\";\n          canvas.style.height = (0, _ui_utils.roundToDivide)(viewport.height, sfy[1]) + \"px\";\n          this.paintedViewportMap.set(canvas, viewport);\n          const transform = outputScale.scaled ? [outputScale.sx, 0, 0, outputScale.sy, 0, 0] : null;\n          const renderContext = {\n            canvasContext: ctx,\n            transform,\n            viewport: this.viewport,\n            annotationMode: this.#annotationMode,\n            optionalContentConfigPromise: this._optionalContentConfigPromise,\n            annotationCanvasMap: this._annotationCanvasMap,\n            pageColors: this.pageColors\n          };\n          const renderTask = this.pdfPage.render(renderContext);\n          renderTask.onContinue = function (cont) {\n            showCanvas();\n            if (result.onRenderContinue) {\n              result.onRenderContinue(cont);\n            } else {\n              cont();\n            }\n          };\n          renderTask.promise.then(function () {\n            showCanvas();\n            renderCapability.resolve();\n          }, function (error) {\n            showCanvas();\n            renderCapability.reject(error);\n          });\n          return result;\n        }\n        paintOnSvg(wrapper) {\n          let cancelled = false;\n          const ensureNotCancelled = () => {\n            if (cancelled) {\n              throw new _pdfjsLib.RenderingCancelledException(`Rendering cancelled, page ${this.id}`, \"svg\");\n            }\n          };\n          const pdfPage = this.pdfPage;\n          const actualSizeViewport = this.viewport.clone({\n            scale: _pdfjsLib.PixelsPerInch.PDF_TO_CSS_UNITS\n          });\n          const promise = pdfPage.getOperatorList({\n            annotationMode: this.#annotationMode\n          }).then(opList => {\n            ensureNotCancelled();\n            const svgGfx = new _pdfjsLib.SVGGraphics(pdfPage.commonObjs, pdfPage.objs);\n            return svgGfx.getSVG(opList, actualSizeViewport).then(svg => {\n              ensureNotCancelled();\n              this.svg = svg;\n              this.paintedViewportMap.set(svg, actualSizeViewport);\n              svg.style.width = wrapper.style.width;\n              svg.style.height = wrapper.style.height;\n              this.renderingState = _ui_utils.RenderingStates.FINISHED;\n              wrapper.appendChild(svg);\n            });\n          });\n          return {\n            promise,\n            onRenderContinue(cont) {\n              cont();\n            },\n            cancel() {\n              cancelled = true;\n            }\n          };\n        }\n        setPageLabel(label) {\n          this.pageLabel = typeof label === \"string\" ? label : null;\n          if (this.pageLabel !== null) {\n            this.div.setAttribute(\"data-page-label\", this.pageLabel);\n          } else {\n            this.div.removeAttribute(\"data-page-label\");\n          }\n        }\n      }\n      exports.PDFPageView = PDFPageView;\n\n      /***/\n    }), (/* 13 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.compatibilityParams = exports.OptionKind = exports.AppOptions = void 0;\n      const compatibilityParams = Object.create(null);\n      exports.compatibilityParams = compatibilityParams;\n      {\n        const userAgent = navigator.userAgent || \"\";\n        const platform = navigator.platform || \"\";\n        const maxTouchPoints = navigator.maxTouchPoints || 1;\n        const isAndroid = /Android/.test(userAgent);\n        const isIOS = /\\b(iPad|iPhone|iPod)(?=;)/.test(userAgent) || platform === \"MacIntel\" && maxTouchPoints > 1;\n        (function checkCanvasSizeLimitation() {\n          if (isIOS || isAndroid) {\n            compatibilityParams.maxCanvasPixels = 5242880;\n          }\n        })();\n      }\n      const OptionKind = {\n        VIEWER: 0x02,\n        API: 0x04,\n        WORKER: 0x08,\n        PREFERENCE: 0x80\n      };\n      exports.OptionKind = OptionKind;\n      const defaultOptions = {\n        annotationMode: {\n          value: 2,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        cursorToolOnLoad: {\n          value: 0,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        defaultUrl: {\n          value: \"compressed.tracemonkey-pldi-09.pdf\",\n          kind: OptionKind.VIEWER\n        },\n        defaultZoomValue: {\n          value: \"\",\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        disableHistory: {\n          value: false,\n          kind: OptionKind.VIEWER\n        },\n        disablePageLabels: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        enablePermissions: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        enablePrintAutoRotate: {\n          value: true,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        enableScripting: {\n          value: true,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        externalLinkRel: {\n          value: \"noopener noreferrer nofollow\",\n          kind: OptionKind.VIEWER\n        },\n        externalLinkTarget: {\n          value: 0,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        historyUpdateUrl: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        ignoreDestinationZoom: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        imageResourcesPath: {\n          value: \"./images/\",\n          kind: OptionKind.VIEWER\n        },\n        maxCanvasPixels: {\n          value: 16777216,\n          compatibility: compatibilityParams.maxCanvasPixels,\n          kind: OptionKind.VIEWER\n        },\n        pageColorsBackground: {\n          value: \"Canvas\",\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        pageColorsForeground: {\n          value: \"CanvasText\",\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        pdfBugEnabled: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        printResolution: {\n          value: 150,\n          kind: OptionKind.VIEWER\n        },\n        renderer: {\n          value: \"canvas\",\n          kind: OptionKind.VIEWER\n        },\n        sidebarViewOnLoad: {\n          value: -1,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        scrollModeOnLoad: {\n          value: -1,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        spreadModeOnLoad: {\n          value: -1,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        textLayerMode: {\n          value: 1,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        useOnlyCssZoom: {\n          value: false,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        viewerCssTheme: {\n          value: 0,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        viewOnLoad: {\n          value: 0,\n          kind: OptionKind.VIEWER + OptionKind.PREFERENCE\n        },\n        cMapPacked: {\n          value: true,\n          kind: OptionKind.API\n        },\n        cMapUrl: {\n          value: \"../web/cmaps/\",\n          kind: OptionKind.API\n        },\n        disableAutoFetch: {\n          value: false,\n          kind: OptionKind.API + OptionKind.PREFERENCE\n        },\n        disableFontFace: {\n          value: false,\n          kind: OptionKind.API + OptionKind.PREFERENCE\n        },\n        disableRange: {\n          value: false,\n          kind: OptionKind.API + OptionKind.PREFERENCE\n        },\n        disableStream: {\n          value: false,\n          kind: OptionKind.API + OptionKind.PREFERENCE\n        },\n        docBaseUrl: {\n          value: \"\",\n          kind: OptionKind.API\n        },\n        enableXfa: {\n          value: true,\n          kind: OptionKind.API + OptionKind.PREFERENCE\n        },\n        fontExtraProperties: {\n          value: false,\n          kind: OptionKind.API\n        },\n        isEvalSupported: {\n          value: true,\n          kind: OptionKind.API\n        },\n        maxImageSize: {\n          value: -1,\n          kind: OptionKind.API\n        },\n        pdfBug: {\n          value: false,\n          kind: OptionKind.API\n        },\n        standardFontDataUrl: {\n          value: \"../web/standard_fonts/\",\n          kind: OptionKind.API\n        },\n        verbosity: {\n          value: 1,\n          kind: OptionKind.API\n        },\n        workerPort: {\n          value: null,\n          kind: OptionKind.WORKER\n        },\n        workerSrc: {\n          value: \"../build/pdf.worker.js\",\n          kind: OptionKind.WORKER\n        }\n      };\n      {\n        defaultOptions.disablePreferences = {\n          value: false,\n          kind: OptionKind.VIEWER\n        };\n        defaultOptions.locale = {\n          value: navigator.language || \"en-US\",\n          kind: OptionKind.VIEWER\n        };\n        defaultOptions.sandboxBundleSrc = {\n          value: \"../build/pdf.sandbox.js\",\n          kind: OptionKind.VIEWER\n        };\n        defaultOptions.renderer.kind += OptionKind.PREFERENCE;\n      }\n      const userOptions = Object.create(null);\n      class AppOptions {\n        constructor() {\n          throw new Error(\"Cannot initialize AppOptions.\");\n        }\n        static get(name) {\n          const userOption = userOptions[name];\n          if (userOption !== undefined) {\n            return userOption;\n          }\n          const defaultOption = defaultOptions[name];\n          if (defaultOption !== undefined) {\n            return defaultOption.compatibility ?? defaultOption.value;\n          }\n          return undefined;\n        }\n        static getAll(kind = null) {\n          const options = Object.create(null);\n          for (const name in defaultOptions) {\n            const defaultOption = defaultOptions[name];\n            if (kind) {\n              if ((kind & defaultOption.kind) === 0) {\n                continue;\n              }\n              if (kind === OptionKind.PREFERENCE) {\n                const value = defaultOption.value,\n                  valueType = typeof value;\n                if (valueType === \"boolean\" || valueType === \"string\" || valueType === \"number\" && Number.isInteger(value)) {\n                  options[name] = value;\n                  continue;\n                }\n                throw new Error(`Invalid type for preference: ${name}`);\n              }\n            }\n            const userOption = userOptions[name];\n            options[name] = userOption !== undefined ? userOption : defaultOption.compatibility ?? defaultOption.value;\n          }\n          return options;\n        }\n        static set(name, value) {\n          userOptions[name] = value;\n        }\n        static setAll(options) {\n          for (const name in options) {\n            userOptions[name] = options[name];\n          }\n        }\n        static remove(name) {\n          delete userOptions[name];\n        }\n        static _hasUserOptions() {\n          return Object.keys(userOptions).length > 0;\n        }\n      }\n      exports.AppOptions = AppOptions;\n\n      /***/\n    }), (/* 14 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFRenderingQueue = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      var _ui_utils = __w_pdfjs_require__(6);\n      const CLEANUP_TIMEOUT = 30000;\n      class PDFRenderingQueue {\n        constructor() {\n          this.pdfViewer = null;\n          this.pdfThumbnailViewer = null;\n          this.onIdle = null;\n          this.highestPriorityPage = null;\n          this.idleTimeout = null;\n          this.printing = false;\n          this.isThumbnailViewEnabled = false;\n        }\n        setViewer(pdfViewer) {\n          this.pdfViewer = pdfViewer;\n        }\n        setThumbnailViewer(pdfThumbnailViewer) {\n          this.pdfThumbnailViewer = pdfThumbnailViewer;\n        }\n        isHighestPriority(view) {\n          return this.highestPriorityPage === view.renderingId;\n        }\n        hasViewer() {\n          return !!this.pdfViewer;\n        }\n        renderHighestPriority(currentlyVisiblePages) {\n          if (this.idleTimeout) {\n            clearTimeout(this.idleTimeout);\n            this.idleTimeout = null;\n          }\n          if (this.pdfViewer.forceRendering(currentlyVisiblePages)) {\n            return;\n          }\n          if (this.isThumbnailViewEnabled && this.pdfThumbnailViewer?.forceRendering()) {\n            return;\n          }\n          if (this.printing) {\n            return;\n          }\n          if (this.onIdle) {\n            this.idleTimeout = setTimeout(this.onIdle.bind(this), CLEANUP_TIMEOUT);\n          }\n        }\n        getHighestPriority(visible, views, scrolledDown, preRenderExtra = false) {\n          const visibleViews = visible.views,\n            numVisible = visibleViews.length;\n          if (numVisible === 0) {\n            return null;\n          }\n          for (let i = 0; i < numVisible; i++) {\n            const view = visibleViews[i].view;\n            if (!this.isViewFinished(view)) {\n              return view;\n            }\n          }\n          const firstId = visible.first.id,\n            lastId = visible.last.id;\n          if (lastId - firstId + 1 > numVisible) {\n            const visibleIds = visible.ids;\n            for (let i = 1, ii = lastId - firstId; i < ii; i++) {\n              const holeId = scrolledDown ? firstId + i : lastId - i;\n              if (visibleIds.has(holeId)) {\n                continue;\n              }\n              const holeView = views[holeId - 1];\n              if (!this.isViewFinished(holeView)) {\n                return holeView;\n              }\n            }\n          }\n          let preRenderIndex = scrolledDown ? lastId : firstId - 2;\n          let preRenderView = views[preRenderIndex];\n          if (preRenderView && !this.isViewFinished(preRenderView)) {\n            return preRenderView;\n          }\n          if (preRenderExtra) {\n            preRenderIndex += scrolledDown ? 1 : -1;\n            preRenderView = views[preRenderIndex];\n            if (preRenderView && !this.isViewFinished(preRenderView)) {\n              return preRenderView;\n            }\n          }\n          return null;\n        }\n        isViewFinished(view) {\n          return view.renderingState === _ui_utils.RenderingStates.FINISHED;\n        }\n        renderView(view) {\n          switch (view.renderingState) {\n            case _ui_utils.RenderingStates.FINISHED:\n              return false;\n            case _ui_utils.RenderingStates.PAUSED:\n              this.highestPriorityPage = view.renderingId;\n              view.resume();\n              break;\n            case _ui_utils.RenderingStates.RUNNING:\n              this.highestPriorityPage = view.renderingId;\n              break;\n            case _ui_utils.RenderingStates.INITIAL:\n              this.highestPriorityPage = view.renderingId;\n              view.draw().finally(() => {\n                this.renderHighestPriority();\n              }).catch(reason => {\n                if (reason instanceof _pdfjsLib.RenderingCancelledException) {\n                  return;\n                }\n                console.error(`renderView: \"${reason}\"`);\n              });\n              break;\n          }\n          return true;\n        }\n      }\n      exports.PDFRenderingQueue = PDFRenderingQueue;\n\n      /***/\n    }), (/* 15 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.TextHighlighter = void 0;\n      class TextHighlighter {\n        constructor({\n          findController,\n          eventBus,\n          pageIndex\n        }) {\n          this.findController = findController;\n          this.matches = [];\n          this.eventBus = eventBus;\n          this.pageIdx = pageIndex;\n          this._onUpdateTextLayerMatches = null;\n          this.textDivs = null;\n          this.textContentItemsStr = null;\n          this.enabled = false;\n        }\n        setTextMapping(divs, texts) {\n          this.textDivs = divs;\n          this.textContentItemsStr = texts;\n        }\n        enable() {\n          if (!this.textDivs || !this.textContentItemsStr) {\n            throw new Error(\"Text divs and strings have not been set.\");\n          }\n          if (this.enabled) {\n            throw new Error(\"TextHighlighter is already enabled.\");\n          }\n          this.enabled = true;\n          if (!this._onUpdateTextLayerMatches) {\n            this._onUpdateTextLayerMatches = evt => {\n              if (evt.pageIndex === this.pageIdx || evt.pageIndex === -1) {\n                this._updateMatches();\n              }\n            };\n            this.eventBus._on(\"updatetextlayermatches\", this._onUpdateTextLayerMatches);\n          }\n          this._updateMatches();\n        }\n        disable() {\n          if (!this.enabled) {\n            return;\n          }\n          this.enabled = false;\n          if (this._onUpdateTextLayerMatches) {\n            this.eventBus._off(\"updatetextlayermatches\", this._onUpdateTextLayerMatches);\n            this._onUpdateTextLayerMatches = null;\n          }\n        }\n        _convertMatches(matches, matchesLength) {\n          if (!matches) {\n            return [];\n          }\n          const {\n            textContentItemsStr\n          } = this;\n          let i = 0,\n            iIndex = 0;\n          const end = textContentItemsStr.length - 1;\n          const result = [];\n          for (let m = 0, mm = matches.length; m < mm; m++) {\n            let matchIdx = matches[m];\n            while (i !== end && matchIdx >= iIndex + textContentItemsStr[i].length) {\n              iIndex += textContentItemsStr[i].length;\n              i++;\n            }\n            if (i === textContentItemsStr.length) {\n              console.error(\"Could not find a matching mapping\");\n            }\n            const match = {\n              begin: {\n                divIdx: i,\n                offset: matchIdx - iIndex\n              }\n            };\n            matchIdx += matchesLength[m];\n            while (i !== end && matchIdx > iIndex + textContentItemsStr[i].length) {\n              iIndex += textContentItemsStr[i].length;\n              i++;\n            }\n            match.end = {\n              divIdx: i,\n              offset: matchIdx - iIndex\n            };\n            result.push(match);\n          }\n          return result;\n        }\n        _renderMatches(matches) {\n          if (matches.length === 0) {\n            return;\n          }\n          const {\n            findController,\n            pageIdx\n          } = this;\n          const {\n            textContentItemsStr,\n            textDivs\n          } = this;\n          const isSelectedPage = pageIdx === findController.selected.pageIdx;\n          const selectedMatchIdx = findController.selected.matchIdx;\n          const highlightAll = findController.state.highlightAll;\n          let prevEnd = null;\n          const infinity = {\n            divIdx: -1,\n            offset: undefined\n          };\n          function beginText(begin, className) {\n            const divIdx = begin.divIdx;\n            textDivs[divIdx].textContent = \"\";\n            return appendTextToDiv(divIdx, 0, begin.offset, className);\n          }\n          function appendTextToDiv(divIdx, fromOffset, toOffset, className) {\n            let div = textDivs[divIdx];\n            if (div.nodeType === Node.TEXT_NODE) {\n              const span = document.createElement(\"span\");\n              div.parentNode.insertBefore(span, div);\n              span.appendChild(div);\n              textDivs[divIdx] = span;\n              div = span;\n            }\n            const content = textContentItemsStr[divIdx].substring(fromOffset, toOffset);\n            const node = document.createTextNode(content);\n            if (className) {\n              const span = document.createElement(\"span\");\n              span.className = `${className} appended`;\n              span.appendChild(node);\n              div.appendChild(span);\n              return className.includes(\"selected\") ? span.offsetLeft : 0;\n            }\n            div.appendChild(node);\n            return 0;\n          }\n          let i0 = selectedMatchIdx,\n            i1 = i0 + 1;\n          if (highlightAll) {\n            i0 = 0;\n            i1 = matches.length;\n          } else if (!isSelectedPage) {\n            return;\n          }\n          for (let i = i0; i < i1; i++) {\n            const match = matches[i];\n            const begin = match.begin;\n            const end = match.end;\n            const isSelected = isSelectedPage && i === selectedMatchIdx;\n            const highlightSuffix = isSelected ? \" selected\" : \"\";\n            let selectedLeft = 0;\n            if (!prevEnd || begin.divIdx !== prevEnd.divIdx) {\n              if (prevEnd !== null) {\n                appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset);\n              }\n              beginText(begin);\n            } else {\n              appendTextToDiv(prevEnd.divIdx, prevEnd.offset, begin.offset);\n            }\n            if (begin.divIdx === end.divIdx) {\n              selectedLeft = appendTextToDiv(begin.divIdx, begin.offset, end.offset, \"highlight\" + highlightSuffix);\n            } else {\n              selectedLeft = appendTextToDiv(begin.divIdx, begin.offset, infinity.offset, \"highlight begin\" + highlightSuffix);\n              for (let n0 = begin.divIdx + 1, n1 = end.divIdx; n0 < n1; n0++) {\n                textDivs[n0].className = \"highlight middle\" + highlightSuffix;\n              }\n              beginText(end, \"highlight end\" + highlightSuffix);\n            }\n            prevEnd = end;\n            if (isSelected) {\n              findController.scrollMatchIntoView({\n                element: textDivs[begin.divIdx],\n                selectedLeft,\n                pageIndex: pageIdx,\n                matchIndex: selectedMatchIdx\n              });\n            }\n          }\n          if (prevEnd) {\n            appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset);\n          }\n        }\n        _updateMatches() {\n          if (!this.enabled) {\n            return;\n          }\n          const {\n            findController,\n            matches,\n            pageIdx\n          } = this;\n          const {\n            textContentItemsStr,\n            textDivs\n          } = this;\n          let clearedUntilDivIdx = -1;\n          for (let i = 0, ii = matches.length; i < ii; i++) {\n            const match = matches[i];\n            const begin = Math.max(clearedUntilDivIdx, match.begin.divIdx);\n            for (let n = begin, end = match.end.divIdx; n <= end; n++) {\n              const div = textDivs[n];\n              div.textContent = textContentItemsStr[n];\n              div.className = \"\";\n            }\n            clearedUntilDivIdx = match.end.divIdx + 1;\n          }\n          if (!findController?.highlightMatches) {\n            return;\n          }\n          const pageMatches = findController.pageMatches[pageIdx] || null;\n          const pageMatchesLength = findController.pageMatchesLength[pageIdx] || null;\n          this.matches = this._convertMatches(pageMatches, pageMatchesLength);\n          this._renderMatches(this.matches);\n        }\n      }\n      exports.TextHighlighter = TextHighlighter;\n\n      /***/\n    }), (/* 16 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.DownloadManager = void 0;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      ;\n      function download(blobUrl, filename) {\n        const a = document.createElement(\"a\");\n        if (!a.click) {\n          throw new Error('DownloadManager: \"a.click()\" is not supported.');\n        }\n        a.href = blobUrl;\n        a.target = \"_parent\";\n        if (\"download\" in a) {\n          a.download = filename;\n        }\n        (document.body || document.documentElement).appendChild(a);\n        a.click();\n        a.remove();\n      }\n      class DownloadManager {\n        constructor() {\n          this._openBlobUrls = new WeakMap();\n        }\n        downloadUrl(url, filename) {\n          if (!(0, _pdfjsLib.createValidAbsoluteUrl)(url, \"http://example.com\")) {\n            console.error(`downloadUrl - not a valid URL: ${url}`);\n            return;\n          }\n          download(url + \"#pdfjs.action=download\", filename);\n        }\n        downloadData(data, filename, contentType) {\n          const blobUrl = URL.createObjectURL(new Blob([data], {\n            type: contentType\n          }));\n          download(blobUrl, filename);\n        }\n        openOrDownloadData(element, data, filename) {\n          const isPdfData = (0, _pdfjsLib.isPdfFile)(filename);\n          const contentType = isPdfData ? \"application/pdf\" : \"\";\n          if (isPdfData) {\n            let blobUrl = this._openBlobUrls.get(element);\n            if (!blobUrl) {\n              blobUrl = URL.createObjectURL(new Blob([data], {\n                type: contentType\n              }));\n              this._openBlobUrls.set(element, blobUrl);\n            }\n            let viewerUrl;\n            viewerUrl = \"?file=\" + encodeURIComponent(blobUrl + \"#\" + filename);\n            try {\n              window.open(viewerUrl);\n              return true;\n            } catch (ex) {\n              console.error(`openOrDownloadData: ${ex}`);\n              URL.revokeObjectURL(blobUrl);\n              this._openBlobUrls.delete(element);\n            }\n          }\n          this.downloadData(data, filename, contentType);\n          return false;\n        }\n        download(blob, url, filename, sourceEventType = \"download\") {\n          const blobUrl = URL.createObjectURL(blob);\n          download(blobUrl, filename);\n        }\n      }\n      exports.DownloadManager = DownloadManager;\n\n      /***/\n    }), (/* 17 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.WaitOnType = exports.EventBus = exports.AutomationEventBus = void 0;\n      exports.waitOnEventOrTimeout = waitOnEventOrTimeout;\n      const WaitOnType = {\n        EVENT: \"event\",\n        TIMEOUT: \"timeout\"\n      };\n      exports.WaitOnType = WaitOnType;\n      function waitOnEventOrTimeout({\n        target,\n        name,\n        delay = 0\n      }) {\n        return new Promise(function (resolve, reject) {\n          if (typeof target !== \"object\" || !(name && typeof name === \"string\") || !(Number.isInteger(delay) && delay >= 0)) {\n            throw new Error(\"waitOnEventOrTimeout - invalid parameters.\");\n          }\n          function handler(type) {\n            if (target instanceof EventBus) {\n              target._off(name, eventHandler);\n            } else {\n              target.removeEventListener(name, eventHandler);\n            }\n            if (timeout) {\n              clearTimeout(timeout);\n            }\n            resolve(type);\n          }\n          const eventHandler = handler.bind(null, WaitOnType.EVENT);\n          if (target instanceof EventBus) {\n            target._on(name, eventHandler);\n          } else {\n            target.addEventListener(name, eventHandler);\n          }\n          const timeoutHandler = handler.bind(null, WaitOnType.TIMEOUT);\n          const timeout = setTimeout(timeoutHandler, delay);\n        });\n      }\n      class EventBus {\n        constructor() {\n          this._listeners = Object.create(null);\n        }\n        on(eventName, listener, options = null) {\n          this._on(eventName, listener, {\n            external: true,\n            once: options?.once\n          });\n        }\n        off(eventName, listener, options = null) {\n          this._off(eventName, listener, {\n            external: true,\n            once: options?.once\n          });\n        }\n        dispatch(eventName, data) {\n          const eventListeners = this._listeners[eventName];\n          if (!eventListeners || eventListeners.length === 0) {\n            return;\n          }\n          let externalListeners;\n          for (const {\n            listener,\n            external,\n            once\n          } of eventListeners.slice(0)) {\n            if (once) {\n              this._off(eventName, listener);\n            }\n            if (external) {\n              (externalListeners ||= []).push(listener);\n              continue;\n            }\n            listener(data);\n          }\n          if (externalListeners) {\n            for (const listener of externalListeners) {\n              listener(data);\n            }\n            externalListeners = null;\n          }\n        }\n        _on(eventName, listener, options = null) {\n          const eventListeners = this._listeners[eventName] ||= [];\n          eventListeners.push({\n            listener,\n            external: options?.external === true,\n            once: options?.once === true\n          });\n        }\n        _off(eventName, listener, options = null) {\n          const eventListeners = this._listeners[eventName];\n          if (!eventListeners) {\n            return;\n          }\n          for (let i = 0, ii = eventListeners.length; i < ii; i++) {\n            if (eventListeners[i].listener === listener) {\n              eventListeners.splice(i, 1);\n              return;\n            }\n          }\n        }\n      }\n      exports.EventBus = EventBus;\n      class AutomationEventBus extends EventBus {\n        dispatch(eventName, data) {\n          throw new Error(\"Not implemented: AutomationEventBus.dispatch\");\n        }\n      }\n      exports.AutomationEventBus = AutomationEventBus;\n\n      /***/\n    }), (/* 18 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.GenericL10n = void 0;\n      __w_pdfjs_require__(19);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      const webL10n = document.webL10n;\n      class GenericL10n {\n        constructor(lang) {\n          this._lang = lang;\n          this._ready = new Promise((resolve, reject) => {\n            webL10n.setLanguage((0, _l10n_utils.fixupLangCode)(lang), () => {\n              resolve(webL10n);\n            });\n          });\n        }\n        getLanguage() {\n          var _this9 = this;\n          return _asyncToGenerator(function* () {\n            const l10n = yield _this9._ready;\n            return l10n.getLanguage();\n          })();\n        }\n        getDirection() {\n          var _this0 = this;\n          return _asyncToGenerator(function* () {\n            const l10n = yield _this0._ready;\n            return l10n.getDirection();\n          })();\n        }\n        get(_x3) {\n          var _this1 = this;\n          return _asyncToGenerator(function* (key, args = null, fallback = (0, _l10n_utils.getL10nFallback)(key, args)) {\n            const l10n = yield _this1._ready;\n            return l10n.get(key, args, fallback);\n          }).apply(this, arguments);\n        }\n        translate(element) {\n          var _this10 = this;\n          return _asyncToGenerator(function* () {\n            const l10n = yield _this10._ready;\n            return l10n.translate(element);\n          })();\n        }\n      }\n      exports.GenericL10n = GenericL10n;\n\n      /***/\n    }), (/* 19 */\n    /***/() => {\n      document.webL10n = function (window, document, undefined) {\n        var gL10nData = {};\n        var gTextData = '';\n        var gTextProp = 'textContent';\n        var gLanguage = '';\n        var gMacros = {};\n        var gReadyState = 'loading';\n        var gAsyncResourceLoading = true;\n        function getL10nResourceLinks() {\n          return document.querySelectorAll('link[type=\"application/l10n\"]');\n        }\n        function getL10nDictionary() {\n          var script = document.querySelector('script[type=\"application/l10n\"]');\n          return script ? JSON.parse(script.innerHTML) : null;\n        }\n        function getTranslatableChildren(element) {\n          return element ? element.querySelectorAll('*[data-l10n-id]') : [];\n        }\n        function getL10nAttributes(element) {\n          if (!element) return {};\n          var l10nId = element.getAttribute('data-l10n-id');\n          var l10nArgs = element.getAttribute('data-l10n-args');\n          var args = {};\n          if (l10nArgs) {\n            try {\n              args = JSON.parse(l10nArgs);\n            } catch (e) {\n              console.warn('could not parse arguments for #' + l10nId);\n            }\n          }\n          return {\n            id: l10nId,\n            args: args\n          };\n        }\n        function xhrLoadText(url, onSuccess, onFailure) {\n          onSuccess = onSuccess || function _onSuccess(data) {};\n          onFailure = onFailure || function _onFailure() {};\n          var xhr = new XMLHttpRequest();\n          xhr.open('GET', url, gAsyncResourceLoading);\n          if (xhr.overrideMimeType) {\n            xhr.overrideMimeType('text/plain; charset=utf-8');\n          }\n          xhr.onreadystatechange = function () {\n            if (xhr.readyState == 4) {\n              if (xhr.status == 200 || xhr.status === 0) {\n                onSuccess(xhr.responseText);\n              } else {\n                onFailure();\n              }\n            }\n          };\n          xhr.onerror = onFailure;\n          xhr.ontimeout = onFailure;\n          try {\n            xhr.send(null);\n          } catch (e) {\n            onFailure();\n          }\n        }\n        function parseResource(href, lang, successCallback, failureCallback) {\n          var baseURL = href.replace(/[^\\/]*$/, '') || './';\n          function evalString(text) {\n            if (text.lastIndexOf('\\\\') < 0) return text;\n            return text.replace(/\\\\\\\\/g, '\\\\').replace(/\\\\n/g, '\\n').replace(/\\\\r/g, '\\r').replace(/\\\\t/g, '\\t').replace(/\\\\b/g, '\\b').replace(/\\\\f/g, '\\f').replace(/\\\\{/g, '{').replace(/\\\\}/g, '}').replace(/\\\\\"/g, '\"').replace(/\\\\'/g, \"'\");\n          }\n          function parseProperties(text, parsedPropertiesCallback) {\n            var dictionary = {};\n            var reBlank = /^\\s*|\\s*$/;\n            var reComment = /^\\s*#|^\\s*$/;\n            var reSection = /^\\s*\\[(.*)\\]\\s*$/;\n            var reImport = /^\\s*@import\\s+url\\((.*)\\)\\s*$/i;\n            var reSplit = /^([^=\\s]*)\\s*=\\s*(.+)$/;\n            function parseRawLines(rawText, extendedSyntax, parsedRawLinesCallback) {\n              var entries = rawText.replace(reBlank, '').split(/[\\r\\n]+/);\n              var currentLang = '*';\n              var genericLang = lang.split('-', 1)[0];\n              var skipLang = false;\n              var match = '';\n              function nextEntry() {\n                while (true) {\n                  if (!entries.length) {\n                    parsedRawLinesCallback();\n                    return;\n                  }\n                  var line = entries.shift();\n                  if (reComment.test(line)) continue;\n                  if (extendedSyntax) {\n                    match = reSection.exec(line);\n                    if (match) {\n                      currentLang = match[1].toLowerCase();\n                      skipLang = currentLang !== '*' && currentLang !== lang && currentLang !== genericLang;\n                      continue;\n                    } else if (skipLang) {\n                      continue;\n                    }\n                    match = reImport.exec(line);\n                    if (match) {\n                      loadImport(baseURL + match[1], nextEntry);\n                      return;\n                    }\n                  }\n                  var tmp = line.match(reSplit);\n                  if (tmp && tmp.length == 3) {\n                    dictionary[tmp[1]] = evalString(tmp[2]);\n                  }\n                }\n              }\n              nextEntry();\n            }\n            function loadImport(url, callback) {\n              xhrLoadText(url, function (content) {\n                parseRawLines(content, false, callback);\n              }, function () {\n                console.warn(url + ' not found.');\n                callback();\n              });\n            }\n            parseRawLines(text, true, function () {\n              parsedPropertiesCallback(dictionary);\n            });\n          }\n          xhrLoadText(href, function (response) {\n            gTextData += response;\n            parseProperties(response, function (data) {\n              for (var key in data) {\n                var id,\n                  prop,\n                  index = key.lastIndexOf('.');\n                if (index > 0) {\n                  id = key.substring(0, index);\n                  prop = key.substring(index + 1);\n                } else {\n                  id = key;\n                  prop = gTextProp;\n                }\n                if (!gL10nData[id]) {\n                  gL10nData[id] = {};\n                }\n                gL10nData[id][prop] = data[key];\n              }\n              if (successCallback) {\n                successCallback();\n              }\n            });\n          }, failureCallback);\n        }\n        function loadLocale(lang, callback) {\n          if (lang) {\n            lang = lang.toLowerCase();\n          }\n          callback = callback || function _callback() {};\n          clear();\n          gLanguage = lang;\n          var langLinks = getL10nResourceLinks();\n          var langCount = langLinks.length;\n          if (langCount === 0) {\n            var dict = getL10nDictionary();\n            if (dict && dict.locales && dict.default_locale) {\n              console.log('using the embedded JSON directory, early way out');\n              gL10nData = dict.locales[lang];\n              if (!gL10nData) {\n                var defaultLocale = dict.default_locale.toLowerCase();\n                for (var anyCaseLang in dict.locales) {\n                  anyCaseLang = anyCaseLang.toLowerCase();\n                  if (anyCaseLang === lang) {\n                    gL10nData = dict.locales[lang];\n                    break;\n                  } else if (anyCaseLang === defaultLocale) {\n                    gL10nData = dict.locales[defaultLocale];\n                  }\n                }\n              }\n              callback();\n            } else {\n              console.log('no resource to load, early way out');\n            }\n            gReadyState = 'complete';\n            return;\n          }\n          var onResourceLoaded = null;\n          var gResourceCount = 0;\n          onResourceLoaded = function () {\n            gResourceCount++;\n            if (gResourceCount >= langCount) {\n              callback();\n              gReadyState = 'complete';\n            }\n          };\n          function L10nResourceLink(link) {\n            var href = link.href;\n            this.load = function (lang, callback) {\n              parseResource(href, lang, callback, function () {\n                console.warn(href + ' not found.');\n                console.warn('\"' + lang + '\" resource not found');\n                gLanguage = '';\n                callback();\n              });\n            };\n          }\n          for (var i = 0; i < langCount; i++) {\n            var resource = new L10nResourceLink(langLinks[i]);\n            resource.load(lang, onResourceLoaded);\n          }\n        }\n        function clear() {\n          gL10nData = {};\n          gTextData = '';\n          gLanguage = '';\n        }\n        function getPluralRules(lang) {\n          var locales2rules = {\n            'af': 3,\n            'ak': 4,\n            'am': 4,\n            'ar': 1,\n            'asa': 3,\n            'az': 0,\n            'be': 11,\n            'bem': 3,\n            'bez': 3,\n            'bg': 3,\n            'bh': 4,\n            'bm': 0,\n            'bn': 3,\n            'bo': 0,\n            'br': 20,\n            'brx': 3,\n            'bs': 11,\n            'ca': 3,\n            'cgg': 3,\n            'chr': 3,\n            'cs': 12,\n            'cy': 17,\n            'da': 3,\n            'de': 3,\n            'dv': 3,\n            'dz': 0,\n            'ee': 3,\n            'el': 3,\n            'en': 3,\n            'eo': 3,\n            'es': 3,\n            'et': 3,\n            'eu': 3,\n            'fa': 0,\n            'ff': 5,\n            'fi': 3,\n            'fil': 4,\n            'fo': 3,\n            'fr': 5,\n            'fur': 3,\n            'fy': 3,\n            'ga': 8,\n            'gd': 24,\n            'gl': 3,\n            'gsw': 3,\n            'gu': 3,\n            'guw': 4,\n            'gv': 23,\n            'ha': 3,\n            'haw': 3,\n            'he': 2,\n            'hi': 4,\n            'hr': 11,\n            'hu': 0,\n            'id': 0,\n            'ig': 0,\n            'ii': 0,\n            'is': 3,\n            'it': 3,\n            'iu': 7,\n            'ja': 0,\n            'jmc': 3,\n            'jv': 0,\n            'ka': 0,\n            'kab': 5,\n            'kaj': 3,\n            'kcg': 3,\n            'kde': 0,\n            'kea': 0,\n            'kk': 3,\n            'kl': 3,\n            'km': 0,\n            'kn': 0,\n            'ko': 0,\n            'ksb': 3,\n            'ksh': 21,\n            'ku': 3,\n            'kw': 7,\n            'lag': 18,\n            'lb': 3,\n            'lg': 3,\n            'ln': 4,\n            'lo': 0,\n            'lt': 10,\n            'lv': 6,\n            'mas': 3,\n            'mg': 4,\n            'mk': 16,\n            'ml': 3,\n            'mn': 3,\n            'mo': 9,\n            'mr': 3,\n            'ms': 0,\n            'mt': 15,\n            'my': 0,\n            'nah': 3,\n            'naq': 7,\n            'nb': 3,\n            'nd': 3,\n            'ne': 3,\n            'nl': 3,\n            'nn': 3,\n            'no': 3,\n            'nr': 3,\n            'nso': 4,\n            'ny': 3,\n            'nyn': 3,\n            'om': 3,\n            'or': 3,\n            'pa': 3,\n            'pap': 3,\n            'pl': 13,\n            'ps': 3,\n            'pt': 3,\n            'rm': 3,\n            'ro': 9,\n            'rof': 3,\n            'ru': 11,\n            'rwk': 3,\n            'sah': 0,\n            'saq': 3,\n            'se': 7,\n            'seh': 3,\n            'ses': 0,\n            'sg': 0,\n            'sh': 11,\n            'shi': 19,\n            'sk': 12,\n            'sl': 14,\n            'sma': 7,\n            'smi': 7,\n            'smj': 7,\n            'smn': 7,\n            'sms': 7,\n            'sn': 3,\n            'so': 3,\n            'sq': 3,\n            'sr': 11,\n            'ss': 3,\n            'ssy': 3,\n            'st': 3,\n            'sv': 3,\n            'sw': 3,\n            'syr': 3,\n            'ta': 3,\n            'te': 3,\n            'teo': 3,\n            'th': 0,\n            'ti': 4,\n            'tig': 3,\n            'tk': 3,\n            'tl': 4,\n            'tn': 3,\n            'to': 0,\n            'tr': 0,\n            'ts': 3,\n            'tzm': 22,\n            'uk': 11,\n            'ur': 3,\n            've': 3,\n            'vi': 0,\n            'vun': 3,\n            'wa': 4,\n            'wae': 3,\n            'wo': 0,\n            'xh': 3,\n            'xog': 3,\n            'yo': 0,\n            'zh': 0,\n            'zu': 3\n          };\n          function isIn(n, list) {\n            return list.indexOf(n) !== -1;\n          }\n          function isBetween(n, start, end) {\n            return start <= n && n <= end;\n          }\n          var pluralRules = {\n            '0': function (n) {\n              return 'other';\n            },\n            '1': function (n) {\n              if (isBetween(n % 100, 3, 10)) return 'few';\n              if (n === 0) return 'zero';\n              if (isBetween(n % 100, 11, 99)) return 'many';\n              if (n == 2) return 'two';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '2': function (n) {\n              if (n !== 0 && n % 10 === 0) return 'many';\n              if (n == 2) return 'two';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '3': function (n) {\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '4': function (n) {\n              if (isBetween(n, 0, 1)) return 'one';\n              return 'other';\n            },\n            '5': function (n) {\n              if (isBetween(n, 0, 2) && n != 2) return 'one';\n              return 'other';\n            },\n            '6': function (n) {\n              if (n === 0) return 'zero';\n              if (n % 10 == 1 && n % 100 != 11) return 'one';\n              return 'other';\n            },\n            '7': function (n) {\n              if (n == 2) return 'two';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '8': function (n) {\n              if (isBetween(n, 3, 6)) return 'few';\n              if (isBetween(n, 7, 10)) return 'many';\n              if (n == 2) return 'two';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '9': function (n) {\n              if (n === 0 || n != 1 && isBetween(n % 100, 1, 19)) return 'few';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '10': function (n) {\n              if (isBetween(n % 10, 2, 9) && !isBetween(n % 100, 11, 19)) return 'few';\n              if (n % 10 == 1 && !isBetween(n % 100, 11, 19)) return 'one';\n              return 'other';\n            },\n            '11': function (n) {\n              if (isBetween(n % 10, 2, 4) && !isBetween(n % 100, 12, 14)) return 'few';\n              if (n % 10 === 0 || isBetween(n % 10, 5, 9) || isBetween(n % 100, 11, 14)) return 'many';\n              if (n % 10 == 1 && n % 100 != 11) return 'one';\n              return 'other';\n            },\n            '12': function (n) {\n              if (isBetween(n, 2, 4)) return 'few';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '13': function (n) {\n              if (isBetween(n % 10, 2, 4) && !isBetween(n % 100, 12, 14)) return 'few';\n              if (n != 1 && isBetween(n % 10, 0, 1) || isBetween(n % 10, 5, 9) || isBetween(n % 100, 12, 14)) return 'many';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '14': function (n) {\n              if (isBetween(n % 100, 3, 4)) return 'few';\n              if (n % 100 == 2) return 'two';\n              if (n % 100 == 1) return 'one';\n              return 'other';\n            },\n            '15': function (n) {\n              if (n === 0 || isBetween(n % 100, 2, 10)) return 'few';\n              if (isBetween(n % 100, 11, 19)) return 'many';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '16': function (n) {\n              if (n % 10 == 1 && n != 11) return 'one';\n              return 'other';\n            },\n            '17': function (n) {\n              if (n == 3) return 'few';\n              if (n === 0) return 'zero';\n              if (n == 6) return 'many';\n              if (n == 2) return 'two';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '18': function (n) {\n              if (n === 0) return 'zero';\n              if (isBetween(n, 0, 2) && n !== 0 && n != 2) return 'one';\n              return 'other';\n            },\n            '19': function (n) {\n              if (isBetween(n, 2, 10)) return 'few';\n              if (isBetween(n, 0, 1)) return 'one';\n              return 'other';\n            },\n            '20': function (n) {\n              if ((isBetween(n % 10, 3, 4) || n % 10 == 9) && !(isBetween(n % 100, 10, 19) || isBetween(n % 100, 70, 79) || isBetween(n % 100, 90, 99))) return 'few';\n              if (n % 1000000 === 0 && n !== 0) return 'many';\n              if (n % 10 == 2 && !isIn(n % 100, [12, 72, 92])) return 'two';\n              if (n % 10 == 1 && !isIn(n % 100, [11, 71, 91])) return 'one';\n              return 'other';\n            },\n            '21': function (n) {\n              if (n === 0) return 'zero';\n              if (n == 1) return 'one';\n              return 'other';\n            },\n            '22': function (n) {\n              if (isBetween(n, 0, 1) || isBetween(n, 11, 99)) return 'one';\n              return 'other';\n            },\n            '23': function (n) {\n              if (isBetween(n % 10, 1, 2) || n % 20 === 0) return 'one';\n              return 'other';\n            },\n            '24': function (n) {\n              if (isBetween(n, 3, 10) || isBetween(n, 13, 19)) return 'few';\n              if (isIn(n, [2, 12])) return 'two';\n              if (isIn(n, [1, 11])) return 'one';\n              return 'other';\n            }\n          };\n          var index = locales2rules[lang.replace(/-.*$/, '')];\n          if (!(index in pluralRules)) {\n            console.warn('plural form unknown for [' + lang + ']');\n            return function () {\n              return 'other';\n            };\n          }\n          return pluralRules[index];\n        }\n        gMacros.plural = function (str, param, key, prop) {\n          var n = parseFloat(param);\n          if (isNaN(n)) return str;\n          if (prop != gTextProp) return str;\n          if (!gMacros._pluralRules) {\n            gMacros._pluralRules = getPluralRules(gLanguage);\n          }\n          var index = '[' + gMacros._pluralRules(n) + ']';\n          if (n === 0 && key + '[zero]' in gL10nData) {\n            str = gL10nData[key + '[zero]'][prop];\n          } else if (n == 1 && key + '[one]' in gL10nData) {\n            str = gL10nData[key + '[one]'][prop];\n          } else if (n == 2 && key + '[two]' in gL10nData) {\n            str = gL10nData[key + '[two]'][prop];\n          } else if (key + index in gL10nData) {\n            str = gL10nData[key + index][prop];\n          } else if (key + '[other]' in gL10nData) {\n            str = gL10nData[key + '[other]'][prop];\n          }\n          return str;\n        };\n        function getL10nData(key, args, fallback) {\n          var data = gL10nData[key];\n          if (!data) {\n            console.warn('#' + key + ' is undefined.');\n            if (!fallback) {\n              return null;\n            }\n            data = fallback;\n          }\n          var rv = {};\n          for (var prop in data) {\n            var str = data[prop];\n            str = substIndexes(str, args, key, prop);\n            str = substArguments(str, args, key);\n            rv[prop] = str;\n          }\n          return rv;\n        }\n        function substIndexes(str, args, key, prop) {\n          var reIndex = /\\{\\[\\s*([a-zA-Z]+)\\(([a-zA-Z]+)\\)\\s*\\]\\}/;\n          var reMatch = reIndex.exec(str);\n          if (!reMatch || !reMatch.length) return str;\n          var macroName = reMatch[1];\n          var paramName = reMatch[2];\n          var param;\n          if (args && paramName in args) {\n            param = args[paramName];\n          } else if (paramName in gL10nData) {\n            param = gL10nData[paramName];\n          }\n          if (macroName in gMacros) {\n            var macro = gMacros[macroName];\n            str = macro(str, param, key, prop);\n          }\n          return str;\n        }\n        function substArguments(str, args, key) {\n          var reArgs = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n          return str.replace(reArgs, function (matched_text, arg) {\n            if (args && arg in args) {\n              return args[arg];\n            }\n            if (arg in gL10nData) {\n              return gL10nData[arg];\n            }\n            console.log('argument {{' + arg + '}} for #' + key + ' is undefined.');\n            return matched_text;\n          });\n        }\n        function translateElement(element) {\n          var l10n = getL10nAttributes(element);\n          if (!l10n.id) return;\n          var data = getL10nData(l10n.id, l10n.args);\n          if (!data) {\n            console.warn('#' + l10n.id + ' is undefined.');\n            return;\n          }\n          if (data[gTextProp]) {\n            if (getChildElementCount(element) === 0) {\n              element[gTextProp] = data[gTextProp];\n            } else {\n              var children = element.childNodes;\n              var found = false;\n              for (var i = 0, l = children.length; i < l; i++) {\n                if (children[i].nodeType === 3 && /\\S/.test(children[i].nodeValue)) {\n                  if (found) {\n                    children[i].nodeValue = '';\n                  } else {\n                    children[i].nodeValue = data[gTextProp];\n                    found = true;\n                  }\n                }\n              }\n              if (!found) {\n                var textNode = document.createTextNode(data[gTextProp]);\n                element.insertBefore(textNode, element.firstChild);\n              }\n            }\n            delete data[gTextProp];\n          }\n          for (var k in data) {\n            element[k] = data[k];\n          }\n        }\n        function getChildElementCount(element) {\n          if (element.children) {\n            return element.children.length;\n          }\n          if (typeof element.childElementCount !== 'undefined') {\n            return element.childElementCount;\n          }\n          var count = 0;\n          for (var i = 0; i < element.childNodes.length; i++) {\n            count += element.nodeType === 1 ? 1 : 0;\n          }\n          return count;\n        }\n        function translateFragment(element) {\n          element = element || document.documentElement;\n          var children = getTranslatableChildren(element);\n          var elementCount = children.length;\n          for (var i = 0; i < elementCount; i++) {\n            translateElement(children[i]);\n          }\n          translateElement(element);\n        }\n        return {\n          get: function (key, args, fallbackString) {\n            var index = key.lastIndexOf('.');\n            var prop = gTextProp;\n            if (index > 0) {\n              prop = key.substring(index + 1);\n              key = key.substring(0, index);\n            }\n            var fallback;\n            if (fallbackString) {\n              fallback = {};\n              fallback[prop] = fallbackString;\n            }\n            var data = getL10nData(key, args, fallback);\n            if (data && prop in data) {\n              return data[prop];\n            }\n            return '{{' + key + '}}';\n          },\n          getData: function () {\n            return gL10nData;\n          },\n          getText: function () {\n            return gTextData;\n          },\n          getLanguage: function () {\n            return gLanguage;\n          },\n          setLanguage: function (lang, callback) {\n            loadLocale(lang, function () {\n              if (callback) callback();\n            });\n          },\n          getDirection: function () {\n            var rtlList = ['ar', 'he', 'fa', 'ps', 'ur'];\n            var shortCode = gLanguage.split('-', 1)[0];\n            return rtlList.indexOf(shortCode) >= 0 ? 'rtl' : 'ltr';\n          },\n          translate: translateFragment,\n          getReadyState: function () {\n            return gReadyState;\n          },\n          ready: function (callback) {\n            if (!callback) {\n              return;\n            } else if (gReadyState == 'complete' || gReadyState == 'interactive') {\n              window.setTimeout(function () {\n                callback();\n              });\n            } else if (document.addEventListener) {\n              document.addEventListener('localized', function once() {\n                document.removeEventListener('localized', once);\n                callback();\n              });\n            }\n          }\n        };\n      }(window, document);\n\n      /***/\n    }), (/* 20 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFFindController = exports.FindState = void 0;\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      var _pdf_find_utils = __w_pdfjs_require__(21);\n      const FindState = {\n        FOUND: 0,\n        NOT_FOUND: 1,\n        WRAPPED: 2,\n        PENDING: 3\n      };\n      exports.FindState = FindState;\n      const FIND_TIMEOUT = 250;\n      const MATCH_SCROLL_OFFSET_TOP = -50;\n      const MATCH_SCROLL_OFFSET_LEFT = -400;\n      const CHARACTERS_TO_NORMALIZE = {\n        \"\\u2010\": \"-\",\n        \"\\u2018\": \"'\",\n        \"\\u2019\": \"'\",\n        \"\\u201A\": \"'\",\n        \"\\u201B\": \"'\",\n        \"\\u201C\": '\"',\n        \"\\u201D\": '\"',\n        \"\\u201E\": '\"',\n        \"\\u201F\": '\"',\n        \"\\u00BC\": \"1/4\",\n        \"\\u00BD\": \"1/2\",\n        \"\\u00BE\": \"3/4\"\n      };\n      const DIACRITICS_EXCEPTION = new Set([0x3099, 0x309a, 0x094d, 0x09cd, 0x0a4d, 0x0acd, 0x0b4d, 0x0bcd, 0x0c4d, 0x0ccd, 0x0d3b, 0x0d3c, 0x0d4d, 0x0dca, 0x0e3a, 0x0eba, 0x0f84, 0x1039, 0x103a, 0x1714, 0x1734, 0x17d2, 0x1a60, 0x1b44, 0x1baa, 0x1bab, 0x1bf2, 0x1bf3, 0x2d7f, 0xa806, 0xa82c, 0xa8c4, 0xa953, 0xa9c0, 0xaaf6, 0xabed, 0x0c56, 0x0f71, 0x0f72, 0x0f7a, 0x0f7b, 0x0f7c, 0x0f7d, 0x0f80, 0x0f74]);\n      const DIACRITICS_EXCEPTION_STR = [...DIACRITICS_EXCEPTION.values()].map(x => String.fromCharCode(x)).join(\"\");\n      const DIACRITICS_REG_EXP = /\\p{M}+/gu;\n      const SPECIAL_CHARS_REG_EXP = /([.*+?^${}()|[\\]\\\\])|(\\p{P})|(\\s+)|(\\p{M})|(\\p{L})/gu;\n      const NOT_DIACRITIC_FROM_END_REG_EXP = /([^\\p{M}])\\p{M}*$/u;\n      const NOT_DIACRITIC_FROM_START_REG_EXP = /^\\p{M}*([^\\p{M}])/u;\n      let normalizationRegex = null;\n      function normalize(text) {\n        if (!normalizationRegex) {\n          const replace = Object.keys(CHARACTERS_TO_NORMALIZE).join(\"\");\n          normalizationRegex = new RegExp(`([${replace}])|(\\\\p{M}+(?:-\\\\n)?)|(\\\\S-\\\\n)|(\\\\n)`, \"gum\");\n        }\n        const rawDiacriticsPositions = [];\n        let m;\n        while ((m = DIACRITICS_REG_EXP.exec(text)) !== null) {\n          rawDiacriticsPositions.push([m[0].length, m.index]);\n        }\n        let normalized = text.normalize(\"NFD\");\n        const positions = [[0, 0]];\n        let k = 0;\n        let shift = 0;\n        let shiftOrigin = 0;\n        let eol = 0;\n        let hasDiacritics = false;\n        normalized = normalized.replace(normalizationRegex, (match, p1, p2, p3, p4, i) => {\n          i -= shiftOrigin;\n          if (p1) {\n            const replacement = CHARACTERS_TO_NORMALIZE[match];\n            const jj = replacement.length;\n            for (let j = 1; j < jj; j++) {\n              positions.push([i - shift + j, shift - j]);\n            }\n            shift -= jj - 1;\n            return replacement;\n          }\n          if (p2) {\n            const hasTrailingDashEOL = p2.endsWith(\"\\n\");\n            const len = hasTrailingDashEOL ? p2.length - 2 : p2.length;\n            hasDiacritics = true;\n            let jj = len;\n            if (i + eol === rawDiacriticsPositions[k]?.[1]) {\n              jj -= rawDiacriticsPositions[k][0];\n              ++k;\n            }\n            for (let j = 1; j < jj + 1; j++) {\n              positions.push([i - 1 - shift + j, shift - j]);\n            }\n            shift -= jj;\n            shiftOrigin += jj;\n            if (hasTrailingDashEOL) {\n              i += len - 1;\n              positions.push([i - shift + 1, 1 + shift]);\n              shift += 1;\n              shiftOrigin += 1;\n              eol += 1;\n              return p2.slice(0, len);\n            }\n            return p2;\n          }\n          if (p3) {\n            positions.push([i - shift + 1, 1 + shift]);\n            shift += 1;\n            shiftOrigin += 1;\n            eol += 1;\n            return p3.charAt(0);\n          }\n          positions.push([i - shift + 1, shift - 1]);\n          shift -= 1;\n          shiftOrigin += 1;\n          eol += 1;\n          return \" \";\n        });\n        positions.push([normalized.length, shift]);\n        return [normalized, positions, hasDiacritics];\n      }\n      function getOriginalIndex(diffs, pos, len) {\n        if (!diffs) {\n          return [pos, len];\n        }\n        const start = pos;\n        const end = pos + len;\n        let i = (0, _ui_utils.binarySearchFirstItem)(diffs, x => x[0] >= start);\n        if (diffs[i][0] > start) {\n          --i;\n        }\n        let j = (0, _ui_utils.binarySearchFirstItem)(diffs, x => x[0] >= end, i);\n        if (diffs[j][0] > end) {\n          --j;\n        }\n        return [start + diffs[i][1], len + diffs[j][1] - diffs[i][1]];\n      }\n      class PDFFindController {\n        constructor({\n          linkService,\n          eventBus\n        }) {\n          this._linkService = linkService;\n          this._eventBus = eventBus;\n          this.#reset();\n          eventBus._on(\"find\", this.#onFind.bind(this));\n          eventBus._on(\"findbarclose\", this.#onFindBarClose.bind(this));\n        }\n        get highlightMatches() {\n          return this._highlightMatches;\n        }\n        get pageMatches() {\n          return this._pageMatches;\n        }\n        get pageMatchesLength() {\n          return this._pageMatchesLength;\n        }\n        get selected() {\n          return this._selected;\n        }\n        get state() {\n          return this._state;\n        }\n        setDocument(pdfDocument) {\n          if (this._pdfDocument) {\n            this.#reset();\n          }\n          if (!pdfDocument) {\n            return;\n          }\n          this._pdfDocument = pdfDocument;\n          this._firstPageCapability.resolve();\n        }\n        #onFind(state) {\n          if (!state) {\n            return;\n          }\n          const pdfDocument = this._pdfDocument;\n          const {\n            type\n          } = state;\n          if (this._state === null || this.#shouldDirtyMatch(state)) {\n            this._dirtyMatch = true;\n          }\n          this._state = state;\n          if (type !== \"highlightallchange\") {\n            this.#updateUIState(FindState.PENDING);\n          }\n          this._firstPageCapability.promise.then(() => {\n            if (!this._pdfDocument || pdfDocument && this._pdfDocument !== pdfDocument) {\n              return;\n            }\n            this.#extractText();\n            const findbarClosed = !this._highlightMatches;\n            const pendingTimeout = !!this._findTimeout;\n            if (this._findTimeout) {\n              clearTimeout(this._findTimeout);\n              this._findTimeout = null;\n            }\n            if (!type) {\n              this._findTimeout = setTimeout(() => {\n                this.#nextMatch();\n                this._findTimeout = null;\n              }, FIND_TIMEOUT);\n            } else if (this._dirtyMatch) {\n              this.#nextMatch();\n            } else if (type === \"again\") {\n              this.#nextMatch();\n              if (findbarClosed && this._state.highlightAll) {\n                this.#updateAllPages();\n              }\n            } else if (type === \"highlightallchange\") {\n              if (pendingTimeout) {\n                this.#nextMatch();\n              } else {\n                this._highlightMatches = true;\n              }\n              this.#updateAllPages();\n            } else {\n              this.#nextMatch();\n            }\n          });\n        }\n        scrollMatchIntoView({\n          element = null,\n          selectedLeft = 0,\n          pageIndex = -1,\n          matchIndex = -1\n        }) {\n          if (!this._scrollMatches || !element) {\n            return;\n          } else if (matchIndex === -1 || matchIndex !== this._selected.matchIdx) {\n            return;\n          } else if (pageIndex === -1 || pageIndex !== this._selected.pageIdx) {\n            return;\n          }\n          this._scrollMatches = false;\n          const spot = {\n            top: MATCH_SCROLL_OFFSET_TOP,\n            left: selectedLeft + MATCH_SCROLL_OFFSET_LEFT\n          };\n          (0, _ui_utils.scrollIntoView)(element, spot, true);\n        }\n        #reset() {\n          this._highlightMatches = false;\n          this._scrollMatches = false;\n          this._pdfDocument = null;\n          this._pageMatches = [];\n          this._pageMatchesLength = [];\n          this._state = null;\n          this._selected = {\n            pageIdx: -1,\n            matchIdx: -1\n          };\n          this._offset = {\n            pageIdx: null,\n            matchIdx: null,\n            wrapped: false\n          };\n          this._extractTextPromises = [];\n          this._pageContents = [];\n          this._pageDiffs = [];\n          this._hasDiacritics = [];\n          this._matchesCountTotal = 0;\n          this._pagesToSearch = null;\n          this._pendingFindMatches = new Set();\n          this._resumePageIdx = null;\n          this._dirtyMatch = false;\n          clearTimeout(this._findTimeout);\n          this._findTimeout = null;\n          this._firstPageCapability = (0, _pdfjsLib.createPromiseCapability)();\n        }\n        get #query() {\n          if (this._state.query !== this._rawQuery) {\n            this._rawQuery = this._state.query;\n            [this._normalizedQuery] = normalize(this._state.query);\n          }\n          return this._normalizedQuery;\n        }\n        #shouldDirtyMatch(state) {\n          if (state.query !== this._state.query) {\n            return true;\n          }\n          switch (state.type) {\n            case \"again\":\n              const pageNumber = this._selected.pageIdx + 1;\n              const linkService = this._linkService;\n              if (pageNumber >= 1 && pageNumber <= linkService.pagesCount && pageNumber !== linkService.page && !linkService.isPageVisible(pageNumber)) {\n                return true;\n              }\n              return false;\n            case \"highlightallchange\":\n              return false;\n          }\n          return true;\n        }\n        #isEntireWord(content, startIdx, length) {\n          let match = content.slice(0, startIdx).match(NOT_DIACRITIC_FROM_END_REG_EXP);\n          if (match) {\n            const first = content.charCodeAt(startIdx);\n            const limit = match[1].charCodeAt(0);\n            if ((0, _pdf_find_utils.getCharacterType)(first) === (0, _pdf_find_utils.getCharacterType)(limit)) {\n              return false;\n            }\n          }\n          match = content.slice(startIdx + length).match(NOT_DIACRITIC_FROM_START_REG_EXP);\n          if (match) {\n            const last = content.charCodeAt(startIdx + length - 1);\n            const limit = match[1].charCodeAt(0);\n            if ((0, _pdf_find_utils.getCharacterType)(last) === (0, _pdf_find_utils.getCharacterType)(limit)) {\n              return false;\n            }\n          }\n          return true;\n        }\n        #calculateRegExpMatch(query, entireWord, pageIndex, pageContent) {\n          const matches = [],\n            matchesLength = [];\n          const diffs = this._pageDiffs[pageIndex];\n          let match;\n          while ((match = query.exec(pageContent)) !== null) {\n            if (entireWord && !this.#isEntireWord(pageContent, match.index, match[0].length)) {\n              continue;\n            }\n            const [matchPos, matchLen] = getOriginalIndex(diffs, match.index, match[0].length);\n            if (matchLen) {\n              matches.push(matchPos);\n              matchesLength.push(matchLen);\n            }\n          }\n          this._pageMatches[pageIndex] = matches;\n          this._pageMatchesLength[pageIndex] = matchesLength;\n        }\n        #convertToRegExpString(query, hasDiacritics) {\n          const {\n            matchDiacritics\n          } = this._state;\n          let isUnicode = false;\n          query = query.replace(SPECIAL_CHARS_REG_EXP, (match, p1, p2, p3, p4, p5) => {\n            if (p1) {\n              return `[ ]*\\\\${p1}[ ]*`;\n            }\n            if (p2) {\n              return `[ ]*${p2}[ ]*`;\n            }\n            if (p3) {\n              return \"[ ]+\";\n            }\n            if (matchDiacritics) {\n              return p4 || p5;\n            }\n            if (p4) {\n              return DIACRITICS_EXCEPTION.has(p4.charCodeAt(0)) ? p4 : \"\";\n            }\n            if (hasDiacritics) {\n              isUnicode = true;\n              return `${p5}\\\\p{M}*`;\n            }\n            return p5;\n          });\n          const trailingSpaces = \"[ ]*\";\n          if (query.endsWith(trailingSpaces)) {\n            query = query.slice(0, query.length - trailingSpaces.length);\n          }\n          if (matchDiacritics) {\n            if (hasDiacritics) {\n              isUnicode = true;\n              query = `${query}(?=[${DIACRITICS_EXCEPTION_STR}]|[^\\\\p{M}]|$)`;\n            }\n          }\n          return [isUnicode, query];\n        }\n        #calculateMatch(pageIndex) {\n          let query = this.#query;\n          if (query.length === 0) {\n            return;\n          }\n          const {\n            caseSensitive,\n            entireWord,\n            phraseSearch\n          } = this._state;\n          const pageContent = this._pageContents[pageIndex];\n          const hasDiacritics = this._hasDiacritics[pageIndex];\n          let isUnicode = false;\n          if (phraseSearch) {\n            [isUnicode, query] = this.#convertToRegExpString(query, hasDiacritics);\n          } else {\n            const match = query.match(/\\S+/g);\n            if (match) {\n              query = match.sort().reverse().map(q => {\n                const [isUnicodePart, queryPart] = this.#convertToRegExpString(q, hasDiacritics);\n                isUnicode ||= isUnicodePart;\n                return `(${queryPart})`;\n              }).join(\"|\");\n            }\n          }\n          const flags = `g${isUnicode ? \"u\" : \"\"}${caseSensitive ? \"\" : \"i\"}`;\n          query = new RegExp(query, flags);\n          this.#calculateRegExpMatch(query, entireWord, pageIndex, pageContent);\n          if (this._state.highlightAll) {\n            this.#updatePage(pageIndex);\n          }\n          if (this._resumePageIdx === pageIndex) {\n            this._resumePageIdx = null;\n            this.#nextPageMatch();\n          }\n          const pageMatchesCount = this._pageMatches[pageIndex].length;\n          if (pageMatchesCount > 0) {\n            this._matchesCountTotal += pageMatchesCount;\n            this.#updateUIResultsCount();\n          }\n        }\n        #extractText() {\n          if (this._extractTextPromises.length > 0) {\n            return;\n          }\n          let promise = Promise.resolve();\n          for (let i = 0, ii = this._linkService.pagesCount; i < ii; i++) {\n            const extractTextCapability = (0, _pdfjsLib.createPromiseCapability)();\n            this._extractTextPromises[i] = extractTextCapability.promise;\n            promise = promise.then(() => {\n              return this._pdfDocument.getPage(i + 1).then(pdfPage => {\n                return pdfPage.getTextContent();\n              }).then(textContent => {\n                const strBuf = [];\n                for (const textItem of textContent.items) {\n                  strBuf.push(textItem.str);\n                  if (textItem.hasEOL) {\n                    strBuf.push(\"\\n\");\n                  }\n                }\n                [this._pageContents[i], this._pageDiffs[i], this._hasDiacritics[i]] = normalize(strBuf.join(\"\"));\n                extractTextCapability.resolve();\n              }, reason => {\n                console.error(`Unable to get text content for page ${i + 1}`, reason);\n                this._pageContents[i] = \"\";\n                this._pageDiffs[i] = null;\n                this._hasDiacritics[i] = false;\n                extractTextCapability.resolve();\n              });\n            });\n          }\n        }\n        #updatePage(index) {\n          if (this._scrollMatches && this._selected.pageIdx === index) {\n            this._linkService.page = index + 1;\n          }\n          this._eventBus.dispatch(\"updatetextlayermatches\", {\n            source: this,\n            pageIndex: index\n          });\n        }\n        #updateAllPages() {\n          this._eventBus.dispatch(\"updatetextlayermatches\", {\n            source: this,\n            pageIndex: -1\n          });\n        }\n        #nextMatch() {\n          const previous = this._state.findPrevious;\n          const currentPageIndex = this._linkService.page - 1;\n          const numPages = this._linkService.pagesCount;\n          this._highlightMatches = true;\n          if (this._dirtyMatch) {\n            this._dirtyMatch = false;\n            this._selected.pageIdx = this._selected.matchIdx = -1;\n            this._offset.pageIdx = currentPageIndex;\n            this._offset.matchIdx = null;\n            this._offset.wrapped = false;\n            this._resumePageIdx = null;\n            this._pageMatches.length = 0;\n            this._pageMatchesLength.length = 0;\n            this._matchesCountTotal = 0;\n            this.#updateAllPages();\n            for (let i = 0; i < numPages; i++) {\n              if (this._pendingFindMatches.has(i)) {\n                continue;\n              }\n              this._pendingFindMatches.add(i);\n              this._extractTextPromises[i].then(() => {\n                this._pendingFindMatches.delete(i);\n                this.#calculateMatch(i);\n              });\n            }\n          }\n          if (this.#query === \"\") {\n            this.#updateUIState(FindState.FOUND);\n            return;\n          }\n          if (this._resumePageIdx) {\n            return;\n          }\n          const offset = this._offset;\n          this._pagesToSearch = numPages;\n          if (offset.matchIdx !== null) {\n            const numPageMatches = this._pageMatches[offset.pageIdx].length;\n            if (!previous && offset.matchIdx + 1 < numPageMatches || previous && offset.matchIdx > 0) {\n              offset.matchIdx = previous ? offset.matchIdx - 1 : offset.matchIdx + 1;\n              this.#updateMatch(true);\n              return;\n            }\n            this.#advanceOffsetPage(previous);\n          }\n          this.#nextPageMatch();\n        }\n        #matchesReady(matches) {\n          const offset = this._offset;\n          const numMatches = matches.length;\n          const previous = this._state.findPrevious;\n          if (numMatches) {\n            offset.matchIdx = previous ? numMatches - 1 : 0;\n            this.#updateMatch(true);\n            return true;\n          }\n          this.#advanceOffsetPage(previous);\n          if (offset.wrapped) {\n            offset.matchIdx = null;\n            if (this._pagesToSearch < 0) {\n              this.#updateMatch(false);\n              return true;\n            }\n          }\n          return false;\n        }\n        #nextPageMatch() {\n          if (this._resumePageIdx !== null) {\n            console.error(\"There can only be one pending page.\");\n          }\n          let matches = null;\n          do {\n            const pageIdx = this._offset.pageIdx;\n            matches = this._pageMatches[pageIdx];\n            if (!matches) {\n              this._resumePageIdx = pageIdx;\n              break;\n            }\n          } while (!this.#matchesReady(matches));\n        }\n        #advanceOffsetPage(previous) {\n          const offset = this._offset;\n          const numPages = this._linkService.pagesCount;\n          offset.pageIdx = previous ? offset.pageIdx - 1 : offset.pageIdx + 1;\n          offset.matchIdx = null;\n          this._pagesToSearch--;\n          if (offset.pageIdx >= numPages || offset.pageIdx < 0) {\n            offset.pageIdx = previous ? numPages - 1 : 0;\n            offset.wrapped = true;\n          }\n        }\n        #updateMatch(found = false) {\n          let state = FindState.NOT_FOUND;\n          const wrapped = this._offset.wrapped;\n          this._offset.wrapped = false;\n          if (found) {\n            const previousPage = this._selected.pageIdx;\n            this._selected.pageIdx = this._offset.pageIdx;\n            this._selected.matchIdx = this._offset.matchIdx;\n            state = wrapped ? FindState.WRAPPED : FindState.FOUND;\n            if (previousPage !== -1 && previousPage !== this._selected.pageIdx) {\n              this.#updatePage(previousPage);\n            }\n          }\n          this.#updateUIState(state, this._state.findPrevious);\n          if (this._selected.pageIdx !== -1) {\n            this._scrollMatches = true;\n            this.#updatePage(this._selected.pageIdx);\n          }\n        }\n        #onFindBarClose(evt) {\n          const pdfDocument = this._pdfDocument;\n          this._firstPageCapability.promise.then(() => {\n            if (!this._pdfDocument || pdfDocument && this._pdfDocument !== pdfDocument) {\n              return;\n            }\n            if (this._findTimeout) {\n              clearTimeout(this._findTimeout);\n              this._findTimeout = null;\n            }\n            if (this._resumePageIdx) {\n              this._resumePageIdx = null;\n              this._dirtyMatch = true;\n            }\n            this.#updateUIState(FindState.FOUND);\n            this._highlightMatches = false;\n            this.#updateAllPages();\n          });\n        }\n        #requestMatchesCount() {\n          const {\n            pageIdx,\n            matchIdx\n          } = this._selected;\n          let current = 0,\n            total = this._matchesCountTotal;\n          if (matchIdx !== -1) {\n            for (let i = 0; i < pageIdx; i++) {\n              current += this._pageMatches[i]?.length || 0;\n            }\n            current += matchIdx + 1;\n          }\n          if (current < 1 || current > total) {\n            current = total = 0;\n          }\n          return {\n            current,\n            total\n          };\n        }\n        #updateUIResultsCount() {\n          this._eventBus.dispatch(\"updatefindmatchescount\", {\n            source: this,\n            matchesCount: this.#requestMatchesCount()\n          });\n        }\n        #updateUIState(state, previous = false) {\n          this._eventBus.dispatch(\"updatefindcontrolstate\", {\n            source: this,\n            state,\n            previous,\n            matchesCount: this.#requestMatchesCount(),\n            rawQuery: this._state?.query ?? null\n          });\n        }\n      }\n      exports.PDFFindController = PDFFindController;\n\n      /***/\n    }), (/* 21 */\n    /***/(__unused_webpack_module, exports) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.CharacterType = void 0;\n      exports.getCharacterType = getCharacterType;\n      const CharacterType = {\n        SPACE: 0,\n        ALPHA_LETTER: 1,\n        PUNCT: 2,\n        HAN_LETTER: 3,\n        KATAKANA_LETTER: 4,\n        HIRAGANA_LETTER: 5,\n        HALFWIDTH_KATAKANA_LETTER: 6,\n        THAI_LETTER: 7\n      };\n      exports.CharacterType = CharacterType;\n      function isAlphabeticalScript(charCode) {\n        return charCode < 0x2e80;\n      }\n      function isAscii(charCode) {\n        return (charCode & 0xff80) === 0;\n      }\n      function isAsciiAlpha(charCode) {\n        return charCode >= 0x61 && charCode <= 0x7a || charCode >= 0x41 && charCode <= 0x5a;\n      }\n      function isAsciiDigit(charCode) {\n        return charCode >= 0x30 && charCode <= 0x39;\n      }\n      function isAsciiSpace(charCode) {\n        return charCode === 0x20 || charCode === 0x09 || charCode === 0x0d || charCode === 0x0a;\n      }\n      function isHan(charCode) {\n        return charCode >= 0x3400 && charCode <= 0x9fff || charCode >= 0xf900 && charCode <= 0xfaff;\n      }\n      function isKatakana(charCode) {\n        return charCode >= 0x30a0 && charCode <= 0x30ff;\n      }\n      function isHiragana(charCode) {\n        return charCode >= 0x3040 && charCode <= 0x309f;\n      }\n      function isHalfwidthKatakana(charCode) {\n        return charCode >= 0xff60 && charCode <= 0xff9f;\n      }\n      function isThai(charCode) {\n        return (charCode & 0xff80) === 0x0e00;\n      }\n      function getCharacterType(charCode) {\n        if (isAlphabeticalScript(charCode)) {\n          if (isAscii(charCode)) {\n            if (isAsciiSpace(charCode)) {\n              return CharacterType.SPACE;\n            } else if (isAsciiAlpha(charCode) || isAsciiDigit(charCode) || charCode === 0x5f) {\n              return CharacterType.ALPHA_LETTER;\n            }\n            return CharacterType.PUNCT;\n          } else if (isThai(charCode)) {\n            return CharacterType.THAI_LETTER;\n          } else if (charCode === 0xa0) {\n            return CharacterType.SPACE;\n          }\n          return CharacterType.ALPHA_LETTER;\n        }\n        if (isHan(charCode)) {\n          return CharacterType.HAN_LETTER;\n        } else if (isKatakana(charCode)) {\n          return CharacterType.KATAKANA_LETTER;\n        } else if (isHiragana(charCode)) {\n          return CharacterType.HIRAGANA_LETTER;\n        } else if (isHalfwidthKatakana(charCode)) {\n          return CharacterType.HALFWIDTH_KATAKANA_LETTER;\n        }\n        return CharacterType.ALPHA_LETTER;\n      }\n\n      /***/\n    }), (/* 22 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFHistory = void 0;\n      exports.isDestArraysEqual = isDestArraysEqual;\n      exports.isDestHashesEqual = isDestHashesEqual;\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _event_utils = __w_pdfjs_require__(17);\n      const HASH_CHANGE_TIMEOUT = 1000;\n      const POSITION_UPDATED_THRESHOLD = 50;\n      const UPDATE_VIEWAREA_TIMEOUT = 1000;\n      function getCurrentHash() {\n        return document.location.hash;\n      }\n      class PDFHistory {\n        constructor({\n          linkService,\n          eventBus\n        }) {\n          this.linkService = linkService;\n          this.eventBus = eventBus;\n          this._initialized = false;\n          this._fingerprint = \"\";\n          this.reset();\n          this._boundEvents = null;\n          this.eventBus._on(\"pagesinit\", () => {\n            this._isPagesLoaded = false;\n            this.eventBus._on(\"pagesloaded\", evt => {\n              this._isPagesLoaded = !!evt.pagesCount;\n            }, {\n              once: true\n            });\n          });\n        }\n        initialize({\n          fingerprint,\n          resetHistory = false,\n          updateUrl = false\n        }) {\n          if (!fingerprint || typeof fingerprint !== \"string\") {\n            console.error('PDFHistory.initialize: The \"fingerprint\" must be a non-empty string.');\n            return;\n          }\n          if (this._initialized) {\n            this.reset();\n          }\n          const reInitialized = this._fingerprint !== \"\" && this._fingerprint !== fingerprint;\n          this._fingerprint = fingerprint;\n          this._updateUrl = updateUrl === true;\n          this._initialized = true;\n          this._bindEvents();\n          const state = window.history.state;\n          this._popStateInProgress = false;\n          this._blockHashChange = 0;\n          this._currentHash = getCurrentHash();\n          this._numPositionUpdates = 0;\n          this._uid = this._maxUid = 0;\n          this._destination = null;\n          this._position = null;\n          if (!this._isValidState(state, true) || resetHistory) {\n            const {\n              hash,\n              page,\n              rotation\n            } = this._parseCurrentHash(true);\n            if (!hash || reInitialized || resetHistory) {\n              this._pushOrReplaceState(null, true);\n              return;\n            }\n            this._pushOrReplaceState({\n              hash,\n              page,\n              rotation\n            }, true);\n            return;\n          }\n          const destination = state.destination;\n          this._updateInternalState(destination, state.uid, true);\n          if (destination.rotation !== undefined) {\n            this._initialRotation = destination.rotation;\n          }\n          if (destination.dest) {\n            this._initialBookmark = JSON.stringify(destination.dest);\n            this._destination.page = null;\n          } else if (destination.hash) {\n            this._initialBookmark = destination.hash;\n          } else if (destination.page) {\n            this._initialBookmark = `page=${destination.page}`;\n          }\n        }\n        reset() {\n          if (this._initialized) {\n            this._pageHide();\n            this._initialized = false;\n            this._unbindEvents();\n          }\n          if (this._updateViewareaTimeout) {\n            clearTimeout(this._updateViewareaTimeout);\n            this._updateViewareaTimeout = null;\n          }\n          this._initialBookmark = null;\n          this._initialRotation = null;\n        }\n        push({\n          namedDest = null,\n          explicitDest,\n          pageNumber\n        }) {\n          if (!this._initialized) {\n            return;\n          }\n          if (namedDest && typeof namedDest !== \"string\") {\n            console.error(\"PDFHistory.push: \" + `\"${namedDest}\" is not a valid namedDest parameter.`);\n            return;\n          } else if (!Array.isArray(explicitDest)) {\n            console.error(\"PDFHistory.push: \" + `\"${explicitDest}\" is not a valid explicitDest parameter.`);\n            return;\n          } else if (!this._isValidPage(pageNumber)) {\n            if (pageNumber !== null || this._destination) {\n              console.error(\"PDFHistory.push: \" + `\"${pageNumber}\" is not a valid pageNumber parameter.`);\n              return;\n            }\n          }\n          const hash = namedDest || JSON.stringify(explicitDest);\n          if (!hash) {\n            return;\n          }\n          let forceReplace = false;\n          if (this._destination && (isDestHashesEqual(this._destination.hash, hash) || isDestArraysEqual(this._destination.dest, explicitDest))) {\n            if (this._destination.page) {\n              return;\n            }\n            forceReplace = true;\n          }\n          if (this._popStateInProgress && !forceReplace) {\n            return;\n          }\n          this._pushOrReplaceState({\n            dest: explicitDest,\n            hash,\n            page: pageNumber,\n            rotation: this.linkService.rotation\n          }, forceReplace);\n          if (!this._popStateInProgress) {\n            this._popStateInProgress = true;\n            Promise.resolve().then(() => {\n              this._popStateInProgress = false;\n            });\n          }\n        }\n        pushPage(pageNumber) {\n          if (!this._initialized) {\n            return;\n          }\n          if (!this._isValidPage(pageNumber)) {\n            console.error(`PDFHistory.pushPage: \"${pageNumber}\" is not a valid page number.`);\n            return;\n          }\n          if (this._destination?.page === pageNumber) {\n            return;\n          }\n          if (this._popStateInProgress) {\n            return;\n          }\n          this._pushOrReplaceState({\n            dest: null,\n            hash: `page=${pageNumber}`,\n            page: pageNumber,\n            rotation: this.linkService.rotation\n          });\n          if (!this._popStateInProgress) {\n            this._popStateInProgress = true;\n            Promise.resolve().then(() => {\n              this._popStateInProgress = false;\n            });\n          }\n        }\n        pushCurrentPosition() {\n          if (!this._initialized || this._popStateInProgress) {\n            return;\n          }\n          this._tryPushCurrentPosition();\n        }\n        back() {\n          if (!this._initialized || this._popStateInProgress) {\n            return;\n          }\n          const state = window.history.state;\n          if (this._isValidState(state) && state.uid > 0) {\n            window.history.back();\n          }\n        }\n        forward() {\n          if (!this._initialized || this._popStateInProgress) {\n            return;\n          }\n          const state = window.history.state;\n          if (this._isValidState(state) && state.uid < this._maxUid) {\n            window.history.forward();\n          }\n        }\n        get popStateInProgress() {\n          return this._initialized && (this._popStateInProgress || this._blockHashChange > 0);\n        }\n        get initialBookmark() {\n          return this._initialized ? this._initialBookmark : null;\n        }\n        get initialRotation() {\n          return this._initialized ? this._initialRotation : null;\n        }\n        _pushOrReplaceState(destination, forceReplace = false) {\n          const shouldReplace = forceReplace || !this._destination;\n          const newState = {\n            fingerprint: this._fingerprint,\n            uid: shouldReplace ? this._uid : this._uid + 1,\n            destination\n          };\n          this._updateInternalState(destination, newState.uid);\n          let newUrl;\n          if (this._updateUrl && destination?.hash) {\n            const baseUrl = document.location.href.split(\"#\")[0];\n            if (!baseUrl.startsWith(\"file://\")) {\n              newUrl = `${baseUrl}#${destination.hash}`;\n            }\n          }\n          if (shouldReplace) {\n            window.history.replaceState(newState, \"\", newUrl);\n          } else {\n            window.history.pushState(newState, \"\", newUrl);\n          }\n        }\n        _tryPushCurrentPosition(temporary = false) {\n          if (!this._position) {\n            return;\n          }\n          let position = this._position;\n          if (temporary) {\n            position = Object.assign(Object.create(null), this._position);\n            position.temporary = true;\n          }\n          if (!this._destination) {\n            this._pushOrReplaceState(position);\n            return;\n          }\n          if (this._destination.temporary) {\n            this._pushOrReplaceState(position, true);\n            return;\n          }\n          if (this._destination.hash === position.hash) {\n            return;\n          }\n          if (!this._destination.page && (POSITION_UPDATED_THRESHOLD <= 0 || this._numPositionUpdates <= POSITION_UPDATED_THRESHOLD)) {\n            return;\n          }\n          let forceReplace = false;\n          if (this._destination.page >= position.first && this._destination.page <= position.page) {\n            if (this._destination.dest !== undefined || !this._destination.first) {\n              return;\n            }\n            forceReplace = true;\n          }\n          this._pushOrReplaceState(position, forceReplace);\n        }\n        _isValidPage(val) {\n          return Number.isInteger(val) && val > 0 && val <= this.linkService.pagesCount;\n        }\n        _isValidState(state, checkReload = false) {\n          if (!state) {\n            return false;\n          }\n          if (state.fingerprint !== this._fingerprint) {\n            if (checkReload) {\n              if (typeof state.fingerprint !== \"string\" || state.fingerprint.length !== this._fingerprint.length) {\n                return false;\n              }\n              const [perfEntry] = performance.getEntriesByType(\"navigation\");\n              if (perfEntry?.type !== \"reload\") {\n                return false;\n              }\n            } else {\n              return false;\n            }\n          }\n          if (!Number.isInteger(state.uid) || state.uid < 0) {\n            return false;\n          }\n          if (state.destination === null || typeof state.destination !== \"object\") {\n            return false;\n          }\n          return true;\n        }\n        _updateInternalState(destination, uid, removeTemporary = false) {\n          if (this._updateViewareaTimeout) {\n            clearTimeout(this._updateViewareaTimeout);\n            this._updateViewareaTimeout = null;\n          }\n          if (removeTemporary && destination?.temporary) {\n            delete destination.temporary;\n          }\n          this._destination = destination;\n          this._uid = uid;\n          this._maxUid = Math.max(this._maxUid, uid);\n          this._numPositionUpdates = 0;\n        }\n        _parseCurrentHash(checkNameddest = false) {\n          const hash = unescape(getCurrentHash()).substring(1);\n          const params = (0, _ui_utils.parseQueryString)(hash);\n          const nameddest = params.get(\"nameddest\") || \"\";\n          let page = params.get(\"page\") | 0;\n          if (!this._isValidPage(page) || checkNameddest && nameddest.length > 0) {\n            page = null;\n          }\n          return {\n            hash,\n            page,\n            rotation: this.linkService.rotation\n          };\n        }\n        _updateViewarea({\n          location\n        }) {\n          if (this._updateViewareaTimeout) {\n            clearTimeout(this._updateViewareaTimeout);\n            this._updateViewareaTimeout = null;\n          }\n          this._position = {\n            hash: location.pdfOpenParams.substring(1),\n            page: this.linkService.page,\n            first: location.pageNumber,\n            rotation: location.rotation\n          };\n          if (this._popStateInProgress) {\n            return;\n          }\n          if (POSITION_UPDATED_THRESHOLD > 0 && this._isPagesLoaded && this._destination && !this._destination.page) {\n            this._numPositionUpdates++;\n          }\n          if (UPDATE_VIEWAREA_TIMEOUT > 0) {\n            this._updateViewareaTimeout = setTimeout(() => {\n              if (!this._popStateInProgress) {\n                this._tryPushCurrentPosition(true);\n              }\n              this._updateViewareaTimeout = null;\n            }, UPDATE_VIEWAREA_TIMEOUT);\n          }\n        }\n        _popState({\n          state\n        }) {\n          const newHash = getCurrentHash(),\n            hashChanged = this._currentHash !== newHash;\n          this._currentHash = newHash;\n          if (!state) {\n            this._uid++;\n            const {\n              hash,\n              page,\n              rotation\n            } = this._parseCurrentHash();\n            this._pushOrReplaceState({\n              hash,\n              page,\n              rotation\n            }, true);\n            return;\n          }\n          if (!this._isValidState(state)) {\n            return;\n          }\n          this._popStateInProgress = true;\n          if (hashChanged) {\n            this._blockHashChange++;\n            (0, _event_utils.waitOnEventOrTimeout)({\n              target: window,\n              name: \"hashchange\",\n              delay: HASH_CHANGE_TIMEOUT\n            }).then(() => {\n              this._blockHashChange--;\n            });\n          }\n          const destination = state.destination;\n          this._updateInternalState(destination, state.uid, true);\n          if ((0, _ui_utils.isValidRotation)(destination.rotation)) {\n            this.linkService.rotation = destination.rotation;\n          }\n          if (destination.dest) {\n            this.linkService.goToDestination(destination.dest);\n          } else if (destination.hash) {\n            this.linkService.setHash(destination.hash);\n          } else if (destination.page) {\n            this.linkService.page = destination.page;\n          }\n          Promise.resolve().then(() => {\n            this._popStateInProgress = false;\n          });\n        }\n        _pageHide() {\n          if (!this._destination || this._destination.temporary) {\n            this._tryPushCurrentPosition();\n          }\n        }\n        _bindEvents() {\n          if (this._boundEvents) {\n            return;\n          }\n          this._boundEvents = {\n            updateViewarea: this._updateViewarea.bind(this),\n            popState: this._popState.bind(this),\n            pageHide: this._pageHide.bind(this)\n          };\n          this.eventBus._on(\"updateviewarea\", this._boundEvents.updateViewarea);\n          window.addEventListener(\"popstate\", this._boundEvents.popState);\n          window.addEventListener(\"pagehide\", this._boundEvents.pageHide);\n        }\n        _unbindEvents() {\n          if (!this._boundEvents) {\n            return;\n          }\n          this.eventBus._off(\"updateviewarea\", this._boundEvents.updateViewarea);\n          window.removeEventListener(\"popstate\", this._boundEvents.popState);\n          window.removeEventListener(\"pagehide\", this._boundEvents.pageHide);\n          this._boundEvents = null;\n        }\n      }\n      exports.PDFHistory = PDFHistory;\n      function isDestHashesEqual(destHash, pushHash) {\n        if (typeof destHash !== \"string\" || typeof pushHash !== \"string\") {\n          return false;\n        }\n        if (destHash === pushHash) {\n          return true;\n        }\n        const nameddest = (0, _ui_utils.parseQueryString)(destHash).get(\"nameddest\");\n        if (nameddest === pushHash) {\n          return true;\n        }\n        return false;\n      }\n      function isDestArraysEqual(firstDest, secondDest) {\n        function isEntryEqual(first, second) {\n          if (typeof first !== typeof second) {\n            return false;\n          }\n          if (Array.isArray(first) || Array.isArray(second)) {\n            return false;\n          }\n          if (first !== null && typeof first === \"object\" && second !== null) {\n            if (Object.keys(first).length !== Object.keys(second).length) {\n              return false;\n            }\n            for (const key in first) {\n              if (!isEntryEqual(first[key], second[key])) {\n                return false;\n              }\n            }\n            return true;\n          }\n          return first === second || Number.isNaN(first) && Number.isNaN(second);\n        }\n        if (!(Array.isArray(firstDest) && Array.isArray(secondDest))) {\n          return false;\n        }\n        if (firstDest.length !== secondDest.length) {\n          return false;\n        }\n        for (let i = 0, ii = firstDest.length; i < ii; i++) {\n          if (!isEntryEqual(firstDest[i], secondDest[i])) {\n            return false;\n          }\n        }\n        return true;\n      }\n\n      /***/\n    }), (/* 23 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.PDFScriptingManager = void 0;\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      class PDFScriptingManager {\n        constructor({\n          eventBus,\n          sandboxBundleSrc = null,\n          scriptingFactory = null,\n          docPropertiesLookup = null\n        }) {\n          this._pdfDocument = null;\n          this._pdfViewer = null;\n          this._closeCapability = null;\n          this._destroyCapability = null;\n          this._scripting = null;\n          this._mouseState = Object.create(null);\n          this._ready = false;\n          this._eventBus = eventBus;\n          this._sandboxBundleSrc = sandboxBundleSrc;\n          this._scriptingFactory = scriptingFactory;\n          this._docPropertiesLookup = docPropertiesLookup;\n          if (!this._scriptingFactory) {\n            window.addEventListener(\"updatefromsandbox\", event => {\n              this._eventBus.dispatch(\"updatefromsandbox\", {\n                source: window,\n                detail: event.detail\n              });\n            });\n          }\n        }\n        setViewer(pdfViewer) {\n          this._pdfViewer = pdfViewer;\n        }\n        setDocument(pdfDocument) {\n          var _this11 = this;\n          return _asyncToGenerator(function* () {\n            if (_this11._pdfDocument) {\n              yield _this11._destroyScripting();\n            }\n            _this11._pdfDocument = pdfDocument;\n            if (!pdfDocument) {\n              return;\n            }\n            const [objects, calculationOrder, docActions] = yield Promise.all([pdfDocument.getFieldObjects(), pdfDocument.getCalculationOrderIds(), pdfDocument.getJSActions()]);\n            if (!objects && !docActions) {\n              yield _this11._destroyScripting();\n              return;\n            }\n            if (pdfDocument !== _this11._pdfDocument) {\n              return;\n            }\n            try {\n              _this11._scripting = _this11._createScripting();\n            } catch (error) {\n              console.error(`PDFScriptingManager.setDocument: \"${error?.message}\".`);\n              yield _this11._destroyScripting();\n              return;\n            }\n            _this11._internalEvents.set(\"updatefromsandbox\", event => {\n              if (event?.source !== window) {\n                return;\n              }\n              _this11._updateFromSandbox(event.detail);\n            });\n            _this11._internalEvents.set(\"dispatcheventinsandbox\", event => {\n              _this11._scripting?.dispatchEventInSandbox(event.detail);\n            });\n            _this11._internalEvents.set(\"pagechanging\", ({\n              pageNumber,\n              previous\n            }) => {\n              if (pageNumber === previous) {\n                return;\n              }\n              _this11._dispatchPageClose(previous);\n              _this11._dispatchPageOpen(pageNumber);\n            });\n            _this11._internalEvents.set(\"pagerendered\", ({\n              pageNumber\n            }) => {\n              if (!_this11._pageOpenPending.has(pageNumber)) {\n                return;\n              }\n              if (pageNumber !== _this11._pdfViewer.currentPageNumber) {\n                return;\n              }\n              _this11._dispatchPageOpen(pageNumber);\n            });\n            _this11._internalEvents.set(\"pagesdestroy\", /*#__PURE__*/function () {\n              var _ref3 = _asyncToGenerator(function* (event) {\n                yield _this11._dispatchPageClose(_this11._pdfViewer.currentPageNumber);\n                yield _this11._scripting?.dispatchEventInSandbox({\n                  id: \"doc\",\n                  name: \"WillClose\"\n                });\n                _this11._closeCapability?.resolve();\n              });\n              return function (_x4) {\n                return _ref3.apply(this, arguments);\n              };\n            }());\n            _this11._domEvents.set(\"mousedown\", event => {\n              _this11._mouseState.isDown = true;\n            });\n            _this11._domEvents.set(\"mouseup\", event => {\n              _this11._mouseState.isDown = false;\n            });\n            for (const [name, listener] of _this11._internalEvents) {\n              _this11._eventBus._on(name, listener);\n            }\n            for (const [name, listener] of _this11._domEvents) {\n              window.addEventListener(name, listener, true);\n            }\n            try {\n              const docProperties = yield _this11._getDocProperties();\n              if (pdfDocument !== _this11._pdfDocument) {\n                return;\n              }\n              yield _this11._scripting.createSandbox({\n                objects,\n                calculationOrder,\n                appInfo: {\n                  platform: navigator.platform,\n                  language: navigator.language\n                },\n                docInfo: {\n                  ...docProperties,\n                  actions: docActions\n                }\n              });\n              _this11._eventBus.dispatch(\"sandboxcreated\", {\n                source: _this11\n              });\n            } catch (error) {\n              console.error(`PDFScriptingManager.setDocument: \"${error?.message}\".`);\n              yield _this11._destroyScripting();\n              return;\n            }\n            yield _this11._scripting?.dispatchEventInSandbox({\n              id: \"doc\",\n              name: \"Open\"\n            });\n            yield _this11._dispatchPageOpen(_this11._pdfViewer.currentPageNumber, true);\n            Promise.resolve().then(() => {\n              if (pdfDocument === _this11._pdfDocument) {\n                _this11._ready = true;\n              }\n            });\n          })();\n        }\n        dispatchWillSave(detail) {\n          var _this12 = this;\n          return _asyncToGenerator(function* () {\n            return _this12._scripting?.dispatchEventInSandbox({\n              id: \"doc\",\n              name: \"WillSave\"\n            });\n          })();\n        }\n        dispatchDidSave(detail) {\n          var _this13 = this;\n          return _asyncToGenerator(function* () {\n            return _this13._scripting?.dispatchEventInSandbox({\n              id: \"doc\",\n              name: \"DidSave\"\n            });\n          })();\n        }\n        dispatchWillPrint(detail) {\n          var _this14 = this;\n          return _asyncToGenerator(function* () {\n            return _this14._scripting?.dispatchEventInSandbox({\n              id: \"doc\",\n              name: \"WillPrint\"\n            });\n          })();\n        }\n        dispatchDidPrint(detail) {\n          var _this15 = this;\n          return _asyncToGenerator(function* () {\n            return _this15._scripting?.dispatchEventInSandbox({\n              id: \"doc\",\n              name: \"DidPrint\"\n            });\n          })();\n        }\n        get mouseState() {\n          return this._mouseState;\n        }\n        get destroyPromise() {\n          return this._destroyCapability?.promise || null;\n        }\n        get ready() {\n          return this._ready;\n        }\n        get _internalEvents() {\n          return (0, _pdfjsLib.shadow)(this, \"_internalEvents\", new Map());\n        }\n        get _domEvents() {\n          return (0, _pdfjsLib.shadow)(this, \"_domEvents\", new Map());\n        }\n        get _pageOpenPending() {\n          return (0, _pdfjsLib.shadow)(this, \"_pageOpenPending\", new Set());\n        }\n        get _visitedPages() {\n          return (0, _pdfjsLib.shadow)(this, \"_visitedPages\", new Map());\n        }\n        _updateFromSandbox(detail) {\n          var _this16 = this;\n          return _asyncToGenerator(function* () {\n            const isInPresentationMode = _this16._pdfViewer.isInPresentationMode || _this16._pdfViewer.isChangingPresentationMode;\n            const {\n              id,\n              siblings,\n              command,\n              value\n            } = detail;\n            if (!id) {\n              switch (command) {\n                case \"clear\":\n                  console.clear();\n                  break;\n                case \"error\":\n                  console.error(value);\n                  break;\n                case \"layout\":\n                  if (isInPresentationMode) {\n                    return;\n                  }\n                  const modes = (0, _ui_utils.apiPageLayoutToViewerModes)(value);\n                  _this16._pdfViewer.spreadMode = modes.spreadMode;\n                  break;\n                case \"page-num\":\n                  _this16._pdfViewer.currentPageNumber = value + 1;\n                  break;\n                case \"print\":\n                  yield _this16._pdfViewer.pagesPromise;\n                  _this16._eventBus.dispatch(\"print\", {\n                    source: _this16\n                  });\n                  break;\n                case \"println\":\n                  console.log(value);\n                  break;\n                case \"zoom\":\n                  if (isInPresentationMode) {\n                    return;\n                  }\n                  _this16._pdfViewer.currentScaleValue = value;\n                  break;\n                case \"SaveAs\":\n                  _this16._eventBus.dispatch(\"save\", {\n                    source: _this16\n                  });\n                  break;\n                case \"FirstPage\":\n                  _this16._pdfViewer.currentPageNumber = 1;\n                  break;\n                case \"LastPage\":\n                  _this16._pdfViewer.currentPageNumber = _this16._pdfViewer.pagesCount;\n                  break;\n                case \"NextPage\":\n                  _this16._pdfViewer.nextPage();\n                  break;\n                case \"PrevPage\":\n                  _this16._pdfViewer.previousPage();\n                  break;\n                case \"ZoomViewIn\":\n                  if (isInPresentationMode) {\n                    return;\n                  }\n                  _this16._pdfViewer.increaseScale();\n                  break;\n                case \"ZoomViewOut\":\n                  if (isInPresentationMode) {\n                    return;\n                  }\n                  _this16._pdfViewer.decreaseScale();\n                  break;\n              }\n              return;\n            }\n            if (isInPresentationMode) {\n              if (detail.focus) {\n                return;\n              }\n            }\n            delete detail.id;\n            delete detail.siblings;\n            const ids = siblings ? [id, ...siblings] : [id];\n            for (const elementId of ids) {\n              const element = document.getElementById(elementId);\n              if (element) {\n                element.dispatchEvent(new CustomEvent(\"updatefromsandbox\", {\n                  detail\n                }));\n              } else {\n                _this16._pdfDocument?.annotationStorage.setValue(elementId, detail);\n              }\n            }\n          })();\n        }\n        _dispatchPageOpen(_x5) {\n          var _this17 = this;\n          return _asyncToGenerator(function* (pageNumber, initialize = false) {\n            const pdfDocument = _this17._pdfDocument,\n              visitedPages = _this17._visitedPages;\n            if (initialize) {\n              _this17._closeCapability = (0, _pdfjsLib.createPromiseCapability)();\n            }\n            if (!_this17._closeCapability) {\n              return;\n            }\n            const pageView = _this17._pdfViewer.getPageView(pageNumber - 1);\n            if (pageView?.renderingState !== _ui_utils.RenderingStates.FINISHED) {\n              _this17._pageOpenPending.add(pageNumber);\n              return;\n            }\n            _this17._pageOpenPending.delete(pageNumber);\n            const actionsPromise = _asyncToGenerator(function* () {\n              const actions = yield !visitedPages.has(pageNumber) ? pageView.pdfPage?.getJSActions() : null;\n              if (pdfDocument !== _this17._pdfDocument) {\n                return;\n              }\n              yield _this17._scripting?.dispatchEventInSandbox({\n                id: \"page\",\n                name: \"PageOpen\",\n                pageNumber,\n                actions\n              });\n            })();\n            visitedPages.set(pageNumber, actionsPromise);\n          }).apply(this, arguments);\n        }\n        _dispatchPageClose(pageNumber) {\n          var _this18 = this;\n          return _asyncToGenerator(function* () {\n            const pdfDocument = _this18._pdfDocument,\n              visitedPages = _this18._visitedPages;\n            if (!_this18._closeCapability) {\n              return;\n            }\n            if (_this18._pageOpenPending.has(pageNumber)) {\n              return;\n            }\n            const actionsPromise = visitedPages.get(pageNumber);\n            if (!actionsPromise) {\n              return;\n            }\n            visitedPages.set(pageNumber, null);\n            yield actionsPromise;\n            if (pdfDocument !== _this18._pdfDocument) {\n              return;\n            }\n            yield _this18._scripting?.dispatchEventInSandbox({\n              id: \"page\",\n              name: \"PageClose\",\n              pageNumber\n            });\n          })();\n        }\n        _getDocProperties() {\n          var _this19 = this;\n          return _asyncToGenerator(function* () {\n            if (_this19._docPropertiesLookup) {\n              return _this19._docPropertiesLookup(_this19._pdfDocument);\n            }\n            const {\n              docPropertiesLookup\n            } = __w_pdfjs_require__(24);\n            return docPropertiesLookup(_this19._pdfDocument);\n          })();\n        }\n        _createScripting() {\n          this._destroyCapability = (0, _pdfjsLib.createPromiseCapability)();\n          if (this._scripting) {\n            throw new Error(\"_createScripting: Scripting already exists.\");\n          }\n          if (this._scriptingFactory) {\n            return this._scriptingFactory.createScripting({\n              sandboxBundleSrc: this._sandboxBundleSrc\n            });\n          }\n          const {\n            GenericScripting\n          } = __w_pdfjs_require__(24);\n          return new GenericScripting(this._sandboxBundleSrc);\n        }\n        _destroyScripting() {\n          var _this20 = this;\n          return _asyncToGenerator(function* () {\n            if (!_this20._scripting) {\n              _this20._pdfDocument = null;\n              _this20._destroyCapability?.resolve();\n              return;\n            }\n            if (_this20._closeCapability) {\n              yield Promise.race([_this20._closeCapability.promise, new Promise(resolve => {\n                setTimeout(resolve, 1000);\n              })]).catch(reason => {});\n              _this20._closeCapability = null;\n            }\n            _this20._pdfDocument = null;\n            try {\n              yield _this20._scripting.destroySandbox();\n            } catch (ex) {}\n            for (const [name, listener] of _this20._internalEvents) {\n              _this20._eventBus._off(name, listener);\n            }\n            _this20._internalEvents.clear();\n            for (const [name, listener] of _this20._domEvents) {\n              window.removeEventListener(name, listener, true);\n            }\n            _this20._domEvents.clear();\n            _this20._pageOpenPending.clear();\n            _this20._visitedPages.clear();\n            _this20._scripting = null;\n            delete _this20._mouseState.isDown;\n            _this20._ready = false;\n            _this20._destroyCapability?.resolve();\n          })();\n        }\n      }\n      exports.PDFScriptingManager = PDFScriptingManager;\n\n      /***/\n    }), (/* 24 */\n    /***/(__unused_webpack_module, exports, __w_pdfjs_require__) => {\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.GenericScripting = void 0;\n      exports.docPropertiesLookup = docPropertiesLookup;\n      var _pdfjsLib = __w_pdfjs_require__(3);\n      function docPropertiesLookup(_x6) {\n        return _docPropertiesLookup.apply(this, arguments);\n      }\n      function _docPropertiesLookup() {\n        _docPropertiesLookup = _asyncToGenerator(function* (pdfDocument) {\n          const url = \"\",\n            baseUrl = url.split(\"#\")[0];\n          let {\n            info,\n            metadata,\n            contentDispositionFilename,\n            contentLength\n          } = yield pdfDocument.getMetadata();\n          if (!contentLength) {\n            const {\n              length\n            } = yield pdfDocument.getDownloadInfo();\n            contentLength = length;\n          }\n          return {\n            ...info,\n            baseURL: baseUrl,\n            filesize: contentLength,\n            filename: contentDispositionFilename || (0, _pdfjsLib.getPdfFilenameFromUrl)(url),\n            metadata: metadata?.getRaw(),\n            authors: metadata?.get(\"dc:creator\"),\n            numPages: pdfDocument.numPages,\n            URL: url\n          };\n        });\n        return _docPropertiesLookup.apply(this, arguments);\n      }\n      class GenericScripting {\n        constructor(sandboxBundleSrc) {\n          this._ready = (0, _pdfjsLib.loadScript)(sandboxBundleSrc, true).then(() => {\n            return window.pdfjsSandbox.QuickJSSandbox();\n          });\n        }\n        createSandbox(data) {\n          var _this21 = this;\n          return _asyncToGenerator(function* () {\n            const sandbox = yield _this21._ready;\n            sandbox.create(data);\n          })();\n        }\n        dispatchEventInSandbox(event) {\n          var _this22 = this;\n          return _asyncToGenerator(function* () {\n            const sandbox = yield _this22._ready;\n            setTimeout(() => sandbox.dispatchEvent(event), 0);\n          })();\n        }\n        destroySandbox() {\n          var _this23 = this;\n          return _asyncToGenerator(function* () {\n            const sandbox = yield _this23._ready;\n            sandbox.nukeSandbox();\n          })();\n        }\n      }\n      exports.GenericScripting = GenericScripting;\n\n      /***/\n    }\n    /******/)];\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __w_pdfjs_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId](module, module.exports, __w_pdfjs_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    var __webpack_exports__ = {};\n    // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n    (() => {\n      var exports = __webpack_exports__;\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      Object.defineProperty(exports, \"AnnotationLayerBuilder\", {\n        enumerable: true,\n        get: function () {\n          return _annotation_layer_builder.AnnotationLayerBuilder;\n        }\n      });\n      Object.defineProperty(exports, \"DefaultAnnotationLayerFactory\", {\n        enumerable: true,\n        get: function () {\n          return _default_factory.DefaultAnnotationLayerFactory;\n        }\n      });\n      Object.defineProperty(exports, \"DefaultStructTreeLayerFactory\", {\n        enumerable: true,\n        get: function () {\n          return _default_factory.DefaultStructTreeLayerFactory;\n        }\n      });\n      Object.defineProperty(exports, \"DefaultTextLayerFactory\", {\n        enumerable: true,\n        get: function () {\n          return _default_factory.DefaultTextLayerFactory;\n        }\n      });\n      Object.defineProperty(exports, \"DefaultXfaLayerFactory\", {\n        enumerable: true,\n        get: function () {\n          return _default_factory.DefaultXfaLayerFactory;\n        }\n      });\n      Object.defineProperty(exports, \"DownloadManager\", {\n        enumerable: true,\n        get: function () {\n          return _download_manager.DownloadManager;\n        }\n      });\n      Object.defineProperty(exports, \"EventBus\", {\n        enumerable: true,\n        get: function () {\n          return _event_utils.EventBus;\n        }\n      });\n      Object.defineProperty(exports, \"GenericL10n\", {\n        enumerable: true,\n        get: function () {\n          return _genericl10n.GenericL10n;\n        }\n      });\n      Object.defineProperty(exports, \"LinkTarget\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_link_service.LinkTarget;\n        }\n      });\n      Object.defineProperty(exports, \"NullL10n\", {\n        enumerable: true,\n        get: function () {\n          return _l10n_utils.NullL10n;\n        }\n      });\n      Object.defineProperty(exports, \"PDFFindController\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_find_controller.PDFFindController;\n        }\n      });\n      Object.defineProperty(exports, \"PDFHistory\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_history.PDFHistory;\n        }\n      });\n      Object.defineProperty(exports, \"PDFLinkService\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_link_service.PDFLinkService;\n        }\n      });\n      Object.defineProperty(exports, \"PDFPageView\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_page_view.PDFPageView;\n        }\n      });\n      Object.defineProperty(exports, \"PDFScriptingManager\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_scripting_manager.PDFScriptingManager;\n        }\n      });\n      Object.defineProperty(exports, \"PDFSinglePageViewer\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_viewer.PDFSinglePageViewer;\n        }\n      });\n      Object.defineProperty(exports, \"PDFViewer\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_viewer.PDFViewer;\n        }\n      });\n      Object.defineProperty(exports, \"ProgressBar\", {\n        enumerable: true,\n        get: function () {\n          return _ui_utils.ProgressBar;\n        }\n      });\n      Object.defineProperty(exports, \"SimpleLinkService\", {\n        enumerable: true,\n        get: function () {\n          return _pdf_link_service.SimpleLinkService;\n        }\n      });\n      Object.defineProperty(exports, \"StructTreeLayerBuilder\", {\n        enumerable: true,\n        get: function () {\n          return _struct_tree_layer_builder.StructTreeLayerBuilder;\n        }\n      });\n      Object.defineProperty(exports, \"TextLayerBuilder\", {\n        enumerable: true,\n        get: function () {\n          return _text_layer_builder.TextLayerBuilder;\n        }\n      });\n      Object.defineProperty(exports, \"XfaLayerBuilder\", {\n        enumerable: true,\n        get: function () {\n          return _xfa_layer_builder.XfaLayerBuilder;\n        }\n      });\n      Object.defineProperty(exports, \"parseQueryString\", {\n        enumerable: true,\n        get: function () {\n          return _ui_utils.parseQueryString;\n        }\n      });\n      var _default_factory = __w_pdfjs_require__(1);\n      var _pdf_link_service = __w_pdfjs_require__(5);\n      var _ui_utils = __w_pdfjs_require__(6);\n      var _pdf_viewer = __w_pdfjs_require__(10);\n      var _annotation_layer_builder = __w_pdfjs_require__(2);\n      var _download_manager = __w_pdfjs_require__(16);\n      var _event_utils = __w_pdfjs_require__(17);\n      var _genericl10n = __w_pdfjs_require__(18);\n      var _l10n_utils = __w_pdfjs_require__(4);\n      var _pdf_find_controller = __w_pdfjs_require__(20);\n      var _pdf_history = __w_pdfjs_require__(22);\n      var _pdf_page_view = __w_pdfjs_require__(12);\n      var _pdf_scripting_manager = __w_pdfjs_require__(23);\n      var _struct_tree_layer_builder = __w_pdfjs_require__(7);\n      var _text_layer_builder = __w_pdfjs_require__(8);\n      var _xfa_layer_builder = __w_pdfjs_require__(9);\n      const pdfjsVersion = '2.14.305';\n      const pdfjsBuild = 'eaaa8b4ad';\n    })();\n\n    /******/\n    return __webpack_exports__;\n    /******/\n  })();\n});\n//# sourceMappingURL=pdf_viewer.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}