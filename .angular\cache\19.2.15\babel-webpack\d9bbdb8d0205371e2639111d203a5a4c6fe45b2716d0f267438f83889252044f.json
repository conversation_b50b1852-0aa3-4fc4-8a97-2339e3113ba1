{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, createHashMap, merge, assert } from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { getAxisMainType, DATA_ZOOM_AXIS_DIMENSIONS } from './helper.js';\nimport { MULTIPLE_REFERRING, SINGLE_REFERRING } from '../../util/model.js';\nvar DataZoomAxisInfo = /** @class */function () {\n  function DataZoomAxisInfo() {\n    this.indexList = [];\n    this.indexMap = [];\n  }\n  DataZoomAxisInfo.prototype.add = function (axisCmptIdx) {\n    // Remove duplication.\n    if (!this.indexMap[axisCmptIdx]) {\n      this.indexList.push(axisCmptIdx);\n      this.indexMap[axisCmptIdx] = true;\n    }\n  };\n  return DataZoomAxisInfo;\n}();\nvar DataZoomModel = /** @class */function (_super) {\n  __extends(DataZoomModel, _super);\n  function DataZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = DataZoomModel.type;\n    _this._autoThrottle = true;\n    _this._noTarget = true;\n    /**\r\n     * It is `[rangeModeForMin, rangeModeForMax]`.\r\n     * The optional values for `rangeMode`:\r\n     * + `'value'` mode: the axis extent will always be determined by\r\n     *     `dataZoom.startValue` and `dataZoom.endValue`, despite\r\n     *     how data like and how `axis.min` and `axis.max` are.\r\n     * + `'percent'` mode: `100` represents 100% of the `[dMin, dMax]`,\r\n     *     where `dMin` is `axis.min` if `axis.min` specified, otherwise `data.extent[0]`,\r\n     *     and `dMax` is `axis.max` if `axis.max` specified, otherwise `data.extent[1]`.\r\n     *     Axis extent will be determined by the result of the percent of `[dMin, dMax]`.\r\n     *\r\n     * For example, when users are using dynamic data (update data periodically via `setOption`),\r\n     * if in `'value`' mode, the window will be kept in a fixed value range despite how\r\n     * data are appended, while if in `'percent'` mode, whe window range will be changed alone with\r\n     * the appended data (suppose `axis.min` and `axis.max` are not specified).\r\n     */\n    _this._rangePropMode = ['percent', 'percent'];\n    return _this;\n  }\n  DataZoomModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputRawOption = retrieveRawOption(option);\n    /**\r\n     * Suppose a \"main process\" start at the point that model prepared (that is,\r\n     * model initialized or merged or method called in `action`).\r\n     * We should keep the `main process` idempotent, that is, given a set of values\r\n     * on `option`, we get the same result.\r\n     *\r\n     * But sometimes, values on `option` will be updated for providing users\r\n     * a \"final calculated value\" (`dataZoomProcessor` will do that). Those value\r\n     * should not be the base/input of the `main process`.\r\n     *\r\n     * So in that case we should save and keep the input of the `main process`\r\n     * separately, called `settledOption`.\r\n     *\r\n     * For example, consider the case:\r\n     * (Step_1) brush zoom the grid by `toolbox.dataZoom`,\r\n     *     where the original input `option.startValue`, `option.endValue` are earsed by\r\n     *     calculated value.\r\n     * (Step)2) click the legend to hide and show a series,\r\n     *     where the new range is calculated by the earsed `startValue` and `endValue`,\r\n     *     which brings incorrect result.\r\n     */\n    this.settledOption = inputRawOption;\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype.mergeOption = function (newOption) {\n    var inputRawOption = retrieveRawOption(newOption);\n    // FIX #2591\n    merge(this.option, newOption, true);\n    merge(this.settledOption, inputRawOption, true);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype._doInit = function (inputRawOption) {\n    var thisOption = this.option;\n    this._setDefaultThrottle(inputRawOption);\n    this._updateRangeUse(inputRawOption);\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      // start/end has higher priority over startValue/endValue if they\n      // both set, but we should make chart.setOption({endValue: 1000})\n      // effective, rather than chart.setOption({endValue: 1000, end: null}).\n      if (this._rangePropMode[index] === 'value') {\n        thisOption[names[0]] = settledOption[names[0]] = null;\n      }\n      // Otherwise do nothing and use the merge result.\n    }, this);\n    this._resetTarget();\n  };\n  DataZoomModel.prototype._resetTarget = function () {\n    var optionOrient = this.get('orient', true);\n    var targetAxisIndexMap = this._targetAxisInfoMap = createHashMap();\n    var hasAxisSpecified = this._fillSpecifiedTargetAxis(targetAxisIndexMap);\n    if (hasAxisSpecified) {\n      this._orient = optionOrient || this._makeAutoOrientByTargetAxis();\n    } else {\n      this._orient = optionOrient || 'horizontal';\n      this._fillAutoTargetAxisByOrient(targetAxisIndexMap, this._orient);\n    }\n    this._noTarget = true;\n    targetAxisIndexMap.each(function (axisInfo) {\n      if (axisInfo.indexList.length) {\n        this._noTarget = false;\n      }\n    }, this);\n  };\n  DataZoomModel.prototype._fillSpecifiedTargetAxis = function (targetAxisIndexMap) {\n    var hasAxisSpecified = false;\n    each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n      var refering = this.getReferringComponents(getAxisMainType(axisDim), MULTIPLE_REFERRING);\n      // When user set axisIndex as a empty array, we think that user specify axisIndex\n      // but do not want use auto mode. Because empty array may be encountered when\n      // some error occurred.\n      if (!refering.specified) {\n        return;\n      }\n      hasAxisSpecified = true;\n      var axisInfo = new DataZoomAxisInfo();\n      each(refering.models, function (axisModel) {\n        axisInfo.add(axisModel.componentIndex);\n      });\n      targetAxisIndexMap.set(axisDim, axisInfo);\n    }, this);\n    return hasAxisSpecified;\n  };\n  DataZoomModel.prototype._fillAutoTargetAxisByOrient = function (targetAxisIndexMap, orient) {\n    var ecModel = this.ecModel;\n    var needAuto = true;\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisDim = orient === 'vertical' ? 'y' : 'x';\n      var axisModels = ecModel.findComponents({\n        mainType: axisDim + 'Axis'\n      });\n      setParallelAxis(axisModels, axisDim);\n    }\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisModels = ecModel.findComponents({\n        mainType: 'singleAxis',\n        filter: function (axisModel) {\n          return axisModel.get('orient', true) === orient;\n        }\n      });\n      setParallelAxis(axisModels, 'single');\n    }\n    function setParallelAxis(axisModels, axisDim) {\n      // At least use the first parallel axis as the target axis.\n      var axisModel = axisModels[0];\n      if (!axisModel) {\n        return;\n      }\n      var axisInfo = new DataZoomAxisInfo();\n      axisInfo.add(axisModel.componentIndex);\n      targetAxisIndexMap.set(axisDim, axisInfo);\n      needAuto = false;\n      // Find parallel axes in the same grid.\n      if (axisDim === 'x' || axisDim === 'y') {\n        var gridModel_1 = axisModel.getReferringComponents('grid', SINGLE_REFERRING).models[0];\n        gridModel_1 && each(axisModels, function (axModel) {\n          if (axisModel.componentIndex !== axModel.componentIndex && gridModel_1 === axModel.getReferringComponents('grid', SINGLE_REFERRING).models[0]) {\n            axisInfo.add(axModel.componentIndex);\n          }\n        });\n      }\n    }\n    if (needAuto) {\n      // If no parallel axis, find the first category axis as default. (Also consider polar).\n      each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n        if (!needAuto) {\n          return;\n        }\n        var axisModels = ecModel.findComponents({\n          mainType: getAxisMainType(axisDim),\n          filter: function (axisModel) {\n            return axisModel.get('type', true) === 'category';\n          }\n        });\n        if (axisModels[0]) {\n          var axisInfo = new DataZoomAxisInfo();\n          axisInfo.add(axisModels[0].componentIndex);\n          targetAxisIndexMap.set(axisDim, axisInfo);\n          needAuto = false;\n        }\n      }, this);\n    }\n  };\n  DataZoomModel.prototype._makeAutoOrientByTargetAxis = function () {\n    var dim;\n    // Find the first axis\n    this.eachTargetAxis(function (axisDim) {\n      !dim && (dim = axisDim);\n    }, this);\n    return dim === 'y' ? 'vertical' : 'horizontal';\n  };\n  DataZoomModel.prototype._setDefaultThrottle = function (inputRawOption) {\n    // When first time user set throttle, auto throttle ends.\n    if (inputRawOption.hasOwnProperty('throttle')) {\n      this._autoThrottle = false;\n    }\n    if (this._autoThrottle) {\n      var globalOption = this.ecModel.option;\n      this.option.throttle = globalOption.animation && globalOption.animationDurationUpdate > 0 ? 100 : 20;\n    }\n  };\n  DataZoomModel.prototype._updateRangeUse = function (inputRawOption) {\n    var rangePropMode = this._rangePropMode;\n    var rangeModeInOption = this.get('rangeMode');\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      var percentSpecified = inputRawOption[names[0]] != null;\n      var valueSpecified = inputRawOption[names[1]] != null;\n      if (percentSpecified && !valueSpecified) {\n        rangePropMode[index] = 'percent';\n      } else if (!percentSpecified && valueSpecified) {\n        rangePropMode[index] = 'value';\n      } else if (rangeModeInOption) {\n        rangePropMode[index] = rangeModeInOption[index];\n      } else if (percentSpecified) {\n        // percentSpecified && valueSpecified\n        rangePropMode[index] = 'percent';\n      }\n      // else remain its original setting.\n    });\n  };\n  DataZoomModel.prototype.noTarget = function () {\n    return this._noTarget;\n  };\n  DataZoomModel.prototype.getFirstTargetAxisModel = function () {\n    var firstAxisModel;\n    this.eachTargetAxis(function (axisDim, axisIndex) {\n      if (firstAxisModel == null) {\n        firstAxisModel = this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n      }\n    }, this);\n    return firstAxisModel;\n  };\n  /**\r\n   * @param {Function} callback param: axisModel, dimNames, axisIndex, dataZoomModel, ecModel\r\n   */\n  DataZoomModel.prototype.eachTargetAxis = function (callback, context) {\n    this._targetAxisInfoMap.each(function (axisInfo, axisDim) {\n      each(axisInfo.indexList, function (axisIndex) {\n        callback.call(context, axisDim, axisIndex);\n      });\n    });\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisProxy = function (axisDim, axisIndex) {\n    var axisModel = this.getAxisModel(axisDim, axisIndex);\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisModel = function (axisDim, axisIndex) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(axisDim && axisIndex != null);\n    }\n    var axisInfo = this._targetAxisInfoMap.get(axisDim);\n    if (axisInfo && axisInfo.indexMap[axisIndex]) {\n      return this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    }\n  };\n  /**\r\n   * If not specified, set to undefined.\r\n   */\n  DataZoomModel.prototype.setRawRange = function (opt) {\n    var thisOption = this.option;\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names) {\n      // Consider the pair <start, startValue>:\n      // If one has value and the other one is `null/undefined`, we both set them\n      // to `settledOption`. This strategy enables the feature to clear the original\n      // value in `settledOption` to `null/undefined`.\n      // But if both of them are `null/undefined`, we do not set them to `settledOption`\n      // and keep `settledOption` with the original value. This strategy enables users to\n      // only set <end or endValue> but not set <start or startValue> when calling\n      // `dispatchAction`.\n      // The pair <end, endValue> is treated in the same way.\n      if (opt[names[0]] != null || opt[names[1]] != null) {\n        thisOption[names[0]] = settledOption[names[0]] = opt[names[0]];\n        thisOption[names[1]] = settledOption[names[1]] = opt[names[1]];\n      }\n    }, this);\n    this._updateRangeUse(opt);\n  };\n  DataZoomModel.prototype.setCalculatedRange = function (opt) {\n    var option = this.option;\n    each(['start', 'startValue', 'end', 'endValue'], function (name) {\n      option[name] = opt[name];\n    });\n  };\n  DataZoomModel.prototype.getPercentRange = function () {\n    var axisProxy = this.findRepresentativeAxisProxy();\n    if (axisProxy) {\n      return axisProxy.getDataPercentWindow();\n    }\n  };\n  /**\r\n   * For example, chart.getModel().getComponent('dataZoom').getValueRange('y', 0);\r\n   *\r\n   * @return [startValue, endValue] value can only be '-' or finite number.\r\n   */\n  DataZoomModel.prototype.getValueRange = function (axisDim, axisIndex) {\n    if (axisDim == null && axisIndex == null) {\n      var axisProxy = this.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        return axisProxy.getDataValueWindow();\n      }\n    } else {\n      return this.getAxisProxy(axisDim, axisIndex).getDataValueWindow();\n    }\n  };\n  /**\r\n   * @param axisModel If axisModel given, find axisProxy\r\n   *      corresponding to the axisModel\r\n   */\n  DataZoomModel.prototype.findRepresentativeAxisProxy = function (axisModel) {\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n    // Find the first hosted axisProxy\n    var firstProxy;\n    var axisDimList = this._targetAxisInfoMap.keys();\n    for (var i = 0; i < axisDimList.length; i++) {\n      var axisDim = axisDimList[i];\n      var axisInfo = this._targetAxisInfoMap.get(axisDim);\n      for (var j = 0; j < axisInfo.indexList.length; j++) {\n        var proxy = this.getAxisProxy(axisDim, axisInfo.indexList[j]);\n        if (proxy.hostedBy(this)) {\n          return proxy;\n        }\n        if (!firstProxy) {\n          firstProxy = proxy;\n        }\n      }\n    }\n    // If no hosted proxy found, still need to return a proxy.\n    // This case always happens in toolbox dataZoom, where axes are all hosted by\n    // other dataZooms.\n    return firstProxy;\n  };\n  DataZoomModel.prototype.getRangePropMode = function () {\n    return this._rangePropMode.slice();\n  };\n  DataZoomModel.prototype.getOrient = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      // Should not be called before initialized.\n      assert(this._orient);\n    }\n    return this._orient;\n  };\n  DataZoomModel.type = 'dataZoom';\n  DataZoomModel.dependencies = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'series', 'toolbox'];\n  DataZoomModel.defaultOption = {\n    // zlevel: 0,\n    z: 4,\n    filterMode: 'filter',\n    start: 0,\n    end: 100\n  };\n  return DataZoomModel;\n}(ComponentModel);\n/**\r\n * Retrieve those raw params from option, which will be cached separately,\r\n * because they will be overwritten by normalized/calculated values in the main\r\n * process.\r\n */\nfunction retrieveRawOption(option) {\n  var ret = {};\n  each(['start', 'end', 'startValue', 'endValue', 'throttle'], function (name) {\n    option.hasOwnProperty(name) && (ret[name] = option[name]);\n  });\n  return ret;\n}\nexport default DataZoomModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}