{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Simple view coordinate system\r\n * Mapping given x, y to transformd view x, y\r\n */\nimport * as vector from 'zrender/lib/core/vector.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport { parsePercent } from '../util/number.js';\nvar v2ApplyTransform = vector.applyTransform;\nvar View = /** @class */function (_super) {\n  __extends(View, _super);\n  function View(name) {\n    var _this = _super.call(this) || this;\n    _this.type = 'view';\n    _this.dimensions = ['x', 'y'];\n    /**\r\n     * Represents the transform brought by roam/zoom.\r\n     * If `View['_viewRect']` applies roam transform,\r\n     * we can get the final displayed rect.\r\n     */\n    _this._roamTransformable = new Transformable();\n    /**\r\n     * Represents the transform from `View['_rect']` to `View['_viewRect']`.\r\n     */\n    _this._rawTransformable = new Transformable();\n    _this.name = name;\n    return _this;\n  }\n  View.prototype.setBoundingRect = function (x, y, width, height) {\n    this._rect = new BoundingRect(x, y, width, height);\n    return this._rect;\n  };\n  /**\r\n   * @return {module:zrender/core/BoundingRect}\r\n   */\n  View.prototype.getBoundingRect = function () {\n    return this._rect;\n  };\n  View.prototype.setViewRect = function (x, y, width, height) {\n    this._transformTo(x, y, width, height);\n    this._viewRect = new BoundingRect(x, y, width, height);\n  };\n  /**\r\n   * Transformed to particular position and size\r\n   */\n  View.prototype._transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var rawTransform = this._rawTransformable;\n    rawTransform.transform = rect.calculateTransform(new BoundingRect(x, y, width, height));\n    var rawParent = rawTransform.parent;\n    rawTransform.parent = null;\n    rawTransform.decomposeTransform();\n    rawTransform.parent = rawParent;\n    this._updateTransform();\n  };\n  /**\r\n   * Set center of view\r\n   */\n  View.prototype.setCenter = function (centerCoord, api) {\n    if (!centerCoord) {\n      return;\n    }\n    this._center = [parsePercent(centerCoord[0], api.getWidth()), parsePercent(centerCoord[1], api.getHeight())];\n    this._updateCenterAndZoom();\n  };\n  View.prototype.setZoom = function (zoom) {\n    zoom = zoom || 1;\n    var zoomLimit = this.zoomLimit;\n    if (zoomLimit) {\n      if (zoomLimit.max != null) {\n        zoom = Math.min(zoomLimit.max, zoom);\n      }\n      if (zoomLimit.min != null) {\n        zoom = Math.max(zoomLimit.min, zoom);\n      }\n    }\n    this._zoom = zoom;\n    this._updateCenterAndZoom();\n  };\n  /**\r\n   * Get default center without roam\r\n   */\n  View.prototype.getDefaultCenter = function () {\n    // Rect before any transform\n    var rawRect = this.getBoundingRect();\n    var cx = rawRect.x + rawRect.width / 2;\n    var cy = rawRect.y + rawRect.height / 2;\n    return [cx, cy];\n  };\n  View.prototype.getCenter = function () {\n    return this._center || this.getDefaultCenter();\n  };\n  View.prototype.getZoom = function () {\n    return this._zoom || 1;\n  };\n  View.prototype.getRoamTransform = function () {\n    return this._roamTransformable.getLocalTransform();\n  };\n  /**\r\n   * Remove roam\r\n   */\n  View.prototype._updateCenterAndZoom = function () {\n    // Must update after view transform updated\n    var rawTransformMatrix = this._rawTransformable.getLocalTransform();\n    var roamTransform = this._roamTransformable;\n    var defaultCenter = this.getDefaultCenter();\n    var center = this.getCenter();\n    var zoom = this.getZoom();\n    center = vector.applyTransform([], center, rawTransformMatrix);\n    defaultCenter = vector.applyTransform([], defaultCenter, rawTransformMatrix);\n    roamTransform.originX = center[0];\n    roamTransform.originY = center[1];\n    roamTransform.x = defaultCenter[0] - center[0];\n    roamTransform.y = defaultCenter[1] - center[1];\n    roamTransform.scaleX = roamTransform.scaleY = zoom;\n    this._updateTransform();\n  };\n  /**\r\n   * Update transform props on `this` based on the current\r\n   * `this._roamTransformable` and `this._rawTransformable`.\r\n   */\n  View.prototype._updateTransform = function () {\n    var roamTransformable = this._roamTransformable;\n    var rawTransformable = this._rawTransformable;\n    rawTransformable.parent = roamTransformable;\n    roamTransformable.updateTransform();\n    rawTransformable.updateTransform();\n    matrix.copy(this.transform || (this.transform = []), rawTransformable.transform || matrix.create());\n    this._rawTransform = rawTransformable.getLocalTransform();\n    this.invTransform = this.invTransform || [];\n    matrix.invert(this.invTransform, this.transform);\n    this.decomposeTransform();\n  };\n  View.prototype.getTransformInfo = function () {\n    var rawTransformable = this._rawTransformable;\n    var roamTransformable = this._roamTransformable;\n    // Because roamTransformabel has `originX/originY` modified,\n    // but the caller of `getTransformInfo` can not handle `originX/originY`,\n    // so need to recalculate them.\n    var dummyTransformable = new Transformable();\n    dummyTransformable.transform = roamTransformable.transform;\n    dummyTransformable.decomposeTransform();\n    return {\n      roam: {\n        x: dummyTransformable.x,\n        y: dummyTransformable.y,\n        scaleX: dummyTransformable.scaleX,\n        scaleY: dummyTransformable.scaleY\n      },\n      raw: {\n        x: rawTransformable.x,\n        y: rawTransformable.y,\n        scaleX: rawTransformable.scaleX,\n        scaleY: rawTransformable.scaleY\n      }\n    };\n  };\n  View.prototype.getViewRect = function () {\n    return this._viewRect;\n  };\n  /**\r\n   * Get view rect after roam transform\r\n   */\n  View.prototype.getViewRectAfterRoam = function () {\n    var rect = this.getBoundingRect().clone();\n    rect.applyTransform(this.transform);\n    return rect;\n  };\n  /**\r\n   * Convert a single (lon, lat) data item to (x, y) point.\r\n   */\n  View.prototype.dataToPoint = function (data, noRoam, out) {\n    var transform = noRoam ? this._rawTransform : this.transform;\n    out = out || [];\n    return transform ? v2ApplyTransform(out, data, transform) : vector.copy(out, data);\n  };\n  /**\r\n   * Convert a (x, y) point to (lon, lat) data\r\n   */\n  View.prototype.pointToData = function (point) {\n    var invTransform = this.invTransform;\n    return invTransform ? v2ApplyTransform([], point, invTransform) : [point[0], point[1]];\n  };\n  View.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.dataToPoint(value) : null;\n  };\n  View.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? coordSys.pointToData(pixel) : null;\n  };\n  /**\r\n   * @implements\r\n   */\n  View.prototype.containPoint = function (point) {\n    return this.getViewRectAfterRoam().contain(point[0], point[1]);\n  };\n  View.dimensions = ['x', 'y'];\n  return View;\n}(Transformable);\nfunction getCoordSys(finder) {\n  var seriesModel = finder.seriesModel;\n  return seriesModel ? seriesModel.coordinateSystem : null; // e.g., graph.\n}\nexport default View;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}