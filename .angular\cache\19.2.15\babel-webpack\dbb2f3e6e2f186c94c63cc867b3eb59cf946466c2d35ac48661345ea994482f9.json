{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as axisPointerModelHelper from './modelHelper.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar clone = zrUtil.clone;\nvar bind = zrUtil.bind;\n/**\r\n * Base axis pointer class in 2D.\r\n */\nvar BaseAxisPointer = /** @class */function () {\n  function BaseAxisPointer() {\n    this._dragging = false;\n    /**\r\n     * In px, arbitrary value. Do not set too small,\r\n     * no animation is ok for most cases.\r\n     */\n    this.animationThreshold = 15;\n  }\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.render = function (axisModel, axisPointerModel, api, forceRender) {\n    var value = axisPointerModel.get('value');\n    var status = axisPointerModel.get('status');\n    // Bind them to `this`, not in closure, otherwise they will not\n    // be replaced when user calling setOption in not merge mode.\n    this._axisModel = axisModel;\n    this._axisPointerModel = axisPointerModel;\n    this._api = api;\n    // Optimize: `render` will be called repeatedly during mouse move.\n    // So it is power consuming if performing `render` each time,\n    // especially on mobile device.\n    if (!forceRender && this._lastValue === value && this._lastStatus === status) {\n      return;\n    }\n    this._lastValue = value;\n    this._lastStatus = status;\n    var group = this._group;\n    var handle = this._handle;\n    if (!status || status === 'hide') {\n      // Do not clear here, for animation better.\n      group && group.hide();\n      handle && handle.hide();\n      return;\n    }\n    group && group.show();\n    handle && handle.show();\n    // Otherwise status is 'show'\n    var elOption = {};\n    this.makeElOption(elOption, value, axisModel, axisPointerModel, api);\n    // Enable change axis pointer type.\n    var graphicKey = elOption.graphicKey;\n    if (graphicKey !== this._lastGraphicKey) {\n      this.clear(api);\n    }\n    this._lastGraphicKey = graphicKey;\n    var moveAnimation = this._moveAnimation = this.determineAnimation(axisModel, axisPointerModel);\n    if (!group) {\n      group = this._group = new graphic.Group();\n      this.createPointerEl(group, elOption, axisModel, axisPointerModel);\n      this.createLabelEl(group, elOption, axisModel, axisPointerModel);\n      api.getZr().add(group);\n    } else {\n      var doUpdateProps = zrUtil.curry(updateProps, axisPointerModel, moveAnimation);\n      this.updatePointerEl(group, elOption, doUpdateProps);\n      this.updateLabelEl(group, elOption, doUpdateProps, axisPointerModel);\n    }\n    updateMandatoryProps(group, axisPointerModel, true);\n    this._renderHandle(value);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.remove = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.dispose = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.determineAnimation = function (axisModel, axisPointerModel) {\n    var animation = axisPointerModel.get('animation');\n    var axis = axisModel.axis;\n    var isCategoryAxis = axis.type === 'category';\n    var useSnap = axisPointerModel.get('snap');\n    // Value axis without snap always do not snap.\n    if (!useSnap && !isCategoryAxis) {\n      return false;\n    }\n    if (animation === 'auto' || animation == null) {\n      var animationThreshold = this.animationThreshold;\n      if (isCategoryAxis && axis.getBandWidth() > animationThreshold) {\n        return true;\n      }\n      // It is important to auto animation when snap used. Consider if there is\n      // a dataZoom, animation will be disabled when too many points exist, while\n      // it will be enabled for better visual effect when little points exist.\n      if (useSnap) {\n        var seriesDataCount = axisPointerModelHelper.getAxisInfo(axisModel).seriesDataCount;\n        var axisExtent = axis.getExtent();\n        // Approximate band width\n        return Math.abs(axisExtent[0] - axisExtent[1]) / seriesDataCount > animationThreshold;\n      }\n      return false;\n    }\n    return animation === true;\n  };\n  /**\r\n   * add {pointer, label, graphicKey} to elOption\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    // Should be implemenented by sub-class.\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createPointerEl = function (group, elOption, axisModel, axisPointerModel) {\n    var pointerOption = elOption.pointer;\n    if (pointerOption) {\n      var pointerEl = inner(group).pointerEl = new graphic[pointerOption.type](clone(elOption.pointer));\n      group.add(pointerEl);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createLabelEl = function (group, elOption, axisModel, axisPointerModel) {\n    if (elOption.label) {\n      var labelEl = inner(group).labelEl = new graphic.Text(clone(elOption.label));\n      group.add(labelEl);\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updatePointerEl = function (group, elOption, updateProps) {\n    var pointerEl = inner(group).pointerEl;\n    if (pointerEl && elOption.pointer) {\n      pointerEl.setStyle(elOption.pointer.style);\n      updateProps(pointerEl, {\n        shape: elOption.pointer.shape\n      });\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updateLabelEl = function (group, elOption, updateProps, axisPointerModel) {\n    var labelEl = inner(group).labelEl;\n    if (labelEl) {\n      labelEl.setStyle(elOption.label.style);\n      updateProps(labelEl, {\n        // Consider text length change in vertical axis, animation should\n        // be used on shape, otherwise the effect will be weird.\n        // TODOTODO\n        // shape: elOption.label.shape,\n        x: elOption.label.x,\n        y: elOption.label.y\n      });\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype._renderHandle = function (value) {\n    if (this._dragging || !this.updateHandleTransform) {\n      return;\n    }\n    var axisPointerModel = this._axisPointerModel;\n    var zr = this._api.getZr();\n    var handle = this._handle;\n    var handleModel = axisPointerModel.getModel('handle');\n    var status = axisPointerModel.get('status');\n    if (!handleModel.get('show') || !status || status === 'hide') {\n      handle && zr.remove(handle);\n      this._handle = null;\n      return;\n    }\n    var isInit;\n    if (!this._handle) {\n      isInit = true;\n      handle = this._handle = graphic.createIcon(handleModel.get('icon'), {\n        cursor: 'move',\n        draggable: true,\n        onmousemove: function (e) {\n          // For mobile device, prevent screen slider on the button.\n          eventTool.stop(e.event);\n        },\n        onmousedown: bind(this._onHandleDragMove, this, 0, 0),\n        drift: bind(this._onHandleDragMove, this),\n        ondragend: bind(this._onHandleDragEnd, this)\n      });\n      zr.add(handle);\n    }\n    updateMandatoryProps(handle, axisPointerModel, false);\n    // update style\n    handle.setStyle(handleModel.getItemStyle(null, ['color', 'borderColor', 'borderWidth', 'opacity', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY']));\n    // update position\n    var handleSize = handleModel.get('size');\n    if (!zrUtil.isArray(handleSize)) {\n      handleSize = [handleSize, handleSize];\n    }\n    handle.scaleX = handleSize[0] / 2;\n    handle.scaleY = handleSize[1] / 2;\n    throttleUtil.createOrUpdate(this, '_doDispatchAxisPointer', handleModel.get('throttle') || 0, 'fixRate');\n    this._moveHandleToValue(value, isInit);\n  };\n  BaseAxisPointer.prototype._moveHandleToValue = function (value, isInit) {\n    updateProps(this._axisPointerModel, !isInit && this._moveAnimation, this._handle, getHandleTransProps(this.getHandleTransform(value, this._axisModel, this._axisPointerModel)));\n  };\n  BaseAxisPointer.prototype._onHandleDragMove = function (dx, dy) {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    this._dragging = true;\n    // Persistent for throttle.\n    var trans = this.updateHandleTransform(getHandleTransProps(handle), [dx, dy], this._axisModel, this._axisPointerModel);\n    this._payloadInfo = trans;\n    handle.stopAnimation();\n    handle.attr(getHandleTransProps(trans));\n    inner(handle).lastProp = null;\n    this._doDispatchAxisPointer();\n  };\n  /**\r\n   * Throttled method.\r\n   */\n  BaseAxisPointer.prototype._doDispatchAxisPointer = function () {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var payloadInfo = this._payloadInfo;\n    var axisModel = this._axisModel;\n    this._api.dispatchAction({\n      type: 'updateAxisPointer',\n      x: payloadInfo.cursorPoint[0],\n      y: payloadInfo.cursorPoint[1],\n      tooltipOption: payloadInfo.tooltipOption,\n      axesInfo: [{\n        axisDim: axisModel.axis.dim,\n        axisIndex: axisModel.componentIndex\n      }]\n    });\n  };\n  BaseAxisPointer.prototype._onHandleDragEnd = function () {\n    this._dragging = false;\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var value = this._axisPointerModel.get('value');\n    // Consider snap or categroy axis, handle may be not consistent with\n    // axisPointer. So move handle to align the exact value position when\n    // drag ended.\n    this._moveHandleToValue(value);\n    // For the effect: tooltip will be shown when finger holding on handle\n    // button, and will be hidden after finger left handle button.\n    this._api.dispatchAction({\n      type: 'hideTip'\n    });\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype.clear = function (api) {\n    this._lastValue = null;\n    this._lastStatus = null;\n    var zr = api.getZr();\n    var group = this._group;\n    var handle = this._handle;\n    if (zr && group) {\n      this._lastGraphicKey = null;\n      group && zr.remove(group);\n      handle && zr.remove(handle);\n      this._group = null;\n      this._handle = null;\n      this._payloadInfo = null;\n    }\n    throttleUtil.clear(this, '_doDispatchAxisPointer');\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.doClear = function () {\n    // Implemented by sub-class if necessary.\n  };\n  BaseAxisPointer.prototype.buildLabel = function (xy, wh, xDimIndex) {\n    xDimIndex = xDimIndex || 0;\n    return {\n      x: xy[xDimIndex],\n      y: xy[1 - xDimIndex],\n      width: wh[xDimIndex],\n      height: wh[1 - xDimIndex]\n    };\n  };\n  return BaseAxisPointer;\n}();\nfunction updateProps(animationModel, moveAnimation, el, props) {\n  // Animation optimize.\n  if (!propsEqual(inner(el).lastProp, props)) {\n    inner(el).lastProp = props;\n    moveAnimation ? graphic.updateProps(el, props, animationModel) : (el.stopAnimation(), el.attr(props));\n  }\n}\nfunction propsEqual(lastProps, newProps) {\n  if (zrUtil.isObject(lastProps) && zrUtil.isObject(newProps)) {\n    var equals_1 = true;\n    zrUtil.each(newProps, function (item, key) {\n      equals_1 = equals_1 && propsEqual(lastProps[key], item);\n    });\n    return !!equals_1;\n  } else {\n    return lastProps === newProps;\n  }\n}\nfunction updateLabelShowHide(labelEl, axisPointerModel) {\n  labelEl[axisPointerModel.get(['label', 'show']) ? 'show' : 'hide']();\n}\nfunction getHandleTransProps(trans) {\n  return {\n    x: trans.x || 0,\n    y: trans.y || 0,\n    rotation: trans.rotation || 0\n  };\n}\nfunction updateMandatoryProps(group, axisPointerModel, silent) {\n  var z = axisPointerModel.get('z');\n  var zlevel = axisPointerModel.get('zlevel');\n  group && group.traverse(function (el) {\n    if (el.type !== 'group') {\n      z != null && (el.z = z);\n      zlevel != null && (el.zlevel = zlevel);\n      el.silent = silent;\n    }\n  });\n}\nexport default BaseAxisPointer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}