{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { use } from '../../extension.js';\nimport AxisView from '../axis/AxisView.js';\nimport PolarAxisPointer from '../axisPointer/PolarAxisPointer.js';\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport PolarModel from '../../coord/polar/PolarModel.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport { AngleAxisModel, RadiusAxisModel } from '../../coord/polar/AxisModel.js';\nimport polarCreator from '../../coord/polar/polarCreator.js';\nimport AngleAxisView from '../axis/AngleAxisView.js';\nimport RadiusAxisView from '../axis/RadiusAxisView.js';\nimport ComponentView from '../../view/Component.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport barLayoutPolar from '../../layout/barPolar.js';\nvar angleAxisExtraOption = {\n  startAngle: 90,\n  clockwise: true,\n  splitNumber: 12,\n  axisLabel: {\n    rotate: 0\n  }\n};\nvar radiusAxisExtraOption = {\n  splitNumber: 5\n};\nvar PolarView = /** @class */function (_super) {\n  __extends(PolarView, _super);\n  function PolarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PolarView.type;\n    return _this;\n  }\n  PolarView.type = 'polar';\n  return PolarView;\n}(ComponentView);\nexport function install(registers) {\n  use(installAxisPointer);\n  AxisView.registerAxisPointerClass('PolarAxisPointer', PolarAxisPointer);\n  registers.registerCoordinateSystem('polar', polarCreator);\n  registers.registerComponentModel(PolarModel);\n  registers.registerComponentView(PolarView);\n  // Model and view for angleAxis and radiusAxis\n  axisModelCreator(registers, 'angle', AngleAxisModel, angleAxisExtraOption);\n  axisModelCreator(registers, 'radius', RadiusAxisModel, radiusAxisExtraOption);\n  registers.registerComponentView(AngleAxisView);\n  registers.registerComponentView(RadiusAxisView);\n  registers.registerLayout(curry(barLayoutPolar, 'bar'));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}