{"ast": null, "code": "import env from './core/env.js';\nvar dpr = 1;\nif (env.hasGlobalWindow) {\n  dpr = Math.max(window.devicePixelRatio || window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI || 1, 1);\n}\nexport var debugMode = 0;\nexport var devicePixelRatio = dpr;\nexport var DARK_MODE_THRESHOLD = 0.4;\nexport var DARK_LABEL_COLOR = '#333';\nexport var LIGHT_LABEL_COLOR = '#ccc';\nexport var LIGHTER_LABEL_COLOR = '#eee';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}