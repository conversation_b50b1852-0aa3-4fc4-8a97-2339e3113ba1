{"ast": null, "code": "import { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/core/services/calls-services/freeRecitation/free-recitation.service\";\nimport * as i5 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/common\";\nfunction FreeRecitationCallComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10);\n    i0.ɵɵelement(4, \"img\", 11);\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"p\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 14);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"p\", 15);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 16);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 17)(17, \"p\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.student_schedule, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.recReqDetails.enTechName : ctx_r0.recReqDetails.arTechName, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.recReqDetails.enTechName : ctx_r0.recReqDetails.arTechName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.recReqDetails.enProgBatName : ctx_r0.recReqDetails.arProgBatName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? ctx_r0.recReqDetails.enProgBatName : ctx_r0.recReqDetails.arProgBatName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.recReqDetails.recDate, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" \", i0.ɵɵpipeBind1(14, 12, \"GENERAL.FROM\"), \" \\u00A0 \", ctx_r0.recReqDetails.fromTime, \"\\u00A0\\u00A0 : \", i0.ɵɵpipeBind1(15, 14, \"GENERAL.TO\"), \" \\u00A0\", ctx_r0.recReqDetails.toTime, \" \\u00A0\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.recReqDetails.details, \" \");\n  }\n}\nfunction FreeRecitationCallComponent_app_jitsi_call_integ_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-jitsi-call-integ\", 19);\n    i0.ɵɵlistener(\"endCallEvent\", function FreeRecitationCallComponent_app_jitsi_call_integ_6_Template_app_jitsi_call_integ_endCallEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.endCall($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"callJoinModel\", ctx_r0.callJoinModel)(\"jitsiSettingOptions\", ctx_r0.jitsiSettingOptions);\n  }\n}\nexport let FreeRecitationCallComponent = /*#__PURE__*/(() => {\n  class FreeRecitationCallComponent {\n    route;\n    translate;\n    dialog;\n    router;\n    freeRecitationService;\n    alertifyService;\n    imagesPathesService;\n    callId;\n    callUserId;\n    callEntityId;\n    callModuleTypehuffazId;\n    modId;\n    isVideoMute;\n    callJoinModel;\n    jitsiSettingOptions;\n    recReqDetails;\n    langEnum = LanguageEnum;\n    constructor(route, translate, dialog, router, freeRecitationService, alertifyService, imagesPathesService) {\n      this.route = route;\n      this.translate = translate;\n      this.dialog = dialog;\n      this.router = router;\n      this.freeRecitationService = freeRecitationService;\n      this.alertifyService = alertifyService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.callId = this.route.snapshot.params.callId;\n      this.callUserId = this.route.snapshot.params.user;\n      this.callModuleTypehuffazId = this.route.snapshot.params.CallType;\n      this.modId = this.route.snapshot.params.modId;\n      this.isVideoMute = this.route.snapshot.params.isVideoMute;\n      this.callEntityId = this.route.snapshot.params.entityId;\n      this.getFreeRecitationRequDetails();\n      this.initCall();\n    }\n    getFreeRecitationRequDetails() {\n      this.freeRecitationService.getFreeRecitationRequestDetailsById(this.callEntityId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.recReqDetails = res.data;\n        } else {\n          this.alertifyService.error(res.message || '');\n        }\n      }, err => {});\n    }\n    initCall() {\n      this.callJoinModel = {\n        callId: this.callId,\n        usrId: this.callUserId,\n        //JSON.parse(localStorage.getItem(\"user\") || '{}').id,\n        callModuleTypehuffazId: this.callModuleTypehuffazId,\n        isMod: false\n      };\n      this.jitsiSettingOptions = {\n        isVideoMute: this.isVideoMute,\n        startCallType: StartCallTypesEnum.JoinMode,\n        endCallType: EndCallTypesEnum.FreeRecitationTechUsrEndCall\n      };\n    }\n    endCall(callId) {\n      this.confirmSardTolabTask();\n    }\n    confirmSardTolabTask() {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Has the free recitation been completed?\" : \"هل تم الانتهاء من السرد الحر\";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Free Recitation' : 'السرد الحر', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.router.navigateByUrl('recitation-task-call/rating/' + this.callId + '/' + StartCallTypesEnum.JoinMode + '/' + RoleEnum.Student + '/' + EndCallTypesEnum.FreeRecitationTechUsrEndCall + '/undefined/undefined'); // double slash as two parameters empty\n        }\n      });\n    }\n    static ɵfac = function FreeRecitationCallComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FreeRecitationCallComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i4.FreeRecitationService), i0.ɵɵdirectiveInject(i5.AlertifyService), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FreeRecitationCallComponent,\n      selectors: [[\"app-free-recitation-call\"]],\n      decls: 7,\n      vars: 2,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"mx-0\"], [1, \"col-3\", \"cus_info\"], [\"class\", \"card-request\", 4, \"ngIf\"], [1, \"col-9\"], [1, \"call-integ\"], [3, \"callJoinModel\", \"jitsiSettingOptions\", \"endCallEvent\", 4, \"ngIf\"], [1, \"card-request\"], [1, \"card_body\"], [1, \"d-flex\", \"pb-0\", \"align-items-center\", \"justify-content-between\", \"mb-3\"], [1, \"d-flex\", \"pb-0\", \"align-items-center\", \"justify-content-start\"], [1, \"calender_img\", 3, \"src\"], [1, \"mx-1\", \"d-inline-block\"], [1, \"program_user\", \"bold\", \"mb-2\", \"ellipsis\", \"p-0\", 3, \"title\"], [1, \"progAr\", \"mb-2\", \"ellipsis\", \"p-0\", 3, \"title\"], [1, \"bold\", \"date\", \"px-2\"], [1, \"to_date\"], [1, \"caed_desc\", \"mb-2\", \"px-1\"], [1, \"desc\"], [3, \"endCallEvent\", \"callJoinModel\", \"jitsiSettingOptions\"]],\n      template: function FreeRecitationCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, FreeRecitationCallComponent_div_3_Template, 19, 16, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵtemplate(6, FreeRecitationCallComponent_app_jitsi_call_integ_6_Template, 1, 2, \"app-jitsi-call-integ\", 6);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.recReqDetails);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.callJoinModel);\n        }\n      },\n      dependencies: [i7.NgIf, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.card-request[_ngcontent-%COMP%]{background:#fff;margin:0 .2rem;box-shadow:0 .188rem 1rem #f2f1f1de;border-radius:.75rem;padding:0;padding-bottom:.7rem;overflow:hidden;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .bold[_ngcontent-%COMP%]{font-weight:700;font-size:1rem}.card-request[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:1.437rem;height:4.125rem}.card-request[_ngcontent-%COMP%]   .action_button[_ngcontent-%COMP%]{height:1.438rem;width:1.438rem;cursor:pointer!important}.card-request[_ngcontent-%COMP%]   .flex-direction-column[_ngcontent-%COMP%]{flex-direction:column}.card-request[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.85rem}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]{background-color:#fff;min-height:6.25rem;padding:1.25rem;padding-bottom:0;padding-top:.625rem;color:#4d4d4d}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(en){text-align:left}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:lang(ar){text-align:right}.card-request[_ngcontent-%COMP%]   .card_body[_ngcontent-%COMP%]   .name_user[_ngcontent-%COMP%]{color:var(--main_color);font-size:1.125rem}.card-request[_ngcontent-%COMP%]   .number_request[_ngcontent-%COMP%]{color:#d6d7d8;font-weight:700;font-size:.75rem}.card-request[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:4.687rem;height:4.687rem;border-radius:.313rem;border:.063rem solid #C1C7D0}.card-request[_ngcontent-%COMP%]   .program_user[_ngcontent-%COMP%]{color:#333;font-size:.875rem}.card-request[_ngcontent-%COMP%]   .daily_work[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.75rem}.card-request[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:8.125rem}.card-request[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%]{font-size:.75rem}.card-request[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.card-request[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%]{height:1.875rem!important}}.calender_img[_ngcontent-%COMP%]{width:2.87rem;height:2.87rem;padding:.3rem}.ellipsis[_ngcontent-%COMP%]{width:99%}.statusHuffaz[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.75rem}.date[_ngcontent-%COMP%]{color:#333;font-size:1.37rem;font-weight:700}.to_date[_ngcontent-%COMP%]{color:#ea5455;font-size:.875rem;font-weight:700}.progAr[_ngcontent-%COMP%]{color:#333;font-size:.875rem}.caed_desc[_ngcontent-%COMP%]{overflow-y:auto;height:10vh}.caed_desc[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{color:#333;font-size:.75rem}.dimm[_ngcontent-%COMP%]{opacity:.7;cursor:no-drop;pointer-events:none}.btn[_ngcontent-%COMP%]{width:50%}.call-integ[_ngcontent-%COMP%]{display:flex;justify-content:center;background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem!important;height:85vh;margin-top:1rem}.cus_info[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:.5rem;margin-top:1rem}\"]\n    });\n  }\n  return FreeRecitationCallComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}