{"ast": null, "code": "import { formatDate } from '@angular/common';\nimport { ChatMessageTypeEnum } from 'src/app/core/enums/chat-message-type-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { Guid } from 'src/app/core/ng-model/generate-guid';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i5 from \"src/app/core/services/push-notification-services/push-notification.service\";\nimport * as i6 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i7 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"ng2-pdf-viewer\";\nconst _c0 = [\"inputBox\"];\nconst _c1 = a0 => ({\n  \"current_user_cardRecour\": a0\n});\nfunction ChatDetailsComponent_ng_container_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.id) === message_r1.sender_id));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", message_r1.message, \" \");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_5_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h4\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"a\", 24);\n    i0.ɵɵelementContainerStart(5);\n    i0.ɵɵelement(6, \"img\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_5_ng_template_6_Template_button_click_8_listener() {\n      const modal_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      return i0.ɵɵresetView(modal_r6.close(\"Close click\"));\n    });\n    i0.ɵɵtext(9, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", message_r1.message, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", message_r1.message, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_5_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const content_r4 = i0.ɵɵreference(7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openVerticallyCentered(content_r4));\n    });\n    i0.ɵɵelement(3, \"img\", 19);\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatDetailsComponent_ng_container_7_div_5_ng_template_6_Template, 10, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"src\", message_r1.message, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", message_r1 == null ? null : message_r1.file_name, \"\");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_6_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h4\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"video\", 31);\n    i0.ɵɵelement(5, \"source\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_6_ng_template_6_Template_button_click_7_listener() {\n      const modal_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      return i0.ɵɵresetView(modal_r10.close(\"Close click\"));\n    });\n    i0.ɵɵtext(8, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", message_r1 == null ? null : message_r1.message, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_6_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const content_r8 = i0.ɵɵreference(7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openVerticallyCentered(content_r8));\n    });\n    i0.ɵɵelement(3, \"img\", 30);\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatDetailsComponent_ng_container_7_div_6_ng_template_6_Template, 9, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.book, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", message_r1 == null ? null : message_r1.file_name, \" \");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_7_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h4\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"audio\", 34);\n    i0.ɵɵelement(5, \"source\", 35)(6, \"source\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_7_ng_template_6_Template_button_click_8_listener() {\n      const modal_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      return i0.ɵɵresetView(modal_r14.close(\"Close click\"));\n    });\n    i0.ɵɵtext(9, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", message_r1 == null ? null : message_r1.message, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", message_r1 == null ? null : message_r1.message, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_7_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const content_r12 = i0.ɵɵreference(7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openVerticallyCentered(content_r12));\n    });\n    i0.ɵɵelement(3, \"img\", 30);\n    i0.ɵɵelementStart(4, \"p\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatDetailsComponent_ng_container_7_div_7_ng_template_6_Template, 10, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.book, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r1 == null ? null : message_r1.file_name);\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", message_r1.message, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 24);\n    i0.ɵɵtemplate(1, ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_4_ng_container_1_Template, 2, 1, \"ng-container\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"href\", message_r1.message, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message.split(\".\").pop() !== \"pdf\");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"pdf-viewer\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", message_r1.message)(\"render-text\", true)(\"original-size\", false)(\"fit-to-page\", true)(\"show-all\", true)(\"zoom-scale\", \"page-width\");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 24);\n    i0.ɵɵtemplate(1, ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_5_ng_container_1_Template, 2, 6, \"ng-container\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"href\", message_r1.message, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message.split(\".\").pop() === \"pdf\");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h4\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_4_Template, 2, 2, \"a\", 38)(5, ChatDetailsComponent_ng_container_7_div_8_ng_template_7_a_5_Template, 2, 2, \"a\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_8_ng_template_7_Template_button_click_7_listener() {\n      const modal_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      return i0.ɵɵresetView(modal_r18.close(\"Close click\"));\n    });\n    i0.ɵɵtext(8, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(message_r1 == null ? null : message_r1.file_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r1.message.split(\".\").pop() !== \"pdf\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.file_name.split(\".\").pop() === \"pdf\");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 28);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_ng_container_7_div_8_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const content_r16 = i0.ɵɵreference(8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openVerticallyCentered(content_r16));\n    });\n    i0.ɵɵelement(4, \"img\", 30);\n    i0.ɵɵelementStart(5, \"p\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, ChatDetailsComponent_ng_container_7_div_8_ng_template_7_Template, 9, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.book, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", message_r1.file_name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", message_r1 == null ? null : message_r1.file_name, \" \");\n  }\n}\nfunction ChatDetailsComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10)(2, \"p\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ChatDetailsComponent_ng_container_7_div_4_Template, 2, 4, \"div\", 12)(5, ChatDetailsComponent_ng_container_7_div_5_Template, 8, 3, \"div\", 13)(6, ChatDetailsComponent_ng_container_7_div_6_Template, 8, 3, \"div\", 14)(7, ChatDetailsComponent_ng_container_7_div_7_Template, 8, 3, \"div\", 14)(8, ChatDetailsComponent_ng_container_7_div_8_Template, 9, 3, \"div\", 13);\n    i0.ɵɵelementStart(9, \"div\", 15);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.id) === message_r1.sender_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r1.sender_name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message_type === ctx_r1.chatMessageTypeEnum.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message_type === ctx_r1.chatMessageTypeEnum.image);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message_type === ctx_r1.chatMessageTypeEnum.video);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message_type === ctx_r1.chatMessageTypeEnum.audio);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r1.message_type === ctx_r1.chatMessageTypeEnum.file);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r1.date, \" \");\n  }\n}\nfunction ChatDetailsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 43)(3, \"div\", 44)(4, \"div\", 45)(5, \"label\", 46);\n    i0.ɵɵelement(6, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 48);\n    i0.ɵɵlistener(\"change\", function ChatDetailsComponent_div_8_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event.target.files));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"input\", 49, 1);\n    i0.ɵɵlistener(\"keypress\", function ChatDetailsComponent_div_8_Template_input_keypress_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addMessageToGroup(ctx_r1.messageChat, ctx_r1.messageType, $event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ChatDetailsComponent_div_8_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.messageChat, $event) || (ctx_r1.messageChat = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ChatDetailsComponent_div_8_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addMessageToGroup(ctx_r1.messageChat, ctx_r1.messageType, \"Enter\"));\n    });\n    i0.ɵɵelement(11, \"img\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"mat-checkbox\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ChatDetailsComponent_div_8_Template_mat_checkbox_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.sendNotification, $event) || (ctx_r1.sendNotification = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.messageChat);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.send, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.sendNotification);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(14, 4, \"CHAT_GROUP.SEND_GROUP_CHAT_MESSAGE\"), \" \");\n  }\n}\nexport let ChatDetailsComponent = /*#__PURE__*/(() => {\n  class ChatDetailsComponent {\n    translate;\n    chatService;\n    modalService;\n    attachmentService;\n    pushNotificationService;\n    alert;\n    imagesPathesService;\n    langEnum = LanguageEnum;\n    listOfMessagess = [];\n    addMessageToChatGroup;\n    resultMessage = {};\n    currentUser;\n    messageChat;\n    messageType = ChatMessageTypeEnum.text;\n    messageAttachmentURL;\n    fileUploadModel = [];\n    groupData;\n    chatMessageTypeEnum = ChatMessageTypeEnum;\n    sendNotification = false;\n    sendChatGroupNotificationMessageRequestModel;\n    constructor(translate, chatService, modalService, attachmentService, pushNotificationService, alert, imagesPathesService) {\n      this.translate = translate;\n      this.chatService = chatService;\n      this.modalService = modalService;\n      this.attachmentService = attachmentService;\n      this.pushNotificationService = pushNotificationService;\n      this.alert = alert;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.getGroupMessages();\n    }\n    elementfocus;\n    setFocus() {\n      this.elementfocus.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n      this.elementfocus.nativeElement.focus();\n    }\n    addMessageToGroup(message, messageType, event) {\n      if (event?.key === \"Enter\" || event === \"Enter\") {\n        this.messageChat = '';\n        if (message && message.length > 0) {\n          var last_date = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en');\n          if (this.messageType === ChatMessageTypeEnum.text) {\n            this.addMessageToChatGroup = {\n              date: last_date,\n              sender_id: this.currentUser?.id,\n              sender_name: this.translate.currentLang === LanguageEnum.ar ? this.currentUser?.fullNameAr : this.currentUser?.fullNameEn,\n              sender_name_ar: this.currentUser?.fullNameAr,\n              sender_name_en: this.currentUser?.fullNameEn,\n              message: message,\n              message_type: messageType,\n              file_name: ''\n            };\n          } else {\n            this.addMessageToChatGroup = {\n              date: last_date,\n              sender_id: this.currentUser?.id,\n              sender_name: this.translate.currentLang === LanguageEnum.ar ? this.currentUser?.fullNameEn : this.currentUser?.fullNameEn,\n              sender_name_ar: this.currentUser?.fullNameAr,\n              sender_name_en: this.currentUser?.fullNameEn,\n              message: this.messageAttachmentURL == null ? '' : this.messageAttachmentURL,\n              file_name: message,\n              message_type: messageType\n            };\n          }\n          if (this.groupData?.last_message || this.groupData?.last_message == \"\") {\n            this.groupData.last_date = last_date;\n            this.groupData.last_message = this.addMessageToChatGroup.message;\n          }\n          if (this.groupData?.messages != null) {\n            this.chatService.pushLatestMessageToGroups(this.groupData, this.addMessageToChatGroup);\n            // this.chatService.pushNewMessageToMessages(this.groupData , this.addMessageToChatGroup);\n            this.listOfMessagess.push(this.addMessageToChatGroup);\n            this.messageAttachmentURL = '';\n            this.messageType = ChatMessageTypeEnum.text;\n            // this.groupViewComponent?.ngOnInit();   \n          } else {\n            //if chat group messages not created ,,, will create it and push first message on it\n            var Value = this.addMessageToChatGroup;\n            var map = [];\n            map[Guid.newGuid()] = Value;\n            this.groupData = {\n              key: this.groupData?.key,\n              group_name: this.groupData?.group_name,\n              allowed: this.groupData?.allowed,\n              last_date: last_date,\n              last_message: message,\n              messages: this.groupData?.messages == null ? map : this.groupData?.messages,\n              participants: this.groupData?.participants\n            };\n            this.chatService.pushLatestMessageToGroups(this.groupData, this.addMessageToChatGroup);\n            this;\n            this.messageAttachmentURL = '';\n            this.messageType = ChatMessageTypeEnum.text;\n          }\n          if (this.sendNotification) {\n            this.sendChatGroupNotification();\n          }\n          this.getGroupMessages();\n        }\n      }\n    }\n    sendChatGroupNotification() {\n      let model = {\n        grpMsg: this.addMessageToChatGroup?.message,\n        grpNm: this.groupData?.group_name,\n        ids: this.groupData?.participants?.map(({\n          id\n        }) => id)\n      };\n      if (model.ids.length <= 0) {\n        this.alert.error(this.translate.instant('CHAT_GROUP.SEND_CHAT_GROUP_NOTIFICATION_IDS_NOT_FOUND'));\n        this.sendNotification = false;\n        return;\n      }\n      this.pushNotificationService.sendChatGroupNotificationMessage(model).subscribe(res => {\n        if (res.isSuccess) {\n          this.alert.success(res.message || '');\n        } else {\n          this.alert.error(res.message || '');\n        }\n        this.sendNotification = false;\n      }, error => {\n        this.sendNotification = false;\n      });\n    }\n    getGroupMessages() {\n      if (this.groupData) {\n        this.chatService.getGroupMessages(this.groupData);\n        this.listOfMessagess = this.chatService.allMessagesList || [];\n      }\n    }\n    onFileChange(files) {\n      this.fileUploadModel = [];\n      if (files.length > 0) {\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 6,\n            file: element\n          };\n          this.fileUploadModel.push(fileUploadObj);\n          this.messageChat = element.name;\n        });\n        this.UploadFiles(this.fileUploadModel);\n      }\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        this.messageAttachmentURL = res.data[0].url || '';\n        var ValueType = res.data[0].contentType;\n        // var isImage = ValueType.includes('image/');\n        if (ValueType.includes('image/')) {\n          this.messageType = ChatMessageTypeEnum.image;\n          this.setFocus();\n        } else if (ValueType.includes('video/')) {\n          this.messageType = ChatMessageTypeEnum.video;\n          this.setFocus();\n        } else if (ValueType.includes('audio/')) {\n          this.messageType = ChatMessageTypeEnum.audio;\n          this.setFocus();\n        } else {\n          this.messageType = ChatMessageTypeEnum.file;\n          this.setFocus();\n        }\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    openVerticallyCentered(content) {\n      this.modalService.open(content, {\n        size: 'lg'\n      });\n    }\n    onChange(event) {\n      this.onFileChange(event.target.files);\n    }\n    static ɵfac = function ChatDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ChatDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ChatService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AttachmentsService), i0.ɵɵdirectiveInject(i5.PushNotificationService), i0.ɵɵdirectiveInject(i6.AlertifyService), i0.ɵɵdirectiveInject(i7.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatDetailsComponent,\n      selectors: [[\"app-chat-details\"]],\n      viewQuery: function ChatDetailsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementfocus = _t.first);\n        }\n      },\n      inputs: {\n        groupData: \"groupData\"\n      },\n      decls: 9,\n      vars: 4,\n      consts: [[\"content\", \"\"], [\"inputBox\", \"\"], [1, \"details_day\"], [1, \"head_groupName\", \"d-flex\", \"align-items-center\"], [1, \"userGroupIcon\", 3, \"src\"], [1, \"mx-2\"], [1, \"row\", \"mx-0\", \"chat_details\"], [1, \"chat_vontainer\", \"w-100\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-group\", 4, \"ngIf\"], [1, \"col-12\", \"cardRecour\", 3, \"ngClass\"], [1, \"message_name\", \"px-0\"], [\"class\", \" mb-2 message_message \", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \" mb-2 message_message\", 4, \"ngIf\"], [\"class\", \"row mx-0\", 4, \"ngIf\"], [1, \"message_date\"], [1, \"mb-2\", \"message_message\", 3, \"ngClass\"], [1, \"mb-2\", \"message_message\"], [1, \"col-4\", \"iamgeMessage\", 3, \"click\"], [1, \"iamgeMessage\", 3, \"src\"], [1, \"ellipsis\", \"mb-0\", \"ml-2\", \"mr-2\", 3, \"matTooltip\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"modal-body\"], [3, \"href\"], [1, \"size_img_model\", 3, \"src\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [1, \"row\", \"mx-0\"], [1, \"col-4\", \"cardRecourd\", 3, \"click\"], [3, \"src\"], [\"width\", \"450px\", \"height\", \"400px\", \"controls\", \"\", \"autoplay\", \"\"], [\"type\", \"video/mp4\", 3, \"src\"], [1, \"ellipsis\", \"mb-0\", 3, \"matTooltip\"], [\"controls\", \"\", \"autoplay\", \"\", 2, \"width\", \"100% !important\"], [\"type\", \"audio/ogg\", 3, \"src\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [1, \"col-4\", \"cardRecourd\", \"mb-2\", 3, \"click\"], [3, \"href\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"src\", \"render-text\", \"original-size\", \"fit-to-page\", \"show-all\", \"zoom-scale\"], [1, \"form-group\"], [1, \"row\"], [1, \"form-group\", \"col-lg-12\", \"mx-auto\", \"mb-0\", \"mt-4\", \"d-flex\", \"justify-content-between\"], [1, \"input-group\", \"mb-3\"], [1, \"input-group-prepend\"], [\"for\", \"upload-photo\", 1, \"input-group-text\", \"bg-white\", \"pl-2\", \"pr-2\", \"border-md\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-upload\"], [\"type\", \"file\", \"autofocus\", \"\", \"name\", \"photo\", \"id\", \"upload-photo\", 1, \"input-group-text\", \"bg-white\", 3, \"change\"], [1, \"px-3\", \"form-control\", \"UserLogin__FormControl\", \"bg-white\", \"border-md\", \"input-sm\", 3, \"keypress\", \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", 1, \"btn\", \"UserLogin__Submit\", \"w-10\", \"mx-2\", 3, \"click\"], [1, \"sendIcon\", 3, \"src\"], [1, \"example-margin\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function ChatDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n          i0.ɵɵelement(2, \"img\", 4);\n          i0.ɵɵelementStart(3, \"P\", 5);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"div\", 7);\n          i0.ɵɵtemplate(7, ChatDetailsComponent_ng_container_7_Template, 11, 10, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, ChatDetailsComponent_div_8_Template, 15, 6, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.user_group, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.groupData == null ? null : ctx.groupData.group_name, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.chatService.allMessagesList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listOfMessagess);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i10.PdfViewerComponent, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.list-group_program-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;margin-top:1rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:57vh;overflow-y:auto;padding-left:.5rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:57vh;overflow-y:auto;padding-right:.5rem}.list-group_program-list[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:pointer}.list-group_program-list[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.list-group_program-list[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-family:Almarai!important;font-weight:700;font-size:1rem;justify-content:space-between}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-copy[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-copy[_ngcontent-%COMP%]{color:#fff}@media (min-width: 37.5rem) and (max-width: 63.938rem){.list-group_program-list[_ngcontent-%COMP%]{padding:.9rem}}.tab_page[_ngcontent-%COMP%]{height:79vh;display:flex;justify-content:space-between;padding-bottom:1rem;background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 .188rem 1rem #fbfbfb;border-radius:1.188rem;height:74vh;overflow-y:auto;overflow-x:hidden;margin-left:.5rem;margin-right:.5rem;margin-top:2rem;padding:1rem .5rem}.tab_page[_ngcontent-%COMP%]   .list_exams[_ngcontent-%COMP%]   .name_prog[_ngcontent-%COMP%]{font-size:.875rem;color:gray}.tab_page[_ngcontent-%COMP%]   .part1[_ngcontent-%COMP%]{flex:0 0 30.333333%;max-width:30.333333%}.tab_page[_ngcontent-%COMP%]   .part2[_ngcontent-%COMP%]{flex:0 0 64.666667%;max-width:64.666667%}.tab_page[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;color:#333}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]{background:transparent;border-radius:0;padding:0;height:auto;margin-top:0}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color)}.tab_page[_ngcontent-%COMP%]   .list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-size:1.12rem}.list_exams[_ngcontent-%COMP%]{overflow-x:hidden!important;height:75vh!important}.tab_page[_ngcontent-%COMP%]{height:80vh}.tab_page-2[_ngcontent-%COMP%]{height:80vh;background:#fbfbfb;border-radius:0 0 .75rem .75rem}p.name[_ngcontent-%COMP%]{font-weight:700;font-size:1.125rem;letter-spacing:0;color:#333;opacity:1}.btn-container[_ngcontent-%COMP%]:lang(en){margin-right:2.3rem}.btn-container[_ngcontent-%COMP%]:lang(ar){margin-left:2.3rem}.back-btn[_ngcontent-%COMP%]{color:var(--second_color);background:#b3b3b3;border:.125rem solid var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--second_color)}.back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.internal_scroll_list_group.order[_ngcontent-%COMP%]{height:25.9375rem!important}.show-exam[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;text-decoration:none;box-shadow:0 .125rem .188rem #f2f1f1de;color:var(--main_color);height:10%;background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;margin-bottom:1rem;opacity:1;margin-top:1.4rem .1rem;font-weight:700;font-size:1rem;justify-content:space-between;padding:.4rem}.show-exam[_ngcontent-%COMP%]   .show-space[_ngcontent-%COMP%]{padding:.5rem 0rem}.show-exam[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:lang(en){transform:rotate(180deg)}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{min-width:3rem}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(ar){float:left}.show-exam[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:lang(en){float:right}.cardRecourd[_ngcontent-%COMP%]{margin:1rem;justify-content:space-between;align-items:center;display:flex;padding:.5rem 1rem;background-color:#fff;border-radius:.438rem;border:.063rem solid rgba(242,241,241,.8705882353);box-shadow:0 .125rem .188rem #f2f1f1de}.redPoint[_ngcontent-%COMP%]{top:16.813rem;left:32.813rem;width:1.25rem;height:1.25rem;background:#ea5455 0% 0% no-repeat padding-box;opacity:1;border-radius:50%}.details_day[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem 1.7rem .5rem;margin-top:1rem;height:75vh}.details_day[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]{color:var(--main_color);font-size:.93rem;font-weight:700;border-bottom:.063rem solid #d6d7d8;padding-bottom:.5rem}.details_day[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.details_day[_ngcontent-%COMP%]   .chat_details[_ngcontent-%COMP%]{height:48vh;overflow-y:auto;overflow-x:hidden;padding-left:.5rem}.details_day[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.563rem}.details_day[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]:lang(ar){text-align:left}.details_day[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]:lang(en){text-align:right}.details_day[_ngcontent-%COMP%]   .cardRecour[_ngcontent-%COMP%]{padding:.5rem 1rem;background-color:#fbfbfb;border-radius:1rem 1rem 1rem 0;margin:.5rem .2rem .2rem;max-width:75%}.details_day[_ngcontent-%COMP%]   .message_message[_ngcontent-%COMP%]{color:#333;font-size:.8rem}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]{padding:.5rem 1rem;background-color:var(--main_color);border-radius:1rem 1rem 1rem 0;margin:.2rem}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]:lang(ar){margin-right:auto!important;text-align:right}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]:lang(en){margin-left:auto!important;text-align:left}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]   .message_message[_ngcontent-%COMP%]{color:#fff;font-size:.8rem}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]{color:#fff;font-size:.5625rem}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]:lang(ar){text-align:left}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]:lang(en){text-align:right}.details_day[_ngcontent-%COMP%]   .current_user_cardRecour[_ngcontent-%COMP%]   .message_name[_ngcontent-%COMP%]{color:#fff;font-size:1rem}.details_day[_ngcontent-%COMP%]   .w-10[_ngcontent-%COMP%]{width:10%}.details_day[_ngcontent-%COMP%]   .sendIcon[_ngcontent-%COMP%]{width:1.312rem;height:1.5rem}.details_day[_ngcontent-%COMP%]   .message_name[_ngcontent-%COMP%]{font-size:1rem}.details_day[_ngcontent-%COMP%]   .userGroupIcon[_ngcontent-%COMP%]{width:4rem;height:4rem;background-color:var(--second_color);padding:.2rem;border-radius:.438rem}.details_day[_ngcontent-%COMP%]:lang(en){text-align:left}.details_day[_ngcontent-%COMP%]:lang(ar){text-align:right}.details_day[_ngcontent-%COMP%]   .fa-upload[_ngcontent-%COMP%]{color:#666}.details_day[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{cursor:pointer}.details_day[_ngcontent-%COMP%]   #upload-photo[_ngcontent-%COMP%]{opacity:0;position:absolute;z-index:-1}.details_day[_ngcontent-%COMP%]   .iamgeMessage[_ngcontent-%COMP%]{width:12.5rem;height:7.5rem}@media (max-width: 64rem){.details_day[_ngcontent-%COMP%]   .w-10[_ngcontent-%COMP%]{width:15%}.details_day[_ngcontent-%COMP%]   .chat_details[_ngcontent-%COMP%]{height:58vh!important}.details_day[_ngcontent-%COMP%]   .details_day[_ngcontent-%COMP%]   .iamgeMessage[_ngcontent-%COMP%]{width:6.5rem;height:4.5rem}.chat_details[_ngcontent-%COMP%]{height:58vh}}\"]\n    });\n  }\n  return ChatDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}