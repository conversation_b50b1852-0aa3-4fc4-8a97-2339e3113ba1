{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Symbol factory\nimport { each, isArray, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from './graphic.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { parsePercent } from './number.js';\n/**\r\n * Triangle shape\r\n * @inner\r\n */\nvar Triangle = graphic.Path.extend({\n  type: 'triangle',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy + height);\n    path.lineTo(cx - width, cy + height);\n    path.closePath();\n  }\n});\n/**\r\n * Diamond shape\r\n * @inner\r\n */\nvar Diamond = graphic.Path.extend({\n  type: 'diamond',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy);\n    path.lineTo(cx, cy + height);\n    path.lineTo(cx - width, cy);\n    path.closePath();\n  }\n});\n/**\r\n * Pin shape\r\n * @inner\r\n */\nvar Pin = graphic.Path.extend({\n  type: 'pin',\n  shape: {\n    // x, y on the cusp\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var w = shape.width / 5 * 3;\n    // Height must be larger than width\n    var h = Math.max(w, shape.height);\n    var r = w / 2;\n    // Dist on y with tangent point and circle center\n    var dy = r * r / (h - r);\n    var cy = y - h + r + dy;\n    var angle = Math.asin(dy / r);\n    // Dist on x with tangent point and circle center\n    var dx = Math.cos(angle) * r;\n    var tanX = Math.sin(angle);\n    var tanY = Math.cos(angle);\n    var cpLen = r * 0.6;\n    var cpLen2 = r * 0.7;\n    path.moveTo(x - dx, cy + dy);\n    path.arc(x, cy, r, Math.PI - angle, Math.PI * 2 + angle);\n    path.bezierCurveTo(x + dx - tanX * cpLen, cy + dy + tanY * cpLen, x, y - cpLen2, x, y);\n    path.bezierCurveTo(x, y - cpLen2, x - dx + tanX * cpLen, cy + dy + tanY * cpLen, x - dx, cy + dy);\n    path.closePath();\n  }\n});\n/**\r\n * Arrow shape\r\n * @inner\r\n */\nvar Arrow = graphic.Path.extend({\n  type: 'arrow',\n  shape: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (ctx, shape) {\n    var height = shape.height;\n    var width = shape.width;\n    var x = shape.x;\n    var y = shape.y;\n    var dx = width / 3 * 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + dx, y + height);\n    ctx.lineTo(x, y + height / 4 * 3);\n    ctx.lineTo(x - dx, y + height);\n    ctx.lineTo(x, y);\n    ctx.closePath();\n  }\n});\n/**\r\n * Map of path constructors\r\n */\n// TODO Use function to build symbol path.\nvar symbolCtors = {\n  line: graphic.Line,\n  rect: graphic.Rect,\n  roundRect: graphic.Rect,\n  square: graphic.Rect,\n  circle: graphic.Circle,\n  diamond: Diamond,\n  pin: Pin,\n  arrow: Arrow,\n  triangle: Triangle\n};\nvar symbolShapeMakers = {\n  line: function (x, y, w, h, shape) {\n    shape.x1 = x;\n    shape.y1 = y + h / 2;\n    shape.x2 = x + w;\n    shape.y2 = y + h / 2;\n  },\n  rect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n  },\n  roundRect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n    shape.r = Math.min(w, h) / 4;\n  },\n  square: function (x, y, w, h, shape) {\n    var size = Math.min(w, h);\n    shape.x = x;\n    shape.y = y;\n    shape.width = size;\n    shape.height = size;\n  },\n  circle: function (x, y, w, h, shape) {\n    // Put circle in the center of square\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.r = Math.min(w, h) / 2;\n  },\n  diamond: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  pin: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  arrow: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  triangle: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  }\n};\nexport var symbolBuildProxies = {};\neach(symbolCtors, function (Ctor, name) {\n  symbolBuildProxies[name] = new Ctor();\n});\nvar SymbolClz = graphic.Path.extend({\n  type: 'symbol',\n  shape: {\n    symbolType: '',\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  calculateTextPosition: function (out, config, rect) {\n    var res = calculateTextPosition(out, config, rect);\n    var shape = this.shape;\n    if (shape && shape.symbolType === 'pin' && config.position === 'inside') {\n      res.y = rect.y + rect.height * 0.4;\n    }\n    return res;\n  },\n  buildPath: function (ctx, shape, inBundle) {\n    var symbolType = shape.symbolType;\n    if (symbolType !== 'none') {\n      var proxySymbol = symbolBuildProxies[symbolType];\n      if (!proxySymbol) {\n        // Default rect\n        symbolType = 'rect';\n        proxySymbol = symbolBuildProxies[symbolType];\n      }\n      symbolShapeMakers[symbolType](shape.x, shape.y, shape.width, shape.height, proxySymbol.shape);\n      proxySymbol.buildPath(ctx, proxySymbol.shape, inBundle);\n    }\n  }\n});\n// Provide setColor helper method to avoid determine if set the fill or stroke outside\nfunction symbolPathSetColor(color, innerColor) {\n  if (this.type !== 'image') {\n    var symbolStyle = this.style;\n    if (this.__isEmptyBrush) {\n      symbolStyle.stroke = color;\n      symbolStyle.fill = innerColor || '#fff';\n      // TODO Same width with lineStyle in LineView\n      symbolStyle.lineWidth = 2;\n    } else if (this.shape.symbolType === 'line') {\n      symbolStyle.stroke = color;\n    } else {\n      symbolStyle.fill = color;\n    }\n    this.markRedraw();\n  }\n}\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n */\nexport function createSymbol(symbolType, x, y, w, h, color,\n// whether to keep the ratio of w/h,\nkeepAspect) {\n  // TODO Support image object, DynamicImage.\n  var isEmpty = symbolType.indexOf('empty') === 0;\n  if (isEmpty) {\n    symbolType = symbolType.substr(5, 1).toLowerCase() + symbolType.substr(6);\n  }\n  var symbolPath;\n  if (symbolType.indexOf('image://') === 0) {\n    symbolPath = graphic.makeImage(symbolType.slice(8), new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else if (symbolType.indexOf('path://') === 0) {\n    symbolPath = graphic.makePath(symbolType.slice(7), {}, new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else {\n    symbolPath = new SymbolClz({\n      shape: {\n        symbolType: symbolType,\n        x: x,\n        y: y,\n        width: w,\n        height: h\n      }\n    });\n  }\n  symbolPath.__isEmptyBrush = isEmpty;\n  // TODO Should deprecate setColor\n  symbolPath.setColor = symbolPathSetColor;\n  if (color) {\n    symbolPath.setColor(color);\n  }\n  return symbolPath;\n}\nexport function normalizeSymbolSize(symbolSize) {\n  if (!isArray(symbolSize)) {\n    symbolSize = [+symbolSize, +symbolSize];\n  }\n  return [symbolSize[0] || 0, symbolSize[1] || 0];\n}\nexport function normalizeSymbolOffset(symbolOffset, symbolSize) {\n  if (symbolOffset == null) {\n    return;\n  }\n  if (!isArray(symbolOffset)) {\n    symbolOffset = [symbolOffset, symbolOffset];\n  }\n  return [parsePercent(symbolOffset[0], symbolSize[0]) || 0, parsePercent(retrieve2(symbolOffset[1], symbolOffset[0]), symbolSize[1]) || 0];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}