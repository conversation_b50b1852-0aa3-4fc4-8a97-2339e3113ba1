{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport { reformIntervals } from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PiecewiseModel = /** @class */function (_super) {\n  __extends(PiecewiseModel, _super);\n  function PiecewiseModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseModel.type;\n    /**\r\n     * The order is always [low, ..., high].\r\n     * [{text: string, interval: Array.<number>}, ...]\r\n     */\n    _this._pieceList = [];\n    return _this;\n  }\n  PiecewiseModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    var mode = this._mode = this._determineMode();\n    this._pieceList = [];\n    resetMethods[this._mode].call(this, this._pieceList);\n    this._resetSelected(newOption, isInit);\n    var categories = this.option.categories;\n    this.resetVisual(function (mappingOption, state) {\n      if (mode === 'categories') {\n        mappingOption.mappingMethod = 'category';\n        mappingOption.categories = zrUtil.clone(categories);\n      } else {\n        mappingOption.dataExtent = this.getExtent();\n        mappingOption.mappingMethod = 'piecewise';\n        mappingOption.pieceList = zrUtil.map(this._pieceList, function (piece) {\n          piece = zrUtil.clone(piece);\n          if (state !== 'inRange') {\n            // FIXME\n            // outOfRange do not support special visual in pieces.\n            piece.visual = null;\n          }\n          return piece;\n        });\n      }\n    });\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.completeVisualOption = function () {\n    // Consider this case:\n    // visualMap: {\n    //      pieces: [{symbol: 'circle', lt: 0}, {symbol: 'rect', gte: 0}]\n    // }\n    // where no inRange/outOfRange set but only pieces. So we should make\n    // default inRange/outOfRange for this case, otherwise visuals that only\n    // appear in `pieces` will not be taken into account in visual encoding.\n    var option = this.option;\n    var visualTypesInPieces = {};\n    var visualTypes = VisualMapping.listVisualTypes();\n    var isCategory = this.isCategory();\n    zrUtil.each(option.pieces, function (piece) {\n      zrUtil.each(visualTypes, function (visualType) {\n        if (piece.hasOwnProperty(visualType)) {\n          visualTypesInPieces[visualType] = 1;\n        }\n      });\n    });\n    zrUtil.each(visualTypesInPieces, function (v, visualType) {\n      var exists = false;\n      zrUtil.each(this.stateList, function (state) {\n        exists = exists || has(option, state, visualType) || has(option.target, state, visualType);\n      }, this);\n      !exists && zrUtil.each(this.stateList, function (state) {\n        (option[state] || (option[state] = {}))[visualType] = visualDefault.get(visualType, state === 'inRange' ? 'active' : 'inactive', isCategory);\n      });\n    }, this);\n    function has(obj, state, visualType) {\n      return obj && obj[state] && obj[state].hasOwnProperty(visualType);\n    }\n    _super.prototype.completeVisualOption.apply(this, arguments);\n  };\n  PiecewiseModel.prototype._resetSelected = function (newOption, isInit) {\n    var thisOption = this.option;\n    var pieceList = this._pieceList;\n    // Selected do not merge but all override.\n    var selected = (isInit ? thisOption : newOption).selected || {};\n    thisOption.selected = selected;\n    // Consider 'not specified' means true.\n    zrUtil.each(pieceList, function (piece, index) {\n      var key = this.getSelectedMapKey(piece);\n      if (!selected.hasOwnProperty(key)) {\n        selected[key] = true;\n      }\n    }, this);\n    if (thisOption.selectedMode === 'single') {\n      // Ensure there is only one selected.\n      var hasSel_1 = false;\n      zrUtil.each(pieceList, function (piece, index) {\n        var key = this.getSelectedMapKey(piece);\n        if (selected[key]) {\n          hasSel_1 ? selected[key] = false : hasSel_1 = true;\n        }\n      }, this);\n    }\n    // thisOption.selectedMode === 'multiple', default: all selected.\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getItemSymbol = function () {\n    return this.get('itemSymbol');\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getSelectedMapKey = function (piece) {\n    return this._mode === 'categories' ? piece.value + '' : piece.index + '';\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getPieceList = function () {\n    return this._pieceList;\n  };\n  /**\r\n   * @return {string}\r\n   */\n  PiecewiseModel.prototype._determineMode = function () {\n    var option = this.option;\n    return option.pieces && option.pieces.length > 0 ? 'pieces' : this.option.categories ? 'categories' : 'splitNumber';\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.setSelected = function (selected) {\n    this.option.selected = zrUtil.clone(selected);\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.getValueState = function (value) {\n    var index = VisualMapping.findPieceIndex(value, this._pieceList);\n    return index != null ? this.option.selected[this.getSelectedMapKey(this._pieceList[index])] ? 'inRange' : 'outOfRange' : 'outOfRange';\n  };\n  /**\r\n   * @public\r\n   * @param pieceIndex piece index in visualMapModel.getPieceList()\r\n   */\n  PiecewiseModel.prototype.findTargetDataIndices = function (pieceIndex) {\n    var result = [];\n    var pieceList = this._pieceList;\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        // Should always base on model pieceList, because it is order sensitive.\n        var pIdx = VisualMapping.findPieceIndex(value, pieceList);\n        pIdx === pieceIndex && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @private\r\n   * @param piece piece.value or piece.interval is required.\r\n   * @return  Can be Infinity or -Infinity\r\n   */\n  PiecewiseModel.prototype.getRepresentValue = function (piece) {\n    var representValue;\n    if (this.isCategory()) {\n      representValue = piece.value;\n    } else {\n      if (piece.value != null) {\n        representValue = piece.value;\n      } else {\n        var pieceInterval = piece.interval || [];\n        representValue = pieceInterval[0] === -Infinity && pieceInterval[1] === Infinity ? 0 : (pieceInterval[0] + pieceInterval[1]) / 2;\n      }\n    }\n    return representValue;\n  };\n  PiecewiseModel.prototype.getVisualMeta = function (getColorVisual) {\n    // Do not support category. (category axis is ordinal, numerical)\n    if (this.isCategory()) {\n      return;\n    }\n    var stops = [];\n    var outerColors = ['', ''];\n    var visualMapModel = this;\n    function setStop(interval, valueState) {\n      var representValue = visualMapModel.getRepresentValue({\n        interval: interval\n      }); // Not category\n      if (!valueState) {\n        valueState = visualMapModel.getValueState(representValue);\n      }\n      var color = getColorVisual(representValue, valueState);\n      if (interval[0] === -Infinity) {\n        outerColors[0] = color;\n      } else if (interval[1] === Infinity) {\n        outerColors[1] = color;\n      } else {\n        stops.push({\n          value: interval[0],\n          color: color\n        }, {\n          value: interval[1],\n          color: color\n        });\n      }\n    }\n    // Suplement\n    var pieceList = this._pieceList.slice();\n    if (!pieceList.length) {\n      pieceList.push({\n        interval: [-Infinity, Infinity]\n      });\n    } else {\n      var edge = pieceList[0].interval[0];\n      edge !== -Infinity && pieceList.unshift({\n        interval: [-Infinity, edge]\n      });\n      edge = pieceList[pieceList.length - 1].interval[1];\n      edge !== Infinity && pieceList.push({\n        interval: [edge, Infinity]\n      });\n    }\n    var curr = -Infinity;\n    zrUtil.each(pieceList, function (piece) {\n      var interval = piece.interval;\n      if (interval) {\n        // Fulfill gap.\n        interval[0] > curr && setStop([curr, interval[0]], 'outOfRange');\n        setStop(interval.slice());\n        curr = interval[1];\n      }\n    }, this);\n    return {\n      stops: stops,\n      outerColors: outerColors\n    };\n  };\n  PiecewiseModel.type = 'visualMap.piecewise';\n  PiecewiseModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    selected: null,\n    minOpen: false,\n    maxOpen: false,\n    align: 'auto',\n    itemWidth: 20,\n    itemHeight: 14,\n    itemSymbol: 'roundRect',\n    pieces: null,\n    categories: null,\n    splitNumber: 5,\n    selectedMode: 'multiple',\n    itemGap: 10,\n    hoverLink: true // Enable hover highlight.\n  });\n  return PiecewiseModel;\n}(VisualMapModel);\n;\n/**\r\n * Key is this._mode\r\n * @type {Object}\r\n * @this {module:echarts/component/viusalMap/PiecewiseMode}\r\n */\nvar resetMethods = {\n  splitNumber: function (outPieceList) {\n    var thisOption = this.option;\n    var precision = Math.min(thisOption.precision, 20);\n    var dataExtent = this.getExtent();\n    var splitNumber = thisOption.splitNumber;\n    splitNumber = Math.max(parseInt(splitNumber, 10), 1);\n    thisOption.splitNumber = splitNumber;\n    var splitStep = (dataExtent[1] - dataExtent[0]) / splitNumber;\n    // Precision auto-adaption\n    while (+splitStep.toFixed(precision) !== splitStep && precision < 5) {\n      precision++;\n    }\n    thisOption.precision = precision;\n    splitStep = +splitStep.toFixed(precision);\n    if (thisOption.minOpen) {\n      outPieceList.push({\n        interval: [-Infinity, dataExtent[0]],\n        close: [0, 0]\n      });\n    }\n    for (var index = 0, curr = dataExtent[0]; index < splitNumber; curr += splitStep, index++) {\n      var max = index === splitNumber - 1 ? dataExtent[1] : curr + splitStep;\n      outPieceList.push({\n        interval: [curr, max],\n        close: [1, 1]\n      });\n    }\n    if (thisOption.maxOpen) {\n      outPieceList.push({\n        interval: [dataExtent[1], Infinity],\n        close: [0, 0]\n      });\n    }\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece, index) {\n      piece.index = index;\n      piece.text = this.formatValueText(piece.interval);\n    }, this);\n  },\n  categories: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.categories, function (cate) {\n      // FIXME category模式也使用pieceList，但在visualMapping中不是使用pieceList。\n      // 是否改一致。\n      outPieceList.push({\n        text: this.formatValueText(cate, true),\n        value: cate\n      });\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n  },\n  pieces: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.pieces, function (pieceListItem, index) {\n      if (!zrUtil.isObject(pieceListItem)) {\n        pieceListItem = {\n          value: pieceListItem\n        };\n      }\n      var item = {\n        text: '',\n        index: index\n      };\n      if (pieceListItem.label != null) {\n        item.text = pieceListItem.label;\n      }\n      if (pieceListItem.hasOwnProperty('value')) {\n        var value = item.value = pieceListItem.value;\n        item.interval = [value, value];\n        item.close = [1, 1];\n      } else {\n        // `min` `max` is legacy option.\n        // `lt` `gt` `lte` `gte` is recommended.\n        var interval = item.interval = [];\n        var close_1 = item.close = [0, 0];\n        var closeList = [1, 0, 1];\n        var infinityList = [-Infinity, Infinity];\n        var useMinMax = [];\n        for (var lg = 0; lg < 2; lg++) {\n          var names = [['gte', 'gt', 'min'], ['lte', 'lt', 'max']][lg];\n          for (var i = 0; i < 3 && interval[lg] == null; i++) {\n            interval[lg] = pieceListItem[names[i]];\n            close_1[lg] = closeList[i];\n            useMinMax[lg] = i === 2;\n          }\n          interval[lg] == null && (interval[lg] = infinityList[lg]);\n        }\n        useMinMax[0] && interval[1] === Infinity && (close_1[0] = 0);\n        useMinMax[1] && interval[0] === -Infinity && (close_1[1] = 0);\n        if (process.env.NODE_ENV !== 'production') {\n          if (interval[0] > interval[1]) {\n            console.warn('Piece ' + index + 'is illegal: ' + interval + ' lower bound should not greater then uppper bound.');\n          }\n        }\n        if (interval[0] === interval[1] && close_1[0] && close_1[1]) {\n          // Consider: [{min: 5, max: 5, visual: {...}}, {min: 0, max: 5}],\n          // we use value to lift the priority when min === max\n          item.value = interval[0];\n        }\n      }\n      item.visual = VisualMapping.retrieveVisuals(pieceListItem);\n      outPieceList.push(item);\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n    // Only pieces\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece) {\n      var close = piece.close;\n      var edgeSymbols = [['<', '≤'][close[1]], ['>', '≥'][close[0]]];\n      piece.text = piece.text || this.formatValueText(piece.value != null ? piece.value : piece.interval, false, edgeSymbols);\n    }, this);\n  }\n};\nfunction normalizeReverse(thisOption, pieceList) {\n  var inverse = thisOption.inverse;\n  if (thisOption.orient === 'vertical' ? !inverse : inverse) {\n    pieceList.reverse();\n  }\n}\nexport default PiecewiseModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}