{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { addEventListener, removeEventListener, normalizeEvent, getNativeEvent } from '../core/event.js';\nimport * as zrUtil from '../core/util.js';\nimport Eventful from '../core/Eventful.js';\nimport env from '../core/env.js';\nvar TOUCH_CLICK_DELAY = 300;\nvar globalEventSupported = env.domSupported;\nvar localNativeListenerNames = function () {\n  var mouseHandlerNames = ['click', 'dblclick', 'mousewheel', 'wheel', 'mouseout', 'mouseup', 'mousedown', 'mousemove', 'contextmenu'];\n  var touchHandlerNames = ['touchstart', 'touchend', 'touchmove'];\n  var pointerEventNameMap = {\n    pointerdown: 1,\n    pointerup: 1,\n    pointermove: 1,\n    pointerout: 1\n  };\n  var pointerHandlerNames = zrUtil.map(mouseHandlerNames, function (name) {\n    var nm = name.replace('mouse', 'pointer');\n    return pointerEventNameMap.hasOwnProperty(nm) ? nm : name;\n  });\n  return {\n    mouse: mouseHandlerNames,\n    touch: touchHandlerNames,\n    pointer: pointerHandlerNames\n  };\n}();\nvar globalNativeListenerNames = {\n  mouse: ['mousemove', 'mouseup'],\n  pointer: ['pointermove', 'pointerup']\n};\nvar wheelEventSupported = false;\nfunction isPointerFromTouch(event) {\n  var pointerType = event.pointerType;\n  return pointerType === 'pen' || pointerType === 'touch';\n}\nfunction setTouchTimer(scope) {\n  scope.touching = true;\n  if (scope.touchTimer != null) {\n    clearTimeout(scope.touchTimer);\n    scope.touchTimer = null;\n  }\n  scope.touchTimer = setTimeout(function () {\n    scope.touching = false;\n    scope.touchTimer = null;\n  }, 700);\n}\nfunction markTouch(event) {\n  event && (event.zrByTouch = true);\n}\nfunction normalizeGlobalEvent(instance, event) {\n  return normalizeEvent(instance.dom, new FakeGlobalEvent(instance, event), true);\n}\nfunction isLocalEl(instance, el) {\n  var elTmp = el;\n  var isLocal = false;\n  while (elTmp && elTmp.nodeType !== 9 && !(isLocal = elTmp.domBelongToZr || elTmp !== el && elTmp === instance.painterRoot)) {\n    elTmp = elTmp.parentNode;\n  }\n  return isLocal;\n}\nvar FakeGlobalEvent = function () {\n  function FakeGlobalEvent(instance, event) {\n    this.stopPropagation = zrUtil.noop;\n    this.stopImmediatePropagation = zrUtil.noop;\n    this.preventDefault = zrUtil.noop;\n    this.type = event.type;\n    this.target = this.currentTarget = instance.dom;\n    this.pointerType = event.pointerType;\n    this.clientX = event.clientX;\n    this.clientY = event.clientY;\n  }\n  return FakeGlobalEvent;\n}();\nvar localDOMHandlers = {\n  mousedown: function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.__mayPointerCapture = [event.zrX, event.zrY];\n    this.trigger('mousedown', event);\n  },\n  mousemove: function (event) {\n    event = normalizeEvent(this.dom, event);\n    var downPoint = this.__mayPointerCapture;\n    if (downPoint && (event.zrX !== downPoint[0] || event.zrY !== downPoint[1])) {\n      this.__togglePointerCapture(true);\n    }\n    this.trigger('mousemove', event);\n  },\n  mouseup: function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.__togglePointerCapture(false);\n    this.trigger('mouseup', event);\n  },\n  mouseout: function (event) {\n    event = normalizeEvent(this.dom, event);\n    var element = event.toElement || event.relatedTarget;\n    if (!isLocalEl(this, element)) {\n      if (this.__pointerCapturing) {\n        event.zrEventControl = 'no_globalout';\n      }\n      this.trigger('mouseout', event);\n    }\n  },\n  wheel: function (event) {\n    wheelEventSupported = true;\n    event = normalizeEvent(this.dom, event);\n    this.trigger('mousewheel', event);\n  },\n  mousewheel: function (event) {\n    if (wheelEventSupported) {\n      return;\n    }\n    event = normalizeEvent(this.dom, event);\n    this.trigger('mousewheel', event);\n  },\n  touchstart: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.__lastTouchMoment = new Date();\n    this.handler.processGesture(event, 'start');\n    localDOMHandlers.mousemove.call(this, event);\n    localDOMHandlers.mousedown.call(this, event);\n  },\n  touchmove: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.handler.processGesture(event, 'change');\n    localDOMHandlers.mousemove.call(this, event);\n  },\n  touchend: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.handler.processGesture(event, 'end');\n    localDOMHandlers.mouseup.call(this, event);\n    if (+new Date() - +this.__lastTouchMoment < TOUCH_CLICK_DELAY) {\n      localDOMHandlers.click.call(this, event);\n    }\n  },\n  pointerdown: function (event) {\n    localDOMHandlers.mousedown.call(this, event);\n  },\n  pointermove: function (event) {\n    if (!isPointerFromTouch(event)) {\n      localDOMHandlers.mousemove.call(this, event);\n    }\n  },\n  pointerup: function (event) {\n    localDOMHandlers.mouseup.call(this, event);\n  },\n  pointerout: function (event) {\n    if (!isPointerFromTouch(event)) {\n      localDOMHandlers.mouseout.call(this, event);\n    }\n  }\n};\nzrUtil.each(['click', 'dblclick', 'contextmenu'], function (name) {\n  localDOMHandlers[name] = function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.trigger(name, event);\n  };\n});\nvar globalDOMHandlers = {\n  pointermove: function (event) {\n    if (!isPointerFromTouch(event)) {\n      globalDOMHandlers.mousemove.call(this, event);\n    }\n  },\n  pointerup: function (event) {\n    globalDOMHandlers.mouseup.call(this, event);\n  },\n  mousemove: function (event) {\n    this.trigger('mousemove', event);\n  },\n  mouseup: function (event) {\n    var pointerCaptureReleasing = this.__pointerCapturing;\n    this.__togglePointerCapture(false);\n    this.trigger('mouseup', event);\n    if (pointerCaptureReleasing) {\n      event.zrEventControl = 'only_globalout';\n      this.trigger('mouseout', event);\n    }\n  }\n};\nfunction mountLocalDOMEventListeners(instance, scope) {\n  var domHandlers = scope.domHandlers;\n  if (env.pointerEventsSupported) {\n    zrUtil.each(localNativeListenerNames.pointer, function (nativeEventName) {\n      mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n        domHandlers[nativeEventName].call(instance, event);\n      });\n    });\n  } else {\n    if (env.touchEventsSupported) {\n      zrUtil.each(localNativeListenerNames.touch, function (nativeEventName) {\n        mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n          domHandlers[nativeEventName].call(instance, event);\n          setTouchTimer(scope);\n        });\n      });\n    }\n    zrUtil.each(localNativeListenerNames.mouse, function (nativeEventName) {\n      mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n        event = getNativeEvent(event);\n        if (!scope.touching) {\n          domHandlers[nativeEventName].call(instance, event);\n        }\n      });\n    });\n  }\n}\nfunction mountGlobalDOMEventListeners(instance, scope) {\n  if (env.pointerEventsSupported) {\n    zrUtil.each(globalNativeListenerNames.pointer, mount);\n  } else if (!env.touchEventsSupported) {\n    zrUtil.each(globalNativeListenerNames.mouse, mount);\n  }\n  function mount(nativeEventName) {\n    function nativeEventListener(event) {\n      event = getNativeEvent(event);\n      if (!isLocalEl(instance, event.target)) {\n        event = normalizeGlobalEvent(instance, event);\n        scope.domHandlers[nativeEventName].call(instance, event);\n      }\n    }\n    mountSingleDOMEventListener(scope, nativeEventName, nativeEventListener, {\n      capture: true\n    });\n  }\n}\nfunction mountSingleDOMEventListener(scope, nativeEventName, listener, opt) {\n  scope.mounted[nativeEventName] = listener;\n  scope.listenerOpts[nativeEventName] = opt;\n  addEventListener(scope.domTarget, nativeEventName, listener, opt);\n}\nfunction unmountDOMEventListeners(scope) {\n  var mounted = scope.mounted;\n  for (var nativeEventName in mounted) {\n    if (mounted.hasOwnProperty(nativeEventName)) {\n      removeEventListener(scope.domTarget, nativeEventName, mounted[nativeEventName], scope.listenerOpts[nativeEventName]);\n    }\n  }\n  scope.mounted = {};\n}\nvar DOMHandlerScope = function () {\n  function DOMHandlerScope(domTarget, domHandlers) {\n    this.mounted = {};\n    this.listenerOpts = {};\n    this.touching = false;\n    this.domTarget = domTarget;\n    this.domHandlers = domHandlers;\n  }\n  return DOMHandlerScope;\n}();\nvar HandlerDomProxy = function (_super) {\n  __extends(HandlerDomProxy, _super);\n  function HandlerDomProxy(dom, painterRoot) {\n    var _this = _super.call(this) || this;\n    _this.__pointerCapturing = false;\n    _this.dom = dom;\n    _this.painterRoot = painterRoot;\n    _this._localHandlerScope = new DOMHandlerScope(dom, localDOMHandlers);\n    if (globalEventSupported) {\n      _this._globalHandlerScope = new DOMHandlerScope(document, globalDOMHandlers);\n    }\n    mountLocalDOMEventListeners(_this, _this._localHandlerScope);\n    return _this;\n  }\n  HandlerDomProxy.prototype.dispose = function () {\n    unmountDOMEventListeners(this._localHandlerScope);\n    if (globalEventSupported) {\n      unmountDOMEventListeners(this._globalHandlerScope);\n    }\n  };\n  HandlerDomProxy.prototype.setCursor = function (cursorStyle) {\n    this.dom.style && (this.dom.style.cursor = cursorStyle || 'default');\n  };\n  HandlerDomProxy.prototype.__togglePointerCapture = function (isPointerCapturing) {\n    this.__mayPointerCapture = null;\n    if (globalEventSupported && +this.__pointerCapturing ^ +isPointerCapturing) {\n      this.__pointerCapturing = isPointerCapturing;\n      var globalHandlerScope = this._globalHandlerScope;\n      isPointerCapturing ? mountGlobalDOMEventListeners(this, globalHandlerScope) : unmountDOMEventListeners(globalHandlerScope);\n    }\n  };\n  return HandlerDomProxy;\n}(Eventful);\nexport default HandlerDomProxy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}