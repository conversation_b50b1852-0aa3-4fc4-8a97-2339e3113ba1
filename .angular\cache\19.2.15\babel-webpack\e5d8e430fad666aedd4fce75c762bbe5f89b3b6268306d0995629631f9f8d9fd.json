{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Swahili [sw]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/fadsel\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var sw = moment.defineLocale('sw', {\n    months: 'Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba'.split('_'),\n    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des'.split('_'),\n    weekdays: 'Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi'.split('_'),\n    weekdaysShort: 'Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos'.split('_'),\n    weekdaysMin: 'J2_J3_J4_J5_Al_Ij_J1'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'hh:mm A',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[leo saa] LT',\n      nextDay: '[kesho saa] LT',\n      nextWeek: '[wiki ijayo] dddd [saat] LT',\n      lastDay: '[jana] LT',\n      lastWeek: '[wiki iliyopita] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s baadaye',\n      past: 'tokea %s',\n      s: 'hivi punde',\n      ss: 'sekunde %d',\n      m: 'dakika moja',\n      mm: 'dakika %d',\n      h: 'saa limoja',\n      hh: 'masaa %d',\n      d: 'siku moja',\n      dd: 'siku %d',\n      M: 'mwezi mmoja',\n      MM: 'miezi %d',\n      y: 'mwaka mmoja',\n      yy: 'miaka %d'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return sw;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}