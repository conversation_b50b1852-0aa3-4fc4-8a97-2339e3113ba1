{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n  var newDom = platformApi.createCanvas();\n  var width = painter.getWidth();\n  var height = painter.getHeight();\n  var newDomStyle = newDom.style;\n  if (newDomStyle) {\n    newDomStyle.position = 'absolute';\n    newDomStyle.left = '0';\n    newDomStyle.top = '0';\n    newDomStyle.width = width + 'px';\n    newDomStyle.height = height + 'px';\n    newDom.setAttribute('data-zr-dom-id', id);\n  }\n  newDom.width = width * dpr;\n  newDom.height = height * dpr;\n  return newDom;\n}\n;\nvar Layer = function (_super) {\n  __extends(Layer, _super);\n  function Layer(id, painter, dpr) {\n    var _this = _super.call(this) || this;\n    _this.motionBlur = false;\n    _this.lastFrameAlpha = 0.7;\n    _this.dpr = 1;\n    _this.virtual = false;\n    _this.config = {};\n    _this.incremental = false;\n    _this.zlevel = 0;\n    _this.maxRepaintRectCount = 5;\n    _this.__dirty = true;\n    _this.__firstTimePaint = true;\n    _this.__used = false;\n    _this.__drawIndex = 0;\n    _this.__startIndex = 0;\n    _this.__endIndex = 0;\n    _this.__prevStartIndex = null;\n    _this.__prevEndIndex = null;\n    var dom;\n    dpr = dpr || devicePixelRatio;\n    if (typeof id === 'string') {\n      dom = createDom(id, painter, dpr);\n    } else if (util.isObject(id)) {\n      dom = id;\n      id = dom.id;\n    }\n    _this.id = id;\n    _this.dom = dom;\n    var domStyle = dom.style;\n    if (domStyle) {\n      util.disableUserSelect(dom);\n      dom.onselectstart = function () {\n        return false;\n      };\n      domStyle.padding = '0';\n      domStyle.margin = '0';\n      domStyle.borderWidth = '0';\n    }\n    _this.painter = painter;\n    _this.dpr = dpr;\n    return _this;\n  }\n  Layer.prototype.getElementCount = function () {\n    return this.__endIndex - this.__startIndex;\n  };\n  Layer.prototype.afterBrush = function () {\n    this.__prevStartIndex = this.__startIndex;\n    this.__prevEndIndex = this.__endIndex;\n  };\n  Layer.prototype.initContext = function () {\n    this.ctx = this.dom.getContext('2d');\n    this.ctx.dpr = this.dpr;\n  };\n  Layer.prototype.setUnpainted = function () {\n    this.__firstTimePaint = true;\n  };\n  Layer.prototype.createBackBuffer = function () {\n    var dpr = this.dpr;\n    this.domBack = createDom('back-' + this.id, this.painter, dpr);\n    this.ctxBack = this.domBack.getContext('2d');\n    if (dpr !== 1) {\n      this.ctxBack.scale(dpr, dpr);\n    }\n  };\n  Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n    if (this.__firstTimePaint) {\n      this.__firstTimePaint = false;\n      return null;\n    }\n    var mergedRepaintRects = [];\n    var maxRepaintRectCount = this.maxRepaintRectCount;\n    var full = false;\n    var pendingRect = new BoundingRect(0, 0, 0, 0);\n    function addRectToMergePool(rect) {\n      if (!rect.isFinite() || rect.isZero()) {\n        return;\n      }\n      if (mergedRepaintRects.length === 0) {\n        var boundingRect = new BoundingRect(0, 0, 0, 0);\n        boundingRect.copy(rect);\n        mergedRepaintRects.push(boundingRect);\n      } else {\n        var isMerged = false;\n        var minDeltaArea = Infinity;\n        var bestRectToMergeIdx = 0;\n        for (var i = 0; i < mergedRepaintRects.length; ++i) {\n          var mergedRect = mergedRepaintRects[i];\n          if (mergedRect.intersect(rect)) {\n            var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n            pendingRect_1.copy(mergedRect);\n            pendingRect_1.union(rect);\n            mergedRepaintRects[i] = pendingRect_1;\n            isMerged = true;\n            break;\n          } else if (full) {\n            pendingRect.copy(rect);\n            pendingRect.union(mergedRect);\n            var aArea = rect.width * rect.height;\n            var bArea = mergedRect.width * mergedRect.height;\n            var pendingArea = pendingRect.width * pendingRect.height;\n            var deltaArea = pendingArea - aArea - bArea;\n            if (deltaArea < minDeltaArea) {\n              minDeltaArea = deltaArea;\n              bestRectToMergeIdx = i;\n            }\n          }\n        }\n        if (full) {\n          mergedRepaintRects[bestRectToMergeIdx].union(rect);\n          isMerged = true;\n        }\n        if (!isMerged) {\n          var boundingRect = new BoundingRect(0, 0, 0, 0);\n          boundingRect.copy(rect);\n          mergedRepaintRects.push(boundingRect);\n        }\n        if (!full) {\n          full = mergedRepaintRects.length >= maxRepaintRectCount;\n        }\n      }\n    }\n    for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n      var el = displayList[i];\n      if (el) {\n        var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n        var prevRect = el.__isRendered && (el.__dirty & REDRAW_BIT || !shouldPaint) ? el.getPrevPaintRect() : null;\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n        var curRect = shouldPaint && (el.__dirty & REDRAW_BIT || !el.__isRendered) ? el.getPaintRect() : null;\n        if (curRect) {\n          addRectToMergePool(curRect);\n        }\n      }\n    }\n    for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n      var el = prevList[i];\n      var shouldPaint = el && el.shouldBePainted(viewWidth, viewHeight, true, true);\n      if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n        var prevRect = el.getPrevPaintRect();\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n      }\n    }\n    var hasIntersections;\n    do {\n      hasIntersections = false;\n      for (var i = 0; i < mergedRepaintRects.length;) {\n        if (mergedRepaintRects[i].isZero()) {\n          mergedRepaintRects.splice(i, 1);\n          continue;\n        }\n        for (var j = i + 1; j < mergedRepaintRects.length;) {\n          if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n            hasIntersections = true;\n            mergedRepaintRects[i].union(mergedRepaintRects[j]);\n            mergedRepaintRects.splice(j, 1);\n          } else {\n            j++;\n          }\n        }\n        i++;\n      }\n    } while (hasIntersections);\n    this._paintRects = mergedRepaintRects;\n    return mergedRepaintRects;\n  };\n  Layer.prototype.debugGetPaintRects = function () {\n    return (this._paintRects || []).slice();\n  };\n  Layer.prototype.resize = function (width, height) {\n    var dpr = this.dpr;\n    var dom = this.dom;\n    var domStyle = dom.style;\n    var domBack = this.domBack;\n    if (domStyle) {\n      domStyle.width = width + 'px';\n      domStyle.height = height + 'px';\n    }\n    dom.width = width * dpr;\n    dom.height = height * dpr;\n    if (domBack) {\n      domBack.width = width * dpr;\n      domBack.height = height * dpr;\n      if (dpr !== 1) {\n        this.ctxBack.scale(dpr, dpr);\n      }\n    }\n  };\n  Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n    var dom = this.dom;\n    var ctx = this.ctx;\n    var width = dom.width;\n    var height = dom.height;\n    clearColor = clearColor || this.clearColor;\n    var haveMotionBLur = this.motionBlur && !clearAll;\n    var lastFrameAlpha = this.lastFrameAlpha;\n    var dpr = this.dpr;\n    var self = this;\n    if (haveMotionBLur) {\n      if (!this.domBack) {\n        this.createBackBuffer();\n      }\n      this.ctxBack.globalCompositeOperation = 'copy';\n      this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n    }\n    var domBack = this.domBack;\n    function doClear(x, y, width, height) {\n      ctx.clearRect(x, y, width, height);\n      if (clearColor && clearColor !== 'transparent') {\n        var clearColorGradientOrPattern = void 0;\n        if (util.isGradientObject(clearColor)) {\n          var shouldCache = clearColor.global || clearColor.__width === width && clearColor.__height === height;\n          clearColorGradientOrPattern = shouldCache && clearColor.__canvasGradient || getCanvasGradient(ctx, clearColor, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          });\n          clearColor.__canvasGradient = clearColorGradientOrPattern;\n          clearColor.__width = width;\n          clearColor.__height = height;\n        } else if (util.isImagePatternObject(clearColor)) {\n          clearColor.scaleX = clearColor.scaleX || dpr;\n          clearColor.scaleY = clearColor.scaleY || dpr;\n          clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n            dirty: function () {\n              self.setUnpainted();\n              self.painter.refresh();\n            }\n          });\n        }\n        ctx.save();\n        ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n        ctx.fillRect(x, y, width, height);\n        ctx.restore();\n      }\n      if (haveMotionBLur) {\n        ctx.save();\n        ctx.globalAlpha = lastFrameAlpha;\n        ctx.drawImage(domBack, x, y, width, height);\n        ctx.restore();\n      }\n    }\n    ;\n    if (!repaintRects || haveMotionBLur) {\n      doClear(0, 0, width, height);\n    } else if (repaintRects.length) {\n      util.each(repaintRects, function (rect) {\n        doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n      });\n    }\n  };\n  return Layer;\n}(Eventful);\nexport default Layer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}