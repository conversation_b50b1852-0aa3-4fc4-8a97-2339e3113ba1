{"ast": null, "code": "import { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/calls-services/recitationExplanationGroup/recitation-explanation-group.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i5 from \"@angular/common\";\nfunction GroupDetailsComponent_ng_container_9_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction GroupDetailsComponent_ng_container_9_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 16);\n  }\n  if (rf & 2) {\n    const itemOfGroupRequests_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", itemOfGroupRequests_r2 == null ? null : itemOfGroupRequests_r2.stuAvatarUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction GroupDetailsComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, GroupDetailsComponent_ng_container_9_img_3_Template, 1, 1, \"img\", 14)(4, GroupDetailsComponent_ng_container_9_img_4_Template, 1, 1, \"img\", 14);\n    i0.ɵɵelementStart(5, \"div\")(6, \"p\", 15);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const itemOfGroupRequests_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(itemOfGroupRequests_r2 == null ? null : itemOfGroupRequests_r2.stuAvatarUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itemOfGroupRequests_r2 == null ? null : itemOfGroupRequests_r2.stuAvatarUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? itemOfGroupRequests_r2 == null ? null : itemOfGroupRequests_r2.studNameEn : itemOfGroupRequests_r2 == null ? null : itemOfGroupRequests_r2.studNameAr, \" \");\n  }\n}\nexport let GroupDetailsComponent = /*#__PURE__*/(() => {\n  class GroupDetailsComponent {\n    translate;\n    router;\n    route;\n    groupExplanationServices;\n    imagesPathesService;\n    detailsGroupExplanation;\n    callId;\n    groupId;\n    isVideoMuted;\n    callJoinModel;\n    langEnum = LanguageEnum;\n    jitsiSettingOptions;\n    constructor(translate, router, route, groupExplanationServices, imagesPathesService) {\n      this.translate = translate;\n      this.router = router;\n      this.route = route;\n      this.groupExplanationServices = groupExplanationServices;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.callId = this.route.snapshot.params.callId;\n      this.groupId = this.route.snapshot.params.groupId;\n      this.isVideoMuted = this.route.snapshot.params.isVideoMuted;\n      this.getGroupDetails();\n      this.setJoinCallModel();\n      this.setJitsiSettingOptions();\n    }\n    getGroupDetails() {\n      if (this.groupId) {\n        this.groupExplanationServices.getGroupExplanationDetails(this.groupId).subscribe(res => {\n          if (res.isSuccess) {\n            this.detailsGroupExplanation = res.data;\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    setJoinCallModel() {\n      this.callJoinModel = {\n        callId: this.callId,\n        isMod: false,\n        usrId: JSON.parse(localStorage.getItem(\"user\") || '{}')?.id,\n        groupId: this.groupId\n      };\n    }\n    setJitsiSettingOptions() {\n      this.jitsiSettingOptions = {\n        endCallType: EndCallTypesEnum.GroupExplanStudUsrEndCall,\n        isVideoMute: this.isVideoMuted,\n        startCallType: StartCallTypesEnum.JoinMode\n      };\n    }\n    endCallLisener() {\n      this.router.navigateByUrl('/dashboard/student-dashboard');\n    }\n    static ɵfac = function GroupDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GroupDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.RecitationExplanationGroupService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupDetailsComponent,\n      selectors: [[\"app-group-details\"]],\n      decls: 13,\n      vars: 4,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-3\"], [1, \"details_day\"], [1, \"head_groupName\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"mx-2\"], [1, \"chat_details\", \"w-100\", \"mt-3\"], [1, \"chat_vontainer\", \"w-100\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-9\"], [1, \"call-integ\"], [3, \"endCallStudEvent\", \"callJoinModel\", \"jitsiSettingOptions\"], [1, \"cardRecourd\", \"d-flex\", \"justify-content-between\", \"mb-3\"], [1, \"group_name\", \"d-flex\", \"pb-0\", \"align-items-center\", \"justify-content-between\"], [\"class\", \"img_user\", \"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"head\", \"mx-1\"], [\"alt\", \"\", 1, \"img_user\", 3, \"src\"]],\n      template: function GroupDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"P\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, GroupDetailsComponent_ng_container_9_Template, 8, 3, \"ng-container\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"app-jitsi-group-call-integ\", 11);\n          i0.ɵɵlistener(\"endCallStudEvent\", function GroupDetailsComponent_Template_app_jitsi_group_call_integ_endCallStudEvent_12_listener() {\n            return ctx.endCallLisener();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629: \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.detailsGroupExplanation == null ? null : ctx.detailsGroupExplanation.groupNameEn : ctx.detailsGroupExplanation == null ? null : ctx.detailsGroupExplanation.groupNameAr, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.detailsGroupExplanation == null ? null : ctx.detailsGroupExplanation.groupMembers);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"callJoinModel\", ctx.callJoinModel)(\"jitsiSettingOptions\", ctx.jitsiSettingOptions);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.details_day[_ngcontent-%COMP%]{background:#fbfbfb;border-radius:1.188rem;opacity:1;padding:1.7rem;margin-top:1rem}.details_day[_ngcontent-%COMP%]:lang(en){text-align:left}.details_day[_ngcontent-%COMP%]:lang(ar){text-align:right}.details_day[_ngcontent-%COMP%]   .head_groupName[_ngcontent-%COMP%]{color:#333;font-size:1.23rem;font-weight:700}.details_day[_ngcontent-%COMP%]   .head_btn_add[_ngcontent-%COMP%]{color:#333;font-size:.85rem;font-weight:700}.details_day[_ngcontent-%COMP%]   .group_name[_ngcontent-%COMP%]{color:var(--main_color);font-weight:700;font-size:.937rem;cursor:pointer}.details_day[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none!important}.details_day[_ngcontent-%COMP%]   .chat_details[_ngcontent-%COMP%]{height:59vh;overflow-y:auto;overflow-x:hidden;padding:.3rem}.details_day[_ngcontent-%COMP%]   .message_date[_ngcontent-%COMP%]{color:#333;font-size:.563rem}.details_day[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:100%}.details_day[_ngcontent-%COMP%]   .cardRecourd[_ngcontent-%COMP%]{padding:.5rem;background-color:transparent;border:.3rem;border-bottom:.094rem solid #d6d7d8;border-radius:0;margin:.2rem;box-shadow:none;width:100%}.details_day[_ngcontent-%COMP%]   .img_user[_ngcontent-%COMP%]{width:3.75rem;height:3.75rem;border:.063rem solid #C1C7D0;border-radius:.313rem;background:#c1c7d0;padding:.5rem}.details_day[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}.details_day[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{background:#f2f1f1de}@media (max-width: 64rem){.vedio[_ngcontent-%COMP%], .call[_ngcontent-%COMP%]{width:.813rem;height:.813rem}.details_day[_ngcontent-%COMP%]   .chat_details[_ngcontent-%COMP%]{height:61vh}}.head[_ngcontent-%COMP%]{font-weight:700;font-size:1rem}.details_day[_ngcontent-%COMP%]{height:85vh}.call-integ[_ngcontent-%COMP%]{display:flex;justify-content:center;background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:2rem;height:85vh;margin-top:1rem}\"]\n    });\n  }\n  return GroupDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}