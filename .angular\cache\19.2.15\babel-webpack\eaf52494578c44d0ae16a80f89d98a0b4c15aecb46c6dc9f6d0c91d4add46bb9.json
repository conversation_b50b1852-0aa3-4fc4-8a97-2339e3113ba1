{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport { getDefaultLabel } from './labelHelper.js';\nimport { extend } from 'zrender/lib/core/util.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar Symbol = /** @class */function (_super) {\n  __extends(Symbol, _super);\n  function Symbol(data, idx, seriesScope, opts) {\n    var _this = _super.call(this) || this;\n    _this.updateData(data, idx, seriesScope, opts);\n    return _this;\n  }\n  Symbol.prototype._createSymbol = function (symbolType, data, idx, symbolSize, keepAspect) {\n    // Remove paths created before\n    this.removeAll();\n    // let symbolPath = createSymbol(\n    //     symbolType, -0.5, -0.5, 1, 1, color\n    // );\n    // If width/height are set too small (e.g., set to 1) on ios10\n    // and macOS Sierra, a circle stroke become a rect, no matter what\n    // the scale is set. So we set width/height as 2. See #4150.\n    var symbolPath = createSymbol(symbolType, -1, -1, 2, 2, null, keepAspect);\n    symbolPath.attr({\n      z2: 100,\n      culling: true,\n      scaleX: symbolSize[0] / 2,\n      scaleY: symbolSize[1] / 2\n    });\n    // Rewrite drift method\n    symbolPath.drift = driftSymbol;\n    this._symbolType = symbolType;\n    this.add(symbolPath);\n  };\n  /**\r\n   * Stop animation\r\n   * @param {boolean} toLastFrame\r\n   */\n  Symbol.prototype.stopSymbolAnimation = function (toLastFrame) {\n    this.childAt(0).stopAnimation(null, toLastFrame);\n  };\n  Symbol.prototype.getSymbolType = function () {\n    return this._symbolType;\n  };\n  /**\r\n   * FIXME:\r\n   * Caution: This method breaks the encapsulation of this module,\r\n   * but it indeed brings convenience. So do not use the method\r\n   * unless you detailedly know all the implements of `Symbol`,\r\n   * especially animation.\r\n   *\r\n   * Get symbol path element.\r\n   */\n  Symbol.prototype.getSymbolPath = function () {\n    return this.childAt(0);\n  };\n  /**\r\n   * Highlight symbol\r\n   */\n  Symbol.prototype.highlight = function () {\n    enterEmphasis(this.childAt(0));\n  };\n  /**\r\n   * Downplay symbol\r\n   */\n  Symbol.prototype.downplay = function () {\n    leaveEmphasis(this.childAt(0));\n  };\n  /**\r\n   * @param {number} zlevel\r\n   * @param {number} z\r\n   */\n  Symbol.prototype.setZ = function (zlevel, z) {\n    var symbolPath = this.childAt(0);\n    symbolPath.zlevel = zlevel;\n    symbolPath.z = z;\n  };\n  Symbol.prototype.setDraggable = function (draggable, hasCursorOption) {\n    var symbolPath = this.childAt(0);\n    symbolPath.draggable = draggable;\n    symbolPath.cursor = !hasCursorOption && draggable ? 'move' : symbolPath.cursor;\n  };\n  /**\r\n   * Update symbol properties\r\n   */\n  Symbol.prototype.updateData = function (data, idx, seriesScope, opts) {\n    this.silent = false;\n    var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';\n    var seriesModel = data.hostModel;\n    var symbolSize = Symbol.getSymbolSize(data, idx);\n    var isInit = symbolType !== this._symbolType;\n    var disableAnimation = opts && opts.disableAnimation;\n    if (isInit) {\n      var keepAspect = data.getItemVisual(idx, 'symbolKeepAspect');\n      this._createSymbol(symbolType, data, idx, symbolSize, keepAspect);\n    } else {\n      var symbolPath = this.childAt(0);\n      symbolPath.silent = false;\n      var target = {\n        scaleX: symbolSize[0] / 2,\n        scaleY: symbolSize[1] / 2\n      };\n      disableAnimation ? symbolPath.attr(target) : graphic.updateProps(symbolPath, target, seriesModel, idx);\n      saveOldStyle(symbolPath);\n    }\n    this._updateCommon(data, idx, symbolSize, seriesScope, opts);\n    if (isInit) {\n      var symbolPath = this.childAt(0);\n      if (!disableAnimation) {\n        var target = {\n          scaleX: this._sizeX,\n          scaleY: this._sizeY,\n          style: {\n            // Always fadeIn. Because it has fadeOut animation when symbol is removed..\n            opacity: symbolPath.style.opacity\n          }\n        };\n        symbolPath.scaleX = symbolPath.scaleY = 0;\n        symbolPath.style.opacity = 0;\n        graphic.initProps(symbolPath, target, seriesModel, idx);\n      }\n    }\n    if (disableAnimation) {\n      // Must stop leave transition manually if don't call initProps or updateProps.\n      this.childAt(0).stopAnimation('leave');\n    }\n  };\n  Symbol.prototype._updateCommon = function (data, idx, symbolSize, seriesScope, opts) {\n    var symbolPath = this.childAt(0);\n    var seriesModel = data.hostModel;\n    var emphasisItemStyle;\n    var blurItemStyle;\n    var selectItemStyle;\n    var focus;\n    var blurScope;\n    var emphasisDisabled;\n    var labelStatesModels;\n    var hoverScale;\n    var cursorStyle;\n    if (seriesScope) {\n      emphasisItemStyle = seriesScope.emphasisItemStyle;\n      blurItemStyle = seriesScope.blurItemStyle;\n      selectItemStyle = seriesScope.selectItemStyle;\n      focus = seriesScope.focus;\n      blurScope = seriesScope.blurScope;\n      labelStatesModels = seriesScope.labelStatesModels;\n      hoverScale = seriesScope.hoverScale;\n      cursorStyle = seriesScope.cursorStyle;\n      emphasisDisabled = seriesScope.emphasisDisabled;\n    }\n    if (!seriesScope || data.hasItemOption) {\n      var itemModel = seriesScope && seriesScope.itemModel ? seriesScope.itemModel : data.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n      selectItemStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n      blurItemStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      emphasisDisabled = emphasisModel.get('disabled');\n      labelStatesModels = getLabelStatesModels(itemModel);\n      hoverScale = emphasisModel.getShallow('scale');\n      cursorStyle = itemModel.getShallow('cursor');\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    symbolPath.attr('rotation', (symbolRotate || 0) * Math.PI / 180 || 0);\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      symbolPath.x = symbolOffset[0];\n      symbolPath.y = symbolOffset[1];\n    }\n    cursorStyle && symbolPath.attr('cursor', cursorStyle);\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var visualColor = symbolStyle.fill;\n    if (symbolPath instanceof ZRImage) {\n      var pathStyle = symbolPath.style;\n      symbolPath.useStyle(extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolStyle));\n    } else {\n      if (symbolPath.__isEmptyBrush) {\n        // fill and stroke will be swapped if it's empty.\n        // So we cloned a new style to avoid it affecting the original style in visual storage.\n        // TODO Better implementation. No empty logic!\n        symbolPath.useStyle(extend({}, symbolStyle));\n      } else {\n        symbolPath.useStyle(symbolStyle);\n      }\n      // Disable decal because symbol scale will been applied on the decal.\n      symbolPath.style.decal = null;\n      symbolPath.setColor(visualColor, opts && opts.symbolInnerColor);\n      symbolPath.style.strokeNoScale = true;\n    }\n    var liftZ = data.getItemVisual(idx, 'liftZ');\n    var z2Origin = this._z2;\n    if (liftZ != null) {\n      if (z2Origin == null) {\n        this._z2 = symbolPath.z2;\n        symbolPath.z2 += liftZ;\n      }\n    } else if (z2Origin != null) {\n      symbolPath.z2 = z2Origin;\n      this._z2 = null;\n    }\n    var useNameLabel = opts && opts.useNameLabel;\n    setLabelStyle(symbolPath, labelStatesModels, {\n      labelFetcher: seriesModel,\n      labelDataIndex: idx,\n      defaultText: getLabelDefaultText,\n      inheritColor: visualColor,\n      defaultOpacity: symbolStyle.opacity\n    });\n    // Do not execute util needed.\n    function getLabelDefaultText(idx) {\n      return useNameLabel ? data.getName(idx) : getDefaultLabel(data, idx);\n    }\n    this._sizeX = symbolSize[0] / 2;\n    this._sizeY = symbolSize[1] / 2;\n    var emphasisState = symbolPath.ensureState('emphasis');\n    emphasisState.style = emphasisItemStyle;\n    symbolPath.ensureState('select').style = selectItemStyle;\n    symbolPath.ensureState('blur').style = blurItemStyle;\n    // null / undefined / true means to use default strategy.\n    // 0 / false / negative number / NaN / Infinity means no scale.\n    var scaleRatio = hoverScale == null || hoverScale === true ? Math.max(1.1, 3 / this._sizeY)\n    // PENDING: restrict hoverScale > 1? It seems unreasonable to scale down\n    : isFinite(hoverScale) && hoverScale > 0 ? +hoverScale : 1;\n    // always set scale to allow resetting\n    emphasisState.scaleX = this._sizeX * scaleRatio;\n    emphasisState.scaleY = this._sizeY * scaleRatio;\n    this.setSymbolScale(1);\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Symbol.prototype.setSymbolScale = function (scale) {\n    this.scaleX = this.scaleY = scale;\n  };\n  Symbol.prototype.fadeOut = function (cb, seriesModel, opt) {\n    var symbolPath = this.childAt(0);\n    var dataIndex = getECData(this).dataIndex;\n    var animationOpt = opt && opt.animation;\n    // Avoid mistaken hover when fading out\n    this.silent = symbolPath.silent = true;\n    // Not show text when animating\n    if (opt && opt.fadeLabel) {\n      var textContent = symbolPath.getTextContent();\n      if (textContent) {\n        graphic.removeElement(textContent, {\n          style: {\n            opacity: 0\n          }\n        }, seriesModel, {\n          dataIndex: dataIndex,\n          removeOpt: animationOpt,\n          cb: function () {\n            symbolPath.removeTextContent();\n          }\n        });\n      }\n    } else {\n      symbolPath.removeTextContent();\n    }\n    graphic.removeElement(symbolPath, {\n      style: {\n        opacity: 0\n      },\n      scaleX: 0,\n      scaleY: 0\n    }, seriesModel, {\n      dataIndex: dataIndex,\n      cb: cb,\n      removeOpt: animationOpt\n    });\n  };\n  Symbol.getSymbolSize = function (data, idx) {\n    return normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n  };\n  return Symbol;\n}(graphic.Group);\nfunction driftSymbol(dx, dy) {\n  this.parent.drift(dx, dy);\n}\nexport default Symbol;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}