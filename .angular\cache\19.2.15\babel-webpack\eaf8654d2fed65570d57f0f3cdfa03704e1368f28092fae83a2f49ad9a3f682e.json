{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { FormsModule } from '@angular/forms';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { NgbModule, NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { PdfViewerModule } from 'ng2-pdf-viewer';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-echarts\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, TranslateModule, MatButtonModule, MatDialogModule, MatCardModule, MatExpansionModule, MatSelectModule, DragDropModule, MatIconModule, FormsModule, MatCheckboxModule, MatRadioModule, MatGridListModule, NgbModule, MatAutocompleteModule, NgbRatingModule, MatTooltipModule, PdfViewerModule, MatDatepickerModule, MatNativeDateModule, NgxEchartsModule.forRoot({\n        echarts: () => import('echarts')\n      }),\n      // Angular Material Modules\n      MatRadioModule, MatCheckboxModule, MatButtonModule, MatDialogModule, MatCardModule, MatExpansionModule, MatSelectModule, DragDropModule, MatGridListModule, MatTooltipModule, MatDatepickerModule, MatNativeDateModule,\n      // Third-party modules\n      NgbRatingModule, NgbModule, NgxEchartsModule\n      // All components are now standalone and don't need to be exported from NgModule\n      ]\n    });\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}