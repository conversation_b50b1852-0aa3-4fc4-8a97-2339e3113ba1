{"ast": null, "code": "import { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/calls-services/calls.service\";\nimport * as i3 from \"src/app/core/services/calls-services/ProgramRecitationTasks/program-recitation-tasks.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"ng2-pdf-viewer\";\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 23);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu.fromUserAvatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_a_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r6.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 35);\n    i0.ɵɵtemplate(1, UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_a_4_ng_container_1_Template, 2, 1, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", item_r6.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r6.fileName.split(\".\").pop() !== \"pdf\");\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"pdf-viewer\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r6.url)(\"render-text\", true)(\"original-size\", false)(\"fit-to-page\", true)(\"show-all\", true)(\"zoom-scale\", \"page-width\");\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h4\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtemplate(4, UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_a_4_Template, 2, 2, \"a\", 31)(5, UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_ng_container_5_Template, 2, 6, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_Template_button_click_7_listener() {\n      const modal_r7 = i0.ɵɵrestoreView(_r5).$implicit;\n      return i0.ɵɵresetView(modal_r7.close(\"Close click\"));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.fileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r6.fileName.split(\".\").pop() !== \"pdf\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r6.fileName.split(\".\").pop() === \"pdf\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 4, \"GENERAL.CLOSE\"), \"\");\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const content_r4 = i0.ɵɵreference(6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openVerticallyCentered(content_r4));\n    });\n    i0.ɵɵelement(2, \"img\", 26);\n    i0.ɵɵelementStart(3, \"p\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_ng_template_5_Template, 10, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.book_sound, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", item_r6.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r6.fileName);\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nfunction UpdateDegreeOfSubmittedTaskComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8)(4, \"div\", 9)(5, \"div\", 10);\n    i0.ɵɵtemplate(6, UpdateDegreeOfSubmittedTaskComponent_div_3_img_6_Template, 1, 1, \"img\", 11)(7, UpdateDegreeOfSubmittedTaskComponent_div_3_img_7_Template, 1, 1, \"img\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\")(9, \"h3\", 13);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"h3\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"h3\", 15);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"br\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 16);\n    i0.ɵɵtemplate(18, UpdateDegreeOfSubmittedTaskComponent_div_3_div_18_Template, 7, 3, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"br\");\n    i0.ɵɵelementStart(20, \"input\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UpdateDegreeOfSubmittedTaskComponent_div_3_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.updateTaskByAddingDegreeRequest.grade, $event) || (ctx_r1.updateTaskByAddingDegreeRequest.grade = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 19);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"br\");\n    i0.ɵɵelementStart(25, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function UpdateDegreeOfSubmittedTaskComponent_div_3_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateDegree());\n    });\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, UpdateDegreeOfSubmittedTaskComponent_div_3_div_28_Template, 2, 4, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu.fromUserAvatar));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu.fromUserAvatar);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar ? ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu.fromNameAr : ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.stu.fromNameEn, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 11, \"CALL.PROGRAM_NAME_SEARCH\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar ? ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.nameAr : ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.nameEn, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.submitStudentTasmeeaTaskDegreeDetail == null ? null : ctx_r1.submitStudentTasmeeaTaskDegreeDetail.bookAttatchments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.updateTaskByAddingDegreeRequest.grade);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 13, \"CALL.RECITATION_RATING_OF\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disableSaveButtons);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 15, \"CALL.DONE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage);\n  }\n}\nexport let UpdateDegreeOfSubmittedTaskComponent = /*#__PURE__*/(() => {\n  class UpdateDegreeOfSubmittedTaskComponent {\n    translate;\n    callsService;\n    programRecitationTasksService;\n    route;\n    router;\n    modalService;\n    imagesPathesService;\n    submitStudentTasmeeaTaskDegreeDetail;\n    updateTaskByAddingDegreeRequest;\n    errorMessage;\n    resMessage = {};\n    langEnum = LanguageEnum;\n    disableSaveButtons = false;\n    constructor(translate, callsService, programRecitationTasksService, route, router, modalService, imagesPathesService) {\n      this.translate = translate;\n      this.callsService = callsService;\n      this.programRecitationTasksService = programRecitationTasksService;\n      this.route = route;\n      this.router = router;\n      this.modalService = modalService;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getAllMemorize();\n    }\n    getAllMemorize() {\n      let filter = {\n        call: this.route.snapshot.params.callId,\n        day: this.route.snapshot.params.dutyDay\n      };\n      this.callsService.getCallStudentTasmeeaTaskDegreeDetails(filter || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.submitStudentTasmeeaTaskDegreeDetail = res.data;\n          this.submitStudentTasmeeaTaskDegreeDetail.bookAttatchments = [];\n          Array.from(res.data.Task).forEach(elm => {\n            let lstBookAttatchments = JSON.parse(elm.detailsTask).bookAttatchments;\n            if (lstBookAttatchments.length > 0) {\n              Array.from(lstBookAttatchments).forEach(item => {\n                this.submitStudentTasmeeaTaskDegreeDetail?.bookAttatchments?.push(item);\n              });\n            } else {\n              this.submitStudentTasmeeaTaskDegreeDetail?.bookAttatchments?.push(elm);\n            }\n          });\n          this.updateTaskByAddingDegreeRequest = {\n            id: this.route.snapshot.params.id\n          };\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    openVerticallyCentered(content) {\n      this.modalService.open(content, {\n        size: 'lg'\n      });\n    }\n    updateDegree() {\n      this.resMessage = {};\n      if (this.updateTaskByAddingDegreeRequest && this.updateTaskByAddingDegreeRequest.grade && this.updateTaskByAddingDegreeRequest.grade > 100) {\n        this.resMessage = {\n          message: this.translate.instant('CALL.SCORE_GREATER_THAN100'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      } else {\n        this.programRecitationTasksService.updateTaskByAddingDegree(this.updateTaskByAddingDegreeRequest || {}).subscribe(res => {\n          if (res.isSuccess) {\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n            this.disableSaveButtons = true;\n            // setTimeout(() => {\n            this.router.navigateByUrl('recitation-task-call/rating/' + this.route.snapshot.params.callId + '/' + StartCallTypesEnum.JoinMode + '/' + RoleEnum.Student + '/' + EndCallTypesEnum.TasmeeaTskTechUsrEndCall + '/undefined/undefined'); // double slash as two parameters empty\n            // }, 3000);\n          } else {\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      }\n    }\n    static ɵfac = function UpdateDegreeOfSubmittedTaskComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UpdateDegreeOfSubmittedTaskComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.CallsService), i0.ɵɵdirectiveInject(i3.ProgramRecitationTasksService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateDegreeOfSubmittedTaskComponent,\n      selectors: [[\"app-update-degree-of-submitted-task\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[\"content\", \"\"], [1, \"container-fluid\"], [1, \"row\", \"justify-content-center\"], [1, \"col-10\"], [\"class\", \"form-group tab_page   text-center w-100  m-3\", 4, \"ngIf\"], [1, \"form-group\", \"tab_page\", \"text-center\", \"w-100\", \"m-3\"], [1, \"pt-3\"], [1, \"info_basic_info\"], [1, \"form-group\", \"mr-3\", \"ml-3\"], [1, \"d-flex\", \"justify-content-start\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-start\"], [\"class\", \"user__image  rounded mx-auto d-block\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"user__image  img-container\", 3, \"src\", 4, \"ngIf\"], [1, \"user__name\"], [1, \"label\", \"mt-4\", \"mb-3\", \"px-3\", \"text-right\"], [1, \"user__name\", \"px-2\"], [1, \"row\", \"mx-0\"], [\"class\", \"col-4\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"number\", \"id\", \"InputGrade\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"hint_validation\"], [1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"click\", \"disabled\"], [3, \"class\", 4, \"ngIf\"], [1, \"user__image\", \"rounded\", \"mx-auto\", \"d-block\", 3, \"src\"], [1, \"user__image\", \"img-container\", 3, \"src\"], [1, \"col-4\"], [1, \"cardRecourd\", 3, \"click\"], [3, \"src\"], [1, \"ellipsis\", \"mb-0\", 3, \"matTooltip\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"modal-body\"], [3, \"href\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [3, \"href\"], [1, \"size_img_model\", 3, \"src\"], [3, \"src\", \"render-text\", \"original-size\", \"fit-to-page\", \"show-all\", \"zoom-scale\"]],\n      template: function UpdateDegreeOfSubmittedTaskComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n          i0.ɵɵtemplate(3, UpdateDegreeOfSubmittedTaskComponent_div_3_Template, 29, 17, \"div\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitStudentTasmeeaTaskDegreeDetail && ctx.updateTaskByAddingDegreeRequest);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgModel, i9.PdfViewerComponent, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.tab_page[_ngcontent-%COMP%]{height:80vh;background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:100%}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]{background-color:#fff;padding:1.5rem;color:#333;margin:1rem 1.8rem;border-radius:1.188rem;font-size:1rem}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   .trash[_ngcontent-%COMP%]{cursor:pointer}.tab_page[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:var(--second_color);font-size:.875rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .title_data[_ngcontent-%COMP%]{color:#4d4d4d;font-size:.875rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .head_conditions[_ngcontent-%COMP%]{width:50%;color:var(--main_color);font-size:1.125rem;font-weight:700;border-bottom:.063rem solid rgba(242,241,241,.8705882353)}.user__image[_ngcontent-%COMP%]{width:11.937rem;border-radius:.75rem;height:11.93rem}.user__name[_ngcontent-%COMP%]{font-weight:600;font-size:1.5rem;color:#333}.user__date[_ngcontent-%COMP%]{font-weight:600;font-size:1rem;color:#333}.user__back[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:var(--second_color);display:flex;align-items:center;justify-content:space-evenly;cursor:pointer}.user__back[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin:0 .313rem}.user__height[_ngcontent-%COMP%]{height:41vh;overflow-y:auto}.switch[_ngcontent-%COMP%]{position:relative;display:inline-block;width:2.5rem;height:1.25rem;margin-bottom:0!important}.switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{opacity:0;width:0;height:0}.slider[_ngcontent-%COMP%]{position:absolute;cursor:pointer;inset:0;background-color:#d6d7d8;transition:.4s}.slider[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";height:1rem;width:1rem;left:.188rem;bottom:.156rem;background-color:#fff;transition:.4s}input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]{background-color:var(--second_color)}input[_ngcontent-%COMP%]:focus + .slider[_ngcontent-%COMP%]{box-shadow:0 0 .063rem var(--second_color)}input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]:before{transform:translate(1.25rem)}.slider.round[_ngcontent-%COMP%]{border-radius:2.125rem}.slider.round[_ngcontent-%COMP%]:before{border-radius:50%}.p_header_sec[_ngcontent-%COMP%], .p_header[_ngcontent-%COMP%]{font-weight:700}.p_header[_ngcontent-%COMP%]{color:maroon;font-size:1rem}.p_header_sec[_ngcontent-%COMP%]{font-size:1.2rem;color:#333}.status[_ngcontent-%COMP%]{width:.625rem;height:.625rem;background:#10af10;border-radius:50%}.status[_ngcontent-%COMP%]:lang(ar){margin-right:.625rem;margin-left:1.25rem}.status[_ngcontent-%COMP%]:lang(ar){margin-right:1.25rem;margin-left:.625rem}.status-text[_ngcontent-%COMP%]{color:#10af10;font-size:1rem;margin:0}.label_info[_ngcontent-%COMP%], .data_view[_ngcontent-%COMP%], .label[_ngcontent-%COMP%]{color:#333;font-size:.875rem;font-weight:700}.tab_page_No_data[_ngcontent-%COMP%]{height:79vh}.user__height[_ngcontent-%COMP%]{height:32vh!important;overflow-y:auto}@media all and (min-width: 19in){.tab_page[_ngcontent-%COMP%]{height:81vh}}@media (max-width: 48rem){.tab_page[_ngcontent-%COMP%]{height:79vh}.trash[_ngcontent-%COMP%], .exportIcon[_ngcontent-%COMP%]{width:1.5rem;cursor:pointer}}.tab_page[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:64vh;margin-top:1rem}.user__height[_ngcontent-%COMP%]{height:26vh!important;overflow-x:hidden}.info_basic_info[_ngcontent-%COMP%]{padding-bottom:0!important}.tab_page[_ngcontent-%COMP%]{height:100%}.cardRecourd[_ngcontent-%COMP%]{display:flex;justify-content:space-between;background:#fbfbfb;padding:.5rem;border-radius:.625rem;margin-top:.2rem;margin-bottom:.2rem}.hint_validation[_ngcontent-%COMP%]{color:#d6d7d8;font-size:.75rem;display:flex;justify-content:flex-end;margin:.2rem .5rem}\"]\n    });\n  }\n  return UpdateDegreeOfSubmittedTaskComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}