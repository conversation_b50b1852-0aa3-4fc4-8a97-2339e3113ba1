{"ast": null, "code": "import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/teacher-program-tab-services/teacher-program-tab-services.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nfunction AdminTeacherProgramDetailsComponent_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.qor2an, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.getTeacherProgramBatchDetailsResponse == null ? null : ctx_r0.getTeacherProgramBatchDetailsResponse.progPic, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_ng_container_20_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28)(1, \"span\", 29);\n    i0.ɵɵtext(2, \"\\u2605\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"\\u2605 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fill_r3 = ctx.fill;\n    i0.ɵɵclassProp(\"filled\", fill_r3 === 100);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", fill_r3, \"%\");\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ngb-rating\", 27);\n    i0.ɵɵtwoWayListener(\"rateChange\", function AdminTeacherProgramDetailsComponent_ng_container_20_Template_ngb_rating_rateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.getTeacherProgramBatchDetailsResponse.rate, $event) || (ctx_r0.getTeacherProgramBatchDetailsResponse.rate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, AdminTeacherProgramDetailsComponent_ng_container_20_ng_template_2_Template, 4, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"rate\", ctx_r0.getTeacherProgramBatchDetailsResponse.rate);\n    i0.ɵɵproperty(\"max\", 5)(\"readonly\", true);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_div_62_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.imagesPathesService.user_avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_div_62_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n  if (rf & 2) {\n    const studentItem_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"src\", studentItem_r6.usrImg, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_div_62_ng_container_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28)(1, \"span\", 29);\n    i0.ɵɵtext(2, \"\\u2605\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"\\u2605 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fill_r8 = ctx.fill;\n    i0.ɵɵclassProp(\"filled\", fill_r8 === 100);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", fill_r8, \"%\");\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_div_62_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ngb-rating\", 27);\n    i0.ɵɵtwoWayListener(\"rateChange\", function AdminTeacherProgramDetailsComponent_div_62_ng_container_7_Template_ngb_rating_rateChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const studentItem_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(studentItem_r6.rate, $event) || (studentItem_r6.rate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, AdminTeacherProgramDetailsComponent_div_62_ng_container_7_ng_template_2_Template, 4, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const studentItem_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"rate\", studentItem_r6.rate);\n    i0.ɵɵproperty(\"max\", 5)(\"readonly\", true);\n  }\n}\nfunction AdminTeacherProgramDetailsComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function AdminTeacherProgramDetailsComponent_div_62_Template_div_click_1_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.loadProgramMaterial();\n      return i0.ɵɵresetView(ctx_r0.selectedIndex = i_r5);\n    });\n    i0.ɵɵtemplate(2, AdminTeacherProgramDetailsComponent_div_62_img_2_Template, 1, 1, \"img\", 32)(3, AdminTeacherProgramDetailsComponent_div_62_img_3_Template, 1, 1, \"img\", 32);\n    i0.ɵɵelementStart(4, \"div\", 33)(5, \"h2\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AdminTeacherProgramDetailsComponent_div_62_ng_container_7_Template, 3, 3, \"ng-container\", 14);\n    i0.ɵɵelementStart(8, \"div\", 13)(9, \"h6\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h6\", 36);\n    i0.ɵɵtext(13, \"98\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 13)(15, \"h6\", 35);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h6\", 36);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const studentItem_r6 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r0.selectedIndex === i_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !studentItem_r6.usrImg);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", studentItem_r6.usrImg);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? studentItem_r6 == null ? null : studentItem_r6.studNameEn : studentItem_r6 == null ? null : studentItem_r6.studNameAr);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.translate.currentLang === ctx_r0.langEnum.en ? studentItem_r6 == null ? null : studentItem_r6.studNameEn : studentItem_r6 == null ? null : studentItem_r6.studNameAr, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", studentItem_r6 && studentItem_r6.rate != null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(11, 10, \"PROGRAMS_LIST.DEGREE\"), \" :\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(17, 12, \"PROGRAMS_LIST.DATE\"), \" :\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 14, studentItem_r6 == null ? null : studentItem_r6.startDate, \"dd/MM/yyyy\"));\n  }\n}\nexport let AdminTeacherProgramDetailsComponent = /*#__PURE__*/(() => {\n  class AdminTeacherProgramDetailsComponent {\n    teacherProgramTabService;\n    translate;\n    imagesPathesService;\n    teacherInfoDetails;\n    starsSelected = 0;\n    selectedIndex = 0;\n    resMessage = {};\n    getTeacherProgramBatchDetailsResponse;\n    setTeacherProgramBatchDetailsRequest;\n    langEnum = LanguageEnum;\n    loadProgramMaterial() {}\n    constructor(teacherProgramTabService, translate, imagesPathesService) {\n      this.teacherProgramTabService = teacherProgramTabService;\n      this.translate = translate;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {}\n    getTeacherProgramBatchDetails(/*batchId? :string*/\n    ) {\n      this.setTeacherProgramBatchDetailsRequest = {\n        techId: this.teacherInfoDetails?.usrId,\n        batId: this.teacherInfoDetails?.batchId\n      };\n      this.teacherProgramTabService.getTeacherProgramDetails(this.setTeacherProgramBatchDetailsRequest).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.getTeacherProgramBatchDetailsResponse = res.data;\n        } else {\n          this.getTeacherProgramBatchDetailsResponse;\n          this.resMessage = {\n            message: response.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    static ɵfac = function AdminTeacherProgramDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminTeacherProgramDetailsComponent)(i0.ɵɵdirectiveInject(i1.TeacherProgramTabService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminTeacherProgramDetailsComponent,\n      selectors: [[\"app-admin-teacher-program-details\"]],\n      inputs: {\n        teacherInfoDetails: \"teacherInfoDetails\"\n      },\n      decls: 63,\n      vars: 34,\n      consts: [[1, \"details\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-xl-4\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [\"class\", \"details__image\", 3, \"src\", 4, \"ngIf\"], [1, \"col-xl-8\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-end\"], [1, \"user_tabs-btn\"], [1, \"mx-1\", 3, \"src\"], [1, \"details__status\"], [1, \"details__status--icon\"], [1, \"details__status--text\"], [1, \"details__name\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-start\"], [4, \"ngIf\"], [1, \"details__payment\"], [1, \"col-xl-3\", \"col-lg-6\"], [1, \"details__time\"], [1, \"details__time--title\"], [1, \"details__time--num\"], [1, \"col-8\"], [1, \"details__title\"], [1, \"col-12\"], [1, \"internal_scroll_list_group\"], [1, \"container-fluid\", \"p-0\"], [\"class\", \" col-xl-6 col-lg-12 col-md-12 col-sm-12 mb-2 \", 4, \"ngFor\", \"ngForOf\"], [1, \"details__image\", 3, \"src\"], [3, \"rateChange\", \"rate\", \"max\", \"readonly\"], [1, \"star\"], [1, \"half\"], [1, \"col-xl-6\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [1, \"list-group-item\", \"d-flex\", \"user\", 3, \"click\"], [\"class\", \"user__image\", 3, \"src\", 4, \"ngIf\"], [1, \"teacher-stu-detail\", \"mx-1\"], [1, \"user__name\", 3, \"matTooltip\"], [1, \"title\"], [1, \"num\"], [1, \"user__image\", 3, \"src\"]],\n      template: function AdminTeacherProgramDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, AdminTeacherProgramDetailsComponent_img_4_Template, 1, 1, \"img\", 4)(5, AdminTeacherProgramDetailsComponent_img_5_Template, 1, 1, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"button\", 7);\n          i0.ɵɵelement(9, \"img\", 8);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9);\n          i0.ɵɵelement(13, \"span\", 10);\n          i0.ɵɵelementStart(14, \"h6\", 11);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"h2\", 12);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 13);\n          i0.ɵɵtemplate(20, AdminTeacherProgramDetailsComponent_ng_container_20_Template, 3, 3, \"ng-container\", 14);\n          i0.ɵɵelementStart(21, \"h4\", 15);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 2)(25, \"div\", 16)(26, \"div\", 17)(27, \"h4\", 18);\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\", 19);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"h4\", 18);\n          i0.ɵɵtext(35);\n          i0.ɵɵpipe(36, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 19);\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 16)(40, \"div\", 17)(41, \"h4\", 18);\n          i0.ɵɵtext(42);\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 19);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 16)(47, \"div\", 17)(48, \"h4\", 18);\n          i0.ɵɵtext(49);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 19);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(53, \"div\", 2)(54, \"div\", 20)(55, \"h2\", 21);\n          i0.ɵɵtext(56);\n          i0.ɵɵpipe(57, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 22)(59, \"div\", 23)(60, \"div\", 24)(61, \"div\", 2);\n          i0.ɵɵtemplate(62, AdminTeacherProgramDetailsComponent_div_62_Template, 21, 17, \"div\", 25);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.progPic));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.progPic);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.imagesPathesService.pause, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 18, \"PROGRAMS_LIST.KICK_OFF\"), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 20, \"GENERAL.ACTIVE\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.translate.currentLang === ctx.langEnum.en ? ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.nameEn : ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.nameAr, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.getTeacherProgramBatchDetailsResponse && ctx.getTeacherProgramBatchDetailsResponse.rate != null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 22, \"PROGRAMS_LIST.FREE\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(29, 24, \"PROGRAMS_LIST.PLANNED_WORK_HOURS\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.planHours);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(36, 26, \"PROGRAMS_LIST.ACTUAL_WORKING_HOURS\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.numHours);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(43, 28, \"PROGRAMS_LIST.number_of_apologies\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.numApologies);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(50, 30, \"PROGRAMS_LIST.NUMBER_OF_STUDENTS\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.numStudents);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(57, 32, \"PROGRAMS_LIST.HIS_STUDENTS\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getTeacherProgramBatchDetailsResponse == null ? null : ctx.getTeacherProgramBatchDetailsResponse.batchStuds);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatTooltip, i6.NgbRating, i4.DatePipe, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.list-group_program-list[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:85vh;margin-top:1rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(ar){height:57vh;overflow-y:auto;padding-left:.5rem}.list-group_program-list[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]:lang(en){height:57vh;overflow-y:auto;padding-right:.5rem}.list-group_program-list[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{cursor:pointer}.list-group_program-list[_ngcontent-%COMP%]:lang(en){margin-left:1rem;text-align:left}.list-group_program-list[_ngcontent-%COMP%]:lang(ar){margin-right:0rem;text-align:right}.list-group_program-list[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;letter-spacing:0;color:#333;opacity:1}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:.75rem;letter-spacing:0;color:gray;margin-bottom:1rem;opacity:1;margin-top:1rem;font-family:Almarai!important;font-weight:700;font-size:1rem;justify-content:space-between}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover{background:var(--second_color) 0% 0% no-repeat padding-box;color:#fff;opacity:1;text-decoration:none}.list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item.active[_ngcontent-%COMP%]   .fa-copy[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-trash-alt[_ngcontent-%COMP%], .list-group_program-list[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]:hover   .fa-copy[_ngcontent-%COMP%]{color:#fff}@media (min-width: 37.5rem) and (max-width: 63.938rem){.list-group_program-list[_ngcontent-%COMP%]{padding:.9rem}}.list-group[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border-radius:.75rem;opacity:1;width:50%;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.list-group[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{width:97%}.list-group[_ngcontent-%COMP%]   .advancedSearchForm[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{display:block;color:#d6d7d8;font-size:.875rem;font-weight:700}.list-group[_ngcontent-%COMP%]   .advancedSearchForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.list-group[_ngcontent-%COMP%]   .advancedSearchForm[_ngcontent-%COMP%]:lang(en){text-align:left}.list-group[_ngcontent-%COMP%]   .advancedSearchForm[_ngcontent-%COMP%]   .btn_replay[_ngcontent-%COMP%]{margin:0 .5rem}.list-group[_ngcontent-%COMP%]   .user[_ngcontent-%COMP%]{height:3.75rem;background:#fff;box-shadow:0 .188rem .625rem #fbfbfb;display:flex;align-items:center;justify-content:space-between;padding:.438rem;border-radius:.75rem;margin:.5rem 0;cursor:pointer}.list-group[_ngcontent-%COMP%]   .user__image[_ngcontent-%COMP%]{width:2.87rem;height:3.312rem;border-radius:.75rem}.list-group[_ngcontent-%COMP%]   .user__name[_ngcontent-%COMP%]{font-size:1rem;font-weight:700;padding:0;margin:0;color:#333}.list-group[_ngcontent-%COMP%]   .user[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%]{font-size:.75rem}.list-group[_ngcontent-%COMP%]   .user.active[_ngcontent-%COMP%]{background:var(--second_color)}.list-group[_ngcontent-%COMP%]   .user.active[_ngcontent-%COMP%]   .user__name[_ngcontent-%COMP%]{color:#fff}.list-group[_ngcontent-%COMP%]   .internal_scroll_list_group[_ngcontent-%COMP%]{height:70vh!important}.list-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{appearance:auto!important}.details[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:75vh;overflow-y:auto;margin-top:1rem}.details[_ngcontent-%COMP%]:lang(ar){text-align:right}.details[_ngcontent-%COMP%]:lang(en){text-align:left}.details__image[_ngcontent-%COMP%]{width:8.438rem;height:8.438rem;border-radius:.75rem}.details__status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.details__status--icon[_ngcontent-%COMP%]{width:.812rem;height:.812rem;background:#10af10;border-radius:50%;margin:0 .75rem}.details__status--text[_ngcontent-%COMP%]{font-size:.78rem;margin:0;color:#10af10}.details__name[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;margin:0}.details__name[_ngcontent-%COMP%]:lang(ar){text-align:right}.details__name[_ngcontent-%COMP%]:lang(en){text-align:left}.details__payment[_ngcontent-%COMP%]{font-size:.9rem;color:#d6d7d8;margin:0 1rem}.details__time[_ngcontent-%COMP%]{text-align:center;margin-top:2rem}.details__time--title[_ngcontent-%COMP%]{font-size:.75rem;color:#d6d7d8}.details__time--num[_ngcontent-%COMP%]{font-size:.93rem;color:var(--main_color);font-weight:700}.details__title[_ngcontent-%COMP%]{color:var(--main_color);font-size:1.2rem;border-bottom:.063rem solid rgba(242,241,241,.8705882353);margin-top:2.188rem;padding-bottom:.5rem}.details__title[_ngcontent-%COMP%]:lang(ar){text-align:right}.details__title[_ngcontent-%COMP%]:lang(en){text-align:left}.internal_scroll_list_group[_ngcontent-%COMP%]{overflow-y:auto;overflow-x:hidden;height:28vh!important}.internal_scroll_list_group[_ngcontent-%COMP%]   .user[_ngcontent-%COMP%]{height:5.5rem;padding:.438rem .625rem .625rem;border-radius:.313rem}.internal_scroll_list_group[_ngcontent-%COMP%]   .user__name[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:9rem;width:99%;font:500 1.25rem/1rem Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 .313rem;font-size:.9rem}.internal_scroll_list_group[_ngcontent-%COMP%]   .user__name[_ngcontent-%COMP%]:lang(en){text-align:left}.internal_scroll_list_group[_ngcontent-%COMP%]   .user__name[_ngcontent-%COMP%]:lang(ar){text-align:right}.internal_scroll_list_group[_ngcontent-%COMP%]   .user.active[_ngcontent-%COMP%]{background-color:var(--second_color)!important}.internal_scroll_list_group[_ngcontent-%COMP%]   .user.active[_ngcontent-%COMP%]   .teacher-stu-detail[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .internal_scroll_list_group[_ngcontent-%COMP%]   .user.active[_ngcontent-%COMP%]   .teacher-stu-detail[_ngcontent-%COMP%]   .num[_ngcontent-%COMP%]{color:#fff}.internal_scroll_list_group[_ngcontent-%COMP%]   .teacher-stu-detail[_ngcontent-%COMP%]{width:8rem}.internal_scroll_list_group[_ngcontent-%COMP%]   .teacher-stu-detail[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:.6rem;color:#333;margin:0}.internal_scroll_list_group[_ngcontent-%COMP%]   .teacher-stu-detail[_ngcontent-%COMP%]   .num[_ngcontent-%COMP%]{font-size:.6rem;color:#333;font-weight:700;margin:0}@media (max-width: 48rem){.user_tabs-btn[_ngcontent-%COMP%]{height:3.5rem}.details__time[_ngcontent-%COMP%]{text-align:center;margin-top:1rem;border-bottom:.2rem solid #666666}}\"]\n    });\n  }\n  return AdminTeacherProgramDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}