{"ast": null, "code": "export var DEFAULT_FONT_SIZE = 12;\nexport var DEFAULT_FONT_FAMILY = 'sans-serif';\nexport var DEFAULT_FONT = DEFAULT_FONT_SIZE + \"px \" + DEFAULT_FONT_FAMILY;\nvar OFFSET = 20;\nvar SCALE = 100;\nvar defaultWidthMapStr = \"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";\nfunction getTextWidthMap(mapStr) {\n  var map = {};\n  if (typeof JSON === 'undefined') {\n    return map;\n  }\n  for (var i = 0; i < mapStr.length; i++) {\n    var char = String.fromCharCode(i + 32);\n    var size = (mapStr.charCodeAt(i) - OFFSET) / SCALE;\n    map[char] = size;\n  }\n  return map;\n}\nexport var DEFAULT_TEXT_WIDTH_MAP = getTextWidthMap(defaultWidthMapStr);\nexport var platformApi = {\n  createCanvas: function () {\n    return typeof document !== 'undefined' && document.createElement('canvas');\n  },\n  measureText: function () {\n    var _ctx;\n    var _cachedFont;\n    return function (text, font) {\n      if (!_ctx) {\n        var canvas = platformApi.createCanvas();\n        _ctx = canvas && canvas.getContext('2d');\n      }\n      if (_ctx) {\n        if (_cachedFont !== font) {\n          _cachedFont = _ctx.font = font || DEFAULT_FONT;\n        }\n        return _ctx.measureText(text);\n      } else {\n        text = text || '';\n        font = font || DEFAULT_FONT;\n        var res = /((?:\\d+)?\\.?\\d*)px/.exec(font);\n        var fontSize = res && +res[1] || DEFAULT_FONT_SIZE;\n        var width = 0;\n        if (font.indexOf('mono') >= 0) {\n          width = fontSize * text.length;\n        } else {\n          for (var i = 0; i < text.length; i++) {\n            var preCalcWidth = DEFAULT_TEXT_WIDTH_MAP[text[i]];\n            width += preCalcWidth == null ? fontSize : preCalcWidth * fontSize;\n          }\n        }\n        return {\n          width: width\n        };\n      }\n    };\n  }(),\n  loadImage: function (src, onload, onerror) {\n    var image = new Image();\n    image.onload = onload;\n    image.onerror = onerror;\n    image.src = src;\n    return image;\n  }\n};\nexport function setPlatformAPI(newPlatformApis) {\n  for (var key in platformApi) {\n    if (newPlatformApis[key]) {\n      platformApi[key] = newPlatformApis[key];\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}