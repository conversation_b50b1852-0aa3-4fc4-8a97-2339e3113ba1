{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseSelectedDateModel } from 'src/app/core/ng-model/base-selected-date-model';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/calls-services/freeRecitation/free-recitation.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = () => ({\n  standalone: true\n});\nfunction ReservationAvailableAppointmentsComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"label\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_div_4_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.objectAddSchedualForm.availableTeachersResponse.techNameAr, $event) || (ctx_r1.objectAddSchedualForm.availableTeachersResponse.techNameAr = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"FREE_RECITATION.TEACHER_NAME\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.objectAddSchedualForm.availableTeachersResponse.techNameAr);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c0));\n  }\n}\nfunction ReservationAvailableAppointmentsComponent_ng_container_5_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 17);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(1, 1, \"SCIENTIFIC_MATERIAL.ALL\"));\n  }\n}\nfunction ReservationAvailableAppointmentsComponent_ng_container_5_input_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_ng_container_5_input_6_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.objectAddSchedualForm.studentProgramsStartedResponse.progName, $event) || (ctx_r1.objectAddSchedualForm.studentProgramsStartedResponse.progName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.objectAddSchedualForm.studentProgramsStartedResponse.progName);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction ReservationAvailableAppointmentsComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"label\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ReservationAvailableAppointmentsComponent_ng_container_5_input_5_Template, 2, 3, \"input\", 15)(6, ReservationAvailableAppointmentsComponent_ng_container_5_input_6_Template, 1, 3, \"input\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 3, \"FREE_RECITATION.PROGRAM\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.objectAddSchedualForm.studentProgramsStartedResponse.batId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.objectAddSchedualForm.studentProgramsStartedResponse.batId);\n  }\n}\nfunction ReservationAvailableAppointmentsComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-milady-hijri-calendar\", 18);\n    i0.ɵɵlistener(\"keypress\", function ReservationAvailableAppointmentsComponent_ng_container_10_Template_app_milady_hijri_calendar_keypress_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.preventDefault());\n    })(\"sendDate\", function ReservationAvailableAppointmentsComponent_ng_container_10_Template_app_milady_hijri_calendar_sendDate_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.SendDatafrom($event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_ng_container_10_Template_app_milady_hijri_calendar_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.addAppointmentCallRequest.date, $event) || (ctx_r1.addAppointmentCallRequest.date = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"editcalenderType\", ctx_r1.calenderType)(\"minGreg\", ctx_r1.minGregorianBatchDate)(\"dateTo\", ctx_r1.batchSubscriptionStartDateInputParam)(\"hijri\", true)(\"milady\", false);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.addAppointmentCallRequest.date);\n  }\n}\nfunction ReservationAvailableAppointmentsComponent_ng_container_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nexport let ReservationAvailableAppointmentsComponent = /*#__PURE__*/(() => {\n  class ReservationAvailableAppointmentsComponent {\n    translate;\n    freeRecitationService;\n    alertify;\n    dateFormatterService;\n    closeShedual = new EventEmitter();\n    objectAddSchedualForm;\n    addAppointmentCallRequest = {};\n    currentUser;\n    langEnum = LanguageEnum;\n    // \n    batchSubscriptionStartDateInputParam = {\n      year: 0,\n      day: 0,\n      month: 0\n    };\n    calenderType = new BaseSelectedDateModel();\n    selectedDateType;\n    maxGregDate = this.dateFormatterService.GetTodayGregorian();\n    typeDateBinding;\n    datafromBinding;\n    dataToBinding;\n    minGregorianBatchDate;\n    hijri = false;\n    milady = false;\n    // \n    resMessage = {};\n    constructor(translate, freeRecitationService, alertify, dateFormatterService) {\n      this.translate = translate;\n      this.freeRecitationService = freeRecitationService;\n      this.alertify = alertify;\n      this.dateFormatterService = dateFormatterService;\n    }\n    ngOnInit() {\n      this.setMilady();\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n    }\n    setMilady() {\n      let toDayTodayGregorian = this.dateFormatterService.GetTodayGregorian();\n      this.minGregorianBatchDate = toDayTodayGregorian;\n    }\n    updateStartDate(data) {\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n    }\n    closeForm() {\n      this.closeShedual.emit(false);\n    }\n    SendDatafrom(data) {\n      this.typeDateBinding = data.selectedDateType;\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.datafromBinding = data.selectedDateValue;\n      this.addAppointmentCallRequest.date = this.datafromBinding;\n      this.selectedDateType = data.selectedDateType;\n    }\n    submitForm() {\n      this.resMessage = {};\n      if (this.addAppointmentCallRequest.date && this.addAppointmentCallRequest.from && this.addAppointmentCallRequest.to && this.addAppointmentCallRequest.desc && this.addAppointmentCallRequest.fromPage && this.addAppointmentCallRequest.toPage) {\n        if (this.addAppointmentCallRequest.to > this.addAppointmentCallRequest.from) {\n          let model = {\n            studId: this.currentUser?.id,\n            techId: this.objectAddSchedualForm?.availableTeachersResponse?.usrId,\n            batId: this.objectAddSchedualForm?.studentProgramsStartedResponse?.batId,\n            date: this.addAppointmentCallRequest.date,\n            from: this.addAppointmentCallRequest.from,\n            to: this.addAppointmentCallRequest.to,\n            fromPage: this.addAppointmentCallRequest.fromPage,\n            toPage: this.addAppointmentCallRequest.toPage,\n            desc: this.addAppointmentCallRequest.desc\n          };\n          this.freeRecitationService.addAppointmentCallRequest(model).subscribe(res => {\n            if (res.isSuccess) {\n              this.closeForm();\n              this.alertify.success(res.message || '');\n            } else {\n              this.resMessage = {\n                message: res.message,\n                type: BaseConstantModel.DANGER_TYPE\n              };\n            }\n          }, error => {\n            //logging\n          });\n        } else {\n          this.resMessage = {\n            message: this.translate.instant('GENERAL.REQUIRED_VALIDATION'),\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FILDES_REQUIRED'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    static ɵfac = function ReservationAvailableAppointmentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReservationAvailableAppointmentsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.FreeRecitationService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.DateFormatterService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReservationAvailableAppointmentsComponent,\n      selectors: [[\"app-reservation-available-appointments\"]],\n      inputs: {\n        objectAddSchedualForm: \"objectAddSchedualForm\"\n      },\n      outputs: {\n        closeShedual: \"closeShedual\"\n      },\n      decls: 54,\n      vars: 39,\n      consts: [[1, \"form-group\", \"ShedualForm\"], [1, \"mb-3\", \"head\"], [\"class\", \"form-group\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"comment\"], [1, \"row\", \"w-100\"], [1, \"col-6\"], [\"type\", \"time\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"cols\", \"1\", \"rows\", \"5\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\"], [1, \"save-btn\", \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", 1, \"cancel-btn\", \"btn\", \"btn-danger\", 3, \"click\"], [\"disabled\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\", \"ngModelOptions\"], [\"class\", \"form-control\", \"disabled\", \"\", 3, \"value\", 4, \"ngIf\"], [\"class\", \"form-control\", \"disabled\", \"\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", 4, \"ngIf\"], [\"disabled\", \"\", 1, \"form-control\", 3, \"value\"], [3, \"keypress\", \"sendDate\", \"ngModelChange\", \"editcalenderType\", \"minGreg\", \"dateTo\", \"hijri\", \"milady\", \"ngModel\"]],\n      template: function ReservationAvailableAppointmentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"label\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ReservationAvailableAppointmentsComponent_div_4_Template, 5, 6, \"div\", 2)(5, ReservationAvailableAppointmentsComponent_ng_container_5_Template, 7, 5, \"ng-container\", 3);\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"label\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, ReservationAvailableAppointmentsComponent_ng_container_10_Template, 2, 6, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\")(12, \"label\");\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 6)(16, \"div\", 7)(17, \"div\", 4)(18, \"label\", 5);\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addAppointmentCallRequest.from, $event) || (ctx.addAppointmentCallRequest.from = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 7)(23, \"div\", 4)(24, \"label\", 5);\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_Template_input_ngModelChange_27_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addAppointmentCallRequest.to, $event) || (ctx.addAppointmentCallRequest.to = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 6)(29, \"div\", 7)(30, \"div\", 4)(31, \"label\", 5);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addAppointmentCallRequest.fromPage, $event) || (ctx.addAppointmentCallRequest.fromPage = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 7)(36, \"div\", 4)(37, \"label\", 5);\n          i0.ɵɵtext(38);\n          i0.ɵɵpipe(39, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_Template_input_ngModelChange_40_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addAppointmentCallRequest.toPage, $event) || (ctx.addAppointmentCallRequest.toPage = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 4)(42, \"label\", 5);\n          i0.ɵɵtext(43);\n          i0.ɵɵpipe(44, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"textarea\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReservationAvailableAppointmentsComponent_Template_textarea_ngModelChange_45_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addAppointmentCallRequest.desc, $event) || (ctx.addAppointmentCallRequest.desc = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, ReservationAvailableAppointmentsComponent_ng_container_46_Template, 3, 4, \"ng-container\", 3);\n          i0.ɵɵelementStart(47, \"div\", 11)(48, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ReservationAvailableAppointmentsComponent_Template_button_click_48_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵtext(49);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ReservationAvailableAppointmentsComponent_Template_button_click_51_listener() {\n            return ctx.closeForm();\n          });\n          i0.ɵɵtext(52);\n          i0.ɵɵpipe(53, \"translate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 19, \"PROGRAM_BATCHES.ADD_PROGRAM_BATCH\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.objectAddSchedualForm && ctx.objectAddSchedualForm.availableTeachersResponse);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.objectAddSchedualForm && ctx.objectAddSchedualForm.studentProgramsStartedResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 21, \"FREE_RECITATION.DAY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.addAppointmentCallRequest.date || ctx.addAppointmentCallRequest);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 23, \"GENERAL.TIME_ADAPTION\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 25, \"GENERAL.FROM\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addAppointmentCallRequest.from);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 27, \"GENERAL.TO\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addAppointmentCallRequest.to);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(33, 29, \"FREE_RECITATION.FROM_PAGE\"), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addAppointmentCallRequest.fromPage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(39, 31, \"FREE_RECITATION.TO_PAGE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addAppointmentCallRequest.toPage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(44, 33, \"FREE_RECITATION.DES\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addAppointmentCallRequest.desc);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resMessage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(50, 35, \"PROGRAM_BATCHES.SEND\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(53, 37, \"PROGRAM_BATCHES.CANCEL\"), \" \");\n        }\n      },\n      dependencies: [i5.NgIf, i6.DefaultValueAccessor, i6.NumberValueAccessor, i6.NgControlStatus, i6.NgModel, i1.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.ShedualForm[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border:.063rem solid #b3b3b3;opacity:1;padding:0rem 1rem 1rem}.ShedualForm[_ngcontent-%COMP%]:lang(ar){text-align:right}.ShedualForm[_ngcontent-%COMP%]:lang(en){text-align:left}.ShedualForm[_ngcontent-%COMP%]   .head[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:700}.ShedualForm[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{text-align:right;font-size:1rem;letter-spacing:0;color:#333;opacity:1}.ShedualForm[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .ShedualForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{width:7.5rem;margin:1rem;padding:.5rem;height:2.31rem}.ShedualForm[_ngcontent-%COMP%]   .dimmed[_ngcontent-%COMP%]{color:gray;cursor:not-allowed;text-decoration:underline;pointer-events:none;opacity:.2}\"]\n    });\n  }\n  return ReservationAvailableAppointmentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}