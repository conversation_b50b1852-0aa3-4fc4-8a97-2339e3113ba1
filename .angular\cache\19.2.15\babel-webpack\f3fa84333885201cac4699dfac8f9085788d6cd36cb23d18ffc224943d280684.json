{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ProgramDutyDaysTaskViewMoodEnum } from 'src/app/core/enums/programs/program-duty-days-task-view-mood-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/user-services/user.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nimport * as i5 from \"@angular/common\";\nfunction ViewExamAnswersComponent_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.profile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewExamAnswersComponent_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.proPic, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewExamAnswersComponent_div_1_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1, \"\\u2605\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fill_r3 = ctx.fill;\n    i0.ɵɵclassProp(\"filled\", fill_r3 === 100);\n  }\n}\nfunction ViewExamAnswersComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n    i0.ɵɵtemplate(5, ViewExamAnswersComponent_div_1_img_5_Template, 1, 1, \"img\", 6)(6, ViewExamAnswersComponent_div_1_img_6_Template, 1, 1, \"img\", 6);\n    i0.ɵɵelementStart(7, \"div\", 7)(8, \"h3\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ngb-rating\", 9);\n    i0.ɵɵtwoWayListener(\"rateChange\", function ViewExamAnswersComponent_div_1_Template_ngb_rating_rateChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.starsSelected, $event) || (ctx_r1.starsSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(11, ViewExamAnswersComponent_div_1_ng_template_11_Template, 2, 2, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h4\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"label\", 13);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \" \\u00A0 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \" \\u00A0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"label\");\n    i0.ɵɵelementStart(25, \"label\", 14);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 12)(28, \"label\", 13);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" \\u00A0 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \" \\u00A0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"label\");\n    i0.ɵɵelementStart(36, \"label\", 14);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(38, \"div\")(39, \"h6\", 15);\n    i0.ɵɵlistener(\"click\", function ViewExamAnswersComponent_div_1_Template_h6_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideViewExam());\n    });\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"translate\");\n    i0.ɵɵelement(42, \"i\", 16);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.proPic));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.proPic);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar ? (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.fnameAr) + \" \" + (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.mnameAr) + \" \" + (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.fanameAr) : (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.fnameEn) + \" \" + (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.mnameEn) + \" \" + (ctx_r1.studentProfileDetails == null ? null : ctx_r1.studentProfileDetails.faNameEn), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"rate\", ctx_r1.starsSelected);\n    i0.ɵɵproperty(\"max\", 5)(\"readonly\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 14, ctx_r1.studentProfileDetails.birthdate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 17, \"UPDATE_USER_PG.PROGRAMS\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang === ctx_r1.langEnum.ar ? ctx_r1.studentExamAnswer.stuProgSub.arProgBatName : ctx_r1.studentExamAnswer.stuProgSub.enProgBatName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 19, \"STUDENT_SUBSCRIBERS.REQUEST_RESULT\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.studentExamAnswer.stuProgSub.totalScore, \" / \", ctx_r1.studentExamAnswer.stuProgSub.stdGrade, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(41, 21, \"UPDATE_USER_PG.BACK\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.translate.currentLang === ctx_r1.langEnum.en ? \"fa-flip-horizontal\" : \"\");\n  }\n}\nfunction ViewExamAnswersComponent_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-question-template\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const question_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionTemplate\", question_r4)(\"viewMode\", true)(\"questionViewMood\", ctx_r1.questionViewMoodEnum);\n  }\n}\nfunction ViewExamAnswersComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ViewExamAnswersComponent_div_3_div_1_Template, 2, 3, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.studentExamAnswer.studentExamAnswer == null ? null : ctx_r1.studentExamAnswer.studentExamAnswer.answList);\n  }\n}\nexport let ViewExamAnswersComponent = /*#__PURE__*/(() => {\n  class ViewExamAnswersComponent {\n    studentProfileService;\n    translate;\n    alertify;\n    imagesPathesService;\n    hideViewExamEvent = new EventEmitter();\n    studentExamAnswer;\n    starsSelected = 5;\n    studentProfileDetails = {};\n    resMessage = {};\n    langEnum = LanguageEnum;\n    questionViewMoodEnum = ProgramDutyDaysTaskViewMoodEnum.student;\n    constructor(studentProfileService, translate, alertify, imagesPathesService) {\n      this.studentProfileService = studentProfileService;\n      this.translate = translate;\n      this.alertify = alertify;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.getStudentProfile();\n    }\n    getStudentProfile() {\n      this.studentProfileService.viewUserProfileDetails(this.studentExamAnswer?.stuProgSub?.usrId || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.studentProfileDetails = res.data;\n        } else {\n          this.alertify.error(res.message || \"\");\n        }\n      }, error => {\n        //logging\n      });\n    }\n    hideViewExam() {\n      this.hideViewExamEvent.emit(false);\n    }\n    static ɵfac = function ViewExamAnswersComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewExamAnswersComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AlertifyService), i0.ɵɵdirectiveInject(i4.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewExamAnswersComponent,\n      selectors: [[\"app-view-exam-answers\"]],\n      inputs: {\n        studentExamAnswer: \"studentExamAnswer\"\n      },\n      outputs: {\n        hideViewExamEvent: \"hideViewExamEvent\"\n      },\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"tab_page\", \"user\"], [4, \"ngIf\"], [1, \"col-md-12\", \"user__height\"], [1, \"info_basic_info\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-start\"], [\"class\", \"user__image\", 3, \"src\", 4, \"ngIf\"], [1, \"mr-3\", \"ml-3\"], [1, \"user__name\"], [3, \"rateChange\", \"rate\", \"max\", \"readonly\"], [1, \"user__date\"], [1, \"row\", \"mt-3\"], [1, \"col-12\"], [1, \"title\"], [1, \"title_data\"], [1, \"user__back\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 3, \"ngClass\"], [1, \"user__image\", 3, \"src\"], [1, \"star\"], [4, \"ngFor\", \"ngForOf\"], [3, \"questionTemplate\", \"viewMode\", \"questionViewMood\"]],\n      template: function ViewExamAnswersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ViewExamAnswersComponent_div_1_Template, 43, 23, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, ViewExamAnswersComponent_div_3_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.studentExamAnswer && ctx.studentExamAnswer.stuProgSub);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.studentExamAnswer && ctx.studentExamAnswer.studentExamAnswer);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, i5.DatePipe, TranslateModule, i2.TranslatePipe],\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}.tab_page[_ngcontent-%COMP%]{height:80vh;background:#fbfbfb;width:100%;border-radius:0 0 .75rem .75rem}.tab_page[_ngcontent-%COMP%]:lang(ar){text-align:right}.tab_page[_ngcontent-%COMP%]:lang(en){text-align:left}.tab_page[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{width:100%}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]{background-color:#fff;padding:1.5rem;color:#333;margin:1rem 1.8rem;border-radius:1.188rem;font-size:1rem}.tab_page[_ngcontent-%COMP%]   .info_basic_info[_ngcontent-%COMP%]   .trash[_ngcontent-%COMP%]{cursor:pointer}.tab_page[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:var(--second_color);font-size:.875rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .title_data[_ngcontent-%COMP%]{color:#4d4d4d;font-size:.875rem;font-weight:700}.tab_page[_ngcontent-%COMP%]   .head_conditions[_ngcontent-%COMP%]{width:50%;color:var(--main_color);font-size:1.125rem;font-weight:700;border-bottom:.063rem solid rgba(242,241,241,.8705882353)}.user__image[_ngcontent-%COMP%]{width:11.937rem;border-radius:.75rem;height:11.93rem}.user__name[_ngcontent-%COMP%]{font-weight:600;font-size:1.5rem;color:#333}.user__date[_ngcontent-%COMP%]{font-weight:600;font-size:1rem;color:#333}.user__back[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:var(--second_color);display:flex;align-items:center;justify-content:space-evenly;cursor:pointer}.user__back[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin:0 .313rem}.user__height[_ngcontent-%COMP%]{height:41vh;overflow-y:auto}.switch[_ngcontent-%COMP%]{position:relative;display:inline-block;width:2.5rem;height:1.25rem;margin-bottom:0!important}.switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{opacity:0;width:0;height:0}.slider[_ngcontent-%COMP%]{position:absolute;cursor:pointer;inset:0;background-color:#d6d7d8;transition:.4s}.slider[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";height:1rem;width:1rem;left:.188rem;bottom:.156rem;background-color:#fff;transition:.4s}input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]{background-color:var(--second_color)}input[_ngcontent-%COMP%]:focus + .slider[_ngcontent-%COMP%]{box-shadow:0 0 .063rem var(--second_color)}input[_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]:before{transform:translate(1.25rem)}.slider.round[_ngcontent-%COMP%]{border-radius:2.125rem}.slider.round[_ngcontent-%COMP%]:before{border-radius:50%}.p_header_sec[_ngcontent-%COMP%], .p_header[_ngcontent-%COMP%]{font-weight:700}.p_header[_ngcontent-%COMP%]{color:maroon;font-size:1rem}.p_header_sec[_ngcontent-%COMP%]{font-size:1.2rem;color:#333}.status[_ngcontent-%COMP%]{width:.625rem;height:.625rem;background:#10af10;border-radius:50%}.status[_ngcontent-%COMP%]:lang(ar){margin-right:.625rem;margin-left:1.25rem}.status[_ngcontent-%COMP%]:lang(ar){margin-right:1.25rem;margin-left:.625rem}.status-text[_ngcontent-%COMP%]{color:#10af10;font-size:1rem;margin:0}.label_info[_ngcontent-%COMP%], .data_view[_ngcontent-%COMP%], .label[_ngcontent-%COMP%]{color:#333;font-size:.875rem;font-weight:700}.tab_page_No_data[_ngcontent-%COMP%]{height:79vh}.user__height[_ngcontent-%COMP%]{height:32vh!important;overflow-y:auto}@media all and (min-width: 19in){.tab_page[_ngcontent-%COMP%]{height:81vh}}@media (max-width: 48rem){.tab_page[_ngcontent-%COMP%]{height:79vh}.trash[_ngcontent-%COMP%], .exportIcon[_ngcontent-%COMP%]{width:1.5rem;cursor:pointer}}.tab_page[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1.7rem;height:64vh;margin-top:1rem}.user__height[_ngcontent-%COMP%]{height:26vh!important;overflow-x:hidden}\"]\n    });\n  }\n  return ViewExamAnswersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}