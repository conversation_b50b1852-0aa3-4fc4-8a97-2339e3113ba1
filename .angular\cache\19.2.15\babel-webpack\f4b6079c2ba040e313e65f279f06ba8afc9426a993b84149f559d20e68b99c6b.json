{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { subPixelOptimize } from '../../util/graphic.js';\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { parsePercent } from '../../util/number.js';\nimport { map, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nvar candlestickLayout = {\n  seriesType: 'candlestick',\n  plan: createRenderPlanner(),\n  reset: function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var data = seriesModel.getData();\n    var candleWidth = calculateCandleWidth(seriesModel, data);\n    var cDimIdx = 0;\n    var vDimIdx = 1;\n    var coordDims = ['x', 'y'];\n    var cDimI = data.getDimensionIndex(data.mapDimension(coordDims[cDimIdx]));\n    var vDimsI = map(data.mapDimensionsAll(coordDims[vDimIdx]), data.getDimensionIndex, data);\n    var openDimI = vDimsI[0];\n    var closeDimI = vDimsI[1];\n    var lowestDimI = vDimsI[2];\n    var highestDimI = vDimsI[3];\n    data.setLayout({\n      candleWidth: candleWidth,\n      // The value is experimented visually.\n      isSimpleBox: candleWidth <= 1.3\n    });\n    if (cDimI < 0 || vDimsI.length < 4) {\n      return;\n    }\n    return {\n      progress: seriesModel.pipelineContext.large ? largeProgress : normalProgress\n    };\n    function normalProgress(params, data) {\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        var ocLow = Math.min(openVal, closeVal);\n        var ocHigh = Math.max(openVal, closeVal);\n        var ocLowPoint = getPoint(ocLow, axisDimVal);\n        var ocHighPoint = getPoint(ocHigh, axisDimVal);\n        var lowestPoint = getPoint(lowestVal, axisDimVal);\n        var highestPoint = getPoint(highestVal, axisDimVal);\n        var ends = [];\n        addBodyEnd(ends, ocHighPoint, 0);\n        addBodyEnd(ends, ocLowPoint, 1);\n        ends.push(subPixelOptimizePoint(highestPoint), subPixelOptimizePoint(ocHighPoint), subPixelOptimizePoint(lowestPoint), subPixelOptimizePoint(ocLowPoint));\n        var itemModel = data.getItemModel(dataIndex);\n        var hasDojiColor = !!itemModel.get(['itemStyle', 'borderColorDoji']);\n        data.setItemLayout(dataIndex, {\n          sign: getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor),\n          initBaseline: openVal > closeVal ? ocHighPoint[vDimIdx] : ocLowPoint[vDimIdx],\n          ends: ends,\n          brushRect: makeBrushRect(lowestVal, highestVal, axisDimVal)\n        });\n      }\n      function getPoint(val, axisDimVal) {\n        var p = [];\n        p[cDimIdx] = axisDimVal;\n        p[vDimIdx] = val;\n        return isNaN(axisDimVal) || isNaN(val) ? [NaN, NaN] : coordSys.dataToPoint(p);\n      }\n      function addBodyEnd(ends, point, start) {\n        var point1 = point.slice();\n        var point2 = point.slice();\n        point1[cDimIdx] = subPixelOptimize(point1[cDimIdx] + candleWidth / 2, 1, false);\n        point2[cDimIdx] = subPixelOptimize(point2[cDimIdx] - candleWidth / 2, 1, true);\n        start ? ends.push(point1, point2) : ends.push(point2, point1);\n      }\n      function makeBrushRect(lowestVal, highestVal, axisDimVal) {\n        var pmin = getPoint(lowestVal, axisDimVal);\n        var pmax = getPoint(highestVal, axisDimVal);\n        pmin[cDimIdx] -= candleWidth / 2;\n        pmax[cDimIdx] -= candleWidth / 2;\n        return {\n          x: pmin[0],\n          y: pmin[1],\n          width: vDimIdx ? candleWidth : pmax[0] - pmin[0],\n          height: vDimIdx ? pmax[1] - pmin[1] : candleWidth\n        };\n      }\n      function subPixelOptimizePoint(point) {\n        point[cDimIdx] = subPixelOptimize(point[cDimIdx], 1);\n        return point;\n      }\n    }\n    function largeProgress(params, data) {\n      // Structure: [sign, x, yhigh, ylow, sign, x, yhigh, ylow, ...]\n      var points = createFloat32Array(params.count * 4);\n      var offset = 0;\n      var point;\n      var tmpIn = [];\n      var tmpOut = [];\n      var dataIndex;\n      var store = data.getStore();\n      var hasDojiColor = !!seriesModel.get(['itemStyle', 'borderColorDoji']);\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        if (isNaN(axisDimVal) || isNaN(lowestVal) || isNaN(highestVal)) {\n          points[offset++] = NaN;\n          offset += 3;\n          continue;\n        }\n        points[offset++] = getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor);\n        tmpIn[cDimIdx] = axisDimVal;\n        tmpIn[vDimIdx] = lowestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[0] : NaN;\n        points[offset++] = point ? point[1] : NaN;\n        tmpIn[vDimIdx] = highestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[1] : NaN;\n      }\n      data.setLayout('largePoints', points);\n    }\n  }\n};\n/**\r\n * Get the sign of a single data.\r\n *\r\n * @returns 0 for doji with hasDojiColor: true,\r\n *          1 for positive,\r\n *          -1 for negative.\r\n */\nfunction getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor) {\n  var sign;\n  if (openVal > closeVal) {\n    sign = -1;\n  } else if (openVal < closeVal) {\n    sign = 1;\n  } else {\n    sign = hasDojiColor\n    // When doji color is set, use it instead of color/color0.\n    ? 0 : dataIndex > 0\n    // If close === open, compare with close of last record\n    ? store.get(closeDimI, dataIndex - 1) <= closeVal ? 1 : -1\n    // No record of previous, set to be positive\n    : 1;\n  }\n  return sign;\n}\nfunction calculateCandleWidth(seriesModel, data) {\n  var baseAxis = seriesModel.getBaseAxis();\n  var extent;\n  var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : (extent = baseAxis.getExtent(), Math.abs(extent[1] - extent[0]) / data.count());\n  var barMaxWidth = parsePercent(retrieve2(seriesModel.get('barMaxWidth'), bandWidth), bandWidth);\n  var barMinWidth = parsePercent(retrieve2(seriesModel.get('barMinWidth'), 1), bandWidth);\n  var barWidth = seriesModel.get('barWidth');\n  return barWidth != null ? parsePercent(barWidth, bandWidth)\n  // Put max outer to ensure bar visible in spite of overlap.\n  : Math.max(Math.min(bandWidth / 2, barMaxWidth), barMinWidth);\n}\nexport default candlestickLayout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}