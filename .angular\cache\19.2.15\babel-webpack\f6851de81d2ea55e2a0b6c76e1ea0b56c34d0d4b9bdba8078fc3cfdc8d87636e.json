{"ast": null, "code": "import { keys, map } from '../core/util.js';\nimport { encodeHTML } from '../core/dom.js';\nexport var SVGNS = 'http://www.w3.org/2000/svg';\nexport var XLINKNS = 'http://www.w3.org/1999/xlink';\nexport var XMLNS = 'http://www.w3.org/2000/xmlns/';\nexport var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace';\nexport var META_DATA_PREFIX = 'ecmeta_';\nexport function createElement(name) {\n  return document.createElementNS(SVGNS, name);\n}\n;\nexport function createVNode(tag, key, attrs, children, text) {\n  return {\n    tag: tag,\n    attrs: attrs || {},\n    children: children,\n    text: text,\n    key: key\n  };\n}\nfunction createElementOpen(name, attrs) {\n  var attrsStr = [];\n  if (attrs) {\n    for (var key in attrs) {\n      var val = attrs[key];\n      var part = key;\n      if (val === false) {\n        continue;\n      } else if (val !== true && val != null) {\n        part += \"=\\\"\" + val + \"\\\"\";\n      }\n      attrsStr.push(part);\n    }\n  }\n  return \"<\" + name + \" \" + attrsStr.join(' ') + \">\";\n}\nfunction createElementClose(name) {\n  return \"</\" + name + \">\";\n}\nexport function vNodeToString(el, opts) {\n  opts = opts || {};\n  var S = opts.newline ? '\\n' : '';\n  function convertElToString(el) {\n    var children = el.children,\n      tag = el.tag,\n      attrs = el.attrs,\n      text = el.text;\n    return createElementOpen(tag, attrs) + (tag !== 'style' ? encodeHTML(text) : text || '') + (children ? \"\" + S + map(children, function (child) {\n      return convertElToString(child);\n    }).join(S) + S : '') + createElementClose(tag);\n  }\n  return convertElToString(el);\n}\nexport function getCssString(selectorNodes, animationNodes, opts) {\n  opts = opts || {};\n  var S = opts.newline ? '\\n' : '';\n  var bracketBegin = \" {\" + S;\n  var bracketEnd = S + \"}\";\n  var selectors = map(keys(selectorNodes), function (className) {\n    return className + bracketBegin + map(keys(selectorNodes[className]), function (attrName) {\n      return attrName + \":\" + selectorNodes[className][attrName] + \";\";\n    }).join(S) + bracketEnd;\n  }).join(S);\n  var animations = map(keys(animationNodes), function (animationName) {\n    return \"@keyframes \" + animationName + bracketBegin + map(keys(animationNodes[animationName]), function (percent) {\n      return percent + bracketBegin + map(keys(animationNodes[animationName][percent]), function (attrName) {\n        var val = animationNodes[animationName][percent][attrName];\n        if (attrName === 'd') {\n          val = \"path(\\\"\" + val + \"\\\")\";\n        }\n        return attrName + \":\" + val + \";\";\n      }).join(S) + bracketEnd;\n    }).join(S) + bracketEnd;\n  }).join(S);\n  if (!selectors && !animations) {\n    return '';\n  }\n  return ['<![CDATA[', selectors, animations, ']]>'].join(S);\n}\nexport function createBrushScope(zrId) {\n  return {\n    zrId: zrId,\n    shadowCache: {},\n    patternCache: {},\n    gradientCache: {},\n    clipPathCache: {},\n    defs: {},\n    cssNodes: {},\n    cssAnims: {},\n    cssStyleCache: {},\n    cssAnimIdx: 0,\n    shadowIdx: 0,\n    gradientIdx: 0,\n    patternIdx: 0,\n    clipPathIdx: 0\n  };\n}\nexport function createSVGVNode(width, height, children, useViewBox) {\n  return createVNode('svg', 'root', {\n    'width': width,\n    'height': height,\n    'xmlns': SVGNS,\n    'xmlns:xlink': XLINKNS,\n    'version': '1.1',\n    'baseProfile': 'full',\n    'viewBox': useViewBox ? \"0 0 \" + width + \" \" + height : false\n  }, children);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}