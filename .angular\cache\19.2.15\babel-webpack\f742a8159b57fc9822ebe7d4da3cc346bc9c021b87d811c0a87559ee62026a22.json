{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport LineDraw from '../helper/LineDraw.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport adjustEdge from './adjustEdge.js';\nimport { getNodeGlobalScale } from './graphHelper.js';\nimport ChartView from '../../view/Chart.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { simpleLayoutEdge } from './simpleLayoutHelper.js';\nimport { circularLayout, rotateNodeLabel } from './circularLayoutHelper.js';\nfunction isViewCoordSys(coordSys) {\n  return coordSys.type === 'view';\n}\nvar GraphView = /** @class */function (_super) {\n  __extends(GraphView, _super);\n  function GraphView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphView.type;\n    return _this;\n  }\n  GraphView.prototype.init = function (ecModel, api) {\n    var symbolDraw = new SymbolDraw();\n    var lineDraw = new LineDraw();\n    var group = this.group;\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    group.add(symbolDraw.group);\n    group.add(lineDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineDraw = lineDraw;\n    this._firstRender = true;\n  };\n  GraphView.prototype.render = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var coordSys = seriesModel.coordinateSystem;\n    this._model = seriesModel;\n    var symbolDraw = this._symbolDraw;\n    var lineDraw = this._lineDraw;\n    var group = this.group;\n    if (isViewCoordSys(coordSys)) {\n      var groupNewProp = {\n        x: coordSys.x,\n        y: coordSys.y,\n        scaleX: coordSys.scaleX,\n        scaleY: coordSys.scaleY\n      };\n      if (this._firstRender) {\n        group.attr(groupNewProp);\n      } else {\n        graphic.updateProps(group, groupNewProp, seriesModel);\n      }\n    }\n    // Fix edge contact point with node\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    var data = seriesModel.getData();\n    symbolDraw.updateData(data);\n    var edgeData = seriesModel.getEdgeData();\n    // TODO: TYPE\n    lineDraw.updateData(edgeData);\n    this._updateNodeAndLinkScale();\n    this._updateController(seriesModel, ecModel, api);\n    clearTimeout(this._layoutTimeout);\n    var forceLayout = seriesModel.forceLayout;\n    var layoutAnimation = seriesModel.get(['force', 'layoutAnimation']);\n    if (forceLayout) {\n      this._startForceLayoutIteration(forceLayout, layoutAnimation);\n    }\n    var layout = seriesModel.get('layout');\n    data.graph.eachNode(function (node) {\n      var idx = node.dataIndex;\n      var el = node.getGraphicEl();\n      var itemModel = node.getModel();\n      if (!el) {\n        return;\n      }\n      // Update draggable\n      el.off('drag').off('dragend');\n      var draggable = itemModel.get('draggable');\n      if (draggable) {\n        el.on('drag', function (e) {\n          switch (layout) {\n            case 'force':\n              forceLayout.warmUp();\n              !_this._layouting && _this._startForceLayoutIteration(forceLayout, layoutAnimation);\n              forceLayout.setFixed(idx);\n              // Write position back to layout\n              data.setItemLayout(idx, [el.x, el.y]);\n              break;\n            case 'circular':\n              data.setItemLayout(idx, [el.x, el.y]);\n              // mark node fixed\n              node.setLayout({\n                fixed: true\n              }, true);\n              // recalculate circular layout\n              circularLayout(seriesModel, 'symbolSize', node, [e.offsetX, e.offsetY]);\n              _this.updateLayout(seriesModel);\n              break;\n            case 'none':\n            default:\n              data.setItemLayout(idx, [el.x, el.y]);\n              // update edge\n              simpleLayoutEdge(seriesModel.getGraph(), seriesModel);\n              _this.updateLayout(seriesModel);\n              break;\n          }\n        }).on('dragend', function () {\n          if (forceLayout) {\n            forceLayout.setUnfixed(idx);\n          }\n        });\n      }\n      el.setDraggable(draggable, !!itemModel.get('cursor'));\n      var focus = itemModel.get(['emphasis', 'focus']);\n      if (focus === 'adjacency') {\n        getECData(el).focus = node.getAdjacentDataIndices();\n      }\n    });\n    data.graph.eachEdge(function (edge) {\n      var el = edge.getGraphicEl();\n      var focus = edge.getModel().get(['emphasis', 'focus']);\n      if (!el) {\n        return;\n      }\n      if (focus === 'adjacency') {\n        getECData(el).focus = {\n          edge: [edge.dataIndex],\n          node: [edge.node1.dataIndex, edge.node2.dataIndex]\n        };\n      }\n    });\n    var circularRotateLabel = seriesModel.get('layout') === 'circular' && seriesModel.get(['circular', 'rotateLabel']);\n    var cx = data.getLayout('cx');\n    var cy = data.getLayout('cy');\n    data.graph.eachNode(function (node) {\n      rotateNodeLabel(node, circularRotateLabel, cx, cy);\n    });\n    this._firstRender = false;\n  };\n  GraphView.prototype.dispose = function () {\n    this.remove();\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  GraphView.prototype._startForceLayoutIteration = function (forceLayout, layoutAnimation) {\n    var self = this;\n    (function step() {\n      forceLayout.step(function (stopped) {\n        self.updateLayout(self._model);\n        (self._layouting = !stopped) && (layoutAnimation ? self._layoutTimeout = setTimeout(step, 16) : step());\n      });\n    })();\n  };\n  GraphView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    if (!isViewCoordSys(seriesModel.coordinateSystem)) {\n      controller.disable();\n      return;\n    }\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale();\n      adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n      _this._lineDraw.updateLayout();\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  GraphView.prototype._updateNodeAndLinkScale = function () {\n    var seriesModel = this._model;\n    var data = seriesModel.getData();\n    var nodeScale = getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el && el.setSymbolScale(nodeScale);\n    });\n  };\n  GraphView.prototype.updateLayout = function (seriesModel) {\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    this._symbolDraw.updateLayout();\n    this._lineDraw.updateLayout();\n  };\n  GraphView.prototype.remove = function () {\n    clearTimeout(this._layoutTimeout);\n    this._layouting = false;\n    this._layoutTimeout = null;\n    this._symbolDraw && this._symbolDraw.remove();\n    this._lineDraw && this._lineDraw.remove();\n  };\n  GraphView.type = 'graph';\n  return GraphView;\n}(ChartView);\nexport default GraphView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}