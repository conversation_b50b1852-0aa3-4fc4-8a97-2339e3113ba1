{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { ProgramSubscriptionUsersEnum } from 'src/app/core/enums/program-subscription-users-enum.enum';\nimport { TeacheProgramSubscriptionStatusEnum } from 'src/app/core/enums/teacher-subscription-enums/teache-program-subscription-status-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"src/app/core/services/teacher-program-subscription-services/teacher-program-subscription-services.service\";\nimport * as i3 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  item_group_active: a0\n});\nfunction TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-program-subscription-grid\", 12);\n    i0.ɵɵlistener(\"acceptTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptTeacherProgramSubscription($event));\n    })(\"acceptAllTeacherProgramSubscriptionCheched\", function TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptAllTeacherProgramSubscriptionCheched_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllTeachersCheckedProgramSubscription());\n    })(\"rejectTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template_app_program_subscription_grid_rejectTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectTeacherProgramSubscriptionEvent($event));\n    })(\"teacherFilterEvent\", function TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherPendingChangePage($event));\n    })(\"teacherIdFormGrid\", function TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherIdFormGrid_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendTeacherIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherItems\", ctx_r1.teacherProgramSubscriptionList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"userMode\", ctx_r1.teacherCard)(\"typeTeacheEnum\", ctx_r1.statusEnum.Pending)(\"totalCount\", ctx_r1.totalCount)(\"teacherFilterRequestModel\", ctx_r1.teacherProgramSubscriptionFilterRequestModel);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TeacherJionProgramTabRequestComponent_div_0_div_18_app_program_subscription_grid_1_Template, 1, 6, \"app-program-subscription-grid\", 11)(2, TeacherJionProgramTabRequestComponent_div_0_div_18_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length === 0);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-program-subscription-grid\", 12);\n    i0.ɵɵlistener(\"acceptTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptTeacherProgramSubscription($event));\n    })(\"acceptAllTeacherProgramSubscriptionCheched\", function TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptAllTeacherProgramSubscriptionCheched_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllTeachersCheckedProgramSubscription());\n    })(\"rejectTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template_app_program_subscription_grid_rejectTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectTeacherProgramSubscriptionEvent($event));\n    })(\"teacherFilterEvent\", function TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherAcceptChangePage($event));\n    })(\"teacherIdFormGrid\", function TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherIdFormGrid_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendTeacherIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherItems\", ctx_r1.teacherProgramSubscriptionList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"userMode\", ctx_r1.teacherCard)(\"typeTeacheEnum\", ctx_r1.statusEnum.Accept)(\"totalCount\", ctx_r1.totalCount)(\"teacherFilterRequestModel\", ctx_r1.teacherProgramSubscriptionFilterRequestModel);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TeacherJionProgramTabRequestComponent_div_0_div_19_app_program_subscription_grid_1_Template, 1, 6, \"app-program-subscription-grid\", 11)(2, TeacherJionProgramTabRequestComponent_div_0_div_19_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length === 0);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-program-subscription-grid\", 12);\n    i0.ɵɵlistener(\"acceptTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptTeacherProgramSubscription($event));\n    })(\"acceptAllTeacherProgramSubscriptionCheched\", function TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template_app_program_subscription_grid_acceptAllTeacherProgramSubscriptionCheched_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.acceptAllTeachersCheckedProgramSubscription());\n    })(\"rejectTeacherProgramSubscription\", function TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template_app_program_subscription_grid_rejectTeacherProgramSubscription_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.rejectTeacherProgramSubscriptionEvent($event));\n    })(\"teacherFilterEvent\", function TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherFilterEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.teacherRejectedChangePage($event));\n    })(\"teacherIdFormGrid\", function TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template_app_program_subscription_grid_teacherIdFormGrid_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendTeacherIDEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"teacherItems\", ctx_r1.teacherProgramSubscriptionList)(\"numberPerRow\", ctx_r1.numberItemsPerRow)(\"userMode\", ctx_r1.teacherCard)(\"typeTeacheEnum\", ctx_r1.statusEnum.Rejected)(\"totalCount\", ctx_r1.totalCount)(\"teacherFilterRequestModel\", ctx_r1.teacherProgramSubscriptionFilterRequestModel);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"GENERAL.NO_DATA\"), \" \");\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TeacherJionProgramTabRequestComponent_div_0_div_20_app_program_subscription_grid_1_Template, 1, 6, \"app-program-subscription-grid\", 11)(2, TeacherJionProgramTabRequestComponent_div_0_div_20_div_2_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.teacherProgramSubscriptionList.length === 0);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"app-search-input\", 4);\n    i0.ɵɵlistener(\"searchTerm\", function TeacherJionProgramTabRequestComponent_div_0_Template_app_search_input_searchTerm_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchByText($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function TeacherJionProgramTabRequestComponent_div_0_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAvancedSearch());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function TeacherJionProgramTabRequestComponent_div_0_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPendingChange());\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function TeacherJionProgramTabRequestComponent_div_0_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptChange());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function TeacherJionProgramTabRequestComponent_div_0_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRejectedChange());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 9);\n    i0.ɵɵtemplate(18, TeacherJionProgramTabRequestComponent_div_0_div_18_Template, 3, 2, \"div\", 10)(19, TeacherJionProgramTabRequestComponent_div_0_div_19_Template, 3, 2, \"div\", 10)(20, TeacherJionProgramTabRequestComponent_div_0_div_20_Template, 3, 2, \"div\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"searchKey\", ctx_r1.teacherProgramSubscriptionFilterRequestModel.usrName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 11, \"TEACHER_SUBSCRIBERS.ADVANCED_SEARCH\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Pending));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 13, \"TEACHER_SUBSCRIBERS.NEW_JOIN_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Accept));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 15, \"TEACHER_SUBSCRIBERS.ACCEPTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.showTap == ctx_r1.statusEnum.Rejected));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 17, \"TEACHER_SUBSCRIBERS.REJECTANCE_REQUESTS\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Pending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Accept);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTap == ctx_r1.statusEnum.Rejected);\n  }\n}\nfunction TeacherJionProgramTabRequestComponent_app_teacher_details_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-teacher-details-view\", 14);\n    i0.ɵɵlistener(\"hideUserDetails\", function TeacherJionProgramTabRequestComponent_app_teacher_details_view_1_Template_app_teacher_details_view_hideUserDetails_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideUserDetailsView($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resiveUserId\", ctx_r1.sendUserID);\n  }\n}\nexport let TeacherJionProgramTabRequestComponent = /*#__PURE__*/(() => {\n  class TeacherJionProgramTabRequestComponent {\n    translate;\n    teacherProgramSubscriptionServicesService;\n    alertify;\n    rejectTeacherProgramSubscription = new EventEmitter();\n    advancedSearchEvent = new EventEmitter();\n    closePopup = new EventEmitter();\n    teacherProgramSubscriptionList = [];\n    teacherProgramSubscriptionFilterRequestModel = {\n      statusNum: TeacheProgramSubscriptionStatusEnum.Pending,\n      skip: 0,\n      take: 9,\n      sortField: '',\n      sortOrder: 1,\n      page: 1\n    };\n    errorMessage;\n    totalCount = 0;\n    teacherCard = ProgramSubscriptionUsersEnum.teacher;\n    numberItemsPerRow = 3;\n    ids = [];\n    typeEnum = TeacheProgramSubscriptionStatusEnum.Pending;\n    showTap = TeacheProgramSubscriptionStatusEnum.Pending;\n    statusEnum = TeacheProgramSubscriptionStatusEnum;\n    sendUserID;\n    showUserDetailsView = false;\n    //teacherName:string='';\n    constructor(translate, teacherProgramSubscriptionServicesService, alertify) {\n      this.translate = translate;\n      this.teacherProgramSubscriptionServicesService = teacherProgramSubscriptionServicesService;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.teacherProgramSubscriptionFilterRequestModel.sortField = 'requestdate';\n      // this.teacherProgramSubscriptionFilterRequestModel.sortField = this.translate.currentLang === LanguageEnum.ar ? 'userNameAr' : 'UserNameEn'\n      this.getTeachersProgramsSubscriptions();\n    }\n    sendTeacherIDEvent(event) {\n      this.sendUserID = event;\n      this.showUserDetailsView = true;\n    }\n    hideUserDetailsView(event) {\n      this.showUserDetailsView = event;\n    }\n    searchByText(searchKey) {\n      this.teacherProgramSubscriptionFilterRequestModel.usrName = searchKey;\n      this.getTeachersProgramsSubscriptions();\n    }\n    getTeachersProgramsSubscriptions() {\n      this.teacherProgramSubscriptionServicesService.getTeachersProgramsSubscriptionsFilter(this.teacherProgramSubscriptionFilterRequestModel || {}).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.teacherProgramSubscriptionList = res.data;\n          // this.totalCount = this.teacherProgramSubscriptionList.length > 0 ? this.teacherProgramSubscriptionList[0].totalRows : 0;\n          this.teacherProgramSubscriptionList?.forEach(function (item) {\n            // item.requestDate = item.requestDate ? new Date(item.requestDate).toDateString(): '';\n          });\n          this.totalCount = res.count ? res.count : 0;\n          if (this.teacherProgramSubscriptionFilterRequestModel.skip > 0 && (!this.teacherProgramSubscriptionList || this.teacherProgramSubscriptionList.length === 0)) {\n            this.teacherProgramSubscriptionFilterRequestModel.page -= 1;\n            this.teacherProgramSubscriptionFilterRequestModel.skip = (this.teacherProgramSubscriptionFilterRequestModel.page - 1) * this.teacherProgramSubscriptionFilterRequestModel.take;\n            this.getTeachersProgramsSubscriptions();\n          }\n        } else {\n          this.errorMessage = response.message;\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onPendingChange() {\n      this.teacherProgramSubscriptionFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacheProgramSubscriptionStatusEnum.Pending,\n        skip: 0,\n        take: 9,\n        sortField: '',\n        sortOrder: 1,\n        page: 1\n      };\n      this.showTap = TeacheProgramSubscriptionStatusEnum.Pending;\n      this.closeAvancedSearch();\n      this.getTeachersProgramsSubscriptions();\n    }\n    onAcceptChange() {\n      this.teacherProgramSubscriptionFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacheProgramSubscriptionStatusEnum.Accept,\n        skip: 0,\n        take: 9,\n        sortField: '',\n        sortOrder: 1,\n        page: 1\n      };\n      this.teacherProgramSubscriptionFilterRequestModel.sortField = 'requestdate';\n      this.showTap = TeacheProgramSubscriptionStatusEnum.Accept;\n      this.closeAvancedSearch();\n      this.getTeachersProgramsSubscriptions();\n    }\n    onRejectedChange() {\n      this.teacherProgramSubscriptionFilterRequestModel = {\n        usrName: '',\n        statusNum: TeacheProgramSubscriptionStatusEnum.Rejected,\n        skip: 0,\n        take: 9,\n        sortField: '',\n        sortOrder: 1,\n        page: 1\n      };\n      this.teacherProgramSubscriptionFilterRequestModel.sortField = 'requestdate';\n      this.showTap = TeacheProgramSubscriptionStatusEnum.Rejected;\n      this.closeAvancedSearch();\n      this.getTeachersProgramsSubscriptions();\n    }\n    acceptTeacherProgramSubscription(teacherSubscripModel) {\n      this.ids?.push(teacherSubscripModel.id || '');\n      this.teacherProgramSubscriptionServicesService.teacherProgramSubscriptionsAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getTeachersProgramsSubscriptions();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    closeAvancedSearch() {\n      this.teacherProgramSubscriptionFilterRequestModel.usrName = '';\n      this.teacherProgramSubscriptionFilterRequestModel.progId = '';\n      this.teacherProgramSubscriptionFilterRequestModel.numberRequest = undefined;\n      this.teacherProgramSubscriptionFilterRequestModel.fromDate = undefined;\n      this.teacherProgramSubscriptionFilterRequestModel.toDate = undefined;\n      this.teacherProgramSubscriptionFilterRequestModel.skip = 0;\n      this.teacherProgramSubscriptionFilterRequestModel.take = 9;\n      this.teacherProgramSubscriptionFilterRequestModel.sortField = '';\n      this.teacherProgramSubscriptionFilterRequestModel.sortOrder = 1;\n      this.teacherProgramSubscriptionFilterRequestModel.page = 1;\n      this.closePopup.emit(); // as per issue number 3250\n    }\n    acceptAllTeachersCheckedProgramSubscription() {\n      this.ids = this.teacherProgramSubscriptionList?.filter(i => i.checked).map(a => a.id || '');\n      this.teacherProgramSubscriptionServicesService.teacherProgramSubscriptionsAcceptance(this.ids).subscribe(res => {\n        var response = res;\n        if (response.isSuccess) {\n          this.alertify.success(res.message || '');\n          this.getTeachersProgramsSubscriptions();\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    rejectTeacherProgramSubscriptionEvent(teacherSubscripModel) {\n      this.rejectTeacherProgramSubscription.emit(teacherSubscripModel);\n    }\n    teacherPendingChangePage(event) {\n      this.teacherProgramSubscriptionFilterRequestModel.statusNum = TeacheProgramSubscriptionStatusEnum.Pending;\n      this.teacherProgramSubscriptionFilterRequestModel = event;\n      this.getTeachersProgramsSubscriptions();\n    }\n    teacherAcceptChangePage(event) {\n      this.teacherProgramSubscriptionFilterRequestModel.statusNum = TeacheProgramSubscriptionStatusEnum.Accept;\n      this.teacherProgramSubscriptionFilterRequestModel = event;\n      this.getTeachersProgramsSubscriptions();\n    }\n    teacherRejectedChangePage(event) {\n      this.teacherProgramSubscriptionFilterRequestModel.statusNum = TeacheProgramSubscriptionStatusEnum.Rejected;\n      this.teacherProgramSubscriptionFilterRequestModel = event;\n      this.getTeachersProgramsSubscriptions();\n    }\n    openAvancedSearch() {\n      this.advancedSearchEvent.emit(this.teacherProgramSubscriptionFilterRequestModel);\n    }\n    advancedSearch(model) {\n      this.teacherProgramSubscriptionFilterRequestModel = model || {\n        skip: 0,\n        take: 9,\n        sortField: '',\n        sortOrder: 1,\n        page: 1\n      };\n      this.getTeachersProgramsSubscriptions();\n    }\n    static ɵfac = function TeacherJionProgramTabRequestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherJionProgramTabRequestComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.TeacherProgramSubscriptionServicesService), i0.ɵɵdirectiveInject(i3.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherJionProgramTabRequestComponent,\n      selectors: [[\"app-teacher-join-program-tab-request\"]],\n      outputs: {\n        rejectTeacherProgramSubscription: \"rejectTeacherProgramSubscription\",\n        advancedSearchEvent: \"advancedSearchEvent\",\n        closePopup: \"closePopup\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"container-fluid reuest_container\", 4, \"ngIf\"], [3, \"resiveUserId\", \"hideUserDetails\", 4, \"ngIf\"], [1, \"container-fluid\", \"reuest_container\"], [1, \"col-12\", \"justify-content-end\", \"text-right\", \"d-flex\", \"align-items-center\"], [3, \"searchTerm\", \"searchKey\"], [1, \"advanced_search\", \"mb-3\", \"mx-3\", 3, \"click\"], [1, \"row\", \"group_header\", \"fix_margin\", \"w-100\"], [1, \"item_group\", 3, \"click\", \"ngClass\"], [1, \"row\", \"fix_margin\", \"w-100\"], [1, \"col-12\", \"px-2\", \"interTab\", \"mb-3\", \"pt-3\"], [4, \"ngIf\"], [3, \"teacherItems\", \"numberPerRow\", \"userMode\", \"typeTeacheEnum\", \"totalCount\", \"teacherFilterRequestModel\", \"acceptTeacherProgramSubscription\", \"acceptAllTeacherProgramSubscriptionCheched\", \"rejectTeacherProgramSubscription\", \"teacherFilterEvent\", \"teacherIdFormGrid\", 4, \"ngIf\"], [3, \"acceptTeacherProgramSubscription\", \"acceptAllTeacherProgramSubscriptionCheched\", \"rejectTeacherProgramSubscription\", \"teacherFilterEvent\", \"teacherIdFormGrid\", \"teacherItems\", \"numberPerRow\", \"userMode\", \"typeTeacheEnum\", \"totalCount\", \"teacherFilterRequestModel\"], [1, \"No_data\"], [3, \"hideUserDetails\", \"resiveUserId\"]],\n      template: function TeacherJionProgramTabRequestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TeacherJionProgramTabRequestComponent_div_0_Template, 21, 25, \"div\", 0)(1, TeacherJionProgramTabRequestComponent_app_teacher_details_view_1_Template, 1, 1, \"app-teacher-details-view\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.showUserDetailsView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserDetailsView);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.TranslatePipe],\n      styles: [\".reuest_container[_ngcontent-%COMP%]{background:#fff 0% 0% no-repeat padding-box;border-radius:1.188rem;opacity:1;padding:1rem;height:74vh;margin-top:1rem}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]{border-bottom:.063rem solid var(--second_color)}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]{cursor:pointer;background-color:#e2e2e2;font-size:.9rem;color:gray;text-align:center;margin-left:.25rem;padding:.625rem 1.25rem;border-radius:.625rem .625rem 0 0}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group[_ngcontent-%COMP%]:lang(en){margin-left:0!important;margin-right:.25rem!important}.reuest_container[_ngcontent-%COMP%]   .group_header[_ngcontent-%COMP%]   .item_group_active[_ngcontent-%COMP%]{background-color:var(--second_color);color:#fff}.reuest_container[_ngcontent-%COMP%]   .fix_margin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.reuest_container[_ngcontent-%COMP%]   .interTab[_ngcontent-%COMP%]{background-color:#fbfbfb;height:55vh;min-height:30vh;overflow-y:auto;overflow-x:hidden;border-radius:0 0 .5rem}.reuest_container[_ngcontent-%COMP%]   .advanced_search[_ngcontent-%COMP%]{font-weight:700;font-size:.75rem;color:var(--second_color);letter-spacing:0;opacity:1;cursor:pointer}\"]\n    });\n  }\n  return TeacherJionProgramTabRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}