{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { eachAfter, eachBefore } from './traversalHelper.js';\nimport { init, firstWalk, secondWalk, separation as sep, radialCoordinate, getViewRect } from './layoutHelper.js';\nexport default function treeLayout(ecModel, api) {\n  ecModel.eachSeriesByType('tree', function (seriesModel) {\n    commonLayout(seriesModel, api);\n  });\n}\nfunction commonLayout(seriesModel, api) {\n  var layoutInfo = getViewRect(seriesModel, api);\n  seriesModel.layoutInfo = layoutInfo;\n  var layout = seriesModel.get('layout');\n  var width = 0;\n  var height = 0;\n  var separation = null;\n  if (layout === 'radial') {\n    width = 2 * Math.PI;\n    height = Math.min(layoutInfo.height, layoutInfo.width) / 2;\n    separation = sep(function (node1, node2) {\n      return (node1.parentNode === node2.parentNode ? 1 : 2) / node1.depth;\n    });\n  } else {\n    width = layoutInfo.width;\n    height = layoutInfo.height;\n    separation = sep();\n  }\n  var virtualRoot = seriesModel.getData().tree.root;\n  var realRoot = virtualRoot.children[0];\n  if (realRoot) {\n    init(virtualRoot);\n    eachAfter(realRoot, firstWalk, separation);\n    virtualRoot.hierNode.modifier = -realRoot.hierNode.prelim;\n    eachBefore(realRoot, secondWalk);\n    var left_1 = realRoot;\n    var right_1 = realRoot;\n    var bottom_1 = realRoot;\n    eachBefore(realRoot, function (node) {\n      var x = node.getLayout().x;\n      if (x < left_1.getLayout().x) {\n        left_1 = node;\n      }\n      if (x > right_1.getLayout().x) {\n        right_1 = node;\n      }\n      if (node.depth > bottom_1.depth) {\n        bottom_1 = node;\n      }\n    });\n    var delta = left_1 === right_1 ? 1 : separation(left_1, right_1) / 2;\n    var tx_1 = delta - left_1.getLayout().x;\n    var kx_1 = 0;\n    var ky_1 = 0;\n    var coorX_1 = 0;\n    var coorY_1 = 0;\n    if (layout === 'radial') {\n      kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n      // here we use (node.depth - 1), bucause the real root's depth is 1\n      ky_1 = height / (bottom_1.depth - 1 || 1);\n      eachBefore(realRoot, function (node) {\n        coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n        coorY_1 = (node.depth - 1) * ky_1;\n        var finalCoor = radialCoordinate(coorX_1, coorY_1);\n        node.setLayout({\n          x: finalCoor.x,\n          y: finalCoor.y,\n          rawX: coorX_1,\n          rawY: coorY_1\n        }, true);\n      });\n    } else {\n      var orient_1 = seriesModel.getOrient();\n      if (orient_1 === 'RL' || orient_1 === 'LR') {\n        ky_1 = height / (right_1.getLayout().x + delta + tx_1);\n        kx_1 = width / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorY_1 = (node.getLayout().x + tx_1) * ky_1;\n          coorX_1 = orient_1 === 'LR' ? (node.depth - 1) * kx_1 : width - (node.depth - 1) * kx_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      } else if (orient_1 === 'TB' || orient_1 === 'BT') {\n        kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n        ky_1 = height / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n          coorY_1 = orient_1 === 'TB' ? (node.depth - 1) * ky_1 : height - (node.depth - 1) * ky_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      }\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}