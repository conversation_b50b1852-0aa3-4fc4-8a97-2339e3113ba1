{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map, isFunction, createHashMap, noop, assert } from 'zrender/lib/core/util.js';\nimport { createTask } from './task.js';\nimport { getUID } from '../util/component.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport { normalizeToArray } from '../util/model.js';\n;\nvar Scheduler = /** @class */function () {\n  function Scheduler(ecInstance, api, dataProcessorHandlers, visualHandlers) {\n    // key: handlerUID\n    this._stageTaskMap = createHashMap();\n    this.ecInstance = ecInstance;\n    this.api = api;\n    // Fix current processors in case that in some rear cases that\n    // processors might be registered after echarts instance created.\n    // Register processors incrementally for a echarts instance is\n    // not supported by this stream architecture.\n    dataProcessorHandlers = this._dataProcessorHandlers = dataProcessorHandlers.slice();\n    visualHandlers = this._visualHandlers = visualHandlers.slice();\n    this._allHandlers = dataProcessorHandlers.concat(visualHandlers);\n  }\n  Scheduler.prototype.restoreData = function (ecModel, payload) {\n    // TODO: Only restore needed series and components, but not all components.\n    // Currently `restoreData` of all of the series and component will be called.\n    // But some independent components like `title`, `legend`, `graphic`, `toolbox`,\n    // `tooltip`, `axisPointer`, etc, do not need series refresh when `setOption`,\n    // and some components like coordinate system, axes, dataZoom, visualMap only\n    // need their target series refresh.\n    // (1) If we are implementing this feature some day, we should consider these cases:\n    // if a data processor depends on a component (e.g., dataZoomProcessor depends\n    // on the settings of `dataZoom`), it should be re-performed if the component\n    // is modified by `setOption`.\n    // (2) If a processor depends on sevral series, speicified by its `getTargetSeries`,\n    // it should be re-performed when the result array of `getTargetSeries` changed.\n    // We use `dependencies` to cover these issues.\n    // (3) How to update target series when coordinate system related components modified.\n    // TODO: simply the dirty mechanism? Check whether only the case here can set tasks dirty,\n    // and this case all of the tasks will be set as dirty.\n    ecModel.restoreData(payload);\n    // Theoretically an overall task not only depends on each of its target series, but also\n    // depends on all of the series.\n    // The overall task is not in pipeline, and `ecModel.restoreData` only set pipeline tasks\n    // dirty. If `getTargetSeries` of an overall task returns nothing, we should also ensure\n    // that the overall task is set as dirty and to be performed, otherwise it probably cause\n    // state chaos. So we have to set dirty of all of the overall tasks manually, otherwise it\n    // probably cause state chaos (consider `dataZoomProcessor`).\n    this._stageTaskMap.each(function (taskRecord) {\n      var overallTask = taskRecord.overallTask;\n      overallTask && overallTask.dirty();\n    });\n  };\n  // If seriesModel provided, incremental threshold is check by series data.\n  Scheduler.prototype.getPerformArgs = function (task, isBlock) {\n    // For overall task\n    if (!task.__pipeline) {\n      return;\n    }\n    var pipeline = this._pipelineMap.get(task.__pipeline.id);\n    var pCtx = pipeline.context;\n    var incremental = !isBlock && pipeline.progressiveEnabled && (!pCtx || pCtx.progressiveRender) && task.__idxInPipeline > pipeline.blockIndex;\n    var step = incremental ? pipeline.step : null;\n    var modDataCount = pCtx && pCtx.modDataCount;\n    var modBy = modDataCount != null ? Math.ceil(modDataCount / step) : null;\n    return {\n      step: step,\n      modBy: modBy,\n      modDataCount: modDataCount\n    };\n  };\n  Scheduler.prototype.getPipeline = function (pipelineId) {\n    return this._pipelineMap.get(pipelineId);\n  };\n  /**\r\n   * Current, progressive rendering starts from visual and layout.\r\n   * Always detect render mode in the same stage, avoiding that incorrect\r\n   * detection caused by data filtering.\r\n   * Caution:\r\n   * `updateStreamModes` use `seriesModel.getData()`.\r\n   */\n  Scheduler.prototype.updateStreamModes = function (seriesModel, view) {\n    var pipeline = this._pipelineMap.get(seriesModel.uid);\n    var data = seriesModel.getData();\n    var dataLen = data.count();\n    // `progressiveRender` means that can render progressively in each\n    // animation frame. Note that some types of series do not provide\n    // `view.incrementalPrepareRender` but support `chart.appendData`. We\n    // use the term `incremental` but not `progressive` to describe the\n    // case that `chart.appendData`.\n    var progressiveRender = pipeline.progressiveEnabled && view.incrementalPrepareRender && dataLen >= pipeline.threshold;\n    var large = seriesModel.get('large') && dataLen >= seriesModel.get('largeThreshold');\n    // TODO: modDataCount should not updated if `appendData`, otherwise cause whole repaint.\n    // see `test/candlestick-large3.html`\n    var modDataCount = seriesModel.get('progressiveChunkMode') === 'mod' ? dataLen : null;\n    seriesModel.pipelineContext = pipeline.context = {\n      progressiveRender: progressiveRender,\n      modDataCount: modDataCount,\n      large: large\n    };\n  };\n  Scheduler.prototype.restorePipelines = function (ecModel) {\n    var scheduler = this;\n    var pipelineMap = scheduler._pipelineMap = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var progressive = seriesModel.getProgressive();\n      var pipelineId = seriesModel.uid;\n      pipelineMap.set(pipelineId, {\n        id: pipelineId,\n        head: null,\n        tail: null,\n        threshold: seriesModel.getProgressiveThreshold(),\n        progressiveEnabled: progressive && !(seriesModel.preventIncremental && seriesModel.preventIncremental()),\n        blockIndex: -1,\n        step: Math.round(progressive || 700),\n        count: 0\n      });\n      scheduler._pipe(seriesModel, seriesModel.dataTask);\n    });\n  };\n  Scheduler.prototype.prepareStageTasks = function () {\n    var stageTaskMap = this._stageTaskMap;\n    var ecModel = this.api.getModel();\n    var api = this.api;\n    each(this._allHandlers, function (handler) {\n      var record = stageTaskMap.get(handler.uid) || stageTaskMap.set(handler.uid, {});\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        // Currently do not need to support to sepecify them both.\n        errMsg = '\"reset\" and \"overallReset\" must not be both specified.';\n      }\n      assert(!(handler.reset && handler.overallReset), errMsg);\n      handler.reset && this._createSeriesStageTask(handler, record, ecModel, api);\n      handler.overallReset && this._createOverallStageTask(handler, record, ecModel, api);\n    }, this);\n  };\n  Scheduler.prototype.prepareView = function (view, model, ecModel, api) {\n    var renderTask = view.renderTask;\n    var context = renderTask.context;\n    context.model = model;\n    context.ecModel = ecModel;\n    context.api = api;\n    renderTask.__block = !view.incrementalPrepareRender;\n    this._pipe(model, renderTask);\n  };\n  Scheduler.prototype.performDataProcessorTasks = function (ecModel, payload) {\n    // If we do not use `block` here, it should be considered when to update modes.\n    this._performStageTasks(this._dataProcessorHandlers, ecModel, payload, {\n      block: true\n    });\n  };\n  Scheduler.prototype.performVisualTasks = function (ecModel, payload, opt) {\n    this._performStageTasks(this._visualHandlers, ecModel, payload, opt);\n  };\n  Scheduler.prototype._performStageTasks = function (stageHandlers, ecModel, payload, opt) {\n    opt = opt || {};\n    var unfinished = false;\n    var scheduler = this;\n    each(stageHandlers, function (stageHandler, idx) {\n      if (opt.visualType && opt.visualType !== stageHandler.visualType) {\n        return;\n      }\n      var stageHandlerRecord = scheduler._stageTaskMap.get(stageHandler.uid);\n      var seriesTaskMap = stageHandlerRecord.seriesTaskMap;\n      var overallTask = stageHandlerRecord.overallTask;\n      if (overallTask) {\n        var overallNeedDirty_1;\n        var agentStubMap = overallTask.agentStubMap;\n        agentStubMap.each(function (stub) {\n          if (needSetDirty(opt, stub)) {\n            stub.dirty();\n            overallNeedDirty_1 = true;\n          }\n        });\n        overallNeedDirty_1 && overallTask.dirty();\n        scheduler.updatePayload(overallTask, payload);\n        var performArgs_1 = scheduler.getPerformArgs(overallTask, opt.block);\n        // Execute stubs firstly, which may set the overall task dirty,\n        // then execute the overall task. And stub will call seriesModel.setData,\n        // which ensures that in the overallTask seriesModel.getData() will not\n        // return incorrect data.\n        agentStubMap.each(function (stub) {\n          stub.perform(performArgs_1);\n        });\n        if (overallTask.perform(performArgs_1)) {\n          unfinished = true;\n        }\n      } else if (seriesTaskMap) {\n        seriesTaskMap.each(function (task, pipelineId) {\n          if (needSetDirty(opt, task)) {\n            task.dirty();\n          }\n          var performArgs = scheduler.getPerformArgs(task, opt.block);\n          // FIXME\n          // if intending to declare `performRawSeries` in handlers, only\n          // stream-independent (specifically, data item independent) operations can be\n          // performed. Because if a series is filtered, most of the tasks will not\n          // be performed. A stream-dependent operation probably cause wrong biz logic.\n          // Perhaps we should not provide a separate callback for this case instead\n          // of providing the config `performRawSeries`. The stream-dependent operations\n          // and stream-independent operations should better not be mixed.\n          performArgs.skip = !stageHandler.performRawSeries && ecModel.isSeriesFiltered(task.context.model);\n          scheduler.updatePayload(task, payload);\n          if (task.perform(performArgs)) {\n            unfinished = true;\n          }\n        });\n      }\n    });\n    function needSetDirty(opt, task) {\n      return opt.setDirty && (!opt.dirtyMap || opt.dirtyMap.get(task.__pipeline.id));\n    }\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.performSeriesTasks = function (ecModel) {\n    var unfinished;\n    ecModel.eachSeries(function (seriesModel) {\n      // Progress to the end for dataInit and dataRestore.\n      unfinished = seriesModel.dataTask.perform() || unfinished;\n    });\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.plan = function () {\n    // Travel pipelines, check block.\n    this._pipelineMap.each(function (pipeline) {\n      var task = pipeline.tail;\n      do {\n        if (task.__block) {\n          pipeline.blockIndex = task.__idxInPipeline;\n          break;\n        }\n        task = task.getUpstream();\n      } while (task);\n    });\n  };\n  Scheduler.prototype.updatePayload = function (task, payload) {\n    payload !== 'remain' && (task.context.payload = payload);\n  };\n  Scheduler.prototype._createSeriesStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var oldSeriesTaskMap = stageHandlerRecord.seriesTaskMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newSeriesTaskMap = stageHandlerRecord.seriesTaskMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    // If a stageHandler should cover all series, `createOnAllSeries` should be declared mandatorily,\n    // to avoid some typo or abuse. Otherwise if an extension do not specify a `seriesType`,\n    // it works but it may cause other irrelevant charts blocked.\n    if (stageHandler.createOnAllSeries) {\n      ecModel.eachRawSeries(create);\n    } else if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, create);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(create);\n    }\n    function create(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      // Init tasks for each seriesModel only once.\n      // Reuse original task instance.\n      var task = newSeriesTaskMap.set(pipelineId, oldSeriesTaskMap && oldSeriesTaskMap.get(pipelineId) || createTask({\n        plan: seriesTaskPlan,\n        reset: seriesTaskReset,\n        count: seriesTaskCount\n      }));\n      task.context = {\n        model: seriesModel,\n        ecModel: ecModel,\n        api: api,\n        // PENDING: `useClearVisual` not used?\n        useClearVisual: stageHandler.isVisual && !stageHandler.isLayout,\n        plan: stageHandler.plan,\n        reset: stageHandler.reset,\n        scheduler: scheduler\n      };\n      scheduler._pipe(seriesModel, task);\n    }\n  };\n  Scheduler.prototype._createOverallStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var overallTask = stageHandlerRecord.overallTask = stageHandlerRecord.overallTask\n    // For overall task, the function only be called on reset stage.\n    || createTask({\n      reset: overallTaskReset\n    });\n    overallTask.context = {\n      ecModel: ecModel,\n      api: api,\n      overallReset: stageHandler.overallReset,\n      scheduler: scheduler\n    };\n    var oldAgentStubMap = overallTask.agentStubMap;\n    // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n    var newAgentStubMap = overallTask.agentStubMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    var overallProgress = true;\n    var shouldOverallTaskDirty = false;\n    // FIXME:TS never used, so comment it\n    // let modifyOutputEnd = stageHandler.modifyOutputEnd;\n    // An overall task with seriesType detected or has `getTargetSeries`, we add\n    // stub in each pipelines, it will set the overall task dirty when the pipeline\n    // progress. Moreover, to avoid call the overall task each frame (too frequent),\n    // we set the pipeline block.\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '\"createOnAllSeries\" is not supported for \"overallReset\", ' + 'because it will block all streams.';\n    }\n    assert(!stageHandler.createOnAllSeries, errMsg);\n    if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, createStub);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(createStub);\n    }\n    // Otherwise, (usually it is legacy case), the overall task will only be\n    // executed when upstream is dirty. Otherwise the progressive rendering of all\n    // pipelines will be disabled unexpectedly. But it still needs stubs to receive\n    // dirty info from upstream.\n    else {\n      overallProgress = false;\n      each(ecModel.getSeries(), createStub);\n    }\n    function createStub(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      var stub = newAgentStubMap.set(pipelineId, oldAgentStubMap && oldAgentStubMap.get(pipelineId) || (\n      // When the result of `getTargetSeries` changed, the overallTask\n      // should be set as dirty and re-performed.\n      shouldOverallTaskDirty = true, createTask({\n        reset: stubReset,\n        onDirty: stubOnDirty\n      })));\n      stub.context = {\n        model: seriesModel,\n        overallProgress: overallProgress\n        // FIXME:TS never used, so comment it\n        // modifyOutputEnd: modifyOutputEnd\n      };\n      stub.agent = overallTask;\n      stub.__block = overallProgress;\n      scheduler._pipe(seriesModel, stub);\n    }\n    if (shouldOverallTaskDirty) {\n      overallTask.dirty();\n    }\n  };\n  Scheduler.prototype._pipe = function (seriesModel, task) {\n    var pipelineId = seriesModel.uid;\n    var pipeline = this._pipelineMap.get(pipelineId);\n    !pipeline.head && (pipeline.head = task);\n    pipeline.tail && pipeline.tail.pipe(task);\n    pipeline.tail = task;\n    task.__idxInPipeline = pipeline.count++;\n    task.__pipeline = pipeline;\n  };\n  Scheduler.wrapStageHandler = function (stageHandler, visualType) {\n    if (isFunction(stageHandler)) {\n      stageHandler = {\n        overallReset: stageHandler,\n        seriesType: detectSeriseType(stageHandler)\n      };\n    }\n    stageHandler.uid = getUID('stageHandler');\n    visualType && (stageHandler.visualType = visualType);\n    return stageHandler;\n  };\n  ;\n  return Scheduler;\n}();\nfunction overallTaskReset(context) {\n  context.overallReset(context.ecModel, context.api, context.payload);\n}\nfunction stubReset(context) {\n  return context.overallProgress && stubProgress;\n}\nfunction stubProgress() {\n  this.agent.dirty();\n  this.getDownstream().dirty();\n}\nfunction stubOnDirty() {\n  this.agent && this.agent.dirty();\n}\nfunction seriesTaskPlan(context) {\n  return context.plan ? context.plan(context.model, context.ecModel, context.api, context.payload) : null;\n}\nfunction seriesTaskReset(context) {\n  if (context.useClearVisual) {\n    context.data.clearAllVisual();\n  }\n  var resetDefines = context.resetDefines = normalizeToArray(context.reset(context.model, context.ecModel, context.api, context.payload));\n  return resetDefines.length > 1 ? map(resetDefines, function (v, idx) {\n    return makeSeriesTaskProgress(idx);\n  }) : singleSeriesTaskProgress;\n}\nvar singleSeriesTaskProgress = makeSeriesTaskProgress(0);\nfunction makeSeriesTaskProgress(resetDefineIdx) {\n  return function (params, context) {\n    var data = context.data;\n    var resetDefine = context.resetDefines[resetDefineIdx];\n    if (resetDefine && resetDefine.dataEach) {\n      for (var i = params.start; i < params.end; i++) {\n        resetDefine.dataEach(data, i);\n      }\n    } else if (resetDefine && resetDefine.progress) {\n      resetDefine.progress(params, data);\n    }\n  };\n}\nfunction seriesTaskCount(context) {\n  return context.data.count();\n}\n/**\r\n * Only some legacy stage handlers (usually in echarts extensions) are pure function.\r\n * To ensure that they can work normally, they should work in block mode, that is,\r\n * they should not be started util the previous tasks finished. So they cause the\r\n * progressive rendering disabled. We try to detect the series type, to narrow down\r\n * the block range to only the series type they concern, but not all series.\r\n */\nfunction detectSeriseType(legacyFunc) {\n  seriesType = null;\n  try {\n    // Assume there is no async when calling `eachSeriesByType`.\n    legacyFunc(ecModelMock, apiMock);\n  } catch (e) {}\n  return seriesType;\n}\nvar ecModelMock = {};\nvar apiMock = {};\nvar seriesType;\nmockMethods(ecModelMock, GlobalModel);\nmockMethods(apiMock, ExtensionAPI);\necModelMock.eachSeriesByType = ecModelMock.eachRawSeriesByType = function (type) {\n  seriesType = type;\n};\necModelMock.eachComponent = function (cond) {\n  if (cond.mainType === 'series' && cond.subType) {\n    seriesType = cond.subType;\n  }\n};\nfunction mockMethods(target, Clz) {\n  /* eslint-disable */\n  for (var name_1 in Clz.prototype) {\n    // Do not use hasOwnProperty\n    target[name_1] = noop;\n  }\n  /* eslint-enable */\n}\nexport default Scheduler;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}