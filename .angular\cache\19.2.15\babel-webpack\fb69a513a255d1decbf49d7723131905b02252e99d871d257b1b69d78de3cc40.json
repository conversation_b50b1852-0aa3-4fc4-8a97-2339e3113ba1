{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { programPredefinedConditionsEnum } from 'src/app/core/enums/programs/program-predefined-conditions-enum.enum';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/program-services/program-conditions.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"src/app/core/services/alertify-services/alertify.service\";\nimport * as i5 from \"@angular/common\";\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-qualifications\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-part-qraan\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-age\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-last-program\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-degree-last-program\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-maxmum-subscribe\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"app-setting-accept\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", item_r1);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewConditionSettingComponent_ng_container_9_ng_container_1_Template, 3, 1, \"ng-container\", 9)(2, ViewConditionSettingComponent_ng_container_9_ng_container_2_Template, 3, 1, \"ng-container\", 9)(3, ViewConditionSettingComponent_ng_container_9_ng_container_3_Template, 3, 1, \"ng-container\", 9)(4, ViewConditionSettingComponent_ng_container_9_ng_container_4_Template, 3, 1, \"ng-container\", 9)(5, ViewConditionSettingComponent_ng_container_9_ng_container_5_Template, 3, 1, \"ng-container\", 9)(6, ViewConditionSettingComponent_ng_container_9_ng_container_6_Template, 3, 1, \"ng-container\", 9)(7, ViewConditionSettingComponent_ng_container_9_ng_container_7_Template, 3, 1, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.qualifications === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.memorizeQuran === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.age === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.programFinished === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.dgreeaLastProgram === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.numberStudentSubscribtion === item_r1.no);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.programPredefinedEnum.accept === item_r1.no);\n  }\n}\nfunction ViewConditionSettingComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"app-custom-conditions\", 13);\n    i0.ɵɵlistener(\"editcustomConditionsCard\", function ViewConditionSettingComponent_ng_container_15_Template_app_custom_conditions_editcustomConditionsCard_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editCustomConditions($event));\n    })(\"deleteCustomConditionsCard\", function ViewConditionSettingComponent_ng_container_15_Template_app_custom_conditions_deleteCustomConditionsCard_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteCustomCard($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const customConditionsData_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customConditionsModel\", customConditionsData_r4);\n  }\n}\nexport let ViewConditionSettingComponent = /*#__PURE__*/(() => {\n  class ViewConditionSettingComponent {\n    programConditionsService;\n    dialog;\n    translate;\n    alertify;\n    addEditCoidition = new EventEmitter();\n    customConditionsList = [];\n    predefineConditionsList = [];\n    programPredefinedEnum = programPredefinedConditionsEnum;\n    getCustomConditionsList = [];\n    constructor(programConditionsService, dialog, translate, alertify) {\n      this.programConditionsService = programConditionsService;\n      this.dialog = dialog;\n      this.translate = translate;\n      this.alertify = alertify;\n    }\n    ngOnInit() {\n      this.getProgramConditionsLis();\n    }\n    getProgramConditionsLis() {\n      this.programConditionsService.getProgramConditionsList().subscribe(res => {\n        if (res.isSuccess) {\n          let allItems = res.data;\n          this.predefineConditionsList = allItems.filter(i => !i.isCustom);\n          this.customConditionsList = allItems.filter(i => i.isCustom);\n          this.customConditionsList.forEach(element => {\n            element.conditionModel = JSON.parse(element.conditionJson || \"{}\");\n          });\n          // this.alertify.success(res.message || '');\n        } else {\n          this.alertify.error(res.message || '');\n        }\n      }, error => {\n        //logging\n      });\n    }\n    AddConditions() {\n      this.addEditCoidition.emit(undefined);\n    }\n    editCustomConditions(event) {\n      this.addEditCoidition.emit(event);\n    }\n    // delete custom card\n    deleteCustomCard(id) {\n      const message = this.translate.currentLang === LanguageEnum.en ? \"Are you sure that you want to delete this custom conditions\" : \"هل متأكد من حذف هذا الشرط \";\n      const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete conditions' : 'حذف  الشرط', message);\n      const dialogRef = this.dialog.open(ConfirmModalComponent, {\n        maxWidth: \"25rem\",\n        data: dialogData\n      });\n      dialogRef.afterClosed().subscribe(dialogResult => {\n        if (dialogResult == true) {\n          this.programConditionsService.deleteProgramPredefinedCustomConditions(id || '').subscribe(res => {\n            if (res.isSuccess) {\n              this.alertify.success(res.message || '');\n              this.getProgramConditionsLis();\n            } else {\n              this.alertify.error(res.message || '');\n            }\n          }, error => {\n            //logging\n          });\n        }\n      });\n    }\n    static ɵfac = function ViewConditionSettingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewConditionSettingComponent)(i0.ɵɵdirectiveInject(i1.ProgramConditionsService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AlertifyService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewConditionSettingComponent,\n      selectors: [[\"app-view-condition-setting\"]],\n      inputs: {\n        customConditionsList: \"customConditionsList\"\n      },\n      outputs: {\n        addEditCoidition: \"addEditCoidition\"\n      },\n      decls: 16,\n      vars: 8,\n      consts: [[1, \"tab_page\", \"notifacation_page\", \"condition_details\"], [1, \"pt-3\", \"mb-3\"], [1, \"row\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"container_haeder\", \"mb-2\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"max_h\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"mt-4\", \"mb-4\", \"title\"], [1, \"title_custom\"], [4, \"ngIf\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-6\", \"col-xs-12\", \"mb-3\"], [3, \"title\"], [1, \"col-4\", \"mb-3\"], [3, \"editcustomConditionsCard\", \"deleteCustomConditionsCard\", \"customConditionsModel\"]],\n      template: function ViewConditionSettingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ViewConditionSettingComponent_Template_button_click_4_listener() {\n            return ctx.AddConditions();\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 2);\n          i0.ɵɵtemplate(9, ViewConditionSettingComponent_ng_container_9_Template, 8, 7, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"p\", 8);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 2);\n          i0.ɵɵtemplate(15, ViewConditionSettingComponent_ng_container_15_Template, 3, 1, \"ng-container\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"CONDITIONDS.ADD_CONDITIONS\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.predefineConditionsList);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 6, \"CONDITIONDS.CUSTOM_CONDITIONDS\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customConditionsList);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, TranslateModule, i3.TranslatePipe],\n      styles: [\".condition_details.tab_page[_ngcontent-%COMP%]{background:#fbfbfb;width:100%;height:85vh;margin-top:1rem;border-radius:.313rem;padding:1rem}.condition_details[_ngcontent-%COMP%]   .max_h[_ngcontent-%COMP%]{min-height:68vh;max-height:68vh;overflow-y:auto}.condition_details[_ngcontent-%COMP%]   .container_haeder[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center}.condition_details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:8.4375rem;padding:.5rem;border:none;color:#fff;display:block}.condition_details[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{border-bottom:.063rem solid rgba(0,0,0,.1)}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]{color:var(--main_color);font-size:1.25rem;font-weight:700}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]:lang(en){text-align:left}.condition_details[_ngcontent-%COMP%]   .title_custom[_ngcontent-%COMP%]:lang(ar){text-align:right}@media (max-width: 48rem){.conditionForm[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{height:3rem}}\"]\n    });\n  }\n  return ViewConditionSettingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}