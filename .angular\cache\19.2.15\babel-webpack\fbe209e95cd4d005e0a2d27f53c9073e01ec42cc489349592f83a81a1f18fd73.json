{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieve, defaults, extend, each, isObject, map, isString, isNumber, isFunction, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { prepareLayoutList, hideOverlap } from '../../label/labelLayoutHelper.js';\nvar PI = Math.PI;\n/**\r\n * A final axis is translated and rotated from a \"standard axis\".\r\n * So opt.position and opt.rotation is required.\r\n *\r\n * A standard axis is and axis from [0, 0] to [0, axisExtent[1]],\r\n * for example: (0, 0) ------------> (0, 50)\r\n *\r\n * nameDirection or tickDirection or labelDirection is 1 means tick\r\n * or label is below the standard axis, whereas is -1 means above\r\n * the standard axis. labelOffset means offset between label and axis,\r\n * which is useful when 'onZero', where axisLabel is in the grid and\r\n * label in outside grid.\r\n *\r\n * Tips: like always,\r\n * positive rotation represents anticlockwise, and negative rotation\r\n * represents clockwise.\r\n * The direction of position coordinate is the same as the direction\r\n * of screen coordinate.\r\n *\r\n * Do not need to consider axis 'inverse', which is auto processed by\r\n * axis extent.\r\n */\nvar AxisBuilder = /** @class */function () {\n  function AxisBuilder(axisModel, opt) {\n    this.group = new graphic.Group();\n    this.opt = opt;\n    this.axisModel = axisModel;\n    // Default value\n    defaults(opt, {\n      labelOffset: 0,\n      nameDirection: 1,\n      tickDirection: 1,\n      labelDirection: 1,\n      silent: true,\n      handleAutoShown: function () {\n        return true;\n      }\n    });\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: opt.position[0],\n      y: opt.position[1],\n      rotation: opt.rotation\n    });\n    // this.group.add(transformGroup);\n    // this._transformGroup = transformGroup;\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n  }\n  AxisBuilder.prototype.hasBuilder = function (name) {\n    return !!builders[name];\n  };\n  AxisBuilder.prototype.add = function (name) {\n    builders[name](this.opt, this.axisModel, this.group, this._transformGroup);\n  };\n  AxisBuilder.prototype.getGroup = function () {\n    return this.group;\n  };\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\nvar builders = {\n  axisLine: function (opt, axisModel, group, transformGroup) {\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto' && opt.handleAutoShown) {\n      shown = opt.handleAutoShown('axisLine');\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var line = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: lineStyle,\n      strokeContainThreshold: opt.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1\n    });\n    graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n    line.anid = 'line';\n    group.add(line);\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: opt.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: opt.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(opt.rotation),\n            y: pt[1] - r * Math.sin(opt.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  axisTickLabel: function (opt, axisModel, group, transformGroup) {\n    var ticksEls = buildAxisMajorTicks(group, transformGroup, axisModel, opt);\n    var labelEls = buildAxisLabel(group, transformGroup, axisModel, opt);\n    fixMinMaxLabelShow(axisModel, labelEls, ticksEls);\n    buildAxisMinorTicks(group, transformGroup, axisModel, opt.tickDirection);\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    if (axisModel.get(['axisLabel', 'hideOverlap'])) {\n      var labelList = prepareLayoutList(map(labelEls, function (label) {\n        return {\n          label: label,\n          priority: label.z2,\n          defaultAttr: {\n            ignore: label.ignore\n          }\n        };\n      }));\n      hideOverlap(labelList);\n    }\n  },\n  axisName: function (opt, axisModel, group, transformGroup) {\n    var name = retrieve(opt.axisName, axisModel.get('name'));\n    if (!name) {\n      return;\n    }\n    var nameLocation = axisModel.get('nameLocation');\n    var nameDirection = opt.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapSignal = extent[0] > extent[1] ? -1 : 1;\n    var pos = [nameLocation === 'start' ? extent[0] - gapSignal * gap : nameLocation === 'end' ? extent[1] + gapSignal * gap : (extent[0] + extent[1]) / 2,\n    // Reuse labelOffset.\n    isNameLocationCenter(nameLocation) ? opt.labelOffset + nameDirection * gap : 0];\n    var labelLayout;\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(opt.rotation, nameRotation != null ? nameRotation : opt.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(opt.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = opt.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(opt.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var textEl = new graphic.Text({\n      x: pos[0],\n      y: pos[1],\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    group.add(textEl);\n    textEl.decomposeTransform();\n  }\n};\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\nfunction fixMinMaxLabelShow(axisModel, labelEls, tickEls) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  labelEls = labelEls || [];\n  tickEls = tickEls || [];\n  var firstLabel = labelEls[0];\n  var nextLabel = labelEls[1];\n  var lastLabel = labelEls[labelEls.length - 1];\n  var prevLabel = labelEls[labelEls.length - 2];\n  var firstTick = tickEls[0];\n  var nextTick = tickEls[1];\n  var lastTick = tickEls[tickEls.length - 1];\n  var prevTick = tickEls[tickEls.length - 2];\n  if (showMinLabel === false) {\n    ignoreEl(firstLabel);\n    ignoreEl(firstTick);\n  } else if (isTwoLabelOverlapped(firstLabel, nextLabel)) {\n    if (showMinLabel) {\n      ignoreEl(nextLabel);\n      ignoreEl(nextTick);\n    } else {\n      ignoreEl(firstLabel);\n      ignoreEl(firstTick);\n    }\n  }\n  if (showMaxLabel === false) {\n    ignoreEl(lastLabel);\n    ignoreEl(lastTick);\n  } else if (isTwoLabelOverlapped(prevLabel, lastLabel)) {\n    if (showMaxLabel) {\n      ignoreEl(prevLabel);\n      ignoreEl(prevTick);\n    } else {\n      ignoreEl(lastLabel);\n      ignoreEl(lastTick);\n    }\n  }\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction isTwoLabelOverlapped(current, next) {\n  // current and next has the same rotation.\n  var firstRect = current && current.getBoundingRect().clone();\n  var nextRect = next && next.getBoundingRect().clone();\n  if (!firstRect || !nextRect) {\n    return;\n  }\n  // When checking intersect of two rotated labels, we use mRotationBack\n  // to avoid that boundingRect is enlarge when using `boundingRect.applyTransform`.\n  var mRotationBack = matrixUtil.identity([]);\n  matrixUtil.rotate(mRotationBack, mRotationBack, -current.rotation);\n  firstRect.applyTransform(matrixUtil.mul([], mRotationBack, current.getLocalTransform()));\n  nextRect.applyTransform(matrixUtil.mul([], mRotationBack, next.getLocalTransform()));\n  return firstRect.intersect(nextRect);\n}\nfunction isNameLocationCenter(nameLocation) {\n  return nameLocation === 'middle' || nameLocation === 'center';\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto' && opt.handleAutoShown) {\n    shown = opt.handleAutoShown('axisTick');\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return;\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = opt.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!minorTickModel.get('show') || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\nfunction buildAxisLabel(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var show = retrieve(opt.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  if (!show || axis.scale.isBlank()) {\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labelMargin = labelModel.get('margin');\n  var labels = axis.getViewLabels();\n  // Special label rotate.\n  var labelRotation = (retrieve(opt.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(opt.rotation, labelRotation, opt.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var silent = AxisBuilder.isLabelSilent(axisModel);\n  var triggerEvent = axisModel.get('triggerEvent');\n  each(labels, function (labelItem, index) {\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var tickCoord = axis.dataToCoord(tickValue);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var textEl = new graphic.Text({\n      x: tickCoord,\n      y: opt.labelOffset + opt.labelDirection * labelMargin,\n      rotation: labelLayout.rotation,\n      silent: silent,\n      z2: 10 + (labelItem.level || 0),\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    labelEls.push(textEl);\n    group.add(textEl);\n    textEl.decomposeTransform();\n  });\n  return labelEls;\n}\nexport default AxisBuilder;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}