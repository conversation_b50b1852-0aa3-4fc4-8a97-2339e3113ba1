{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nvar CartesianAxisPointer = /** @class */function (_super) {\n  __extends(CartesianAxisPointer, _super);\n  function CartesianAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisPointerType = axisPointerModel.get('type');\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var pixelValue = axis.toGlobalCoord(axis.dataToCoord(value, true));\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = cartesianAxisHelper.layout(grid.model, axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = cartesianAxisHelper.layout(axisModel.axis.grid.model, axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var pos = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: pos[0],\n      y: pos[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisExtent = axis.getGlobalExtent(true);\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var dimIndex = axis.dim === 'x' ? 0 : 1;\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    // Make tooltip do not overlap axisPointer and in the middle of the grid.\n    var tooltipOptions = [{\n      verticalAlign: 'middle'\n    }, {\n      align: 'center'\n    }];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: tooltipOptions[dimIndex]\n    };\n  };\n  return CartesianAxisPointer;\n}(BaseAxisPointer);\nfunction getCartesian(grid, axis) {\n  var opt = {};\n  opt[axis.dim + 'AxisIndex'] = axis.index;\n  return grid.getCartesian(opt);\n}\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getAxisDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getAxisDimIndex(axis))\n    };\n  }\n};\nfunction getAxisDimIndex(axis) {\n  return axis.dim === 'x' ? 0 : 1;\n}\nexport default CartesianAxisPointer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}