{"ast": null, "code": "import { TeacherRequestEnum } from 'src/app/core/enums/teacher-subscription-enums/teacher-request-enum.enum';\nimport { AdminTeacherCallComponent } from './admin-teacher-call/admin-teacher-call.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction TeacherProgramRequestViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"app-teacher-list-request\", 6);\n    i0.ɵɵlistener(\"selectedTeatcherRequest\", function TeacherProgramRequestViewComponent_div_1_Template_app_teacher_list_request_selectedTeatcherRequest_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendTeatcherRequest($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"app-teacher-request-details\", 8);\n    i0.ɵɵlistener(\"adminTaecherCallEvent\", function TeacherProgramRequestViewComponent_div_1_Template_app_teacher_request_details_adminTaecherCallEvent_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.adminCallToTeacher($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"selectedTeatcherRequest\", ctx_r1.selectedTeatcherRequest);\n  }\n}\nfunction TeacherProgramRequestViewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"app-admin-teacher-call\", 10);\n    i0.ɵɵlistener(\"endCallEvent\", function TeacherProgramRequestViewComponent_div_3_Template_app_admin_teacher_call_endCallEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.endCall($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"adminTaecherCallEvent\", ctx_r1.adminTaecherCallEvent);\n  }\n}\nexport let TeacherProgramRequestViewComponent = /*#__PURE__*/(() => {\n  class TeacherProgramRequestViewComponent {\n    adminTaecherCallEvent;\n    selectedTeatcherRequest = TeacherRequestEnum.JoinRequest;\n    adminTeacherCall;\n    interviewCall = false;\n    constructor() {}\n    ngOnInit() {}\n    sendTeatcherRequest(teatcherRequestItem) {\n      this.selectedTeatcherRequest = teatcherRequestItem;\n    }\n    adminCallToTeacher(event) {\n      this.interviewCall = true;\n      this.adminTaecherCallEvent = event;\n    }\n    endCall(event) {\n      this.interviewCall = event;\n    }\n    static ɵfac = function TeacherProgramRequestViewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TeacherProgramRequestViewComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherProgramRequestViewComponent,\n      selectors: [[\"app-teacher-program-request-view\"]],\n      viewQuery: function TeacherProgramRequestViewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AdminTeacherCallComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.adminTeacherCall = _t.first);\n        }\n      },\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"container-fluid\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"container-fluid\", \"px-0\"], [\"class\", \"row px-0 \", 4, \"ngIf\"], [1, \"row\"], [1, \"col-lg-3\", \"col-md-3\", \"col-sm-12\", \"px-0\"], [3, \"selectedTeatcherRequest\"], [1, \"col-lg-9\", \"col-md-9\", \"col-sm-12\", \"pl-0\"], [3, \"adminTaecherCallEvent\", \"selectedTeatcherRequest\"], [1, \"row\", \"px-0\"], [3, \"endCallEvent\", \"adminTaecherCallEvent\"]],\n      template: function TeacherProgramRequestViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TeacherProgramRequestViewComponent_div_1_Template, 5, 1, \"div\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, TeacherProgramRequestViewComponent_div_3_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.interviewCall);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.interviewCall);\n        }\n      },\n      dependencies: [i1.NgIf],\n      encapsulation: 2\n    });\n  }\n  return TeacherProgramRequestViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}