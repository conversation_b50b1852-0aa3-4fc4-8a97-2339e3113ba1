{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/data/Projects/Mostaneer/huffadh-white-label-app - Copy/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormGroup, Validators } from '@angular/forms';\nimport { LanguageEnum } from 'src/app/core/enums/language-enum.enum';\nimport { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';\nimport { BaseSelectedDateModel } from '../../../../core/ng-model/base-selected-date-model';\nimport { DateType } from 'ngx-hijri-gregorian-datepicker';\nimport { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';\nimport { RoleEnum } from 'src/app/core/enums/role-enum.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/services/lookup-services/lookup.service\";\nimport * as i3 from \"src/app/core/services/user-services/user.service\";\nimport * as i4 from \"src/app/core/services/attachments-services/attachments.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"src/app/core/services/language-services/language.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/core/services/auth-services/auth.service\";\nimport * as i9 from \"ngx-hijri-gregorian-datepicker\";\nimport * as i10 from \"src/app/core/services/chat-services/chat.service\";\nimport * as i11 from \"@angular/router\";\nimport * as i12 from \"src/app/core/services/images-pathes-services/images-pathes.service\";\nfunction UpdateUserProfileComponent_div_0_div_18_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_18_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_18_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_18_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_18_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_18_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_18_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameAr.errors == null ? null : ctx_r1.f.firstNameAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameAr.errors == null ? null : ctx_r1.f.firstNameAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameAr.errors == null ? null : ctx_r1.f.firstNameAr.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 56);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_18_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FIRST_NAME\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameAr.errors && (ctx_r1.f.firstNameAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_19_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_19_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_19_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_19_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_19_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_19_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_19_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameAr.errors == null ? null : ctx_r1.f.middleNameAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameAr.errors == null ? null : ctx_r1.f.middleNameAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameAr.errors == null ? null : ctx_r1.f.middleNameAr.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 60);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_19_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.FATHER_NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FATHER_NAME_AR\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameAr.errors && (ctx_r1.f.middleNameAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_20_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_AR_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_20_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_AR_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_20_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_AR_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_20_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_20_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_20_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_20_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameAr.errors == null ? null : ctx_r1.f.familyNameAr.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameAr.errors == null ? null : ctx_r1.f.familyNameAr.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameAr.errors == null ? null : ctx_r1.f.familyNameAr.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 62);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_20_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.FAMILY_NAME_AR\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FAMILY_NAME_AR\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameAr.errors && (ctx_r1.f.familyNameAr.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_21_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_21_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_21_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_21_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_21_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_21_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_21_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameEn.errors == null ? null : ctx_r1.f.firstNameEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameEn.errors == null ? null : ctx_r1.f.firstNameEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameEn.errors == null ? null : ctx_r1.f.firstNameEn.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 64);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_21_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FIRST_NAME\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.firstNameEn.errors && (ctx_r1.f.firstNameEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_22_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_22_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_22_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FATHER_NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_22_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_22_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_22_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_22_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameEn.errors == null ? null : ctx_r1.f.middleNameEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameEn.errors == null ? null : ctx_r1.f.middleNameEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameEn.errors == null ? null : ctx_r1.f.middleNameEn.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 66);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_22_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.FATHER_NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FATHER_NAME_EN\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.middleNameEn.errors && (ctx_r1.f.middleNameEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_23_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_EN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_23_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_EN_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_23_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.FAMILY_NAME_EN_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_23_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_23_div_6_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_23_div_6_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_23_div_6_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameEn.errors == null ? null : ctx_r1.f.familyNameEn.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameEn.errors == null ? null : ctx_r1.f.familyNameEn.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameEn.errors == null ? null : ctx_r1.f.familyNameEn.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"label\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 69);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, UpdateUserProfileComponent_div_0_div_23_div_6_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"UPDATE_USER_PG.FAMILY_NAME_EN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 5, \"UPDATE_TEACHER_PG.FAMILY_NAME_EN\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.familyNameEn.errors && (ctx_r1.f.familyNameEn.touched || ctx_r1.isSubmit));\n  }\n}\nfunction UpdateUserProfileComponent_div_0_app_milady_hijri_calendar_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-milady-hijri-calendar\", 70);\n    i0.ɵɵlistener(\"sendDate\", function UpdateUserProfileComponent_div_0_app_milady_hijri_calendar_29_Template_app_milady_hijri_calendar_sendDate_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.Hijri($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"dateTo\", ctx_r1.hijriBirthDateInputParam)(\"hijri\", ctx_r1.hijri)(\"milady\", ctx_r1.milady)(\"maxHijri\", ctx_r1.maxHijriDate)(\"maxGreg\", ctx_r1.maxGregDate)(\"minGreg\", ctx_r1.minGregDate)(\"minHijri\", ctx_r1.minHijriDate)(\"editcalenderType\", ctx_r1.updateCalenderType);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.BIRTHDATE_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_30_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hijriBinding);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_mat_radio_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? item_r5.nameEn : item_r5.nameAr, \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_TEACHER_PG.GENDER_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_39_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.gender.errors.required);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_51_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.PHONE_NUMBER_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_51_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.phoneNumber.errors.required);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_option_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const nationalityItem_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", nationalityItem_r6.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? nationalityItem_r6.nameEn : nationalityItem_r6.nameAr, \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_68_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.NATIONALITY_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_68_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.nationality.errors.required);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_75_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.ADDRESS_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_75_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.ADDRESS_MAX_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_75_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.ADDRESS_MIN_LENGTH\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_75_div_1_Template, 3, 3, \"div\", 58)(2, UpdateUserProfileComponent_div_0_div_75_div_2_Template, 3, 3, \"div\", 58)(3, UpdateUserProfileComponent_div_0_div_75_div_3_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors == null ? null : ctx_r1.f.address.errors.maxlength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors == null ? null : ctx_r1.f.address.errors.minlength);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_option_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const educationalLevelItem_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", educationalLevelItem_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.translate.currentLang == ctx_r1.langEnum.en ? educationalLevelItem_r7.nameEn : educationalLevelItem_r7.nameAr, \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_91_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.EDU_LEVEL_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_91_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.educationallevel.errors.required);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_option_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    i0.ɵɵproperty(\"value\", i_r8 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i_r8 + 1, \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_101_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"UPDATE_USER_PG.AMOUNT_QURAN_REQUIRED\"), \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, UpdateUserProfileComponent_div_0_div_101_div_1_Template, 3, 3, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.quraanMemorization.errors.pattern);\n  }\n}\nfunction UpdateUserProfileComponent_div_0_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx_r1.resMessage.type, \" my-4 py-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resMessage.message, \" \");\n  }\n}\nfunction UpdateUserProfileComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function UpdateUserProfileComponent_div_0_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit(ctx_r1.profileForm.value));\n    });\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵlistener(\"dragover\", function UpdateUserProfileComponent_div_0_Template_div_dragover_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragOver($event));\n    })(\"drop\", function UpdateUserProfileComponent_div_0_Template_div_drop_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDropSuccess($event));\n    });\n    i0.ɵɵelement(5, \"div\", 7)(6, \"img\", 8);\n    i0.ɵɵelementStart(7, \"input\", 9, 0);\n    i0.ɵɵlistener(\"change\", function UpdateUserProfileComponent_div_0_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function UpdateUserProfileComponent_div_0_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const fileInput_r3 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(fileInput_r3.click());\n    });\n    i0.ɵɵelement(10, \"img\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"div\", 13)(13, \"label\", 14);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"div\", 16);\n    i0.ɵɵtemplate(18, UpdateUserProfileComponent_div_0_div_18_Template, 7, 7, \"div\", 17)(19, UpdateUserProfileComponent_div_0_div_19_Template, 7, 7, \"div\", 17)(20, UpdateUserProfileComponent_div_0_div_20_Template, 7, 7, \"div\", 17)(21, UpdateUserProfileComponent_div_0_div_21_Template, 7, 7, \"div\", 17)(22, UpdateUserProfileComponent_div_0_div_22_Template, 7, 7, \"div\", 17)(23, UpdateUserProfileComponent_div_0_div_23_Template, 7, 7, \"div\", 18);\n    i0.ɵɵelementStart(24, \"div\", 19)(25, \"div\", 20)(26, \"label\", 21);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, UpdateUserProfileComponent_div_0_app_milady_hijri_calendar_29_Template, 1, 8, \"app-milady-hijri-calendar\", 22)(30, UpdateUserProfileComponent_div_0_div_30_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 24)(32, \"label\", 25);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 3)(36, \"section\", 26)(37, \"mat-radio-group\", 27);\n    i0.ɵɵtemplate(38, UpdateUserProfileComponent_div_0_mat_radio_button_38_Template, 2, 2, \"mat-radio-button\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(39, UpdateUserProfileComponent_div_0_div_39_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(40, \"div\", 29)(41, \"label\", 30);\n    i0.ɵɵtext(42);\n    i0.ɵɵpipe(43, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 31)(45, \"div\", 16)(46, \"div\", 32)(47, \"label\", 33);\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"app-tel-input\", 34);\n    i0.ɵɵlistener(\"getPhonNumber\", function UpdateUserProfileComponent_div_0_Template_app_tel_input_getPhonNumber_50_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyPhoneNumber($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, UpdateUserProfileComponent_div_0_div_51_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 35)(53, \"label\", 36);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"translate\");\n    i0.ɵɵpipe(56, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 35)(59, \"label\", 38);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\")(63, \"select\", 39)(64, \"option\", 40);\n    i0.ɵɵtext(65);\n    i0.ɵɵpipe(66, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(67, UpdateUserProfileComponent_div_0_option_67_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(68, UpdateUserProfileComponent_div_0_div_68_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 42)(70, \"label\", 43);\n    i0.ɵɵtext(71);\n    i0.ɵɵpipe(72, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"input\", 44);\n    i0.ɵɵpipe(74, \"translate\");\n    i0.ɵɵtemplate(75, UpdateUserProfileComponent_div_0_div_75_Template, 4, 3, \"div\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 29)(77, \"label\", 30);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 31)(81, \"div\", 16)(82, \"div\", 45)(83, \"label\", 46);\n    i0.ɵɵtext(84);\n    i0.ɵɵpipe(85, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"select\", 47)(87, \"option\", 40);\n    i0.ɵɵtext(88);\n    i0.ɵɵpipe(89, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(90, UpdateUserProfileComponent_div_0_option_90_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(91, UpdateUserProfileComponent_div_0_div_91_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"div\", 48)(93, \"label\", 49);\n    i0.ɵɵtext(94);\n    i0.ɵɵpipe(95, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"select\", 50)(97, \"option\", 51);\n    i0.ɵɵtext(98);\n    i0.ɵɵpipe(99, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(100, UpdateUserProfileComponent_div_0_option_100_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(101, UpdateUserProfileComponent_div_0_div_101_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(102, \"div\", 52)(103, \"button\", 53);\n    i0.ɵɵtext(104);\n    i0.ɵɵpipe(105, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(106, UpdateUserProfileComponent_div_0_div_106_Template, 2, 4, \"div\", 54);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.userProfileDetails == null ? null : ctx_r1.userProfileDetails.proPic, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.imagesPathesService.upload_image_white, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 43, \"UPDATE_USER_PG.PERSONAL_INFORMATION\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.ar);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.translate.currentLang == ctx_r1.langEnum.en);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 45, \"UPDATE_USER_PG.BIRTHDATE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hijriBirthDateInputParam && ctx_r1.updateCalenderType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hijriBinding && (ctx_r1.f.birthdate.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(34, 47, \"GENERAL.GENDER\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.GENDER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.gender.errors && (ctx_r1.f.gender.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(43, 49, \"UPDATE_USER_PG.CONTACT_INFO\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(49, 51, \"GENERAL.PHONE_NUMBER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"telInputParam\", ctx_r1.telInputParam);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.phoneNumber.errors && (ctx_r1.f.phoneNumber.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(55, 53, \"GENERAL.EMAIL\"), \" (\", i0.ɵɵpipeBind1(56, 55, \"UPDATE_USER_PG.OPTIONAL\"), \")\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(61, 57, \"GENERAL.NATIONALITY\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(66, 59, \"UPDATE_TEACHER_PG.SELECT_NATIONALITY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.NATIONALITY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.nationality.errors && (ctx_r1.f.nationality.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(72, 61, \"UPDATE_USER_PG.ADDRESS\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(74, 63, \"UPDATE_TEACHER_PG.SELECT_ADDRESS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.address.errors && (ctx_r1.f.address.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(79, 65, \"UPDATE_USER_PG.EDU\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(85, 67, \"UPDATE_USER_PG.EDU_LEVEL\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(89, 69, \"UPDATE_TEACHER_PG.SELECT_EDU_LEVEL\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.collectionOfLookup.EDU_LEVEL);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.educationallevel.errors && (ctx_r1.f.educationallevel.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(95, 71, \"UPDATE_USER_PG.AMOUNT_QURAN\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(99, 73, \"UPDATE_USER_PG.INPUT_AMOUNT_QURAN\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quraanParts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.quraanMemorization.errors && (ctx_r1.f.quraanMemorization.touched || ctx_r1.isSubmit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(105, 75, \"GENERAL.SAVE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage.message);\n  }\n}\nexport let UpdateUserProfileComponent = /*#__PURE__*/(() => {\n  class UpdateUserProfileComponent {\n    fb;\n    lookupService;\n    userService;\n    attachmentService;\n    userProfileService;\n    translate;\n    languageService;\n    dialog;\n    authService;\n    dateFormatterService;\n    chatService;\n    router;\n    imagesPathesService;\n    profileForm = new FormGroup({});\n    completeUserProfile = false;\n    updateUserProfile = false;\n    resMessage = {};\n    currentUser;\n    isSubmit = false;\n    listOfLookupProfile = ['GENDER', 'EDU_LEVEL', 'NATIONALITY', 'COUNTRY', 'SCIENTIFIC_ARCHIVES', 'TRAINING_COURSES', 'SYSTEM_SHEIKHS'];\n    userProfileDetails;\n    updateUserModel = {};\n    collectionOfLookup = {};\n    fileUploadModel = [];\n    fileList = [];\n    ejazaAttachmentIds = [];\n    quraanParts = new Array(30);\n    selectedShiekhsList = Array();\n    selectedArchivesList = Array();\n    archiveMessage = {};\n    shiekhsMessage = {};\n    selectedTrainingCourseList = Array();\n    coursesMessage = {};\n    langEnum = LanguageEnum;\n    telInputParam = {};\n    hijriBinding;\n    hijri = false;\n    milady = false;\n    hijriBirthDateInputParam; //= {year: 0, day: 0, month: 0};\n    // = {\n    //   // phoneNumber:'+201062100486',\n    //   isRequired : true,\n    //   // countryIsoCode: '{\"initialCountry\": \"eg\"}'\n    // }\n    selectedDateType;\n    updateCalenderType = new BaseSelectedDateModel();\n    maxHijriDate;\n    maxGregDate;\n    participantModel;\n    minGregDate;\n    minHijriDate;\n    constructor(fb, lookupService, userService, attachmentService, userProfileService, translate, languageService, dialog, authService, dateFormatterService, chatService, router, imagesPathesService) {\n      this.fb = fb;\n      this.lookupService = lookupService;\n      this.userService = userService;\n      this.attachmentService = attachmentService;\n      this.userProfileService = userProfileService;\n      this.translate = translate;\n      this.languageService = languageService;\n      this.dialog = dialog;\n      this.authService = authService;\n      this.dateFormatterService = dateFormatterService;\n      this.chatService = chatService;\n      this.router = router;\n      this.imagesPathesService = imagesPathesService;\n    }\n    ngOnInit() {\n      this.setScssImages();\n      this.currentUser = JSON.parse(localStorage.getItem(\"user\"));\n      this.chatService.getParticipantById(this.currentUser?.id || '');\n      this.getCountryIsoCode();\n      this.setCurrentLang();\n      this.buildForm();\n      this.lookupService.getLookupByKey(this.listOfLookupProfile).subscribe(res => {\n        this.collectionOfLookup = res.data;\n        if (res.isSuccess) {\n          this.getUserProfile(this.currentUser?.id);\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n      this.setHijri();\n      this.setGreg();\n    }\n    setScssImages() {\n      this.imagesPathesService.setBackgroundPannerInStyle();\n    }\n    // @HostListener('window:beforeunload', ['$event'])\n    // public onPageUnload($event: BeforeUnloadEvent) {\n    //   if (this.unsavedDataCheck()) {\n    //     $event.returnValue = true;\n    //     // return \"message\";\n    //   }\n    //   else{\n    //     $event.returnValue = false;\n    //     // return '';\n    //   }\n    // }\n    // @HostListener('window:popstate', ['$event'])\n    // onPopState(event:any) {\n    //   this.userService.setCanDeActivate(this.unsavedDataCheck());\n    // }\n    unsavedDataCheck() {\n      let birthDateFromDetails = new Date(this.userProfileDetails?.birthdate || \"\");\n      return this.profileForm.value.firstNameAr != this.userProfileDetails?.fnameAr || this.profileForm.value.firstNameEn != this.userProfileDetails?.faNameEn || this.profileForm.value.middleNameAr != this.userProfileDetails?.mnameAr || this.profileForm.value.middleNameEn != this.userProfileDetails?.mnameEn || this.profileForm.value.familyNameAr != this.userProfileDetails?.fanameAr || this.profileForm.value.familyNameEn != this.userProfileDetails?.faNameEn\n      // || this.profileForm.value.birthdate.getTime() != birthDateFromDetails.getTime()\n      // || this.profileForm.value.birthdate != this.userProfileDetails?.birthdate\n      || this.profileForm.value.gender != this.userProfileDetails?.gender || this.profileForm.value.phoneNumber != this.userProfileDetails?.mobile\n      // || this.profileForm.value.countryCode != this.userProfileDetails?.countryCode\n      // || this.profileForm.value.city != this.userProfileDetails?.city\n      || this.profileForm.value.nationality != this.userProfileDetails?.nationality || this.profileForm.value.educationallevel != this.userProfileDetails?.eduLevel\n      // || this.profileForm.value.occupation != this.userProfileDetails?.occupation\n      || this.profileForm.value.address != this.userProfileDetails?.address || this.profileForm.value.quraanMemorization != this.userProfileDetails?.quraanMemorizeAmount;\n      // || this.profileForm.value.ejazaAttachments!= this.userProfileDetails?.ejazaAttachments\n    }\n    getCountryIsoCode() {\n      this.userService.getCountryIsoCode().subscribe(res => {\n        let code = res.data;\n        this.telInputParam = {\n          // phoneNumber:'+201062100486',\n          isRequired: true,\n          countryIsoCode: '{\"initialCountry\": \"' + code.toLowerCase() + '\"}'\n        };\n        // this.telInputParam.countryIsoCode = '{\"initialCountry\": \"' + code.toLowerCase() +'\"}';\n      });\n    }\n    getCitiesLookupByCountry(id) {\n      let countryId = this.f['countryCode'].value;\n      //let countryId = this.profileForm.value.countryCode;\n      this.lookupService.getCitiesByCountryId(countryId || '').subscribe(res => {\n        // this.collectionOfLookup.CITY = res.data;\n        if (res.isSuccess) {\n          this.collectionOfLookup.CITY = res.data;\n          this.collectionOfLookup && this.collectionOfLookup.CITY ? this.f.city.setValue(this.collectionOfLookup.CITY[0].id) : '';\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      });\n    }\n    setCurrentLang() {\n      this.emitHeaderTitle();\n      this.languageService.currentLanguageEvent.subscribe(res => {\n        this.emitHeaderTitle();\n        this.buildForm();\n        this.PopulateForm();\n      });\n    }\n    emitHeaderTitle() {\n      this.languageService.headerPageNameEvent.emit(this.translate.instant('UPDATE_USER_PG.TITLE'));\n    }\n    isRtlMode() {\n      return this.translate.currentLang == LanguageEnum.ar ? true : false;\n    }\n    getUserProfile(id) {\n      this.userService.viewUserProfileDetails(id || '').subscribe(res => {\n        if (res.isSuccess) {\n          this.userProfileDetails = res.data;\n          if (!this.userProfileDetails?.proPic) {\n            this.userProfileDetails.proPic = this.imagesPathesService.profile;\n          }\n          this.PopulateForm();\n          if (this.userProfileDetails.countryCode) {\n            this.getCitiesLookupByCountry(this.userProfileDetails.countryCode);\n          }\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    onSubmit(value) {\n      this.isSubmit = true;\n      this.resMessage = {};\n      if (this.profileForm.valid) {\n        this.updateUserModel = {\n          usrId: this.currentUser?.id,\n          firstAr: this.profileForm.value.firstNameAr != null ? this.profileForm.value.firstNameAr : this.userProfileDetails?.fnameAr,\n          firstEn: this.profileForm.value.firstNameEn != null ? this.profileForm.value.firstNameEn : this.userProfileDetails?.fnameEn,\n          middleAr: this.profileForm.value.middleNameAr != null ? this.profileForm.value.middleNameAr : this.userProfileDetails?.mnameAr,\n          middleEn: this.profileForm.value.middleNameEn != null ? this.profileForm.value.middleNameEn : this.userProfileDetails?.mnameEn,\n          familyAr: this.profileForm.value.familyNameAr != null ? this.profileForm.value.familyNameAr : this.userProfileDetails?.fanameAr,\n          familyEn: this.profileForm.value.familyNameEn != null ? this.profileForm.value.familyNameEn : this.userProfileDetails?.faNameEn,\n          // birthdate: this.profileForm.value.birthdate,\n          birthdate: this.selectedDateType == 1 ? this.profileForm.value.birthdate : null,\n          birthGregorian: this.selectedDateType == 2 ? this.profileForm.value.birthdate : null,\n          gender: this.profileForm.value.gender,\n          mobile: this.profileForm.value.phoneNumber,\n          // countryCode: this.profileForm.value.countryCode,\n          // city: this.profileForm.value.city,\n          nationality: this.profileForm.value.nationality,\n          eduLevel: this.profileForm.value.educationallevel,\n          // occupation: this.profileForm.value.occupation,\n          address: this.profileForm.value.address,\n          quraanMemorizeAmount: this.profileForm.value.quraanMemorization,\n          ejazaIds: this.ejazaAttachmentIds,\n          birthDispMode: this.selectedDateType\n        };\n        this.coursesMessage = {};\n        this.archiveMessage = {};\n        this.shiekhsMessage = {};\n        this.updateUserModel.sheikhs = [];\n        if (this.selectedShiekhsList.length) {\n          Array.from(this.selectedShiekhsList).forEach(elm => {\n            if (this.updateUserModel.sheikhs) {\n              this.updateUserModel.sheikhs.push({\n                sheikhsIds: elm.id\n              });\n            }\n          });\n        }\n        this.updateUserModel.scientificArchives = [];\n        if (this.selectedArchivesList.length) {\n          Array.from(this.selectedArchivesList).forEach(elm => {\n            if (this.updateUserModel.scientificArchives) {\n              this.updateUserModel.scientificArchives.push({\n                archiveId: elm.id\n              });\n            }\n          });\n        }\n        this.updateUserModel.courses = [];\n        if (this.selectedTrainingCourseList.length) {\n          Array.from(this.selectedTrainingCourseList).forEach(elm => {\n            if (this.updateUserModel.courses) {\n              this.updateUserModel.courses.push({\n                coursesIds: elm.id\n              });\n            }\n          });\n        }\n        this.participantModel = {\n          id: this.updateUserModel.usrId,\n          name_ar: this.updateUserModel.firstAr + \" \" + this.updateUserModel.middleAr + \" \" + this.updateUserModel.familyAr,\n          name_en: this.updateUserModel.firstEn === null ? '' : this.updateUserModel.firstEn + \" \" + this.updateUserModel.middleEn === null ? '' : this.updateUserModel.middleEn + \" \" + this.updateUserModel.familyEn === null ? '' : this.updateUserModel.familyEn,\n          key: this.updateUserModel.usrId,\n          hoffazId: \"1\",\n          avatar_url: this.userProfileDetails?.proPic == null ? this.imagesPathesService.profile : this.userProfileDetails.proPic,\n          role: RoleEnum.Teacher,\n          gender: this.updateUserModel.gender\n        };\n        if (this.chatService.participantData && this.userProfileDetails) {\n          this.chatService.participantData.id = this.updateUserModel.usrId;\n          this.chatService.participantData.name_ar = (this.updateUserModel.firstAr === null ? this.updateUserModel.firstAr : this.updateUserModel.firstAr) + \" \" + (this.updateUserModel.middleAr === null ? this.updateUserModel.middleAr : this.updateUserModel.middleAr) + \" \" + (this.updateUserModel.familyAr === null ? this.updateUserModel.familyAr : this.updateUserModel.familyAr);\n          this.chatService.participantData.name_en = (this.updateUserModel.firstEn === null ? this.updateUserModel.firstEn : this.updateUserModel.firstEn) + \" \" + (this.updateUserModel.middleEn === null ? this.updateUserModel.middleEn : this.updateUserModel.middleEn) + \" \" + (this.updateUserModel.familyEn === null ? this.updateUserModel.familyEn : this.updateUserModel.familyEn);\n          this.chatService.participantData.key = this.updateUserModel.usrId;\n          this.chatService.participantData.hoffazId = \"1\";\n          this.chatService.participantData.avatar_url = this.userProfileDetails.proPic == null ? this.imagesPathesService.profile : this.userProfileDetails.proPic;\n          this.chatService.participantData.role = RoleEnum.Student;\n          this.chatService.participantData.gender = this.updateUserModel.gender;\n          this.chatService.updateParticipant(this.chatService.participantData);\n        } else {\n          this.chatService.addParticipant(this.participantModel);\n        }\n        this.userProfileService.updateUser(this.updateUserModel).subscribe(res => {\n          if (res.isSuccess) {\n            if (!this.userProfileDetails?.id) {\n              this.confirmDialog();\n            } else {\n              this.router.navigateByUrl('user/view-user-profile-details');\n            }\n            this.isSubmit = false;\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.SUCCESS_TYPE\n            };\n          } else {\n            this.isSubmit = false;\n            this.resMessage = {\n              message: res.message,\n              type: BaseConstantModel.DANGER_TYPE\n            };\n          }\n        }, error => {\n          //logging\n        });\n      } else {\n        this.resMessage = {\n          message: this.translate.instant('GENERAL.FORM_INPUT_COMPLETION_MESSAGE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n      }\n    }\n    get f() {\n      return this.profileForm.controls;\n    }\n    buildForm() {\n      if (this.translate.currentLang === LanguageEnum.ar) {\n        this.profileForm = this.fb.group({\n          firstNameAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          middleNameAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          familyNameAr: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          birthdate: ['', [Validators.required]],\n          email: [''],\n          nationality: [null, Validators.required],\n          educationallevel: [null, Validators.required],\n          gender: [null, Validators.required],\n          address: [''],\n          // address: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(50)]],\n          phoneNumber: ['', [Validators.required /*,Validators.pattern(BaseConstantModel.mobilePattern), Validators.minLength(6), Validators.maxLength(16)*/]],\n          // occupation: [null, Validators.required],\n          // countryCode: [null, Validators.required],\n          // city: [null, Validators.required],\n          quraanMemorization: ['', [Validators.pattern(BaseConstantModel.numberBiggerThanZero)]]\n          // userSheikhs: [],\n          // userArchives: [],\n          // userCourses: []\n        });\n      } else {\n        this.profileForm = this.fb.group({\n          firstNameEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          middleNameEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          familyNameEn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n          birthdate: ['', [Validators.required]],\n          email: [''],\n          nationality: [null, Validators.required],\n          educationallevel: [null, Validators.required],\n          gender: [null, Validators.required],\n          address: [''],\n          phoneNumber: ['', [Validators.required, Validators.pattern(BaseConstantModel.mobilePattern)]],\n          // occupation: [null, Validators.required],\n          // countryCode: [null, Validators.required],\n          // city: [null, Validators.required],\n          quraanMemorization: ['', [Validators.pattern(BaseConstantModel.numberBiggerThanZero)]]\n          // userSheikhs: [],\n          // userArchives: [],\n          // userCourses: []\n        });\n      }\n    }\n    PopulateForm() {\n      if (this.translate.currentLang === LanguageEnum.ar) {\n        this.f.firstNameAr.setValue(this.userProfileDetails?.fnameAr ? this.userProfileDetails?.fnameAr : this.userProfileDetails?.fnameEn);\n        this.f.middleNameAr.setValue(this.userProfileDetails?.mnameAr ? this.userProfileDetails?.mnameAr : this.userProfileDetails?.mnameEn);\n        this.f.familyNameAr.setValue(this.userProfileDetails?.fanameAr ? this.userProfileDetails?.fanameAr : this.userProfileDetails?.faNameEn);\n      }\n      if (this.translate.currentLang === LanguageEnum.en) {\n        this.f.firstNameEn.setValue(this.userProfileDetails?.fnameEn ? this.userProfileDetails?.fnameEn : this.userProfileDetails?.fnameAr);\n        this.f.middleNameEn.setValue(this.userProfileDetails?.mnameEn ? this.userProfileDetails?.mnameEn : this.userProfileDetails?.mnameAr);\n        this.f.familyNameEn.setValue(this.userProfileDetails?.faNameEn ? this.userProfileDetails?.faNameEn : this.userProfileDetails?.fanameAr);\n      }\n      this.f.address.setValue(this.userProfileDetails?.address);\n      this.f.gender.setValue(this.userProfileDetails?.gender);\n      this.f.email.setValue(this.userProfileDetails?.usrEmail);\n      // let birthdate = new Date(this.userProfileDetails?.birthdate || '');\n      // if (!isNaN(birthdate.getTime())) {\n      //   this.f.birthdate.setValue(\n      //     new Date(birthdate.setDate(birthdate.getDate() + 1))\n      //       .toISOString()\n      //       .slice(0, 10)\n      //   );\n      // }\n      // let date = new Date(this.userProfileDetails?.birthdate || '');\n      //\n      // this.hijriBirthDateInputParam = {year : date.getFullYear(), month : date.getMonth() + 1, day:date.getDay()}\n      // this.f.birthdate.setValue(date);\n      if (this.userProfileDetails?.birthDispMode == 1) {\n        this.updateCalenderType.selectedDateType = DateType.Hijri;\n        this.selectedDateType = DateType.Hijri;\n        let date = new Date(this.userProfileDetails?.birthdate || '');\n        this.hijriBirthDateInputParam = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n        this.f.birthdate.setValue(date.toDateString());\n      } else if (this.userProfileDetails?.birthDispMode == 2) {\n        this.updateCalenderType.selectedDateType = DateType.Gregorian;\n        this.selectedDateType = DateType.Gregorian;\n        let date = new Date(this.userProfileDetails?.birthGregorian || '');\n        this.hijriBirthDateInputParam = {\n          year: date?.getFullYear(),\n          month: date?.getMonth() + 1,\n          day: date?.getDate()\n        };\n        this.f.birthdate.setValue(date.toDateString());\n      } else {\n        this.updateCalenderType.selectedDateType = DateType.Hijri;\n        this.hijriBirthDateInputParam = {\n          year: NaN,\n          month: NaN,\n          day: NaN\n        };\n      }\n      this.f.nationality.setValue(this.userProfileDetails?.nationality);\n      // this.f.occupation.setValue(this.userProfileDetails?.occupation);\n      this.f.educationallevel.setValue(this.userProfileDetails?.eduLevel);\n      this.f.phoneNumber.setValue(this.userProfileDetails?.mobile);\n      this.telInputParam.phoneNumber = this.userProfileDetails?.mobile;\n      // this.f.countryCode.setValue(this.userProfileDetails?.countryCode);\n      // this.f.city.setValue(this.userProfileDetails?.city);\n      this.f.quraanMemorization.setValue(this.userProfileDetails?.quraanMemorizeAmount);\n      this.fileList = this.userProfileDetails?.ejazaAttachments || [];\n      this.userProfileDetails?.ejazaAttachments.forEach(element => {\n        this.ejazaAttachmentIds.push(element.id);\n      });\n      if (this.userProfileDetails?.usrSheikhs) {\n        this.selectedShiekhsList = this.userProfileDetails?.usrSheikhs;\n      }\n      if (this.userProfileDetails?.usrScientificArchives) {\n        this.selectedArchivesList = this.userProfileDetails?.usrScientificArchives;\n      }\n      if (this.userProfileDetails?.usrCourses) {\n        this.selectedTrainingCourseList = this.userProfileDetails?.usrCourses;\n      }\n    }\n    // fillUserShiekhsList(list) {\n    //     let look = this.jobSectorsLookup;\n    //     return list.map(function mapping(item) {\n    //       let obj: BaseLookupModel;\n    //       let fil = look.filter(i => i.id === item.jobSectorId)[0]\n    //       obj = {\n    //         id: fil.id,\n    //         code: fil.code,\n    //         nameAr: fil.nameAr,\n    //         nameEn: fil.nameEn\n    //       }\n    //       return obj;\n    //     });\n    // }\n    onFileChange(files) {\n      let profImagModel = {\n        usrId: this.currentUser?.id,\n        image: files[0]\n      };\n      this.updateProfilePic(profImagModel);\n    }\n    updateProfilePic(profImagModel) {\n      const formData = new FormData();\n      // formData.append('image', profImagModel.image);\n      // profImagModel.image = formData;\n      formData.append('UserProfilePictureModel.UserId', profImagModel.usrId || '');\n      formData.append('UserProfilePictureModel.ProfileImage', profImagModel.image);\n      this.userService.updateUserProfilePic(formData).subscribe(res => {\n        if (res.isSuccess && this.userProfileDetails) {\n          this.userProfileDetails.proPic = res.data;\n        } else {\n          this.resMessage = {\n            message: res.message,\n            type: BaseConstantModel.DANGER_TYPE\n          };\n        }\n      }, error => {\n        //logging\n      });\n    }\n    DeleteAttachment(index, id) {\n      this.fileList.splice(index, 1);\n      this.ejazaAttachmentIds = this.ejazaAttachmentIds.filter(a => a !== id);\n    }\n    onEjazaFileChange(files) {\n      if (files.length > 0) {\n        Array.from(files).forEach(element => {\n          var fileUploadObj = {\n            containerNameIndex: 4,\n            // need to be changed based on file type\n            file: element\n          };\n          this.fileUploadModel.push(fileUploadObj);\n        });\n        this.UploadFiles(this.fileUploadModel);\n      }\n    }\n    UploadFiles(files) {\n      if (files.length === 0) {\n        return;\n      }\n      this.attachmentService.upload(files).subscribe(res => {\n        Array.from(res.data).forEach(elm => {\n          this.ejazaAttachmentIds.push(elm.id);\n          this.fileList.push(elm);\n        });\n        this.fileUploadModel = [];\n      }, error => {\n        this.fileUploadModel = [];\n        //logging\n      });\n    }\n    addUserShiekhs() {\n      if (!this.profileForm.value.userSheikhs) {\n        this.shiekhsMessage = {\n          message: this.translate.instant('UPDATE_USER_PG.CHOOSE_SHEIKHS'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        // if (this.translate.currentLang == 'ar') {\n        //   this.shiekhsMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.CHOOSE_SHEIKHS'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // } else {\n        //   this.shiekhsMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.CHOOSE_SHEIKHS'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // }\n        return;\n      }\n      this.shiekhsMessage = {};\n      const exist = this.selectedShiekhsList.some(el => el.id === this.profileForm.value.userSheikhs);\n      if (!exist) {\n        if (this.collectionOfLookup.SYSTEM_SHEIKHS) {\n          this.selectedShiekhsList.push(this.collectionOfLookup.SYSTEM_SHEIKHS.filter(el => el.id == this.profileForm.value.userSheikhs)[0]);\n        }\n      }\n    }\n    removeItemFromSelectedShiekhs(item) {\n      let index = this.selectedShiekhsList.indexOf(item);\n      this.selectedShiekhsList.splice(index, 1);\n    }\n    addUserArchives() {\n      if (!this.profileForm.value.userArchives) {\n        // if (this.translate.currentLang == 'ar') {\n        //   this.archiveMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.ASRCHIVE'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // } else {\n        //   this.archiveMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.ASRCHIVE'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // }\n        this.archiveMessage = {\n          message: this.translate.instant('UPDATE_USER_PG.ASRCHIVE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.archiveMessage = {};\n      const exist = this.selectedArchivesList.some(el => el.id === this.profileForm.value.userArchives);\n      if (!exist) {\n        if (this.collectionOfLookup.SCIENTIFIC_ARCHIVES) {\n          this.selectedArchivesList.push(this.collectionOfLookup.SCIENTIFIC_ARCHIVES.filter(el => el.id == this.profileForm.value.userArchives)[0]);\n        }\n      }\n    }\n    removeItemFromSelectedArchives(item) {\n      let index = this.selectedArchivesList.indexOf(item);\n      this.selectedArchivesList.splice(index, 1);\n    }\n    addUserCourses() {\n      if (!this.profileForm.value.userCourses) {\n        // if (this.translate.currentLang == 'ar') {\n        //   this.coursesMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.COURSE'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // } else {\n        //   this.coursesMessage = {\n        //     message: this.translate.instant('UPDATE_USER_PG.COURSE'),\n        //     type: BaseConstantModel.DANGER_TYPE\n        //   }\n        // }\n        this.coursesMessage = {\n          message: this.translate.instant('UPDATE_USER_PG.COURSE'),\n          type: BaseConstantModel.DANGER_TYPE\n        };\n        return;\n      }\n      this.coursesMessage = {};\n      const exist = this.selectedTrainingCourseList.some(el => el.id === this.profileForm.value.userCourses);\n      if (!exist) {\n        if (this.collectionOfLookup.TRAINING_COURSES) {\n          this.selectedTrainingCourseList.push(this.collectionOfLookup.TRAINING_COURSES.filter(el => el.id == this.profileForm.value.userCourses)[0]);\n        }\n      }\n    }\n    removeItemFromSelectedCourses(item) {\n      let index = this.selectedTrainingCourseList.indexOf(item);\n      this.selectedTrainingCourseList.splice(index, 1);\n    }\n    applyPhoneNumber(phoneNumber) {\n      this.f.phoneNumber.setValue(phoneNumber);\n    }\n    Hijri(data) {\n      // date = date.year + '/' + date.month + '/' + date.day;\n      // this.hijriBinding = date\n      data.selectedDateValue = data.selectedDateValue.year + '/' + data.selectedDateValue.month + '/' + data.selectedDateValue.day;\n      this.hijriBinding = data.selectedDateValue;\n      this.selectedDateType = data.selectedDateType;\n      this.f.birthdate.setValue(data.selectedDateValue);\n    }\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    // From drag and drop\n    onDropSuccess(event) {\n      event.preventDefault();\n      this.onFileChange(event.dataTransfer.files);\n    }\n    // From attachment link\n    onChange(event) {\n      this.onFileChange(event.target.files);\n    }\n    setHijri() {\n      let toDayHijriDate = this.dateFormatterService.GetTodayHijri();\n      toDayHijriDate.day = toDayHijriDate.day;\n      this.maxHijriDate = toDayHijriDate;\n      this.minHijriDate = this.dateFormatterService.GetTodayHijri();\n      this.minHijriDate.year = this.minHijriDate.year - 150;\n    }\n    setGreg() {\n      let toDayGreDate = this.dateFormatterService.GetTodayGregorian();\n      toDayGreDate.day = toDayGreDate.day;\n      this.maxGregDate = toDayGreDate;\n      this.minGregDate = this.dateFormatterService.GetTodayGregorian();\n      this.minGregDate.year = this.maxGregDate.year - 150;\n    }\n    // add by shereen\n    logout() {\n      this.authService.logout().subscribe(res => {}, error => {});\n      // log out is required, evenif log out api failed.. need to excute logout operation. also if no internet connection will logout then when he login agian expecting problem with login \n      this.authService.logoutClientAction();\n    }\n    result = '';\n    confirmDialog(id) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const confirm = _this.translate.instant('GENERAL.LOGOUT_CONFIRM');\n        const message = _this.translate.instant('GENERAL.CONFIRM_LOGOUT_MESSAGE');\n        const dialogData = new ConfirmDialogModel(confirm, message);\n        const dialogRef = _this.dialog.open(ConfirmModalComponent, {\n          maxWidth: \"31.25rem\",\n          data: dialogData\n        });\n        dialogRef.afterClosed().subscribe(dialogResult => {\n          _this.result = dialogResult;\n          if (dialogResult == true) {\n            _this.logout();\n          }\n        });\n      })();\n    }\n    static ɵfac = function UpdateUserProfileComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UpdateUserProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LookupService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.AttachmentsService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.LanguageService), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i9.DateFormatterService), i0.ɵɵdirectiveInject(i10.ChatService), i0.ɵɵdirectiveInject(i11.Router), i0.ɵɵdirectiveInject(i12.ImagesPathesService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateUserProfileComponent,\n      selectors: [[\"app-update-user-profile\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"fileInput\", \"\"], [\"class\", \"container-fluid UpdateUser\", 4, \"ngIf\"], [1, \"container-fluid\", \"UpdateUser\"], [1, \"row\"], [1, \"box_container\"], [1, \"form-group\", 3, \"ngSubmit\", \"formGroup\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"profile\", \"p-0\", \"mb-50\", 3, \"dragover\", \"drop\"], [1, \"profile_background\"], [1, \"img_profile\", 3, \"src\"], [\"type\", \"file\", 2, \"display\", \"none\", 3, \"change\"], [\"id\", \"upload_link\", 1, \"custom_file_input\", 2, \"text-decoration\", \"none\", 3, \"click\"], [3, \"src\"], [1, \"internal_scroll\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"mt-4\", \"mb-3\", \"pl-0\", \"pr-0\", \"border_bottom\"], [1, \"UpdateUser__Label\", \"mt-5\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mb-4\", \"pl-0\", \"pr-0\"], [1, \"row\", \"pl-0\", \"pr-0\"], [\"class\", \"col-xl-2 col-lg-2 col-md-4 col-sm-4 col-xs-12\", 4, \"ngIf\"], [\"class\", \"col-xl-2 col-lg-2 col-md-2 col-sm-2 col-xs-12\", 4, \"ngIf\"], [1, \"col-xl-5\", \"col-lg-5\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"updateTeacher_hijriBirthDate\"], [1, \"hijriBirthDate_contanier\"], [\"for\", \"hijriBirthDate\", 1, \"Register__Label\"], [3, \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"minGreg\", \"minHijri\", \"editcalenderType\", \"sendDate\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-2 mb-2\", 4, \"ngIf\"], [1, \"col-xl-3\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"pl-3\", \"pr-3\", \"gender_contanier\"], [\"for\", \"gender\", 1, \"Register__Label\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mt-3\"], [\"aria-label\", \"Select an option\", \"formControlName\", \"gender\", 1, \"example-radio-group\"], [\"class\", \"example-radio-button ml-3\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"mt-4\", \"mb-3\", \"pl-0\", \"pr-0\", \"mt-3\", \"mb-4\", \"border_bottom\"], [1, \"UpdateUser__Label\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"pl-0\", \"pr-0\"], [1, \"col-xl-3\", \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\"], [\"for\", \"phoneNumber\", 1, \"Register__Label\"], [3, \"getPhonNumber\", \"telInputParam\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\"], [\"for\", \"email\", 1, \"Register__Label\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"readonly\", \"\", \"name\", \"email\", 1, \"form-control\", \"UserRegister__FormControl\", \"dimmed\", 2, \"width\", \"100%\"], [\"for\", \"nationality\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"nationality\", 1, \"UpdateUser__Options\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\", \"mt-3\"], [\"for\", \"address\", 1, \"Register__Label\"], [\"id\", \"address\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"address\", \"name\", \"address\", \"width\", \"70px\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\"], [\"for\", \"educationalLevel\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"educationallevel\", 1, \"UpdateUser__Options\"], [1, \"col-xl-3\", \"col-lg-3\", \"col-md-3\", \"col-sm-3\", \"col-xs-12\"], [\"for\", \"quraanMemorization\", 1, \"Register__Label\"], [\"name\", \"nameEn\", \"formControlName\", \"quraanMemorization\", 1, \"UpdateUser__Options\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"value\"], [1, \"form-group\", \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\", \"mx-auto\", \"mb-0\", \"mt-4\"], [\"type\", \"submit\", 1, \"btn\", \"UserLogin__Submit\"], [3, \"class\", 4, \"ngIf\"], [\"for\", \"firstNameAr\", 1, \"Register__Label\"], [\"id\", \"firstNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"firstNameAr\", \"name\", \"firstNameAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"alert\", \"alert-danger\", \"mt-2\", \"mb-2\"], [4, \"ngIf\"], [\"for\", \"middleNameAr\", 1, \"Register__Label\"], [\"id\", \"middleNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"middleNameAr\", \"name\", \"middleNameAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"familyNameAr\", 1, \"Register__Label\"], [\"id\", \"familyNameAr\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"familyNameAr\", \"name\", \"familyNameAr\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"firstNameEn\", 1, \"Register__Label\"], [\"id\", \"firstNameEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"firstNameEn\", \"name\", \"firstNameEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [\"for\", \"middleNameEn\", 1, \"Register__Label\"], [\"id\", \"middleNameEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"middleNameEn\", \"name\", \"middleNameEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-2\", \"col-sm-2\", \"col-xs-12\"], [\"for\", \"familyNameEn\", 1, \"Register__Label\"], [\"id\", \"familyNameEn\", \"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"familyNameEn\", \"name\", \"familyNameEn\", 1, \"form-control\", \"UserRegister__FormControl\", 3, \"placeholder\"], [3, \"sendDate\", \"dateTo\", \"hijri\", \"milady\", \"maxHijri\", \"maxGreg\", \"minGreg\", \"minHijri\", \"editcalenderType\"], [1, \"example-radio-button\", \"ml-3\", 3, \"value\"], [3, \"value\"]],\n      template: function UpdateUserProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, UpdateUserProfileComponent_div_0_Template, 107, 77, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.telInputParam == null ? null : ctx.telInputParam.countryIsoCode);\n        }\n      },\n      styles: [\".save-btn[_ngcontent-%COMP%]{background:var(--main_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_replay[_ngcontent-%COMP%]{background-color:#d6d7d8;border-radius:.75rem;opacity:1;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;border:none;color:#fff;display:block}.user_tabs-btn[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#333;border-radius:.75rem;opacity:1;border:.063rem solid #d6d7d8;width:auto;min-width:7rem;margin:1rem;padding:.5rem;height:2.31rem;display:block;font-size:1rem}.cancel-btn[_ngcontent-%COMP%]{background:var(--second_color);border-radius:.75rem;opacity:1;width:auto;min-width:7rem;padding:.5rem;border:none;color:#fff;display:block;font-size:.875rem}.btn_trash[_ngcontent-%COMP%]{padding:.5rem;background:transparent;border-radius:.5rem;margin:0 .5rem}.btn_fram[_ngcontent-%COMP%]{border:.063rem solid var(--second_color)!important;color:var(--second_color);border-radius:.625rem;padding:.5rem;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}.schedule_btn[_ngcontent-%COMP%]{border:.063rem solid var(--main_color);color:#fff;background-color:var(--main_color);padding:.3rem 1rem;border-radius:.625rem;width:auto;text-align:center;text-decoration:none!important;font-weight:700;font-size:.875rem;cursor:pointer}@media (max-width: 48rem){.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%]{height:3rem}}[_ngcontent-%COMP%]:root{--background_panner: \\\"\\\"}.UpdateUser__Label[_ngcontent-%COMP%]{color:var(--main_color);border-width:70%;border-bottom:aliceblue;font-weight:700;font-size:1.25rem}.UpdateUser__Options[_ngcontent-%COMP%]{width:100%;height:2.1875rem;border-radius:.65rem;border-color:#b3b3b3}.UpdateUser[_ngcontent-%COMP%]   Label[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.UpdateUser[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{white-space:normal!important}.UpdateUser[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]{width:2rem}.UpdateUser[_ngcontent-%COMP%]   .box_container[_ngcontent-%COMP%]{background:#fbfbfb 0% 0% no-repeat padding-box;border-radius:1.1875rem;padding:1.625rem 6rem;height:85vh;margin:auto auto 0;margin-top:1rem}.UpdateUser[_ngcontent-%COMP%]   .box_container[_ngcontent-%COMP%]:lang(ar){text-align:right}.UpdateUser[_ngcontent-%COMP%]   .PlusIcon[_ngcontent-%COMP%]{margin-top:.5rem;margin-left:.5rem}.UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{color:#fff;background-color:#fff}.UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .UpdateUser[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .UpdateUser[_ngcontent-%COMP%]   .show[_ngcontent-%COMP%] > .btn-primary.dropdown-toggle[_ngcontent-%COMP%]{color:#fff;background-color:#fff}.UpdateUser[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:400;text-align:center;vertical-align:middle;-webkit-user-select:none;user-select:none;border:.063rem solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem}.UpdateUser[_ngcontent-%COMP%]   .profile_background[_ngcontent-%COMP%]{background:var(--background_panner);background-size:cover;height:11.9375rem;width:100%;border-radius:1.875rem}.UpdateUser[_ngcontent-%COMP%]   .img_profile[_ngcontent-%COMP%]{position:absolute;top:0;left:40%!important;top:20%;width:13.313rem;height:13.313rem;border-radius:1rem;z-index:1}.UpdateUser[_ngcontent-%COMP%]   .mb-50[_ngcontent-%COMP%]{margin-bottom:3.125rem}.UpdateUser[_ngcontent-%COMP%]   .internal_scroll[_ngcontent-%COMP%]{height:47vh;overflow-y:auto;overflow-x:hidden;padding-left:.5rem;padding-right:.5rem}.UpdateUser[_ngcontent-%COMP%]   .border_bottom[_ngcontent-%COMP%]{border-bottom:.063rem solid #808080}.UpdateUser[_ngcontent-%COMP%]   .custom_file_input[_ngcontent-%COMP%]:lang(ar){left:53%!important;position:absolute;bottom:-2.875rem;cursor:pointer;z-index:1}.UpdateUser[_ngcontent-%COMP%]   .custom_file_input[_ngcontent-%COMP%]:lang(en){left:41%!important;position:absolute;bottom:-2.875rem;cursor:pointer;z-index:1}@media all and (min-width: 19in){.box_container[_ngcontent-%COMP%]{height:90vh;width:100%}.internal_scroll[_ngcontent-%COMP%]{height:60vh}.custom_file_input[_ngcontent-%COMP%]:lang(ar){left:51%!important}.profile_background[_ngcontent-%COMP%]{height:13.9375rem}}@media (max-width: 64rem){.custom_file_input[_ngcontent-%COMP%]:lang(ar){left:58%!important;bottom:-1.25rem}.custom_file_input[_ngcontent-%COMP%]:lang(en){right:58%!important;bottom:-1.25rem}.box_container[_ngcontent-%COMP%]{padding:1.625rem 5rem}.alert-danger[_ngcontent-%COMP%]{line-height:1rem}.UpdateUser__Options[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 48rem){.img_profile[_ngcontent-%COMP%]{top:50%}.internal_scroll[_ngcontent-%COMP%]{height:40rem}}\"]\n    });\n  }\n  return UpdateUserProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}