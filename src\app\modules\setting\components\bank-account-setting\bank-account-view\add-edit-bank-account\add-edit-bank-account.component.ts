import { Component, EventEmitter, OnInit, Output, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UrlTree } from '@angular/router';
import { AlertifyService } from 'src/app/core/services/alertify-services/alertify.service';
import { IBankAccountCreatModel } from 'src/app/core/interfaces/bank-account/ibank-account-creat-model';
import { BaseConstantModel } from 'src/app/core/ng-model/base-constant-model';
import { BaseMessageModel } from 'src/app/core/ng-model/base-message-model';
import { BankAccountService } from 'src/app/core/services/bank-account/bank-account.service';

import { AttachmentsService } from 'src/app/core/services/attachments-services/attachments.service';
import { IFileUpload } from 'src/app/core/interfaces/attachments-interfaces/ifile-upload';
import { IAttachment } from 'src/app/core/interfaces/attachments-interfaces/iattachment';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { IBankAccountUpdateModel } from 'src/app/core/interfaces/bank-account/ibank-account-update-model';
import { IBankAccountDetailsModel } from 'src/app/core/interfaces/bank-account/ibank-account-details-model';
import { ImagesPathesService } from 'src/app/core/services/images-pathes-services/images-pathes.service';

@Component({
  selector: 'app-add-edit-bank-account',
  templateUrl: './add-edit-bank-account.component.html',
  styleUrls: ['./add-edit-bank-account.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule]
})
export class AddEditBankAccountComponent implements OnInit {
  @Output() closeBankAccountForm = new EventEmitter<boolean>();
  @Input() idBankAccount: string | undefined

  bankAccountDetails: IBankAccountDetailsModel = {}
  bankAccountCreatModel: IBankAccountCreatModel | undefined
  bankAccountUpdateModel: IBankAccountUpdateModel | undefined

  resultMessage: BaseMessageModel = {};
  fileUploadModel: IFileUpload[] = [];
  fileList?: IAttachment[] = [];
  logoIds: string[] = [];
  currentUser: IUser | undefined;

  constructor(
    public translate: TranslateService,
    private bankAccountService: BankAccountService,
    private attachmentService: AttachmentsService,
    private alertify: AlertifyService,
    public imagesPathesService:ImagesPathesService) { }

  ngOnInit(): void {
    this.setScssImages();
    if (this.idBankAccount) { this.getDetails() }
  }
  
  setScssImages(){
    this.imagesPathesService.setBackgroundPannerInStyle();
  }

  getDetails() {

    this.bankAccountService.detailsBankAccount(this.idBankAccount || "").subscribe(res => {
      if (res.isSuccess) {
        this.bankAccountDetails = res.data
        this.bankAccountDetails.lgo = res.data.lgo

        if (!this.bankAccountDetails.lgo) {
          this.bankAccountDetails.lgo = this.imagesPathesService.bank;
        }

      }
      else {

      }
    }, error => {
//logging
    })

  }

  listExt = ["jpg", "png", "jpeg", "gif", "bmp", "tif", "tiff", "docx", "svg", "jfif"];
  onFileChange(files: FileList) {
    if (files.length > 0) {
      if (!this.attachmentService.checkFileExtention(files[0], this.listExt)) {
        this.resultMessage = {
          message: this.translate.instant('GENERAL.EXTENTION_FILE'),
          type: BaseConstantModel.DANGER_TYPE
        }
        return;
      }
      Array.from(files).forEach(element => {
        var fileUploadObj: IFileUpload = {
          containerNameIndex: 3, // need to be changed based on file type
          file: element
        }
        this.fileUploadModel?.push(fileUploadObj)
      });
      this.UploadFiles(this.fileUploadModel);
    }

  }

  UploadFiles(files: any) {
    if (files.length === 0) {
      return;
    }
    this.attachmentService.upload(files).subscribe(
      (res: any) => {
        if (this.bankAccountDetails) {
          this.bankAccountDetails.lgo = res.data[0].url
          // Array.from(res.data).forEach((elm: any) => {
          //   this.fileList?.push(elm);
          //   this.bankAccountDetails.lgo = elm.url

          // })
          this.fileUploadModel = [];
        }
      }, error => {
        this.fileUploadModel = [];
        //logging
      }

    )
  }



  savaBankAccount() {
    if (!this.idBankAccount) {
      this.bankAccountCreatModel = {
        arabBnkName: this.bankAccountDetails?.arabBnkName,
        engBnkName: this.bankAccountDetails?.engBnkName,
        bnkNum: this.bankAccountDetails?.bnkNum,
        lgo: this.bankAccountDetails?.lgo,
        iBAN:this.bankAccountDetails?.iBAN

      }
      if (this.bankAccountDetails.lgo == null) {
        this.bankAccountCreatModel.lgo = this.imagesPathesService.bank
      }
      this.bankAccountService.addBankAccount(this.bankAccountCreatModel).subscribe(res => {
        if (res.isSuccess) {
          this.closeForm();
          this.alertify.success(res.message || '');
        }
        else {
          this.alertify.error(res.message || '');
        }
      }, error => {
        //logging
      })
    }
    else {

      this.bankAccountUpdateModel = {
        id: this.idBankAccount,
        arabBnkName: this.bankAccountDetails?.arabBnkName,
        engBnkName: this.bankAccountDetails?.engBnkName,
        bnkNum: this.bankAccountDetails?.bnkNum,
        lgo: this.bankAccountDetails?.lgo,
        iBAN:this.bankAccountDetails?.iBAN

      }
      this.bankAccountService.UpdateBankAccount(this.bankAccountUpdateModel).subscribe(res => {
        if (res.isSuccess) {
          this.closeForm();
          this.alertify.success(res.message || '');
        }
        else {
          this.alertify.error(res.message || '');
        }
      }, error => {
        //logging
      })

    }
  }
  closeForm() {
    this.closeBankAccountForm.emit(false)

  }


}
