import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';
import { IBankAccountListModel } from 'src/app/core/interfaces/bank-account/ibank-account-list-model';
import { AlertifyService } from 'src/app/core/services/alertify-services/alertify.service';
import { BankAccountService } from 'src/app/core/services/bank-account/bank-account.service';
import { ConfirmDialogModel, ConfirmModalComponent } from 'src/app/shared/components/confirm-modal/confirm-modal.component';

@Component({
  selector: 'app-bank-account-list',
  templateUrl: './bank-account-list.component.html',
  styleUrls: ['./bank-account-list.component.scss'],
  standalone: true,
  imports: [CommonModule, TranslateModule]
})
export class BankAccountListComponent implements OnInit {
  @Output() idBankAccount = new EventEmitter<string>();
  bankAccountListModel: IBankAccountListModel[] = []

  constructor(
    public translate: TranslateService,
    private bankAccountService: BankAccountService,
    public dialog: MatDialog,
    private alertify: AlertifyService,
  ) { }

  ngOnInit(): void {
    this.getListBankAccount()
  }
  deleteCard(id: string) {
    const message = this.translate.currentLang === LanguageEnum.en ? "Are you sure that you want to delete this bank account" : "هل متأكد من حذف هذا حساب البنكي ";

    const dialogData = new ConfirmDialogModel(this.translate.currentLang === LanguageEnum.en ? 'Delete  bank account' : 'حذف  الحساب البنكي', message);

    const dialogRef = this.dialog.open(ConfirmModalComponent, {
      maxWidth: "25rem",
      data: dialogData
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult == true) {


        this.bankAccountService.deleteBankAccount(id).subscribe(res => {
          if (res.isSuccess) {
            this.alertify.success(res.message || '');
            this.getListBankAccount();
          }
          else {
            this.alertify.error(res.message || '');
          }
        }, error => {
          //logging
        }
        )
      }
    });
  }
  getListBankAccount() {
    this.bankAccountService.viewBankAccount().subscribe(res => {
      if (res.isSuccess) {
        this.bankAccountListModel = res.data
        // this.alertify.success(res.message || '');
      }
      else {

      }
    }, error => {
      //logging
    })
  }
  editOrAddCardBankaccount(event?: string) {
    this.idBankAccount.emit(event);
  }

}
