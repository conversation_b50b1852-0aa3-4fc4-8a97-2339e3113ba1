import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BankAccountListComponent } from './bank-account-list/bank-account-list.component';
import { AddEditBankAccountComponent } from './add-edit-bank-account/add-edit-bank-account.component';

@Component({
  selector: 'app-bank-account-view',
  templateUrl: './bank-account-view.component.html',
  styleUrls: ['./bank-account-view.component.scss'],
  standalone: true,
  imports: [CommonModule, BankAccountListComponent, AddEditBankAccountComponent]
})
export class BankAccountViewComponent implements OnInit {

  @ViewChild(BankAccountListComponent) bankAccountList: BankAccountListComponent | undefined;


  openBankAccount: boolean = false
  idBankAccount: string | undefined;
  constructor() { }

  ngOnInit(): void {
  }
  editCardBankaccount(event: string) {
    this.openBankAccount = true;
    this.idBankAccount = event;
  }

  closeForm(event: boolean) {
    this.openBankAccount = event;
    this.bankAccountList?.getListBankAccount()
  }
}
