<div class="tab_page notifacation_page condition_details">
    <div class=" pt-3 mb-3">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12 container_haeder mb-2">
                <button class="cancel-btn" (click)="AddConditions()">
                    {{'CONDITIONDS.ADD_CONDITIONS' | translate}}
                </button>
            </div>
        </div>
    </div>

    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12 max_h">
        <div class="row">
            <ng-container *ngFor="let item of predefineConditionsList ;let i=index">

                <ng-container *ngIf="programPredefinedEnum.qualifications === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-qualifications [title]="item"> </app-setting-qualifications>
                    </div>
                </ng-container>

                <ng-container *ngIf="programPredefinedEnum.memorizeQuran === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-part-qraan [title]="item"></app-setting-part-qraan>
                    </div>
                </ng-container>


                <ng-container *ngIf="programPredefinedEnum.age === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-age [title]="item"></app-setting-age>
                    </div>
                </ng-container>



                <ng-container *ngIf="programPredefinedEnum.programFinished === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-last-program [title]="item"></app-setting-last-program>
                    </div>
                </ng-container>



                <ng-container *ngIf="programPredefinedEnum.dgreeaLastProgram === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-degree-last-program [title]="item"></app-setting-degree-last-program>
                    </div>
                </ng-container>



                <ng-container *ngIf="programPredefinedEnum.numberStudentSubscribtion === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-maxmum-subscribe [title]="item"></app-setting-maxmum-subscribe>
                    </div>
                </ng-container>


                <ng-container *ngIf="programPredefinedEnum.accept === item.no">
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-3">
                        <app-setting-accept [title]="item"></app-setting-accept>
                    </div>
                </ng-container>


            </ng-container>

        </div>
        <!-- customConditions cards  -->
        <div class="col-6 mt-4 mb-4 title ">

            <p class="title_custom"> {{'CONDITIONDS.CUSTOM_CONDITIONDS' | translate}} </p>
        </div>
        <div class="row">
            <ng-container *ngFor="let customConditionsData of customConditionsList ;let i=index">
                <div class="col-4 mb-3">
                    <app-custom-conditions [customConditionsModel]='customConditionsData'
                        (editcustomConditionsCard)="editCustomConditions($event)"
                        (deleteCustomConditionsCard)="deleteCustomCard($event)"></app-custom-conditions>
                </div>
            </ng-container>
        </div>

    </div>


</div>