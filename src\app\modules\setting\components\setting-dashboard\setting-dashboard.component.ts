import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ImagesPathesService } from 'src/app/core/services/images-pathes-services/images-pathes.service';
import { LanguageService } from 'src/app/core/services/language-services/language.service';
import { RouterModule } from '@angular/router';

@Component({
    selector: 'app-setting-dashboard',
    templateUrl: './setting-dashboard.component.html',
    styleUrls: ['./setting-dashboard.component.scss'],
    standalone: true,
    imports: [CommonModule, TranslateModule, RouterModule]
})
export class SettingDashboardComponent implements OnInit {

    constructor(
        private languageService: LanguageService,
        public translate: TranslateService,
        public imagesPathesService:ImagesPathesService
    ) { }

    ngOnInit(): void {
        this.setCurrentLang();
    }

    setCurrentLang() {
        this.emitHeaderTitle();
        this.languageService.currentLanguageEvent.subscribe(res => {
            this.emitHeaderTitle();
        });
    }

    emitHeaderTitle() {
        this.languageService.headerPageNameEvent.emit(this.translate.instant('SETTING_PG.TITLE'));
    }

}
