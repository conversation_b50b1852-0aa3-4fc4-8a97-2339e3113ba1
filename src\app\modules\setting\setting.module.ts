import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SettingRoutingModule } from './setting-routing.module';
import { SettingDashboardComponent } from './components/setting-dashboard/setting-dashboard.component';
import { ProgramConditionSettingComponent } from './components/program-condition-setting/program-condition-setting.component';
import { AddConditionSettingComponent } from './components/program-condition-setting/add-condition-setting/add-condition-setting.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { ViewConditionSettingComponent } from './components/program-condition-setting/view-condition-setting/view-condition-setting.component';
import { ProgramCategoriesComponent } from './components/program-categories/program-categories.component';
import { ViewProgramCategoriesComponent } from './components/program-categories/view-program-categories/view-program-categories.component';
import { AddProgramCategoriesComponent } from './components/program-categories/add-program-categories/add-program-categories.component';
import { BankAccountViewComponent } from './components/bank-account-setting/bank-account-view/bank-account-view.component';
import { AddEditBankAccountComponent } from './components/bank-account-setting/bank-account-view/add-edit-bank-account/add-edit-bank-account.component';
import { BankAccountListComponent } from './components/bank-account-setting/bank-account-view/bank-account-list/bank-account-list.component';


@NgModule({
  declarations: [
    ProgramConditionSettingComponent,
    AddConditionSettingComponent,
    ViewConditionSettingComponent,
    ProgramCategoriesComponent,
    ViewProgramCategoriesComponent,
    AddProgramCategoriesComponent,
    BankAccountViewComponent,
    AddEditBankAccountComponent,
    BankAccountListComponent
  ],
  imports: [
    CommonModule,
    SettingRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
    SettingDashboardComponent,
    SharedModule
  ],
  exports: [SettingDashboardComponent]
})
export class SettingModule { }
