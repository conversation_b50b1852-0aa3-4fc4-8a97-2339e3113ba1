<div class="container-fluid">
  <div class="row ">
    <div class="col-lg-6 col-md-6 col-sm-6 LoginBackground_left_img pl-0 pr-0  ">
      <div class="col-12  identity_img pl-0 pr-0 ">
      </div>
      <section class="row justify-content-center  ">
        <div class="box">
          <form class="form-group UserLogin__LoginForm">
            <div class="content_form ">
              <div class="col-lg-12 mb-4 mt-2">
                <h1 class="font-weight-bold "> Login Number </h1>
                <p class="UserLogin__Label">The access code has been sent to your email<small
                    class="hint_yellow px-2">email&#64;gmail.com</small></p>
              </div>
              <div class="col-12 mb-3  d-flex">
                <input class=" form-control bg-white border-md input-sm w-75 valid_num">
                <input class=" form-control bg-white border-md input-sm w-75 valid_num">
                <input class=" form-control bg-white border-md input-sm w-75 valid_num">
                <input class=" form-control bg-white border-md input-sm w-75 valid_num">
              </div>
              <div class="col-lg-12 mb-4 mt-2">
                <p class="UserLogin__Label">I did not receive the passcode.<small class="hint_yellow px-2">Please
                    Resend</small></p>
              </div>
              <div class="col-12">
                <label class="UserLogin__Label" for="UserEmail">Search</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text bg-white pl-2 pr-2 border-md border-right-0">
                      <img class="UserLogin_icon_input" [src]="imagesPathesService.search">
                    </span>
                  </div>
                  <input pInputText placeholder="search"
                    class=" px-1 form-control UserLogin__FormControl bg-white border-left-0 border-md input-sm">
                </div>
              </div>
              <!-- Submit Button -->
              <div class="form-group col-lg-12 mx-auto mb-0 mt-2">
                <button type="submit" class="btn  UserLogin__Submit ">Send</button>
              </div>
            </div>
          </form>
        </div>
      </section>
    </div>
    <div class="col-6 mt-5">
      <mat-card>
        <mat-card-content>
          <h2 class="example-h2">Checkbox configuration</h2>
          <h2 class="example-h2">git color from TS file</h2>
          <section class="example-section">
            <mat-checkbox class="example-margin">Checked</mat-checkbox>
            <mat-checkbox class="example-margin ml-5">Indeterminate</mat-checkbox>
          </section>
          <section>
            <label class="UserLogin__Label" for="UserEmail">Radio-button</label>
            <mat-radio-group aria-label="Select an option" class="example-radio-group">
              <mat-radio-button value="1" class="example-radio-button">Option 1</mat-radio-button>
              <mat-radio-button value="2" class="example-radio-button">Option 2</mat-radio-button>
            </mat-radio-group>
          </section>
        </mat-card-content>
      </mat-card>
      <hr>
      <br>
      <mat-card>
        <div class="row">
          <div class="col-12">
            <label>
              hijri and milady
            </label>
            <div>
              <app-milady-hijri-calendar (sendDate)="HijriTOMilady($event)" [hijri]="hijri" [milady]="milady">
              </app-milady-hijri-calendar>
            </div>
            <p>{{dataPinding}}</p>
          </div>
          <div class="col-12">
            <label>
              hijri
            </label>
            <div>
              <app-milady-hijri-calendar (sendDate)="Hijri($event)" [hijri]="hijri" [milady]="!milady">
              </app-milady-hijri-calendar>
            </div>
            <p>{{higriPinding}}</p>
          </div>
          <div class="col-12">
            <label>
              milady
            </label>
            <div>
              <app-milady-hijri-calendar [hijri]="!hijri" [milady]="milady"
                (sendDate)="Milady($event)"></app-milady-hijri-calendar>
            </div>
            <p>{{MiladyPinding}}</p>
          </div>
        </div>
      </mat-card>
    </div>
  </div>

  <!-- CARDS  -->
  <div class="container mb-5 mt-5">
    <div class="row">
      <div class="col-12">
        <h1>CARDS</h1>
      </div>
      <div class="col-4 mb-3 mt-3">
        <mat-card class="example-card">
          <mat-card-header>
            <div mat-card-avatar class="example-header-image"></div>
            <mat-card-title>The name of the scientific article</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="d-flex justify-content-between">
              <p class="content_card">
                The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan.
                A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally
                bred for hunting.
              </p>
              <img [src]="imagesPathesService.book" class="align-self-baseline">
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="col-4 mb-3 mt-3">
        <mat-card class="example-card">
          <mat-card-header>
            <div mat-card-avatar class="example-header-image"></div>
            <mat-card-title>The name of the scientific article</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="d-flex justify-content-between">
              <p class="content_card">
                The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan.
                A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally
                bred for hunting.
              </p>
              <img [src]="imagesPathesService.audio" class="align-self-baseline">
            </div>
          </mat-card-content>
        </mat-card>
      </div>


      <div class="col-4 mb-3 mt-3">
        <mat-card class="example-card">
          <mat-card-header>
            <div mat-card-avatar class="example-header-image"></div>
            <mat-card-title>The name of the scientific article</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="d-flex justify-content-between">
              <p class="content_card">
                The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan.
                A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally
                bred for hunting.
              </p>
              <img [src]="imagesPathesService.audio" class="align-self-baseline">
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- EXPANDED  -->

  <div class="container mb-5 mt-5">
    <div class="row">
      <div class="col-12">
        <h1>EXPENDED</h1>
      </div>


      <div class="col-9 mb-5">
        <mat-accordion>
          <mat-expansion-panel hideToggle>
            <mat-expansion-panel-header>
              <mat-panel-title>
                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's
              </mat-panel-title>
              <!-- <mat-panel-description>
              This is a summary of the content
            </mat-panel-description> -->
            </mat-expansion-panel-header>
            <ng-template matExpansionPanelContent>
              <p> Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                software like Aldus PageMaker including versions of Lorem Ipsum.
                Why do we use it?
                It is a long established fact that a reader will be distracted by the readable content of a page when
                looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution
                of letters, as opposed to using 'Content here, content here', making it look like readable English. Many
                desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a
                search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have
                evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).

              </p>
              <p> Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's</p>

            </ng-template>
          </mat-expansion-panel>

        </mat-accordion>
      </div>




    </div>
  </div>
  <hr>
  <div class="container-fluid mb-5">
    <h1> Accordion drag and drop</h1>
    <p>Just drag and drop unassigned tasks to assigned tasks and magic will happen!</p>


    <div class="row">
      <div class="col-md-6 mb-5">
        <h5>
          Unassigned tasks
        </h5>

        <mat-accordion cdkDropList #unassignedList="cdkDropList" [cdkDropListData]="unassignedTasks"
          [cdkDropListConnectedTo]="[assignedList]" (cdkDropListDropped)="drop($event)">
          <mat-expansion-panel hideToggle cdkDrag *ngFor="let task of unassignedTasks">
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{ task.title }}
              </mat-panel-title>
              <mat-panel-description>
                <!-- {{ task.description }} -->
              </mat-panel-description>
            </mat-expansion-panel-header>
            <p class="mt-2">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, .</p>
          </mat-expansion-panel>
        </mat-accordion>
      </div>

      <div class="col-md-6 mb-5 ">
        <h5>
          Assigned tasks
        </h5>

        <mat-accordion cdkDropList #assignedList="cdkDropList" [cdkDropListData]="assignedTasks"
          [cdkDropListConnectedTo]="[unassignedList]" (cdkDropListDropped)="drop($event)">
          <mat-expansion-panel *ngFor="let task of assignedTasks" cdkDrag hideToggle>
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{ task.title }}
              </mat-panel-title>
              <mat-panel-description>
                <!-- {{ task.description }} -->
              </mat-panel-description>
            </mat-expansion-panel-header>
            <p>This is the primary content of the panel.</p>
          </mat-expansion-panel>
        </mat-accordion>
      </div>
    </div>



  </div>
  <!--  END EXPANDED  -->
  <!-- OVERLAY -->

  <div class="container border overlay_container">
    <div class="row">
      <div class="col-7 first_container border">
        <button class="btn btn-primary"> OVERLAY_DIV</button>
        <!-- <button mat-button (click)="openDialog()">Open dialog</button> -->

      </div>
      <div class="col-1"></div>
      <div class="col-4 sec_container border"></div>

    </div>

  </div>

  <!--phone number-->
  <div class="container mb-5 mt-5">
    <div class="row">
      <input type="text" value="{{pp}}" ng2TelInput [ng2TelInputOptions]="{initialCountry: 'in'}"
        (hasError)="hasError($event)" (ng2TelOutput)="getNumber($event)" (intlTelInputObject)="telInputObject($event)"
        (countryChange)="onCountryChange($event)" />
    </div>
  </div>

  <!--custom pop-up-->
  <div class="container mb-5 mt-5">
    <div class="row">
      <button mat-raised-button color="primary" (click)="confirmDialog()">Confirm</button>
      <br>
      Response is: {{result}}
    </div>
  </div>

  <!--cusome card-->
  <div class="container mb-5 mt-5">
    <div class="row">
      <div class="col-12">
        <h1>CARDS</h1>
      </div>
      <div class="col-4 mb-3 mt-3" *ngFor="let item of cardLst">

        <app-custome-card [title]='item.title' [content]='item.content' [imgPath]='item.imgPath'></app-custome-card>

      </div>
    </div>
  </div>
  <!-- card student scientific problem -->

  <div class="container-fluid">
    <div class="row ">
      <div class="col-lg-3 col-md-3 col-sm-12" style="background-color:#e6e6e6;">
      </div>
      <div class="col-lg-9  col-md-9 col-sm-12 " style="background-color:#e6e6e6;">
        <div class="row">
          <div class="col-4 mt-3" *ngFor="let card of student_card_scientificProblem">
            <app-card-student-scientific-problem [scientificProblem]='card'></app-card-student-scientific-problem>
          </div>

        </div>

      </div>
      <!-- card admin scientific problem -->

      <div class="col-12" style="background-color: bisque; height: 25rem;">
        <div class="row">
          <div class="col-3 mt-3" *ngFor="let Admincard of admin_card_scientificProblem">
            <app-card-admin-scientific-problem [scientificProblem]='Admincard'></app-card-admin-scientific-problem>
          </div>
        </div>

      </div>
    </div>
    <!--custom accordion with drag and drop-->
    <div class="container mb-5 mt-5">
      <div class="row">
        <div class="col-md-8 mb-5">
          <h5>
            List
          </h5>
          <app-custom-accordion [items]="items"></app-custom-accordion>
        </div>
      </div>
      <div *ngFor="let item of items">
        {{item.id}}
      </div>
    </div>

    <!-- container-fluid -->
  </div>
  <mat-accordion>
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          Welcome
        </mat-panel-title>
      </mat-expansion-panel-header>
      <p>I am the content!</p>
    </mat-expansion-panel>
  </mat-accordion>
  <!-- --phon number-- -->

  <app-tel-input (getPhonNumber)="savePhonNumber($event)" [telInputParam]="telInputParam"></app-tel-input>

  {{telInputParam.phoneNumber}}

  <!-- shared components  in Dashboard  -->

  <div class="container-fluid">
    <app-students-rating></app-students-rating>
    <app-users-counter></app-users-counter>
    <app-khatmeen-students></app-khatmeen-students>
    <app-student-numbers></app-student-numbers>
  </div>


  <!-- END  card student scientific problem -->

  <!--exam builder-->
  <div class="input-group "><button type="button" class="btn btn-primary" (click)="addQuestion()">Add question</button>
  </div>
  <br>
  <div class="input-group ">
    <div *ngFor='let q of exam.questions'>

      <app-question-template [questionTemplate]='q'></app-question-template>
    </div>
  </div>
  <br>
  <div class="input-group "><button type="button" class="btn btn-success" (click)="saveExam()">save</button></div>
  <!-- <button (click)="saveExam()">save</button> -->
  <br>
  <div *ngIf="submitExam" class="input-group ">
    {{examJson}}
    <div *ngFor='let q of exam.questions'>

      <app-question-template [questionTemplate]='q'></app-question-template>
    </div>
  </div>


  <!--recording-->
  <div>
    <app-voice-recording (getVoiceUrl)="saveVoiceUrl($event)"></app-voice-recording>
    {{voiceUrl}}
  </div>

  <!--scientific problem grid-->
  <app-scientific-problems-grid [items]='student_card_scientificProblem'></app-scientific-problems-grid>
  <app-scientific-problems-grid [items]='admin_card_scientificProblem' [numberPerRow]='4'
    [userMode]='adminCard'></app-scientific-problems-grid>

  <!-- not-auth  -->
  <div class="container-fluid">
    <app-not-auth></app-not-auth>
  </div>

  <div echarts [options]="chartOption" class="demo-chart"></div>