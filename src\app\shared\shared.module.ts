import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfirmModalComponent } from './components/confirm-modal/confirm-modal.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { CustomeCardComponent } from './components/custome-card/custome-card.component';
import { MatCardModule } from '@angular/material/card';
import { CustomAccordionComponent } from './components/custom-accordion/custom-accordion.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ViewUserProfileCustomComponent } from './components/view-user-profile-custom/view-user-profile-custom.component';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SearchInputComponent } from './components/search-input/search-input.component';

import { MatIconModule } from '@angular/material/icon';
import { Ng2TelInputModule } from 'ng2-tel-input';
import { TelInputComponent } from './components/tel-input/tel-input.component';
import { MiladyHijriCalendarComponent } from './components/milady-hijri-calendar/milady-hijri-calendar.component';
import { NgxHijriGregorianDatepickerModule } from 'ngx-hijri-gregorian-datepicker';
import { UsersCounterComponent } from './components/users-counter/users-counter.component';
import { KhatmeenStudentsComponent } from './components/khatmeen-students/khatmeen-students.component';
import { StudentNumbersComponent } from './components/student-numbers/student-numbers.component';
import { StudentsRatingComponent } from './components/students-rating/students-rating.component';
import { CardStudentScientificProblemComponent } from './components/card-student-scientific-problem/card-student-scientific-problem.component';
import { CardAdminScientificProblemComponent } from './components/card-admin-scientific-problem/card-admin-scientific-problem.component';
import { QuestionTemplateComponent } from './components/question-template/question-template.component';
import { FormsModule } from '@angular/forms';
import { VoiceRecordingComponent } from './components/voice-recording/voice-recording.component';
import { ScientificProblemsGridComponent } from './components/scientific-problems-grid/scientific-problems-grid.component';
import { MatGridListModule } from '@angular/material/grid-list';
import { NotAuthComponent } from './components/not-auth/not-auth.component';
import { NgbModule, NgbRating, NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';
import { GroupUsersCardComponent } from './components/group-users-card/group-users-card.component';
import { InputSearchListComponent } from './components/input-search-list/input-search-list.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CardNotifacationsComponent } from './components/card-notifacations/card-notifacations.component';
import { ProgramDayTaskHearingComponent } from './components/program-day-tasks/program-day-task-hearing/program-day-task-hearing.component';
import { ProgramDayTaskReadExplanationComponent } from './components/program-day-tasks/program-day-task-read-explanation/program-day-task-read-explanation.component';
import { ProgramDayTaskMemorizeComponent } from './components/program-day-tasks/program-day-task-memorize/program-day-task-memorize.component';
import { ProgramDayTaskRepetitionComponent } from './components/program-day-tasks/program-day-task-repetition/program-day-task-repetition.component';
import { ProgramDayTaskLinkingComponent } from './components/program-day-tasks/program-day-task-linking/program-day-task-linking.component';
import { ProgramDayTaskDailyTestComponent } from './components/program-day-tasks/program-day-task-daily-test/program-day-task-daily-test.component';
import { ProgramDayTaskEncouragementLetterComponent } from './components/program-day-tasks/program-day-task-encouragement-letter/program-day-task-encouragement-letter.component';
import { ProgramDayTaskVideoComponent } from './components/program-day-tasks/program-day-task-video/program-day-task-video.component';
import { ProgramDayTaskReviewComponent } from './components/program-day-tasks/program-day-task-review/program-day-task-review.component';
import { ProgramDayTaskRecitationComponent } from './components/program-day-tasks/program-day-task-recitation/program-day-task-recitation.component';
import { ProgramDayTaskRecitationStudentsComponent } from './components/program-day-tasks/program-day-task-recitation-students/program-day-task-recitation-students.component';
import { ProgramDayTaskTestPhasedComponent } from './components/program-day-tasks/program-day-task-test-phased/program-day-task-test-phased.component';
import { ProgramDayTaskTasmeaComponent } from './components/program-day-tasks/program-day-task-tasmea/program-day-task-tasmea.component';
import { CardFeelingsComponent } from './components/card-feelings/card-feelings.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SettingAgeComponent } from './components/setting-conditions/setting-age/setting-age.component';
import { SettingLastProgramComponent } from './components/setting-conditions/setting-last-program/setting-last-program.component';
import { SettingDegreeLastProgramComponent } from './components/setting-conditions/setting-degree-last-program/setting-degree-last-program.component';
import { SettingMaxmumSubscribeComponent } from './components/setting-conditions/setting-maxmum-subscribe/setting-maxmum-subscribe.component';
import { SettingPartQraanComponent } from './components/setting-conditions/setting-part-qraan/setting-part-qraan.component';
import { CustomConditionsComponent } from './components/setting-conditions/custom-conditions/custom-conditions.component';
import { SettingQualificationsComponent } from './components/setting-conditions/setting-qualifications/setting-qualifications.component';
import { SettingAcceptComponent } from './components/setting-conditions/setting-accept/setting-accept.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { StuCardRequestComponent } from './components/stu-card-request/stu-card-request.component';
import { TeacherCardRequestComponent } from './components/teacher-card-request/teacher-card-request.component';
import { ProgramSubscriptionGridComponent } from './components/program-subscription-grid/program-subscription-grid.component';
import { TeacherSystemCardRequestComponent } from './components/teacher-system-card-request/teacher-system-card-request.component';
import { TeacherSystemSubscriptionGridComponent } from './components/teacher-system-subscription-grid/teacher-system-subscription-grid.component';
import { StudentProgramVacationGridComponent } from './components/student-program-vacation-grid/student-program-vacation-grid.component';
import { StudentProgramVacationCardAdminComponent } from './components/student-program-vacation-card-admin/student-program-vacation-card-admin.component';
import { TeacherDropOutRequestAdminCardComponent } from './components/teacher-drop-out-request-admin-card/teacher-drop-out-request-admin-card.component';
import { TeacherDropOutRequestAdminGridComponent } from './components/teacher-drop-out-request-admin-grid/teacher-drop-out-request-admin-grid.component';
import { TeacherDropOutRequestTeacherCardComponent } from './components/teacher-drop-out-request-teacher-card/teacher-drop-out-request-teacher-card.component';
import { StudentDropOutRequestAdminCardComponent } from './components/student-drop-out-request-admin-card/student-drop-out-request-admin-card.component';
import { StudentDropOutRequestStudentCardComponent } from './components/student-drop-out-request-student-card/student-drop-out-request-student-card.component';
import { StudentDropOutGridComponent } from './components/student-drop-out-grid/student-drop-out-grid.component';
import { TeacherDetailsViewComponent } from './components/teacher-details-view/teacher-details-view.component';
import { StudentDetailsViewComponent } from './components/student-details-view/student-details-view.component';
import { FormatTimePipe } from '../core/pipe/format-time.pipe';
import { JoinExamComponent } from './components/program-day-tasks/join-exam/join-exam.component';
import { TeacherAppointmentRequestsGridComponent } from './components/teacher-appointment-requests-grid/teacher-appointment-requests-grid.component';
import { TeacherAppointmentRequestCardComponent } from './components/teacher-appointment-request-card/teacher-appointment-request-card.component';
import { TeacherStudentProgramForSubscriptionComponent } from './components/teacher-student-program-for-subscription-card/teacher-student-program-for-subscription.component';
import { TeacherStuProgramForSubscriptionGridComponent } from './components/teacher-stu-program-for-subscription-grid/teacher-stu-program-for-subscription-grid.component';
import { StudentProgramVacationCardStudentViewComponent } from './components/student-program-vacation-card-student-view/student-program-vacation-card-student-view.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { GetAllAvailableTeachersGridComponent } from './components/get-all-available-teachers-grid/get-all-available-teachers-grid.component';
import { GetAllAvailableTeachersCardComponent } from './components/get-all-available-teachers-card/get-all-available-teachers-card.component';
import { GetAllAvailableStudentGridComponent } from './components/get-all-available-student-grid/get-all-available-student-grid.component';
import { GetAllAvailableStudentCardComponent } from './components/get-all-available-student-card/get-all-available-student-card.component';
import { ViewExamAnswersComponent } from './components/view-exam-answers/view-exam-answers.component';
import { ChatInputSearchListComponent } from './components/chat-input-search-list/chat-input-search-list.component';
import { AlertDialogComponentComponent } from './components/alert-dialog-component/alert-dialog-component.component';
import { ProgramDayTaskExplanationComponent } from './components/program-day-tasks/program-day-task-explanation/program-day-task-explanation.component';
import { JitsiCallIntegComponent } from './components/jitsi-call-integ/jitsi-call-integ.component';
import { StudentFreeRecitationGridComponent } from './components/student-free-recitation-grid/student-free-recitation-grid.component';
import { StudentScheduleFreeRecitationCardComponent } from './components/student-schedule-free-recitation-card/student-schedule-free-recitation-card.component';
import { StudentScheduleFreeRecitationGridComponent } from './components/student-schedule-free-recitation-grid/student-schedule-free-recitation-grid.component';
import { StudentFreeRecitationCardsComponent } from './components/student-free-recitation-cards/student-free-recitation-cards.component';
import { TeacherScheduleFreeRecitationCardComponent } from './components/teacher-schedule-free-recitation-card/teacher-schedule-free-recitation-card.component';
import { TeacherScheduleFreeRecitationGridComponent } from './components/teacher-schedule-free-recitation-grid/teacher-schedule-free-recitation-grid.component';
import { JitsiGroupCallIntegComponent } from './components/jitsi-group-call-integ/jitsi-group-call-integ.component';
import { UserGroupManagementCardComponent } from './components/user-group-management-card/user-group-management-card.component';
import { SharedOwnerBoardCardComponent } from './components/shared-owner-board-card/shared-owner-board-card.component';
import { CorruptedFilesRequestsGridComponent } from './components/corrupted-files-requests-grid/corrupted-files-requests-grid.component';
import { CorruptedFilesRequestsCardComponent } from './components/corrupted-files-requests-card/corrupted-files-requests-card.component';
import { StudTotalExamTaskDegreeEchartComponent } from './components/admin-dash-board-widgets/stud-total-exam-task-degree-echart/stud-total-exam-task-degree-echart.component';
import { DoughnutChartDegreeCountComponent } from './components/admin-dash-board-widgets/doughnut-chart-degree-count/doughnut-chart-degree-count.component';
import { NgxEchartsModule } from 'ngx-echarts';
import { JitsiCallInterviewComponent } from './components/jitsi-call-interview/jitsi-call-interview.component';
import { BankAccountCardComponent } from './components/bank-account-card/bank-account-card.component';
import { AdminStudentStatisticsCountsChartComponent } from './components/admin-student-statistics-counts-chart/admin-student-statistics-counts-chart.component';
import { PrintCertificateGridComponent } from './components/print-certificate/print-certificate-grid/print-certificate-grid.component';
import { PrintCertificateCardComponent } from './components/print-certificate/print-certificate-card/print-certificate-card.component';
import { AdminFreeRecitationRequestsGridComponent } from './components/admin-free-recitation-requests-grid/admin-free-recitation-requests-grid.component';
import { AdminFreeRecitationRequestsCardComponent } from './components/admin-free-recitation-requests-card/admin-free-recitation-requests-card.component';
import { ShortNumberFormatPipe } from '../core/pipe/short-number-format.pipe';

@NgModule({
  declarations: [ConfirmModalComponent, CustomeCardComponent, CustomAccordionComponent, ViewUserProfileCustomComponent,
    SearchInputComponent, TelInputComponent, MiladyHijriCalendarComponent, UsersCounterComponent, KhatmeenStudentsComponent,
    StudentNumbersComponent, StudentsRatingComponent, CardStudentScientificProblemComponent, CardAdminScientificProblemComponent,
    QuestionTemplateComponent, VoiceRecordingComponent, ScientificProblemsGridComponent, NotAuthComponent, GroupUsersCardComponent,
    InputSearchListComponent,
    CardNotifacationsComponent,
    ProgramDayTaskHearingComponent,
    ProgramDayTaskReadExplanationComponent,
    ProgramDayTaskMemorizeComponent,
    ProgramDayTaskRepetitionComponent,
    ProgramDayTaskLinkingComponent,
    ProgramDayTaskDailyTestComponent,
    ProgramDayTaskEncouragementLetterComponent,
    ProgramDayTaskVideoComponent,
    ProgramDayTaskReviewComponent,
    ProgramDayTaskRecitationComponent,
    ProgramDayTaskRecitationStudentsComponent,
    ProgramDayTaskTestPhasedComponent,
    ProgramDayTaskTasmeaComponent,
    CardFeelingsComponent,
    SettingAgeComponent,
    SettingLastProgramComponent,
    SettingDegreeLastProgramComponent,
    SettingMaxmumSubscribeComponent,
    SettingPartQraanComponent,
    CustomConditionsComponent,
    SettingQualificationsComponent,
    SettingAcceptComponent,
    StuCardRequestComponent,
    TeacherCardRequestComponent,
    ProgramSubscriptionGridComponent,
    TeacherStudentProgramForSubscriptionComponent,
    TeacherStuProgramForSubscriptionGridComponent,
    TeacherSystemCardRequestComponent,
    TeacherSystemSubscriptionGridComponent,
    StudentProgramVacationGridComponent,
    StudentProgramVacationCardAdminComponent,
    TeacherDropOutRequestAdminCardComponent,
    TeacherDropOutRequestAdminGridComponent,
    TeacherDropOutRequestTeacherCardComponent,
    StudentDropOutRequestAdminCardComponent,
    StudentDropOutRequestStudentCardComponent,
    StudentDropOutGridComponent,
    StudentProgramVacationCardStudentViewComponent,
    TeacherDetailsViewComponent,
    StudentDetailsViewComponent,
    FormatTimePipe,
    //AdminJoinRequestForTeacherTabAndStudentTabGridComponent,
    //AdminJoinRequestForTeacherTabAndStudentTabCardComponent,
    //AdminDropOutRequestForTeacherTabAndStudentTabGridComponent,
    //AdminVacationRequestForStudentTabGridComponent,
    //AdminVacationRequestForStudentTabCardComponent,
    JoinExamComponent,
    TeacherAppointmentRequestsGridComponent,
    TeacherAppointmentRequestCardComponent,
    GetAllAvailableTeachersGridComponent,
    GetAllAvailableTeachersCardComponent,
    GetAllAvailableStudentGridComponent,
    GetAllAvailableStudentCardComponent,
    // Standalone components removed from declarations
  ],
  imports: [
    CommonModule, RouterModule, TranslateModule,
    MatButtonModule, MatDialogModule, MatCardModule, MatExpansionModule, MatSelectModule, DragDropModule,
    MatIconModule, FormsModule, MatCheckboxModule, MatRadioModule, MatGridListModule,
    NgbModule, MatAutocompleteModule, NgbRatingModule, MatTooltipModule, PdfViewerModule, MatDatepickerModule, MatNativeDateModule,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts'),
    }),
  ],

    exports: [
        MatRadioModule, MatCheckboxModule, MatButtonModule, MatDialogModule, MatCardModule,
        MatExpansionModule, MatSelectModule, DragDropModule, CustomeCardComponent, CustomAccordionComponent,
        SearchInputComponent, TelInputComponent, MiladyHijriCalendarComponent,
        UsersCounterComponent, KhatmeenStudentsComponent, StudentNumbersComponent, StudentsRatingComponent,
        CardStudentScientificProblemComponent, QuestionTemplateComponent, MatGridListModule,
        ScientificProblemsGridComponent, CardAdminScientificProblemComponent, NotAuthComponent, GroupUsersCardComponent,
        InputSearchListComponent, ChatInputSearchListComponent, NgbRatingModule, CardNotifacationsComponent, ProgramDayTaskHearingComponent,
        NgbModule, ProgramDayTaskReadExplanationComponent, ProgramDayTaskRepetitionComponent,
        ProgramDayTaskMemorizeComponent, ProgramDayTaskLinkingComponent, ProgramDayTaskVideoComponent, ProgramDayTaskDailyTestComponent,
        ProgramDayTaskEncouragementLetterComponent, ProgramDayTaskReviewComponent, ProgramDayTaskRecitationComponent,
        ProgramDayTaskRecitationStudentsComponent, ProgramDayTaskTestPhasedComponent,
        ProgramDayTaskTasmeaComponent, CardFeelingsComponent, MatTooltipModule,
        SettingAgeComponent, SettingDegreeLastProgramComponent, SettingLastProgramComponent, SettingQualificationsComponent,
        SettingMaxmumSubscribeComponent, SettingPartQraanComponent, CustomConditionsComponent,
        SettingAcceptComponent, StuCardRequestComponent, TeacherCardRequestComponent, TeacherSystemCardRequestComponent, TeacherStuProgramForSubscriptionGridComponent,
        ProgramSubscriptionGridComponent
        , TeacherSystemSubscriptionGridComponent, StudentProgramVacationGridComponent, TeacherStudentProgramForSubscriptionComponent,
        StudentDropOutGridComponent,
        FormatTimePipe,
        MatDatepickerModule, MatNativeDateModule,
        NgxEchartsModule
        // Removed standalone components from exports
    ]
})
export class SharedModule { }
